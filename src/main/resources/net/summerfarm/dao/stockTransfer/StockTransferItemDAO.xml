<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.dao.stockTransfer.StockTransferItemDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pre_transfer_in_num" jdbcType="BIGINT" property="preTransferInNum"/>
        <result column="transfer_in_sku" jdbcType="VARCHAR" property="transferInSku"/>
        <result column="stock_transfer_id" jdbcType="BIGINT" property="stockTransferId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,pre_transfer_in_num,transfer_in_sku,stock_transfer_id,created_at,updated_at
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into stock_transfer_item(
        stock_transfer_id, transfer_in_sku, pre_transfer_in_num
        )values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.stockTransferId}, #{item.transferInSku}, #{item.preTransferInNum})
        </foreach>
    </insert>

    <select id="selectById" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item
        where id=#{id}
    </select>

    <select id="listByStockTransferId" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item
        where stock_transfer_id=#{transferId}
    </select>

    <select id="listByIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="listBySku" resultType="java.lang.Long">
        select stock_transfer_id from stock_transfer_item
        where transfer_in_sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>

    <select id="listByStockTransferIdsAndSku" resultType="java.lang.Long">
        select id from stock_transfer_item
        where stock_transfer_id in
        <foreach collection="transferIds" item="transferId" open="(" close=")" separator=",">
            #{transferId}
        </foreach>
        and transfer_in_sku = #{sku}
    </select>

    <select id="listByStockTransferIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item
        where stock_transfer_id in
        <foreach collection="transferIds" item="transferId" open="(" close=")" separator=",">
            #{transferId}
        </foreach>
    </select>
</mapper>