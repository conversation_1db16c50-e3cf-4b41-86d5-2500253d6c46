<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.dao.WmsStoreTaskLogDAO">

    <resultMap id="BaseResultMap" type="net.summerfarm.dao.dataobject.WmsStoreTaskLogDO">
        <!--@Table wms_store_task_log-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="bizId" column="biz_id" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="INTEGER"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
        <result property="operateInfo" column="operate_info" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, create_time, update_time, biz_id, biz_type, operator, operator_name, operate_type, operate_info
        from wms_store_task_log
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, create_time, update_time, biz_id, biz_type, operator, operator_name, operate_type, operate_info
        from wms_store_task_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="bizId != null and bizId != ''">
                and biz_id = #{bizId}
            </if>
            <if test="bizType != null">
                and biz_type = #{bizType}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="operatorName != null and operatorName != ''">
                and operator_name = #{operatorName}
            </if>
            <if test="operateType != null and operateType != ''">
                and operate_type = #{operateType}
            </if>
            <if test="operateInfo != null and operateInfo != ''">
                and operate_info = #{operateInfo}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_store_task_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="bizId != null and bizId != ''">
                and biz_id = #{bizId}
            </if>
            <if test="bizType != null">
                and biz_type = #{bizType}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="operatorName != null and operatorName != ''">
                and operator_name = #{operatorName}
            </if>
            <if test="operateType != null and operateType != ''">
                and operate_type = #{operateType}
            </if>
            <if test="operateInfo != null and operateInfo != ''">
                and operate_info = #{operateInfo}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_store_task_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="bizId != null">`biz_id`,</if>
            <if test="bizType != null">`biz_type`,</if>
            <if test="operator != null">`operator`,</if>
            <if test="operatorName != null">`operator_name`,</if>
            <if test="operateType != null">`operate_type`,</if>
            <if test="operateInfo != null">`operate_info`,</if>
        </trim >
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="operator != null">#{operator},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="operateInfo != null">#{operateInfo},</if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_store_task_log
        (biz_id, biz_type, operator, operator_name, operate_type, operate_info)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.bizId}, #{entity.bizType}, #{entity.operator}, #{entity.operatorName}, #{entity.operateType}, #{entity.operateInfo})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_store_task_log(create_time, update_time, biz_id, biz_type, operator, operator_name, operate_type, operate_info)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createTime}, #{entity.updateTime}, #{entity.bizId}, #{entity.bizType}, #{entity.operator}, #{entity.operatorName}, #{entity.operateType}, #{entity.operateInfo})
        </foreach>
        on duplicate key update
        create_time = values(create_time),
        update_time = values(update_time),
        biz_id = values(biz_id),
        biz_type = values(biz_type),
        operator = values(operator),
        operator_name = values(operator_name),
        operate_type = values(operate_type),
        operate_info = values(operate_info)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_store_task_log
        <set>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="bizId != null and bizId != ''">
                biz_id = #{bizId},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name = #{operatorName},
            </if>
            <if test="operateType != null and operateType != ''">
                operate_type = #{operateType},
            </if>
            <if test="operateInfo != null and operateInfo != ''">
                operate_info = #{operateInfo},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_store_task_log where id = #{id}
    </delete>

</mapper>

