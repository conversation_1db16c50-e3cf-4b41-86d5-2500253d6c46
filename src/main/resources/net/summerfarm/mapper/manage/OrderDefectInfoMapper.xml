<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.OrderDefectInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.OrderDefectInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="delivery_plan_id" jdbcType="INTEGER" property="deliveryPlanId" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="defect_amount" jdbcType="INTEGER" property="defectAmount" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, m_id, order_no, delivery_plan_id, weight, sku_name, defect_amount, amount, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_defect_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_defect_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.OrderDefectInfo" useGeneratedKeys="true">
    insert into order_defect_info (sku, m_id, order_no, 
      delivery_plan_id, weight, sku_name, 
      defect_amount, amount, create_time, 
      update_time)
    values (#{sku,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{deliveryPlanId,jdbcType=INTEGER}, #{weight,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{defectAmount,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.OrderDefectInfo" useGeneratedKeys="true">
    insert into order_defect_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        sku,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="defectAmount != null">
        defect_amount,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="defectAmount != null">
        #{defectAmount,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into order_defect_info(sku, m_id, order_no, delivery_plan_id, weight, sku_name, sku_pic, defect_amount, amount)
    values
    <foreach collection="orderDefectInfos" item="item" separator=",">
      (#{item.sku}, #{item.mId}, #{item.orderNo}, #{item.deliveryPlanId}, #{item.weight}, #{item.skuName},#{item.skuPic}
      , #{item.defectAmount}, #{item.amount})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.OrderDefectInfo">
    update order_defect_info
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="defectAmount != null">
        defect_amount = #{defectAmount,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.OrderDefectInfo">
    update order_defect_info
    set sku = #{sku,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      weight = #{weight,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      defect_amount = #{defectAmount,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>