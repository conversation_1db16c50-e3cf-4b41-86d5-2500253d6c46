<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantSituationMapper">
    <resultMap id="baseResultMap" type="net.summerfarm.model.domain.MerchantSituation">
        <id column="id"  property="id"></id>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="status" property="status"/>
        <result column="create_location" property="createLocation"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="creator_id" property="creatorId"/>
        <result column="examine_id" property="examineId"/>
        <result column="examine_time" property="examineTime"/>
        <result column="approval_id" property="approvalId"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="examine_remark" property="examineRemark"/>
        <result column="examine_name" property="examineName"/>
        <result column="approval_name" property="approvalName"/>
        <result column="coupon_id" property="couponId"/>
        <result column="bd_name" property="creatorName"/>
        <result column="situation_type" property="situationType"/>
        <result column="admin_id" property="adminId"/>
    </resultMap>

    <sql id="baseSql">
        id,
        gmt_create,
        gmt_modified  ,
        status,
        create_location ,
        merchant_id ,
        creator_id ,
        examine_id ,
        examine_time ,
        approval_id  ,
        approval_time ,
        approval_remark ,
        examine_remark ,
        examine_name ,
        approval_name,
        coupon_id,
        creator_name,
        admin_id,
        admin_name,
        situation_remake,
        situation_type
    </sql>

    <select id="querySituation" resultMap="baseResultMap">
        select
        <include refid="baseSql"/>
        from merchant_situation
        where id = #{msId}
    </select>


    <select id="querySituationList" resultType="net.summerfarm.model.vo.MerchantSituationVO">
        select
        ms.id,
        ms.gmt_create gmtCreate,
        ms.gmt_modified gmtModified ,
        ms.status,
        ms.create_location createLocation,
        ms.merchant_id merchantId,
        ms.creator_id creatorId,
        ms.examine_id examineId,
        ms.examine_time examineTime,
        ms.approval_id  approvalId,
        ms.approval_time approvalTime,
        ms.approval_remark approvalRemark,
        ms.examine_remark examineRemark,
        ms.examine_name examineName,
        ms.approval_name approvalName,
        ms.coupon_id couponId,
        ms.creator_name creatorName,
        ms.admin_id adminId,
        ms.admin_name adminName,
        c.money couponAmount,
        c.threshold threshold,
        c.reamrk couponRemake,
        c.name couponName,
        m.mname mname,
        m.grade,
        m.size,
        a.area_name areaName,
        m.area_no areaNo,
        ms.situation_remake situationRemake
        from merchant_situation ms
        left JOIN coupon c on c.id = ms.coupon_id
        LEFT JOIN merchant m on m.m_id = ms.merchant_id
        LEFT JOIN area a on a.area_no = m.area_no
        <if test="msVO.adminId != null">
            LEFT JOIN crm_bd_area cba on cba.area_no = m.area_no and cba.admin_id = #{msVO.adminId}
        </if>

        <where>
            <if test = "msVO.id != null">
                AND ms.id = #{msVO.id}
            </if>
            <if test = "msVO.mname != null" >
                AND m.mname LIKE CONCAT('%',#{msVO.mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="msVO.status != null">
                AND ms.status = #{msVO.status}
            </if>
            <if test="msVO.areaNo != null">
                AND m.area_no = #{msVO.areaNo}
            </if>
            <if test="msVO.creatorName != null">
                AND ms.creator_name LIKE CONCAT('%',#{msVO.creatorName,jdbcType=VARCHAR},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (ms.creator_name LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') OR m.mname LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') )
            </if>
            <if test="msVO.creatorId != null">
                AND ms.creator_id = #{msVO.creatorId}
            </if>
            <if test="msVO.adminId != null">
                AND (cba.area_no IS NOT NULL OR  ms.creator_id = #{msVO.adminId})
            </if>
        </where>
        order by ms.gmt_create DESC
    </select>

    <insert id="insertMerchantSituation" parameterType="net.summerfarm.model.domain.MerchantSituation" useGeneratedKeys="true" keyProperty="id">
        insert into merchant_situation (gmt_create,gmt_modified,status,create_location ,merchant_id ,creator_id ,examine_id ,examine_time ,approval_id ,approval_time ,approval_remark ,examine_remark ,examine_name ,approval_name,coupon_id,creator_name,admin_id,admin_name,situation_remake)
        values (now(),now(),#{status},#{createLocation},#{merchantId},#{creatorId},#{examineId},#{examineTime},#{approvalId},#{approvalTime},#{approvalRemark},#{examineRemark},#{examineName},#{approvalName},#{couponId},#{creatorName},#{adminId},#{adminName},#{situationRemake})
    </insert>

    <update id="updateSituation" parameterType="net.summerfarm.model.domain.MerchantSituation">
        update merchant_situation
        <set>
            gmt_modified = now()
            <if test="status != null">
                ,status = #{status}
            </if>
            <if test="examineId != null">
               , examine_id = #{examineId}
            </if>
            <if test="examineTime != null">
               , examine_time = #{examineTime}
            </if>
            <if test="approvalId != null">
               , approval_id = #{approvalId}
            </if>
            <if test="approvalTime != null">
               , approval_time = #{approvalTime}
            </if>
            <if test="approvalRemark != null">
               , approval_remark =#{approvalRemark}
            </if>
            <if test="examineRemark != null">
               , examine_remark = #{examineRemark}
            </if>
            <if test="examineName != null">
               , examine_name = #{examineName}
            </if>
            <if test="approvalName != null">
               , approval_name = #{approvalName}
            </if>
          </set>
        where id = #{id}
    </update>


    <update id="updateSituationALl" parameterType="net.summerfarm.model.domain.MerchantSituation">
        update merchant_situation
        <set>
            gmt_modified =now(),status = 3
            <if test="examineId != null">
                , examine_id = #{examineId}
            </if>
            <if test="examineTime != null">
                , examine_time = #{examineTime}
            </if>
            <if test="approvalId != null">
                , approval_id = #{approvalId}
            </if>
            <if test="approvalTime != null">
                , approval_time = #{approvalTime}
            </if>
            <if test="approvalRemark != null">
                , approval_remark =#{approvalRemark}
            </if>
            <if test="examineRemark != null">
                , examine_remark = #{examineRemark}
            </if>
            <if test="examineName != null">
                , examine_name = #{examineName}
            </if>
            <if test="approvalName != null">
                , approval_name = #{approvalName}
            </if>
        </set>
        where status = #{status}
    </update>


    <select id="querySituationListTime" resultMap="baseResultMap">
        select
        <include refid="baseSql"/>
        from merchant_situation
        <where>
            <if test="endTime != null">
                and gmt_create  <![CDATA[<=]]> #{endTime}
            </if>
            <if test="creatTime != null">
                and gmt_create  <![CDATA[ > ]]> #{creatTime}
            </if>
            <if test="status != null">
                and status  = #{status}
            </if>
            <if test="merchantId != null">
                and merchant_id  = #{merchantId}
            </if>
        </where>
    </select>


    <select id="selectCouponByMid" resultType="net.summerfarm.model.vo.MerchantSituationVO">
        SELECT c.`name` couponName,ms.gmt_create creatTime,c.grouping
        FROM merchant_situation ms
        INNER JOIN coupon c ON ms.coupon_id = c.id
        WHERE ms.merchant_id = #{merchantId} AND ms.gmt_create <![CDATA[ > ]]> #{creatTime}
    </select>

    <select id="selectByMerchantCouponId" resultMap="baseResultMap">
        select
        <include refid="baseSql"/>
        from merchant_situation
        where merchant_coupon_id=#{merchantCouponId}
    </select>
</mapper>