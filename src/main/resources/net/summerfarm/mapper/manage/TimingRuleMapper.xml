<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TimingRuleMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TimingRule" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="timing_sku" property="timingSku" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="display" property="display" jdbcType="INTEGER" />
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime" />
    <result column="delivery_start" property="deliveryStart" />
    <result column="delivery_end" property="deliveryEnd" />
    <result column="rule_information" property="ruleInformation" jdbcType="VARCHAR" />
    <result column="delivery_unit" property="deliveryUnit" jdbcType="INTEGER" />
    <result column="delivery_upper_limit" property="deliveryUpperLimit" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" />
    <result column="type" property="type" />
    <result column="delivery_period" property="deliveryPeriod"/>
    <result column="delivery_start_type" property="deliveryStartType"/>
    <result column="threshold" property="threshold"/>
    <result column="plus_day" property="plusDay" jdbcType="INTEGER"/>
  </resultMap>

  <resultMap id="AreaResultMap" type="net.summerfarm.model.vo.TimingRuleVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="timing_sku" property="timingSku" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="display" property="display" jdbcType="INTEGER" />
    <result column="type" property="type" />
    <result column="threshold" property="threshold"/>
  </resultMap>

  <resultMap id="EffectiveSkuPriceMap" type="net.summerfarm.model.vo.TimingRuleVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="timing_sku" property="timingSku" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="delivery_frequent" property="deliveryFrequent" jdbcType="VARCHAR" />
    <result column="display" property="display" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" />
    <result column="end_time" property="endTime" />
    <result column="delivery_start" property="deliveryStart" />
    <result column="delivery_end" property="deliveryEnd" />
    <result column="rule_information" property="ruleInformation" jdbcType="VARCHAR" />
    <result column="delivery_unit" property="deliveryUnit" jdbcType="INTEGER" />
    <result column="delivery_upper_limit" property="deliveryUpperLimit" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" />
    <result column="type" property="type" />
    <result column="delivery_period" property="deliveryPeriod"/>
    <result column="priority" property="priority" />
    <result column="auto_calculate" property="autoCalculate" jdbcType="BIT" />
    <result column="delivery_start_type" property="deliveryStartType"/>
    <result column="ext_type" property="extType"/>
    <result column="threshold" property="threshold"/>
    <result column="ladder_price" property="ladderPrice"/>
    <result column="plus_day" property="plusDay" jdbcType="INTEGER"/>
  </resultMap>

  <resultMap id="VOMap" type="net.summerfarm.model.vo.TimingRuleVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="timing_sku" property="timingSku" jdbcType="VARCHAR" />
    <result column="pd_name" property="pdName" jdbcType="VARCHAR" />
    <result column="areaNo" property="areaNo" jdbcType="INTEGER" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="delivery_frequent" property="deliveryFrequent" jdbcType="VARCHAR" />
    <result column="display" property="display" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" />
    <result column="end_time" property="endTime" />
    <result column="delivery_start" property="deliveryStart" />
    <result column="delivery_end" property="deliveryEnd" />
    <result column="rule_information" property="ruleInformation" jdbcType="VARCHAR" />
    <result column="delivery_unit" property="deliveryUnit" jdbcType="INTEGER" />
    <result column="delivery_upper_limit" property="deliveryUpperLimit" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" />
    <result column="type" property="type" />
    <result column="delivery_period" property="deliveryPeriod"/>
    <result column="priority" property="priority" />
    <result column="auto_calculate" property="autoCalculate" jdbcType="BIT" />
    <result column="delivery_start_type" property="deliveryStartType"/>
    <result column="ext_type" property="extType"/>
    <result column="threshold" property="threshold"/>
    <result column="plus_day" property="plusDay" jdbcType="INTEGER"/>
    <collection property="ladderPrices" resultMap="net.summerfarm.mapper.manage.LadderPriceMapper.BaseResultMap"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, name, timing_sku, area_no, display, start_time, end_time, delivery_start, delivery_end, rule_information, delivery_unit, delivery_upper_limit,
    update_time, type, priority, auto_calculate, delivery_start_type, plus_day
  </sql>

  <select id="selectById" resultMap="VOMap" parameterType="java.lang.Integer" >
   SELECT tr.id, tr.name, tr.timing_sku, tr.display, tr.start_time, tr.end_time, tr.delivery_start, tr.delivery_end, tr.rule_information, tr.update_time, p.pd_name,tr.threshold,
   delivery_unit, delivery_upper_limit, tr.area_no areaNo, a.area_name, a.delivery_frequent, tr.type, tr.priority, tr.auto_calculate,tr.delivery_period, delivery_start_type,plus_day
    FROM timing_rule tr
    LEFT JOIN inventory i ON tr.timing_sku = i.sku
    LEFT JOIN products p ON p.pd_id = i.pd_id
    LEFT JOIN area a ON a.area_no = tr.area_no
    WHERE tr.id = #{id}
  </select>

  <select id="select" parameterType="net.summerfarm.model.vo.TimingRuleVO" resultMap="VOMap" >
    SELECT tr.id, tr.name, tr.timing_sku, tr.display, tr.start_time, tr.end_time, tr.delivery_start, tr.delivery_end,tr.update_time,
    delivery_unit, delivery_upper_limit, p.pd_name, tr.area_no areaNo, a.area_name, a.delivery_frequent, tr.type,
    tr.priority,tr.delivery_period, delivery_start_type, i.ext_type,tr.plus_day
    FROM timing_rule tr
    LEFT JOIN inventory i ON tr.timing_sku = i.sku
    LEFT JOIN products p ON p.pd_id = i.pd_id
    LEFT JOIN area a on a.area_no = tr.area_no
    <where>
      <if test="timingSku != null" >
        AND tr.timing_sku LIKE CONCAT('%',#{timingSku},'%')
      </if>
      <if test="pdName != null" >
        AND p.pd_name LIKE CONCAT('%',#{pdName},'%')
      </if>
      <if test="areaNoList != null and areaNoList.size > 0" >
        AND tr.area_no in
        <foreach collection="areaNoList" item="areaNo" open="(" separator="," close=")">
            #{areaNo}
        </foreach>
      </if>
      <if test="type != null">
        AND tr.type = #{type}
      </if>
      <if test="display != null">
        AND tr.display = #{display}
      </if>
    </where>
    order BY  tr.id DESC
  </select>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.TimingRule" >
    insert into timing_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        name,
      </if>
      <if test="timingSku != null" >
        timing_sku,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="display != null" >
        display,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="deliveryStart != null" >
        delivery_start,
      </if>
      <if test="deliveryEnd != null" >
        delivery_end,
      </if>
      <if test="ruleInformation != null" >
        rule_information,
      </if>
      <if test="deliveryUnit != null">
        delivery_unit,
      </if>
      <if test="deliveryUpperLimit != null">
        delivery_upper_limit,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="type != null" >
        `type`,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="autoCalculate != null">
        auto_calculate,
      </if>
      <if test="deliveryPeriod != null">
        delivery_period,
      </if>
      <if test="deliveryStartType != null">
        delivery_start_type,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="plusDay != null">
        plus_day,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="timingSku != null" >
        #{timingSku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        #{areaNo},
      </if>
      <if test="display != null" >
        #{display},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryStart != null" >
        #{deliveryStart,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryEnd != null" >
        #{deliveryEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleInformation != null" >
        #{ruleInformation,jdbcType=VARCHAR},
      </if>
      <if test="deliveryUnit != null">
        #{deliveryUnit},
      </if>
      <if test="deliveryUpperLimit != null">
        #{deliveryUpperLimit},
      </if>
      <if test="updateTime != null" >
        #{updateTime},
      </if>
      <if test="type != null" >
        #{type},
      </if>
      <if test="priority != null" >
        #{priority},
      </if>
      <if test="autoCalculate != null">
        #{autoCalculate},
      </if>
      <if test="deliveryPeriod != null">
       #{deliveryPeriod},
      </if>
      <if test="deliveryStartType != null">
        #{deliveryStartType},
      </if>
      <if test="threshold != null">
        #{threshold},
      </if>
      <if test="plusDay != null">
        #{plusDay},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TimingRule" >
    update timing_rule
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test=" timingSku !=null">
        timing_sku = #{timingSku,jdbcType=VARCHAR},
      </if>
      <if test=" display !=null">
        display = #{display},
      </if>
      <if test="areaNo !=null">
        area_no = #{areaNo},
      </if>
      <if test="startTime !=null">
        start_time = #{startTime},
      </if>
      <if test="endTime !=null">
        end_time = #{endTime},
      </if>
      <if test="deliveryStart !=null">
        delivery_start = #{deliveryStart},
      </if>
      <if test="deliveryEnd !=null">
        delivery_end = #{deliveryEnd},
      </if>
      <if test="ruleInformation !=null">
        rule_information = #{ruleInformation,jdbcType=VARCHAR},
      </if>
      <if test="deliveryUnit !=null">
        delivery_unit = #{deliveryUnit},
      </if>
      <if test="deliveryUpperLimit !=null">
        delivery_upper_limit = #{deliveryUpperLimit},
      </if>
      <if test="priority !=null">
        priority = #{priority},
      </if>
      <if test="autoCalculate != null">
        auto_calculate = #{autoCalculate},
      </if>
      <if test="deliveryPeriod != null">
        delivery_period = #{deliveryPeriod},
      </if>
      <if test="deliveryStartType != null">
        delivery_start_type = #{deliveryStartType},
      </if>
      <if test="threshold != null">
        threshold = #{threshold},
      </if>
      <if test="plusDay != null">
        plus_day = #{plusDay},
      </if>
      update_time = now()
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.vo.TimingRuleVO">
    insert into timing_rule
        (name, timing_sku, area_no, display, delivery_start, delivery_end, rule_information, delivery_unit,
         delivery_upper_limit, type, priority, auto_calculate, delivery_period, delivery_start_type, threshold, plus_day)
    values
        <foreach collection="areaNoList" item="item" index="index" separator=",">
          (#{name}, #{timingSku}, #{item}, #{display}, #{deliveryStart}, #{deliveryEnd}, #{ruleInformation}, #{deliveryUnit},
           #{deliveryUpperLimit}, #{type}, #{priority}, #{autoCalculate}, #{deliveryPeriod}, #{deliveryStartType}, #{threshold}, #{plusDay})
        </foreach>
  </insert>

  <update id="updatePriority">
    UPDATE timing_rule
    SET priority = #{priority}
    WHERE id = #{id}
  </update>
  <select id="selectBySku" resultType="java.lang.Integer">
    select tr.id from timing_rule tr
    inner join area a on a.area_no = tr.area_no
    LEFT JOIN (
        select *
        from fence
        where status = 0
        group by area_no
    ) f on f.area_no = a.area_no
    LEFT join warehouse_inventory_mapping wim on tr.timing_sku = wim.sku and f.store_no = wim.store_no
    where   (tr.end_time <![CDATA[>=]]> #{startTime} or  tr.delivery_end <![CDATA[>=]]> #{startTime})
     and tr.timing_sku =#{sku} and tr.type = 0
  </select>
  <select id="selectByArea" resultType="net.summerfarm.model.domain.TimingRule">
    select * from timing_rule tr
                    left join area a on a.area_no = tr.area_no
                         LEFT JOIN (
                select *
                from fence where status = 0
                group by area_no
            ) f on f.area_no = a.area_no
                    left join warehouse_inventory_mapping wim on wim.sku = tr.timing_sku and f.store_no = wim.store_no
                    left join area_store ao on wim.sku = ao.sku and wim.warehouse_no = ao.area_no
    where (tr.end_time &gt;= #{startTime} or tr.delivery_end &gt;= #{startTime})
      and tr.type = 0
      and wim.warehouse_no = #{storeNo}
    group by tr.id
  </select>

  <select id="selectTimingRuleList" parameterType="net.summerfarm.model.domain.TimingRule"
          resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM timing_rule
    <where>
      <if test="timingSku != null">
        AND timing_sku = #{timingSku}
      </if>
      <if test="areaNo != null">
        AND area_no = #{areaNo}
      </if>
      <if test="display != null">
        AND display = #{display}
      </if>
      <if test="type != null">
        AND `type` = #{type}
      </if>
      <if test="id != null">
        AND id != #{id}
      </if>
    </where>
  </select>

  <select id="selectPreOrderListById" parameterType="net.summerfarm.model.domain.TimingRule"
          resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM timing_rule
    where id = #{id}
  </select>

  <select id="selectConfiguredAreaBySku"  resultMap="AreaResultMap" >
    SELECT DISTINCT a.area_no , a.area_name
    FROM timing_rule tr
    INNER JOIN area a on a.area_no = tr.area_no
    <where>
      <if test="timingSku != null" >
        AND tr.timing_sku = #{timingSku}
      </if>
      <if test="areaNo != null" >
        AND tr.area_no = #{areaNo}
      </if>
      <if test="type != null">
        AND tr.type = #{type}
      </if>
      <if test="display != null">
        AND tr.display = #{display}
      </if>
    </where>
  </select>
  
  <select id="listByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
        from timing_rule
    where id in
    <foreach collection="ids" open="(" separator="," close=")" item="id">
      #{id}
    </foreach>
  </select>

  <select id="listByAreaNos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from timing_rule
    where display = 1
    <if test="areaNos != null and areaNos.size > 0">
      and area_no in
      <foreach collection="areaNos" open="(" separator="," close=")" item="areaNo">
        #{areaNo}
      </foreach>
    </if>
    <if test="type != null">
      and type = #{type}
    </if>
    <if test="sku != null">
      and timing_sku = #{sku}
    </if>
  </select>
</mapper>