<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.RefundMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Refund" >
    <id column="refund_id" property="refundId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="refund_no" property="refundNo" jdbcType="VARCHAR" />
    <result column="transaction_number" property="transactionNumber" jdbcType="VARCHAR" />
    <result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR" />
    <result column="total_fee" property="totalFee" jdbcType="DECIMAL" />
    <result column="refund_fee" property="refundFee" jdbcType="DECIMAL" />
    <result column="cash_fee" property="cashFee" jdbcType="DECIMAL" />
    <result column="cash_refund_fee" property="cashRefundFee" jdbcType="DECIMAL" />
    <result column="refund_desc" property="refundDesc" jdbcType="VARCHAR" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="refund_channel" property="refundChannel" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="err_code" property="errCode" jdbcType="VARCHAR" />
    <result column="err_code_des" property="errCodeDes" jdbcType="VARCHAR" />
    <result column="coupon_id" property="couponId" />
  </resultMap>
  <sql id="Base_Column_List" >
    refund_id, order_no, refund_no, after_sale_order_no, transaction_number, total_fee, refund_fee, cash_fee,
    cash_refund_fee, refund_desc, end_time, refund_channel, status, err_code, err_code_des,coupon_id
  </sql>

  <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from refund
    where order_no = #{orderNo}
  </select>


  <update id="updateSelectiveByAfterSaleOrderNo" parameterType="net.summerfarm.model.domain.Refund">
    update refund
    <set>
      <if test="cashRefundFee != null">
        cash_refund_fee = #{cashRefundFee},
      </if>
      <if test="status != null">
        status = #{status}
      </if>
    </set>
    WHERE after_sale_order_no = #{afterSaleOrderNo}
  </update>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Refund" >
    insert into refund
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="refundId != null" >
        refund_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="refundNo != null" >
        refund_no,
      </if>
      <if test="transactionNumber != null" >
        transaction_number,
      </if>
      <if test="afterSaleOrderNo != null" >
        after_sale_order_no,
      </if>
      <if test="totalFee != null" >
        total_fee,
      </if>
      <if test="refundFee != null" >
        refund_fee,
      </if>
      <if test="cashFee != null" >
        cash_fee,
      </if>
      <if test="cashRefundFee != null" >
        cash_refund_fee,
      </if>
      <if test="refundDesc != null" >
        refund_desc,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="refundChannel != null" >
        refund_channel,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="errCode != null" >
        err_code,
      </if>
      <if test="errCodeDes != null" >
        err_code_des,
      </if>
      <if test="couponId != null" >
        coupon_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="refundId != null" >
        #{refundId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundNo != null" >
        #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNumber != null" >
        #{transactionNumber,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleOrderNo != null" >
        #{afterSaleOrderNo},
      </if>
      <if test="totalFee != null" >
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null" >
        #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="cashFee != null" >
        #{cashFee,jdbcType=DECIMAL},
      </if>
      <if test="cashRefundFee != null" >
        #{cashRefundFee,jdbcType=DECIMAL},
      </if>
      <if test="refundDesc != null" >
        #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundChannel != null" >
        #{refundChannel,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="errCode != null" >
        #{errCode,jdbcType=VARCHAR},
      </if>
      <if test="errCodeDes != null" >
        #{errCodeDes,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null" >
        #{couponId},
      </if>
    </trim>
  </insert>

  <select id="selectRefundAmount" resultType="java.math.BigDecimal">
    select sum(refund_fee)/100 from refund r LEFT JOIN payment p on r.order_no = p.order_no
    WHERE r.status = 1
    <if test="accountId != null">
      and p.company_account_id=#{accountId}
    </if>
    <if test="payTypes !=null and !payTypes.isEmpty()">
      and p.pay_type in
      <foreach collection="payTypes" item="payType" open="(" close=")" separator=",">
        #{payType}
      </foreach>
    </if>

    and r.end_time >= #{startTime} and r.end_time <![CDATA[<=]]> #{endTime};
  </select>
    <select id="selectByAfterSaleOrderNo" resultType="net.summerfarm.model.vo.RefundVO">
      select CASE status
       WHEN 0 THEN '新建'
       WHEN 1 THEN '成功'
       WHEN 2 THEN '拒绝'
       WHEN 3 THEN '失败'
       WHEN 4 THEN '退款中'
       ELSE '暂无'
       END refundStatus ,
       err_code_des errCodeDes
       from refund where after_sale_order_no = #{afterSaleOrderNo}
       limit 1
    </select>
</mapper>