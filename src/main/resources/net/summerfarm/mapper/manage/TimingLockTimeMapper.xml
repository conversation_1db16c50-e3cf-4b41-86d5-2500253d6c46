<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TimingLockTimeMapper">


    <insert id="insertTimingLockTime" parameterType="net.summerfarm.model.domain.Activity" useGeneratedKeys="true" keyProperty="id">
        insert into timing_lock_time (delivery_plan_id, type,status,begin_time, end_time)
        values (#{deliveryPlanId},#{type},#{status},#{beginTime},#{endTime})
    </insert>



</mapper>