<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaMarketRuleMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaMarketRule">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="area_name" jdbcType="VARCHAR" property="areaName" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
        <result column="scope_type" jdbcType="INTEGER" property="scopeType" />
    </resultMap>

    <insert id="insertBatch">
        insert into area_market_rule (rule_id, area_no,area_name,creator,updater, scope_id, scope_type) VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.ruleId}, #{item.areaNo}, #{item.areaName},#{item.creator},#{item.updater},#{item.scopeId},#{item.scopeType})
        </foreach>
    </insert>

    <select id="select" resultType="net.summerfarm.model.DTO.AreaDTO">
        select area_no areaNo,area_name areaName, scope_id scopeId, scope_type scopeType from area_market_rule where rule_id = #{ruleId}
    </select>

    <delete id="deleteByRuleId" parameterType="java.lang.Integer">
        delete from area_market_rule where rule_id = #{ruleId}
    </delete>

    <select id="batchSelectList" parameterType="java.util.List" resultType="net.summerfarm.model.DTO.AreaDTO">
        select area_no areaNo,area_name areaName,rule_id ruleId from area_market_rule where rule_id in
        <foreach collection="list" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </select>
    <select id="selectByScope" resultType="net.summerfarm.model.DTO.market.MarketRuleDTO">
        select amr.scope_id scopeId, amr.scope_type scopeType, mr.id, mr.create_time createTime, mr.end_time endTime, mr.start_time startTime
        from market_rule mr
        left join area_market_rule amr on amr.rule_id = mr.id
        where mr.type = 2 and mr.status = 1 and mr.end_time > #{orderTime} and #{orderTime} >= mr.start_time
        and (amr.scope_id, amr.scope_type) in
        <foreach collection="scopes" item="item" open="(" separator="," close=")">
            (#{item.scopeId}, #{item.scopeType})
        </foreach>
    </select>
</mapper>