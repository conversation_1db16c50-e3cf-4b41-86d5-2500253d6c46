<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MsgHistoryMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MsgHistory" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="mode" property="mode" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="operate" property="operate" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, content, mode, user_id, operate, operator, add_time
  </sql>





</mapper>