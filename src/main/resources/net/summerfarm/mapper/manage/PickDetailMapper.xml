<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PickDetailMapper">
    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.PickDetail">
        insert into pick_detail (tms_task_id,`sku`, `pd_name` ,`sku_cnt`,`unit` ,`detail_status` ,`short_cnt` ,`addtime`  ,`temperature`,weight,admin_id,admin_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tmsTaskId},#{item.sku},#{item.pdName},#{item.skuCnt},#{item.unit},#{item.detailStatus},#{item.shortCnt},#{item.addTime},#{item.temperature},#{item.weight}
            ,#{item.adminId},#{item.adminName})
        </foreach>
    </insert>

    <select id="selectShortNumByTaskId" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM( short_cnt ),0)
        FROM
            `pick_detail`
        WHERE
            tms_task_id = #{taskId}
        GROUP BY
            tms_task_id
    </select>
    <select id="selectSkuCntNumByTaskId" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM( sku_cnt ),0)
        FROM
            `pick_detail`
        WHERE
            tms_task_id = #{taskId}
        GROUP BY
            tms_task_id
    </select>
</mapper>