<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TrolleyMapper">
  <update id="cancelCheckByMid" >
    UPDATE trolley
    SET `del_flag` = 1
    where m_id = #{mId,jdbcType=BIGINT}
  </update>

  <update id="cancelCheck" >
    UPDATE trolley
    SET `check` = 0
    where m_id = #{mId,jdbcType=BIGINT} and account_id = #{accountId,jdbcType=BIGINT}
  </update>

  <insert id="merge" parameterType="net.summerfarm.model.domain.Trolley" >
    INSERT INTO `trolley` (`m_id`,`account_id` ,`sku`, suit_id, product_type,`quantity`, `update_time`)
    VALUES (#{mId},#{accountId} , #{sku}, #{suitId}, #{productType}, #{quantity}, NOW())
    ON DUPLICATE KEY UPDATE quantity = VALUES(quantity) ,update_time = NOW(), del_flag = 0 ,`check` =1
  </insert>
  <delete id="deleteByMid">
    delete from trolley where m_id = #{mId}
  </delete>

  <delete id="deleteByBizIdAndSku">
      delete from trolley where biz_id = #{bizId} and sku = #{sku} limit 200
   </delete>

  <delete id="deleteByBizId">
      delete from trolley where biz_id = #{bizId}  limit 200
  </delete>

    <update id="deleteByMidList">
     update  trolley
     set del_flag = 1
     where
    <if test="mIdList != null and mIdList.size > 0 ">
       m_id  In
      <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
        #{mId}
      </foreach>
    </if>
  </update>
  <select id="selectOrderReminder" resultType="net.summerfarm.model.vo.OrderItemVO">
    SELECT t.quantity amount,p.pd_name pdName,c.type
    FROM trolley t
      INNER JOIN inventory i ON t.sku = i.sku
      INNER JOIN products p ON p.pd_id = i.pd_id
      INNER JOIN category c ON p.category_id = c.id
    WHERE t.quantity <![CDATA[ >= ]]> 50 AND m_id = #{mId} AND del_flag = 0 AND t.update_time <![CDATA[ >= ]]> #{time}
  </select>
</mapper>