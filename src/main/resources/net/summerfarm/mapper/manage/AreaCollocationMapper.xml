<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaCollocationMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaCollocation">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="collocation_id" jdbcType="INTEGER" property="collocationId" />
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="area_name" jdbcType="VARCHAR" property="areaName" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <insert id="insertBatch">
        insert into area_collocation (collocation_id, area_no,area_name,creator,updater) VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.collocationId}, #{item.areaNo}, #{item.areaName},#{item.creator},#{item.updater})
        </foreach>
    </insert>
    
    <delete id="deleteByCollocationId" parameterType="java.lang.Integer">
        delete from area_collocation where collocation_id = #{collocationId}
    </delete>

    <select id="selectByCollocationId" resultMap="BaseResultMap">
        select area_no,area_name from area_collocation where collocation_id = #{collocationId}
    </select>
</mapper>