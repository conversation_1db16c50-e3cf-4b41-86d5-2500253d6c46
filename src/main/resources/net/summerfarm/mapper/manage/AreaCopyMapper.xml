<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaCopyMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaCopy">
    <!--@mbg.generated-->
    <!--@Table area_copy-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_area_no" jdbcType="INTEGER" property="sourceAreaNo" />
    <result column="target_area_no" jdbcType="INTEGER" property="targetAreaNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, source_area_no, target_area_no, `status`, creator, updater, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from area_copy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from area_copy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AreaCopy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy (source_area_no, target_area_no, `status`, 
      creator, updater, create_time, 
      update_time)
    values (#{sourceAreaNo,jdbcType=INTEGER}, #{targetAreaNo,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AreaCopy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceAreaNo != null">
        source_area_no,
      </if>
      <if test="targetAreaNo != null">
        target_area_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sourceAreaNo != null">
        #{sourceAreaNo,jdbcType=INTEGER},
      </if>
      <if test="targetAreaNo != null">
        #{targetAreaNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.AreaCopy">
    <!--@mbg.generated-->
    update area_copy
    <set>
      <if test="sourceAreaNo != null">
        source_area_no = #{sourceAreaNo,jdbcType=INTEGER},
      </if>
      <if test="targetAreaNo != null">
        target_area_no = #{targetAreaNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.AreaCopy">
    <!--@mbg.generated-->
    update area_copy
    set source_area_no = #{sourceAreaNo,jdbcType=INTEGER},
      target_area_no = #{targetAreaNo,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      updater = #{updater,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy
    (source_area_no, target_area_no, `status`, creator, updater, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.sourceAreaNo,jdbcType=INTEGER}, #{item.targetAreaNo,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectByTargetAreaNoAndStatus" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from area_copy
    where target_area_no=#{targetAreaNo,jdbcType=INTEGER}
        <if test="statusList != null and statusList.size() > 0">
          and `status` in <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                              #{status,jdbcType=INTEGER}
                          </foreach>
        </if>
    </select>

  <update id="updateStatusById">
    update area_copy
    set status = #{status}
    where id = #{copyId}
  </update>

  <resultMap id="AreaCopyListDTOResultMap" type="net.summerfarm.model.DTO.areacopy.AreaCopyListDTO">
    <id column="id" property="id"/>
    <result column="source_area_no" property="sourceAreaNo"/>
    <result column="target_area_no" property="targetAreaNo"/>
    <result column="status" property="status"/>
    <result column="create_time" property="createTime"/>
    <result column="creatorName" property="creatorName"/>
  </resultMap>
  <select id="selectList" resultMap="AreaCopyListDTOResultMap">
    select area_copy.id, source_area_no, target_area_no, area_copy.create_time, admin.realname creatorName,status
    from area_copy
        left join admin on area_copy.creator = admin.admin_id
    <where>
      <if test=" id != null">
        and id = #{id}
      </if>
      <if test="targetAreaNos != null and targetAreaNos.size() != 0">
        and target_area_no in <foreach collection="targetAreaNos" item = "targetAreaNo" open="(" close=")" separator=",">
                                #{targetAreaNo}
                              </foreach>
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="creator != null">
        and creator = #{creator}
      </if>
    </where>
    order by id desc
  </select>
</mapper>