<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.LadderPriceMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.LadderPrice" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="timing_rule_id" property="timingRuleId" jdbcType="INTEGER" />
    <result column="ladder" property="ladder" jdbcType="INTEGER" />
    <result column="price" property="price" jdbcType="DECIMAL" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="discount_delivery_times" property="discountDeliveryTimes" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, timing_rule_id, ladder, price, update_time, discount_delivery_times
  </sql>


  <delete id="deleteBatchById" parameterType="java.util.List">
    delete from ladder_price
    where id in (
    <foreach collection="list" item="item" separator=",">
      #{item}
    </foreach>
    )
  </delete>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.LadderPrice" >
    update ladder_price
    <set>
      <if test="ladder != null">
        ladder = #{ladder,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="discountDeliveryTimes != null">
        discount_delivery_times = #{discountDeliveryTimes,jdbcType=INTEGER},
      </if>
      update_time = now(),
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTimingRuleId" parameterType="java.lang.Integer" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ladder_price
    where timing_rule_id = #{timingRuleId,jdbcType=INTEGER}
  </select>


  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO ladder_price (timing_rule_id, ladder, price, update_time, discount_delivery_times) VALUES
    <foreach collection="list" item="item"  separator="," >
      (#{item.timingRuleId}, #{item.ladder}, #{item.price}, now(), #{item.discountDeliveryTimes})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySeletive" parameterType="net.summerfarm.model.domain.LadderPrice" >
    update ladder_price
    <set>
      update_time = now(),
      <if test="ladder != null">
        ladder = #{ladder,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="discountDeliveryTimes != null">
        discount_delivery_times = #{discountDeliveryTimes,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectById"  parameterType="java.lang.Integer" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ladder_price
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectMinLadder" resultMap="BaseResultMap">
    select timing_rule_id,MIN(ladder) ladder FROM ladder_price lp
    INNER JOIN timing_rule tr ON lp.timing_rule_id = tr.id
    WHERE tr.display = 1
    GROUP BY timing_rule_id ORDER BY timing_rule_id DESC ;
  </select>
</mapper>