<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FrontCategoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FrontCategory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="outdated" jdbcType="INTEGER" property="outdated" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="f_category_type" jdbcType="INTEGER" property="fCategoryType" />
  </resultMap>
  <resultMap id="BaseResultVOMap" type="net.summerfarm.model.vo.FrontCategoryVO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="outdated" jdbcType="INTEGER" property="outdated" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="f_category_type" jdbcType="INTEGER" property="fCategoryType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, `name`, outdated, icon, updater, update_time, creator, create_time,f_category_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from front_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from front_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FrontCategory" useGeneratedKeys="true">
    insert into front_category (parent_id, `name`, outdated, 
      icon, updater, update_time, 
      creator, create_time)
    values (#{parentId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{outdated,jdbcType=INTEGER}, 
      #{icon,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FrontCategory" useGeneratedKeys="true">
    insert into front_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="outdated != null">
        outdated,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="fCategoryType != null">
        f_category_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="outdated != null">
        #{outdated,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fCategoryType != null">
        #{fCategoryType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FrontCategory">
    update front_category
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="outdated != null">
        outdated = #{outdated,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FrontCategory">
    update front_category
    set parent_id = #{parentId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      outdated = #{outdated,jdbcType=INTEGER},
      icon = #{icon,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="select" resultMap="BaseResultVOMap">
    select
    <include refid="Base_Column_List" />
    from front_category
    where outdated = 0
  </select>
</mapper>