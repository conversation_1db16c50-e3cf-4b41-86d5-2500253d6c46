<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.RamTaskMessageMapper" >

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.RamTaskMessage">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="expected_time" property="expectedTime" jdbcType="VARCHAR" />
        <result column="execution_time" property="executionTime"  jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="error_stack" property="errorStack" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="base_cloumn">
        id,user_id,expected_time,execution_time,status,error_stack,create_time
    </sql>


    <insert id="insertTask" parameterType="net.summerfarm.model.domain.RamTaskMessage">
        INSERT INTO ram_task_message (user_id, expected_time,create_time) VALUES
        (#{userId},#{expectedTime},now())
    </insert>


    <select id="select" resultMap="BaseResultMap" >
        select
        <include refid="base_cloumn"/>
        from ram_task_message
        where status=0
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.RamTaskMessage">
        UPDATE ram_task_message
        <set>
            <if test="status != null">
                status = #{status} ,
            </if>
        </set>
        WHERE user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>