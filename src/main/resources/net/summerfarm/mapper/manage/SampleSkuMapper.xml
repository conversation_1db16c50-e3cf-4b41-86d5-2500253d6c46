<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SampleSkuMapper">

    <resultMap id="baseResultMap" type="net.summerfarm.model.domain.SampleApply">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <insert id="insertSampleSku" keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="net.summerfarm.model.domain.SampleSku">
        insert into sample_sku (sample_id,sku,pd_name,amount,weight)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sampleId},#{item.sku},#{item.pdName},#{item.amount},#{item.weight})
        </foreach>
    </insert>

    <select id="selectBySampleId" parameterType="integer" resultType="net.summerfarm.model.domain.SampleSku">
        select id, sample_id sampleId, sku, pd_name pdName, amount, weight
        from sample_sku
        where sample_id = #{sampleId}
    </select>

    <select id="updateInterceptFlagByIdList">
        UPDATE sample_sku
        SET intercept_flag = #{interceptFlag}
        ,show_flag = #{showFlag}
        ,intercept_time = #{interceptTime}
        WHERE
            id IN
        <foreach collection="sampleIds" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateById" parameterType="net.summerfarm.model.domain.SampleSku">
        update sample_sku
        <set >
            <if test="interceptFlag != null">
                intercept_flag = #{interceptFlag},
            </if>
            <if test="interceptTime != null">
                intercept_time = #{interceptTime},
            </if>
            <if test="showFlag != null">
                show_flag = #{showFlag},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>