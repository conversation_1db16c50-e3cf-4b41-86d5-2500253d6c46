<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ArrivalNoticeMapper">

    <select id="select" resultType="net.summerfarm.model.vo.ArrivalNoticeVO"
            parameterType="net.summerfarm.model.vo.ArrivalNoticeVO">
        select
        an.sku,an.pd_name pdName,an.weight,count(an.id) quantity,sum(an.num) num,an.add_time addTime,an.store_name storeName,an.store_no storeNo,
        MAX(an.add_time) lastTime,MIN(an.add_time) firstTime,an.type, ad.name_remakes nameRemakes,wsc.warehouse_name warehouseName
        from arrival_notice an
        left join warehouse_inventory_mapping  wim on wim.store_no = an.store_no and an.sku = wim.sku
        left join warehouse_storage_center  wsc on wsc.warehouse_no = wim.warehouse_no
        left join inventory inv on an.sku = inv.sku
        left join admin ad on ad.admin_id = inv.admin_id
        where
        an.status=1
        <if test="sku !=null">
            AND an.sku=#{sku}
        </if>
        <if test="pdName != null">
            AND an.pd_name LIKE CONCAT('%',#{pdName},'%')
        </if>
        <if test="storeNo != null">
            AND an.store_no=#{storeNo}
        </if>
        <if test="type != null">
            AND an.type=#{type}
        </if>
        group by an.sku,an.store_no
        <if test="firstTime != null">
            HAVING firstTime <![CDATA[>=]]> #{firstTime}
            AND lastTime <![CDATA[<]]> #{lastTime}
        </if>
        order by quantity desc
    </select>

    <select id="selectNewSubscription" resultType="net.summerfarm.model.vo.ArrivalNoticeVO"
            parameterType="net.summerfarm.model.vo.ArrivalNoticeVO">
        select
        an.sku,an.pd_name pdName,an.weight,count(an.id) quantity,sum(an.num) num,an.add_time addTime,an.store_name storeName,an.store_no storeNo,
        MAX(an.add_time) lastTime,MIN(an.add_time) firstTime,an.type, ad.name_remakes nameRemakes
        from arrival_notice an
        left join inventory inv on an.sku = inv.sku
        left join admin ad on ad.admin_id = inv.admin_id
        where
        an.status=1
        <if test="sku !=null">
            AND an.sku=#{sku}
        </if>
        <if test="pdName != null">
            AND an.pd_name LIKE CONCAT('%',#{pdName},'%')
        </if>
        <if test="storeNo != null">
            AND an.store_no=#{storeNo}
        </if>
        <if test="type != null">
            AND an.type=#{type}
        </if>
        group by an.sku,an.store_no
        <if test="firstTime != null">
            HAVING firstTime <![CDATA[>=]]> #{firstTime}
            AND lastTime <![CDATA[<]]> #{lastTime}
        </if>
        order by quantity desc
    </select>

    <select id="selectList" resultType="net.summerfarm.model.vo.ArrivalNoticeVO"
            parameterType="net.summerfarm.model.vo.ArrivalNoticeVO">
        SELECT an.sku,an.pd_name pdName,an.add_time addTime,an.store_name storeName,an.store_no storeNo,m.area_no areaNo,an.type
        FROM arrival_notice an
        INNER JOIN merchant m ON an.m_id=m.m_id
        WHERE an.`status`=1
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="sku != null">
            AND an.sku = #{sku}
        </if>
        <if test="storeNo != null">
            AND an.store_no = #{storeNo}
        </if>
        GROUP BY an.sku,m.area_no
    </select>


    <select id="selectBySku" resultType="net.summerfarm.model.domain.ArrivalNotice"
            parameterType="net.summerfarm.model.domain.ArrivalNotice">
        select an.id,m.m_id mId,an.pd_name pdName,an.weight,an.sku,an.num num,an.store_no storeNo,m.mname,an.add_time addTime,i.pd_id pdId,m.openid,an.type
        from arrival_notice an
        left join merchant m on m.m_id=an.m_id
        left join inventory i on i.sku=an.sku
        where an.status = 1

        <if test="sku != null">
            and an.sku = #{sku}
        </if>
        <if test="storeNo != null">
            AND an.store_no=#{storeNo}
        </if>


    </select>

    <select id="selectByAreaNo" resultType="net.summerfarm.model.domain.ArrivalNotice">
        select an.id,m.m_id mId,an.pd_name pdName,an.weight,an.sku,an.store_no storeNo,m.mname,an.add_time addTime,i.pd_id pdId,m.openid,an.type
        from arrival_notice an
        left join merchant m on m.m_id=an.m_id
        left join inventory i on i.sku=an.sku
        where an.status = 1
        AND an.sku = #{sku}
        AND m.area_no = #{areaNo}
    </select>
    <delete id="deleteById">
        delete from arrival_notice where id = #{id}
    </delete>
    <delete id="deleteByMid">
        delete from arrival_notice where m_id = #{mId} and `type` = #{type}
    </delete>
    <update id="changeNoticeMerchant">
        update arrival_notice set m_id = #{oldMid} where type = 0 and status = 1 and m_id = #{newMid}
    </update>
    <select id="selectRepeat" resultType="net.summerfarm.model.domain.ArrivalNotice">
        select id, sku, count(1) `count` from arrival_notice where type = 0 and status = 1 and m_id = #{mId} group by sku having `count` > 1
    </select>

</mapper>