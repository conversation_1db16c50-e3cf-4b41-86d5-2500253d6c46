<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CardMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Card">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="card_type" property="cardType" jdbcType="TINYINT" />
        <result column="money" property="money" jdbcType="DECIMAL"/>
        <result column="threshold" property="threshold" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="vaild_date" property="vaildDate"/>
        <result column="vaild_time" property="vaildTime" jdbcType="INTEGER"/>
        <result column="grouping" property="grouping" jdbcType="INTEGER" />
        <result column="times" property="times" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime"/>
        <result column="status" property="status" jdbcType="INTEGER" />
    </resultMap>

    <sql id="BaseColumn">
        id,name,code,card_type,money,threshold,type,vaild_date,vaild_time,grouping,times,remark,add_time,status
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.Card">
        INSERT INTO card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="money != null">
                money,
            </if>
            <if test="threshold != null">
                threshold,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="vaildDate != null">
                vaild_date,
            </if>
            <if test="vaildTime != null">
                vaild_time,
            </if>
            <if test="grouping != null">
                grouping,
            </if>
            <if test="times != null">
                times,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="code != null">
                #{code} ,
            </if>
            <if test="cardType != null">
                #{cardType} ,
            </if>
            <if test="money != null">
                #{money} ,
            </if>
            <if test="threshold != null">
                #{threshold} ,
            </if>
            <if test="type != null">
                #{type} ,
            </if>
            <if test="vaildDate != null">
                #{vaildDate} ,
            </if>
            <if test="vaildTime != null">
                #{vaildTime} ,
            </if>
            <if test="grouping != null">
                #{grouping} ,
            </if>
            <if test="times != null">
                #{times} ,
            </if>
            <if test="remark != null">
                #{remark} ,
            </if>
            <if test="addTime != null">
                #{addTime} ,
            </if>
            <if test="status != null">
                #{status} ,
            </if>
        </trim>
    </insert>

    <update id="updateStatus" parameterType="java.lang.Integer">
        UPDATE card
        SET status = 2
        WHERE id = #{id}
    </update>

    <select id="select" parameterType="net.summerfarm.model.domain.Card" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM  card
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="name != null">
                AND name LIKE concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.domain.Card" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card
        <where>
            <if test="name != null">
                AND name = #{name}
            </if>
            <if test="cardType != null">
                AND card_type = #{cardType}
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="vaildDate != null">
                AND vaild_date = #{vaildDate}
            </if>
            <if test="vaildTime != null">
                AND vaild_time = #{vaildTime}
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card
        WHERE id = #{id,jdbcType=INTEGER}
    </select>


</mapper>