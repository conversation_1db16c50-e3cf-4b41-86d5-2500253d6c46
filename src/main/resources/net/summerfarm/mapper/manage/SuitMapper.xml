<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SuitMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Suit" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="suit_name" property="suitName" jdbcType="VARCHAR" />
    <result column="addtime" property="addtime" />
    <result column="updatetime" property="updatetime" />
    <result column="suit_pic" property="suitPic"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, suit_name, addtime, updatetime,suit_pic
  </sql>


  <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.input.SuitQuery">
    select
     t.id, t.suit_name, t.addtime, t.updatetime
    from suit t
    <if test="sku != null">
      LEFT JOIN suit_item si on t.id = si.suit_id
    </if>
    <where>
      <if test="sku != null">
        AND si.sku = #{sku}
      </if>
      <if test="suitName !=null">
        AND t.suit_name like concat('%',#{suitName},'%')
      </if>
    </where>
    order by id desc
  </select>

  <!---->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from suit
    where id = #{id,jdbcType=INTEGER}
  </select>

    <select id="selectAll" resultType="net.summerfarm.model.domain.Suit">
      select id, suit_name suitName from suit order by id desc
    </select>

    <insert id="insert" useGeneratedKeys="true"  keyProperty="id" parameterType="net.summerfarm.model.domain.Suit" >
    insert into suit (suit_name,suit_pic, updatetime) VALUES (#{suitName},#{suitPic},now())
  </insert>

  <update id="update" parameterType="net.summerfarm.model.domain.Suit">
    update suit
    set suit_name = #{suitName},
        suit_pic=#{suitPic}
    where id = #{id}
  </update>

</mapper>