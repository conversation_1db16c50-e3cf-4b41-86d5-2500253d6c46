<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesPlanMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchasesPlan">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchase_no" property="purchaseNo" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="specification" property="specification" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="supplier" property="supplier" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="production_date" property="productionDate" jdbcType="DATE"/>
        <result column="check_report" property="checkReport" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="in_quantity" property="inQuantity" jdbcType="INTEGER"/>
        <result column="in_price" property="inPrice" jdbcType="DECIMAL"/>
        <result column="adv_quantity" property="advQuantity" jdbcType="INTEGER"/>
        <result column="ver_quantity" property="verQuantity" jdbcType="INTEGER"/>
        <result column="origin_id" property="originId" jdbcType="INTEGER"/>
        <result column="quality_time" property="qualityTime" jdbcType="INTEGER"/>
        <result column="quality_time_unit" property="qualityTimeUnit" jdbcType="VARCHAR"/>
        <result column="market_price" property="marketPrice" jdbcType="DECIMAL"/>
        <result column="settle_flag" property="settleFlag" jdbcType="INTEGER"/>
        <result column="plan_status" property="planStatus" jdbcType="INTEGER"/>
        <result column="arrange_quantity" property="arrangeQuantity" jdbcType="INTEGER"/>
        <result column="price_type" property="priceType"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="latest_arrival_date" property="latestArrivalDate" jdbcType="DATE"/>

    </resultMap>
    <resultMap id="BaseMap" type="net.summerfarm.model.vo.PurchasesPlanVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchase_no" property="purchaseNo" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="specification" property="specification" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="supplier" property="supplier" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="check_report" property="checkReport" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="in_quantity" property="inQuantity" jdbcType="INTEGER"/>
        <result column="in_price" property="inPrice" jdbcType="DECIMAL"/>
        <result column="origin_id" property="originId" jdbcType="INTEGER"/>
        <result column="quality_time" property="qualityTime" jdbcType="INTEGER"/>
        <result column="quality_time_unit" property="qualityTimeUnit" jdbcType="VARCHAR"/>
        <result column="market_price" property="marketPrice" jdbcType="DECIMAL"/>
        <result column="settle_flag" property="settleFlag" jdbcType="INTEGER"/>
        <result column="plan_status" property="planStatus" jdbcType="INTEGER"/>
        <result column="nameRemakes" property="nameRemakes" jdbcType="VARCHAR"/>
        <result column="skuType" property="skuType" javaType="INTEGER"/>
        <result column="avgPrice" property="avgPrice" javaType="DECIMAL"/>
        <result column="price_type" property="priceType"/>
        <result column="processState" property="processState"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , purchase_no, title, specification, sku, price, quantity, pack,unit, supplier,supplier_id,quality_date,check_report,in_quantity,in_price,origin_id,market_price,settle_flag,adv_quantity,ver_quantity,arrange_quantity,price_type,latest_arrival_date
    </sql>

    <select id="selectByPurchasesNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT p.id,
               p.tax_rate,
               p.purchase_no,
               p.title,
               p.specification,
               p.sku,
               p.price,
               p.quantity,
               p.pack,
               p.supplier,
               p.supplier_id,
               p.quality_date,
               p.check_report,
               i.weight,
               p.in_quantity,
               p.in_price,
               p.origin_id,
               p.market_price,
               settle_flag,
               i.type          skuType,
               ad.name_remakes nameRemakes,
               p.adv_quantity,
               p.ver_quantity,
               p.production_date
        FROM purchases_plan p
                 left join inventory i on p.sku = i.sku
                 left join admin ad on i.admin_id = ad.admin_id
        where p.plan_status = 1
          and purchase_no = #{purchaseNo,jdbcType=INTEGER}
    </select>

    <select id="selectByPurchasesNosV2" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT p.id,
        p.tax_rate,
        p.purchase_no,
        p.title,
        p.specification,
        p.sku,
        p.price,
        p.quantity,
        p.pack,
        p.supplier,
        p.supplier_id,
        p.quality_date,
        p.check_report,
        i.weight,
        p.in_quantity,
        p.in_price,
        p.origin_id,
        p.market_price,
        settle_flag,
        i.type skuType,
        ad.name_remakes nameRemakes,
        p.adv_quantity,
        p.ver_quantity,
        p.production_date
        FROM purchases_plan p
        left join inventory i on p.sku = i.sku
        left join admin ad on i.admin_id = ad.admin_id
        <where>
            p.plan_status = 1
            <if test="purchasesNos!=null and purchasesNos.size!=0">
                and purchase_no in
                <foreach collection="purchasesNos" item="purchasesNo" separator="," open="(" close=")">
                    #{purchasesNo}
                </foreach>
            </if>
        </where>
    </select>


    <select id="selectWithPurchasesNos" resultMap="BaseResultMap">
        SELECT p.id, p.purchase_no, p.title, p.specification, p.sku, p.price, p.quantity, p.pack,
        p.supplier,p.supplier_id,p.quality_date,p.check_report,p.in_quantity,p.in_price,p.origin_id,p.market_price,
        settle_flag,p.adv_quantity,p.ver_quantity,p.production_date
        FROM purchases_plan p
        where p.plan_status = 1 and p.purchase_no in
        <foreach collection="list" item="purchaseNo" open="(" close=")" separator=",">
            #{purchaseNo,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectByMatch" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        SELECT p.id,
               p.purchase_no   purchaseNo,
               p.title,
               p.specification,
               p.sku,
               p.price,
               p.quantity,
               p.pack,
               p.supplier,
               p.supplier_id   supplierId,
               p.quality_date  qualityDate,
               p.check_report  checkReport,
               i.weight,
               p.in_quantity   inQuantity,
               p.in_price      inPrice,
               p.origin_id     originId,
               p.market_price  marketPrice,
               settle_flag     settleFlag,
               i.type          skuType,
               ad.name_remakes nameRemakes,
               mpp.wait_match  waitMatch
        FROM purchases_plan p
                 left join inventory i on p.sku = i.sku
                 left join admin ad on i.admin_id = ad.admin_id
                 left join match_purchases_plan mpp on p.id = mpp.purchases_plan_id
        where p.plan_status = 1
          and purchase_no = #{purchaseNo}
          and p.origin_id IS NULL
    </select>

    <select id="selectByPurchases" parameterType="java.lang.String" resultMap="BaseMap">
        SELECT p.id,
               p.purchase_no,
               p.title,
               p.specification,
               p.sku,
               p.price,
               p.quantity,
               p.pack,
               p.supplier,
               p.supplier_id,
               p.quality_date,
               p.check_report,
               i.weight,
               p.in_quantity,
               p.in_price,
               p.origin_id,
               p.market_price,
               settle_flag,
               i.type                skuType,
               ad.name_remakes       nameRemakes,
               p.adv_quantity        advQuantity,
               p.ver_quantity        verQuantity,
               p.arrange_quantity    arrangeQuantity,
               p.latest_arrival_date latestArrivalDate,
               ps.process_state processState
        FROM purchases_plan p
        left join purchases ps on ps.purchase_no=p.purchase_no
        left join inventory i on p.sku = i.sku
        left join admin ad on i.admin_id = ad.admin_id
        where p.plan_status = 1
          and p.purchase_no = #{purchaseNo}
          and p.price <![CDATA[<>]]> 0
    </select>

    <select id="selectByPurchasesPlanIds" resultMap="BaseMap">
        SELECT p.id, p.purchase_no, p.title, p.specification, p.sku, p.price, p.quantity, p.pack,
        p.supplier,p.supplier_id,p.quality_date,p.check_report,i.weight,p.in_quantity,p.in_price,p.origin_id
        FROM purchases_plan p
        left join inventory i on p.sku=i.sku
        LEFT JOIN products pt ON i.pd_id=pt.pd_id
        LEFT JOIN admin ad on i.admin_id = ad.admin_id
        WHERE p.plan_status = 1 and p.id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        OR p.origin_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectVOByPurchasesNo" parameterType="java.lang.String"
            resultType="net.summerfarm.model.vo.PurchasesPlanResultVO">
        SELECT pp.id,
               pp.purchase_no               purchaseNo,
               pp.title,
               pp.specification,
               pp.sku,
               pp.price,
               pp.quantity,
               pp.pack,
               s.`name`                     supplier,
               pp.supplier_id               supplierId,
               pp.quality_date              qualityDate,
               pp.check_report              checkReport,
               i.weight,
               i.unit                       unit,
               i.weight_num                 weightNum,
               i.volume,
               ifnull(i.sku_pic, pt.picture_path) pic,
               pt.pd_name                   pdName,
               pp.in_quantity               inQuantity,
               pp.in_price                  inPrice,
               pp.origin_id                 originId,
               ar.status,
               pt.storage_location          storageLocation,
               pp.production_date           productionDate,
               pp.market_price              marketPrice,
               pt.quality_time              qualityTime,
               pt.quality_time_unit         qualityTimeUnit,
               pt.category_id               categoryId,
               ad.name_remakes              nameRemakes,
               pp.price_type                priceType,
               pp.latest_arrival_date       latestArrivalDate,
               t.backQuantity,
               i.type                       skuType,
               mpp.matching_schedule        matchingSchedule,
               i.ext_type                   extType,
               pp.adv_quantity              advQuantity,
               pp.ver_quantity              verQuantity,
               pp.arrange_quantity          arrangeQuantity,
               ifnull(psp.adjust_amount, 0) adjustAmount,
               pp.latest_arrival_date,
               pp.tax_rate                  taxRate,
               pp.net_weight                 netWeight
        FROM purchases_plan pp
                 left join inventory i on pp.sku = i.sku
                 LEFT JOIN products pt ON i.pd_id = pt.pd_id
                 LEFT JOIN purchases p ON p.purchase_no = pp.purchase_no
                 LEFT JOIN warehouse_stock_ext ar ON p.area_no = ar.warehouse_no AND pp.sku = ar.sku
                 LEFT JOIN supplier s ON pp.supplier_id = s.id
                 LEFT JOIN admin ad on ad.admin_id = i.admin_id
                 LEFT JOIN match_purchases_plan mpp on pp.id = mpp.purchases_plan_id
                 LEFT JOIN purchase_supplier_payment psp
                           on psp.purchase_no = pp.purchase_no and psp.supplier_id = pp.supplier_id
                 LEFT JOIN (
            SELECT sku, batch, quality_date, SUM(quantity) backQuantity
            FROM store_record
            WHERE type = 56
              and batch = #{purchaseNo,jdbcType=INTEGER}
            GROUP BY batch, sku, quality_date
        ) t ON pp.purchase_no = t.batch AND pp.sku = t.sku AND
               ((pp.quality_date = t.quality_date) OR (pp.quality_date IS NULL AND t.quality_date IS NULL))
        where pp.plan_status = 1
          and pp.purchase_no = #{purchaseNo,jdbcType=INTEGER}
    </select>

    <!--查询-->
    <select id="selectOne" resultMap="BaseResultMap">
        SELECT id,
               purchase_no,
               sku,
               quantity
        FROM purchases_plan
        where plan_status = 1
          and id = #{id}
    </select>

    <select id="selectByNoAndSku" resultMap="BaseResultMap">
        SELECT
        id,purchase_no,title,specification,sku,price,quantity,pack,unit,supplier,supplier_id,quality_date,check_report,in_quantity,in_price,origin_id,market_price
        FROM purchases_plan
        where plan_status = 1 and purchase_no=#{purchaseNo} and sku=#{sku}
        <if test="qualityDate ==null">
            and quality_date is null
        </if>
        <if test="qualityDate !=null">
            and quality_date =#{qualityDate}
        </if>

    </select>

    <!--批量插入-->
    <insert id="insertBatch">
        INSERT INTO purchases_plan (purchase_no, specification, title,adv_quantity,
        sku, price,quantity, pack,
        unit,supplier,supplier_id,quality_date,check_report,in_quantity,in_price,origin_id,production_date,market_price,plan_status,price_type,
        latest_arrival_date,tax_rate,arrange_quantity,net_weight)
        values
        <foreach collection="list" item="item" separator=",">
            (#{purchaseNo,jdbcType=INTEGER}, #{item.specification,jdbcType=VARCHAR},#{item.title,jdbcType=VARCHAR},
            ifnull(#{item.advQuantity,jdbcType=INTEGER},0),#{item.sku,jdbcType=VARCHAR},#{item.price},
            #{item.quantity,jdbcType=INTEGER},
            #{item.pack,jdbcType=VARCHAR},
            #{item.unit,jdbcType=VARCHAR}, #{item.supplier}, #{item.supplierId},#{item.qualityDate},#{item.checkReport},
            #{item.inQuantity,jdbcType=INTEGER},#{item.inPrice,jdbcType=DECIMAL},#{item.originId,jdbcType=INTEGER},#{item.productionDate},#{item.marketPrice},#{item.planStatus},#{item.priceType},
            #{item.latestArrivalDate},#{item.taxRate},#{item.arrangeQuantity},#{item.netWeight})
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.PurchasesPlan" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO purchases_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchaseNo != null">
                purchase_no,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="specification != null">
                specification,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="pack != null">
                pack,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="supplier != null">
                supplier,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="checkReport != null">
                check_report,
            </if>
            <if test="inQuantity != null">
                in_quantity,
            </if>
            <if test="inPrice != null">
                in_price,
            </if>
            <if test="originId != null">
                origin_id,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="marketPrice != null">
                market_price,
            </if>
            <if test="planStatus != null">
                plan_status,
            </if>

        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="purchaseNo != null">
                #{purchaseNo,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                #{specification,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="pack != null">
                #{pack,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="supplier != null">
                #{supplier,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=INTEGER},
            </if>
            <if test="qualityDate != null">
                #{qualityDate,jdbcType=DATE},
            </if>
            <if test="checkReport != null">
                #{checkReport,jdbcType=VARCHAR},
            </if>
            <if test="inQuantity != null">
                #{inQuantity,jdbcType=INTEGER},
            </if>
            <if test="inPrice != null">
                #{inPrice,jdbcType=DECIMAL},
            </if>
            <if test="originId != null">
                #{originId,jdbcType=INTEGER},
            </if>
            <if test="productionDate != null">
                #{productionDate},
            </if>
            <if test="marketPrice != null">
                #{marketPrice},
            </if>
            <if test="planStatus != null">
                #{planStatus},
            </if>
        </trim>
    </insert>

    <update id="updateWithPurchases" parameterType="net.summerfarm.model.domain.PurchasesPlan">
        update purchases_plan
        <set>
        <trim>
            production_date = #{productionDate},
            net_weight = #{netWeight},
            <if test="title != null">
                title = #{title},
            </if>
            <if test="sku != null">
                sku = #{sku},
            </if>
            <if test="qualityDate != null">
                quality_date=#{qualityDate},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate},
            </if>
            <if test="checkReport != null">
                check_report = #{checkReport},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplier != null">
                supplier = #{supplier},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="arrangeQuantity != null">
                arrange_quantity = #{arrangeQuantity},
            </if>
            <if test="priceType != null">
                price_type = #{priceType},
            </if>
            <if test="latestArrivalDate != null">
                latest_arrival_date = #{latestArrivalDate},
            </if>
        </trim>
    </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="update" parameterType="net.summerfarm.model.domain.PurchasesPlan">
        UPDATE purchases_plan
        <set>
            <if test="purchaseNo != null">
                purchase_no = #{purchaseNo},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="specification != null">
                specification = #{specification},
            </if>
            <if test="sku != null">
                sku = #{sku},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="pack != null">
                pack = #{pack},
            </if>
            <if test="unit != null">
                unit = #{unit},
            </if>
            <if test="supplier != null">
                supplier = #{supplier},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="qualityDate != null">
                quality_date = #{qualityDate},
            </if>
            <if test="checkReport != null">
                check_report = #{checkReport},
            </if>
            <if test="inQuantity != null">
                in_quantity = #{inQuantity},
            </if>
            <if test="inPrice != null">
                in_price = #{inPrice},
            </if>
            <if test="productionDate != null">
                production_date = #{productionDate},
            </if>
            <if test="marketPrice != null">
                market_price = #{marketPrice},
            </if>
            <if test="settleFlag != null">
                settle_flag = #{settleFlag},
            </if>
            <if test="advQuantity != null">
                adv_quantity = #{advQuantity},
            </if>
            <if test="verQuantity != null">
                ver_quantity = #{verQuantity},
            </if>
            <if test="latestArrivalDate != null">
                latest_arrival_date = #{latestArrivalDate},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectPurchasesPlans" parameterType="net.summerfarm.model.vo.PurchasesPlanVO"
            resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        SELECT wsc.warehouse_name areaName,pp.purchase_no purchaseNo,pp.sku,pd.pd_name pdName,pp.quantity,
        pp.price/pp.quantity avgPrice,pp.price,p.purchase_time purchaseTime,pp.market_price
        FROM purchases_plan pp
        LEFT JOIN purchases p ON pp.purchase_no=p.purchase_no
        LEFT JOIN inventory i ON pp.sku=i.sku
        LEFT JOIN products pd ON i.pd_id=pd.pd_id
        left join warehouse_storage_center wsc on wsc.warehouse_no = p.area_no
        WHERE pp.origin_id IS NULL
        AND pp.plan_status = 1
        AND pp.quantity != 0
        <if test="supplierId != null">
            AND pp.supplier_id = #{supplierId}
        </if>
        <if test="sku != null">
            AND pp.sku = #{sku}
        </if>
        <if test="purchaseNo != null">
            AND pp.purchase_no = #{purchaseNo}
        </if>
        <if test="pdName != null">
            AND pd.pd_name LIKE CONCAT('%',#{pdName},'%')
        </if>
        ORDER BY p.purchase_time DESC
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        WHERE plan_status = 1 and id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectBySku" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        <where>
            AND plan_status = 1
            <if test="purchaseNo != null">
                AND purchase_no = #{purchaseNo}
            </if>
            <if test="sku != null">
                AND sku = #{sku,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectListByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        WHERE plan_status = 1 And (id = #{id,jdbcType=INTEGER} OR origin_id = #{id,jdbcType=INTEGER})
    </select>
    <select id="selectNewest" resultMap="BaseResultMap">
        select pp.id,
               pp.purchase_no,
               title,
               specification,
               sku,
               price,
               quantity,
               pack,
               unit,
               pp.supplier,
               supplier_id,
               check_report,
               quality_date,
               in_quantity,
               in_price,
               origin_id,
               production_date,
               market_price
        from purchases_plan pp
                 inner join purchases p on p.purchase_no = pp.purchase_no
                 inner join stock_task st on p.purchase_no = st.task_no and st.type = 11
        where pp.plan_status = 1
          and st.state in (1, 2)
          and p.area_no = #{storeNo}
          and sku = #{sku}
        order by add_time desc limit 1
    </select>
    <select id="selectLast" resultMap="BaseResultMap">
        select pp.id,
               pp.purchase_no,
               title,
               specification,
               sku,
               price,
               quantity,
               pack,
               unit,
               pp.supplier,
               supplier_id,
               check_report,
               quality_date,
               in_quantity,
               in_price,
               origin_id,
               production_date,
               market_price
        from purchases_plan pp
                 inner join purchases p on p.purchase_no = pp.purchase_no
        where pp.plan_status = 1
          and p.area_no = #{storeNo}
          and sku = #{sku}
        order by add_time desc limit 1,1
    </select>
    <select id="selectLastBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        WHERE plan_status = 1 and sku = #{sku}
        and purchase_no = (
        select purchase_no from purchases_plan pp
        where sku= #{sku}
        group by purchase_no
        order by id desc
        limit 1)
    </select>

    <select id="selectPurchasePrice" resultType="net.summerfarm.model.domain.PurchasesPlan">
        SELECT pp.quantity,pp.price
        FROM stock_task st
        INNER JOIN purchases_plan pp ON st.task_no=pp.purchase_no
        AND st.type=11
        AND pp.plan_status = 1
        <if test="areaNo != null">
            AND st.area_no= 11
        </if>
        AND pp.origin_id IS NULL
        AND pp.price <![CDATA[>]]> 0
        AND pp.sku= #{sku}
        ORDER BY st.addtime desc
        LIMIT 1
    </select>

    <select id="selectUnSettle" parameterType="net.summerfarm.model.domain.PurchasesPlan"
            resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        select pp.id,
        pp.purchase_no purchaseNo,
        title,
        specification,
        pp.sku,
        pp.price,
        quantity,
        pp.pack,
        pp.unit,
        pp.supplier,
        supplier_id supplierId,
        check_report checkReport,
        quality_date qualityDate,
        in_quantity inQuantity,
        in_price inPrice,
        origin_id originId,
        pp.production_date productionDate,
        market_price marketPrice,
        settle_flag settleFlag,
        p.area_no storeNo,
        i.weight,
        p.purchase_time purchaseTime,
        s.settle_form settleForm,
        p.purchases_type purchasesType,
        i.type skuType,
        ad.name_remakes nameRemakes,
        mpp.matching_schedule matchingSchedule,
        i.ext_type extType,
        pp.adv_quantity advQuantity,
        pp.ver_quantity verQuantity
        from purchases_plan pp
        left join purchases p on pp.purchase_no = p.purchase_no
        left join inventory i on pp.sku = i.sku
        left join supplier s on s.id = pp.supplier_id
        left join admin ad on i.admin_id = ad.admin_id
        left join match_purchases_plan mpp on pp.id = mpp.purchases_plan_id
        <where>
            plan_status = 1 and p.state != -1 and i.type = 0 and pp.quantity != 0 and p.purchases_type in (0,1)
            <if test="settleFlag != null">
                and pp.settle_flag = #{settleFlag}
            </if>
            <if test="purchaseNo != null">
                and pp.purchase_no = #{purchaseNo}
            </if>
            <if test="supplierId != null">
                and pp.supplier_id = #{supplierId}
            </if>
        </where>
    </select>

    <select id="countSupplier" parameterType="arraylist" resultType="integer">
        select count(1)
        from (select supplier_id
        from purchases_plan
        where plan_status = 1
        and id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        group by supplier_id) t
    </select>

    <!--    <delete id="deleteById" parameterType="integer">-->
    <!--        delete from purchases_plan where id = #{id}-->
    <!--    </delete>-->

    <!--delete修改为update，将plan_status改为作废 -->
    <update id="deleteById">
        update purchases_plan
        SET plan_status=0
        WHERE id = #{id}
    </update>

    <select id="unSettlePlanTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(price), 0) unSettele
        from purchases_plan pp
                 left join purchases p on pp.purchase_no = p.purchase_no
                 left join inventory i on pp.sku = i.sku
                 left join supplier s on pp.supplier_id = s.id
        where s.settle_form = 0
          and pp.plan_status = 1
          and p.state != -1
          and i.type = 0
          and pp.quantity != 0
          and pp.price > 0
          and pp.settle_flag = 0
    </select>

    <select id="unSettleCashTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(price), 0) unSettele
        from purchases_plan pp
                 left join purchases p on pp.purchase_no = p.purchase_no
                 left join inventory i on pp.sku = i.sku
                 left join supplier s on pp.supplier_id = s.id
        where s.settle_form in (2, 3)
          and pp.plan_status = 1
          and p.state != -1
          and i.type = 0
          and pp.quantity != 0
          and pp.price > 0
          and pp.settle_flag = 0
    </select>

    <select id="queryPurchasePlan" resultType="net.summerfarm.model.vo.PurchasesPlanVO">

        select *
        from (
                 select *
                 from (
                          select pp.id           id,
                                 pp.purchase_no  purchaseNo,
                                 p.purchase_time purchaseTime,
                                 pp.sku,
                                 pp.supplier,
                                 pp.title,
                                 pp.quantity,
                                 pp.price,
                                 pp.supplier_id  supplierId,
                                 i.weight,
                                 pa.status,
                                 pa.id           pid
                          from purchases p
                                   inner join purchases_plan pp
                                              on p.purchase_no = pp.purchase_no and pp.origin_id is null
                                   inner join inventory i on i.sku = pp.sku
                                   left join purchases_account_plan pap on pap.plan_id = pp.id and pap.type = 0
                                   left join purchase_accounting pa on pa.id = pap.account_id
                                   inner join supplier s on s.id = pp.supplier_id and settle_form = 1
                          where pp.plan_status = 1
                            and p.purchase_time >= #{startDate}
                            and p.purchase_time <![CDATA[<=]]> #{endDate}
                            and p.state in (1, 2)
                          order by id, pid desc
                      ) tb
                 group by id
             ) tbb
        where tbb.status IS null
           or tbb.status = 3
        order by tbb.purchaseTime, tbb.id
    </select>

    <select id="selectOriginBySku" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        where plan_status = 1
        and origin_id is null
        <if test="purchaseNo != null">
            AND purchase_no = #{purchaseNo}
        </if>
        <if test="sku != null">
            AND sku = #{sku,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectOriginPlan" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from purchases_plan where plan_status = 1 and origin_id is null and purchase_no = #{purchaseNo}
    </select>

    <select id="selectSingleOriginPlan" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from purchases_plan where plan_status = 1 and origin_id is null and purchase_no = #{purchaseNo} and sku = #{sku}
    </select>


    <select id="selectValidByAddTimeAndSku" resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        SELECT pp.purchase_no purchaseNo, pp.sku sku, pp.quantity quantity, p.area_no areaNo
        FROM purchases_plan pp
        INNER JOIN purchases p ON pp.purchase_no = p.purchase_no
        WHERE p.add_time <![CDATA[ >= ]]> #{startTime}
        AND pp.plan_status = 1
        AND p.add_time <![CDATA[ <= ]]> #{endTime}
        AND p.state IN (0, 1, 2)
        AND p.area_no is not NULL
        AND pp.sku IN
        <foreach collection="skus" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>


    <select id="queryUnSettlePurchasePlan" resultType="net.summerfarm.model.vo.UnSettlePurchasePlanVO">
        SELECT p.purchase_no            purchaseNo,
               wsc.warehouse_name       areaName,
               p.purchases_type         purchasesType,
               p.purchaser              purchaser,
               pp.sku                   sku,
               pp.title                 productName,
               i.weight                 weight,
               pp.quantity              quantity,
               pp.price    AS           cost,
               p.logistics_payment_type logisticsPaymentType,
               p.logistics_cost         logisticsCost,
               pp.supplier AS           supplier,
               s.settle_form            settleForm,
               p.remark                 remark,
               p.logistics_settle_flag  logisticsSettleFlag
        FROM purchases_plan pp
                 LEFT JOIN purchases p ON pp.purchase_no = p.purchase_no
                 LEFT JOIN inventory i ON pp.sku = i.sku
                 LEFT JOIN supplier s ON pp.supplier_id = s.id
                 left join warehouse_storage_center wsc on wsc.warehouse_no = p.area_no
        WHERE s.settle_form != 1
          AND pp.plan_status = 1
          AND p.state !=- 1
          AND i.type = 0
          AND pp.quantity != 0
          AND pp.price > 0
          AND pp.settle_flag = 0
        ORDER BY
            p.purchase_time DESC
    </select>

    <select id="waitForMatch" parameterType="net.summerfarm.model.vo.MatchPurchasesVO"
            resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
        select pp.id id,pp.purchase_no purchaseNo,pp.sku sku,pp.price price,i.weight weight,p.pd_name
        pdName,pu.purchase_time purchaseTime,pu.purchaser purchaser,
        mpp.matching_schedule matchingSchedule,mpp.wait_match waitMatch
        from purchases_plan pp
        left join purchases pu on pp.purchase_no = pu.purchase_no and pu.state <![CDATA[>]]> 0
        left join inventory i on pp.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join match_purchases_plan mpp on pp.id = mpp.purchases_plan_id and mpp.status = 0
        left join supplier_account sa on pp.supplier_id = sa.supplier_id
        <where>
            pp.plan_status = 1
            and sa.account_name = #{supplierName}
            and mpp.wait_match <![CDATA[>]]> 0
            and mpp.matching_schedule <![CDATA[<>]]> '100'
            and pp.price <![CDATA[<>]]> 0
            and pp.quantity  <![CDATA[<>]]> 0
            <if test="purchaseTimeStart != null and purchaseTimeEnd != null">
                and pu.purchase_time <![CDATA[>=]]> #{purchaseTimeStart}
                and pu.purchase_time <![CDATA[<=]]> #{purchaseTimeEnd}
            </if>
            <if test="purchaseNo != null">
                and pp.purchase_no like concat('%',#{purchaseNo},'%')
            </if>
            <if test="pdName != null">
                and p.pd_name = #{pdName}
            </if>
            <if test="purchaser != null">
                and pu.purchaser = #{purchaser}
            </if>
        </where>
        group by pp.id
        order by
        <choose>
            <when test="orderType == 3">mpp.matching_schedule DESC</when>
            <when test="orderType == 4">pu.purchase_time DESC</when>
            <otherwise>pp.id</otherwise>
        </choose>
    </select>

    <select id="selectWaitMatchAmount" parameterType="net.summerfarm.model.vo.MatchPurchasesVO"
            resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
        select IFNULL(sum(mpp.wait_match),0) as waitMatchAmount
        from purchases_plan pp
        left join purchases pu on pp.purchase_no = pu.purchase_no
        left join (select supplier_id,account_name from supplier_account group by supplier_id) sa on pp.supplier_id =
        sa.supplier_id
        left join inventory i on pp.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join match_purchases_plan mpp on pp.id = mpp.purchases_plan_id and mpp.status = 0
        <where>
            pp.plan_status = 1
            and sa.account_name = #{supplierName}
            and mpp.wait_match <![CDATA[>]]> 0
            and mpp.matching_schedule <![CDATA[<>]]> '100'
            and pp.price <![CDATA[<>]]> 0
            and pp.quantity  <![CDATA[<>]]> 0
            <if test="purchaseTimeStart != null and purchaseTimeEnd != null">
                and pu.purchase_time <![CDATA[>=]]> #{purchaseTimeStart}
                and pu.purchase_time <![CDATA[<=]]> #{purchaseTimeEnd}
            </if>
            <if test="purchaseNo != null">
                and pp.purchase_no = #{purchaseNo}
            </if>
            <if test="pdName != null">
                and p.pd_name = #{pdName}
            </if>
            <if test="purchaser != null">
                and pu.purchaser = #{purchaser}
            </if>
        </where>
    </select>

    <select id="selectId" resultType="net.summerfarm.model.domain.PurchasesPlan">
        select id, sku, price, purchase_no purchaseNo, quantity, adv_quantity advQuantity, ver_quantity verQuantity
        from purchases_plan
        where sku = #{sku}
          and purchase_no = #{purchaseNo}
          and plan_status = 1
          and price <![CDATA[<>]]> 0
          and origin_id is null
    </select>

    <select id="selectZero" resultType="net.summerfarm.model.domain.PurchasesPlan">
        select id, sku, price, purchase_no purchaseNo
        from purchases_plan
        where sku = #{sku}
          and purchase_no = #{purchaseNo}
          and plan_status = 1
          and price <![CDATA[>=]]> 0
          and origin_id is null
    </select>

    <select id="selectMatchingSchedule" parameterType="integer" resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        select mpp.matching_schedule matchingSchedule
        from purchases_plan pp
                 left join match_purchases_plan mpp on pp.id = mpp.purchases_plan_id
        where pp.id = #{id}
    </select>

    <update id="updateSupplier" parameterType="net.summerfarm.model.domain.PurchasesPlan">
        UPDATE purchases_plan
        <set>
            <if test="supplier != null">
                supplier = #{supplier},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
        </set>
        WHERE purchase_no = #{purchaseNo} and sku = #{sku}
    </update>

    <update id="updatePurchaseNoById">
        update purchases_plan
        set purchase_no = #{purchaseNo}
        where id = #{id}
    </update>

    <update id="updatePlanStatusByNo">
        update purchases_plan
        set plan_status = #{planStatus}
        where purchase_no = #{purchaseNo}
    </update>

    <select id="selectExistenceSupplier" parameterType="int" resultType="int">
        select ifnull(count(1), 0)
        from purchases_plan
        where supplier_id = #{supplierId}
    </select>


    <select id="selectUnSettleSheet" parameterType="net.summerfarm.model.domain.PurchasesPlan"
            resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        select pp.id,
               pp.purchase_no     purchaseNo,
               pp.title,
               pp.sku,
               pp.price,
               quantity,
               pp.supplier,
               supplier_id        supplierId,
               quality_date       qualityDate,
               in_quantity        inQuantity,
               in_price           inPrice,
               origin_id          originId,
               pp.production_date productionDate,
               settle_flag        settleFlag,
               p.area_no          storeNo,
               i.weight,
               p.purchase_time    purchaseTime,
               s.settle_form      settleForm,
               p.purchases_type   purchasesType,
               i.type             skuType,
               i.ext_type         extType
        from purchases_plan pp
                 left join purchases p on pp.purchase_no = p.purchase_no
                 left join inventory i on pp.sku = i.sku
                 left join supplier s on s.id = pp.supplier_id
        where plan_status = 1
          and p.state != -1 and i.type = 0 and pp.quantity != 0 and p.purchases_type in (0,1)and pp.settle_flag = 0 and p.purchase_no = #{purchaseNo}
          and pp.supplier_id = #{supplierId}
    </select>

    <select id="selectByPurchasesNos" resultMap="BaseResultMap">
        SELECT p.id, p.purchase_no, p.title, p.specification, p.sku, p.price, p.quantity, p.pack,
        p.supplier,p.supplier_id,p.quality_date,p.check_report,i.weight,p.in_quantity,p.in_price,p.origin_id,p.market_price,
        settle_flag,i.type skuType,ad.name_remakes nameRemakes
        FROM purchases_plan p
        left join inventory i on p.sku=i.sku
        left join admin ad on i.admin_id = ad.admin_id
        where p.sku= #{sku} and purchase_no
        in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByPurchasesNosAndSkus" resultMap="BaseResultMap">
        SELECT p.id, p.purchase_no, p.title, p.specification, p.sku, p.price, p.quantity, p.pack,
        p.supplier,p.supplier_id,p.quality_date,p.check_report,p.in_quantity,p.in_price,p.origin_id,p.market_price,
        settle_flag
        FROM purchases_plan p
        where p.plan_status = 1
          and  p.sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
        and purchase_no in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectBySequential" resultType="net.summerfarm.model.domain.PurchasesPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM purchases_plan
        where purchase_no = #{purchaseNo} and sku = #{sku}
        <if test="flag == true">
            order by id asc
        </if>
        <if test="flag == false">
            order by id desc
        </if>
        limit 1
    </select>

    <select id="selectNonPerformance" resultType="net.summerfarm.model.vo.PurchasesPlanVO">
        SELECT p.id,
               sku,
               pp.adv_quantity            advQuantity,
               ifnull(pp.ver_quantity, 0) verQuantity,
               p.area_no                  areaNo,
               wsc.warehouse_name         areaName,
               p.purchaser
        FROM purchases_plan pp
                 left join purchases p on pp.purchase_no = p.purchase_no
                 left join warehouse_storage_center wsc on wsc.warehouse_no = p.area_no
        where pp.adv_quantity &gt; pp.ver_quantity
          and pp.plan_status = 1
    </select>

    <select id="queryAdvanceAble" resultMap="BaseMap">
        select pp.id,pp.purchase_no,pp.sku,pp.title,pp.quantity,pp.price/pp.quantity
        avgPrice,pp.price,i.weight,pp.settle_flag
        from purchases_plan pp
        inner join purchases p on p.purchase_no = pp.purchase_no
        left join inventory i on i.sku = pp.sku
        <where>
            pp.plan_status = 1 and pp.origin_id is null and p.purchases_type != 2 and p.state = 1
            <if test="purchaseNo != null">
                and pp.purchase_no = #{purchaseNo}
            </if>
            <if test="supplierId != null">
                and pp.supplier_id = #{supplierId}
            </if>
            <if test="settleFlag != null">
                and pp.settle_flag = #{settleFlag}
            </if>
        </where>
    </select>

    <select id="selectBySupplierAndNo" resultMap="BaseResultMap">
        select p.id, p.purchase_no, p.title, p.specification, p.sku, p.price, p.quantity, p.pack,
        p.supplier,p.supplier_id,p.quality_date,p.check_report,i.weight,p.in_quantity,p.in_price,p.origin_id,p.market_price,
        settle_flag,i.type skuType,p.adv_quantity,p.ver_quantity,p.production_date
        from purchases_plan p
        left join inventory i on p.sku = i.sku
        <where>
            p.origin_id is null and p.plan_status = 1
            <if test="supplierId != null">
                and p.supplier_id = #{supplierId}
            </if>
            <if test="purchaseNo != null">
                and p.purchase_no = #{purchaseNo}
            </if>
        </where>
    </select>

    <select id="selectArrange" parameterType="java.lang.String"
            resultType="net.summerfarm.model.vo.PurchasesPlanResultVO">
        SELECT pp.id,
               pp.purchase_no                                   purchaseNo,
               pp.title,
               pp.specification,
               pp.sku,
               pp.price,
               pp.quantity,
               pp.check_report                                   checkReport,
               pp.pack,
               pp.supplier,
               pp.quality_date                                  qualityDate,
               pp.in_quantity                                   inQuantity,
               i.weight_num                                     weightNum,
               i.volume                                         volume,
               arrange_quantity                                 arrangeQuantity,
               i.type,
               i.weight,
               p.pd_name                                        pdName,
               ps.area_no                                       areaNo,
               convert(pp.price / pp.quantity, decimal (15, 8)) singlePlice,
               p.quality_time                                   qualityTime,
               p.quality_time_unit                              qualityTimeUnit,
               pp.supplier_id                                   supplierId,
               if(i.sku_pic is null, p.picture_path, i.sku_pic) pic,
               i.unit                                           unit
        FROM purchases_plan pp
                 left join purchases ps on ps.purchase_no = pp.purchase_no
                 left join inventory i on i.sku = pp.sku
                 left join products p on i.pd_id = p.pd_id
        where pp.arrange_quantity <![CDATA[<>]]> 0
          and pp.plan_status = 1
          and pp.origin_id is null
          and pp.purchase_no = #{purchaseNo,jdbcType=INTEGER}
    </select>

    <select id="selectByIdAndSku" resultMap="BaseResultMap">
        SELECT
        id,purchase_no,title,specification,sku,price,quantity,pack,unit,supplier,supplier_id,quality_date,check_report,in_quantity,in_price,origin_id,market_price
        FROM purchases_plan
        where plan_status = 1 and sku=#{sku}
        <if test="qualityDate ==null">
            and quality_date is null
        </if>
        <if test="qualityDate !=null">
            and quality_date =#{qualityDate}
        </if>

    </select>

    <select id="queryByNoAndSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from purchases_plan pp
        where pp.plan_status = 1 and pp.origin_id is null and purchase_no = #{purchaseNo} and sku = #{sku}
    </select>

    <update id="updateDefaultArrQuantity">
        update purchases_plan pp
        set arrange_quantity = quantity
        where plan_status = 1
          and purchase_no = #{purchaseNo}
          and origin_id is null
    </update>

    <update id="updateArrangeQuantity">
        update purchases_plan pp
        set update_time      = now(),
            arrange_quantity = arrange_quantity + #{outQuantity}
        where sku = #{sku}
          and purchase_no = #{purchaseNo}
          and origin_id is null
    </update>

    <update id="updateArrangeQuantityByNoAndSku">
        update purchases_plan pp
        set update_time      = now(),
            arrange_quantity = arrange_quantity + #{brokenQuantity}
        where sku = #{sku}
          and purchase_no = #{purchaseNo}
          and origin_id is null
    </update>

    <select id="selectByAreaNo" resultMap="BaseResultMap">
        SELECT p.id,
               p.purchase_no,
               title,
               specification,
               sku,
               price,
               quantity,
               pack,
               unit,
               pp.supplier,
               pp.supplier_id,
               pp.arrange_quantity,
               quality_date,
               check_report,
               in_quantity,
               in_price,
               origin_id,
               market_price
        FROM purchases p
                 left join purchases_plan pp on pp.purchase_no = p.purchase_no
        where pp.plan_status = 1
          and p.area_no = #{warehouseNo}
          and p.add_time <![CDATA[<=]]> #{endTime}
          and p.add_time <![CDATA[>=]]> #{startTime}
    </select>
    <select id="selectArrangeByPurchasesNoAndSupplierId" resultType="net.summerfarm.model.vo.PurchasesPlanResultVO">
        SELECT pp.id,
               pp.purchase_no                                   purchaseNo,
               pp.title,
               pp.specification,
               pp.sku,
               pp.price,
               pp.quantity,
               pp.pack,
               pp.unit,
               pp.supplier,
               pp.quality_date                                  qualityDate,
               pp.in_quantity                                   inQuantity,
               i.weight_num                                     weightNum,
               arrange_quantity                                 arrangeQuantity,
               i.type,
               i.weight,
               p.pd_name                                        pdName,
               ps.area_no                                       areaNo,
               convert(pp.price / pp.quantity, decimal (15, 8)) singlePlice,
               p.quality_time                                   qualityTime,
               p.quality_time_unit                              qualityTimeUnit,
               pp.supplier_id                                   supplierId,
               if(i.sku_pic is null, p.picture_path, i.sku_pic) pic
        FROM purchases_plan pp
                 left join purchases ps on ps.purchase_no = pp.purchase_no
                 left join inventory i on i.sku = pp.sku
                 left join products p on i.pd_id = p.pd_id
        where pp.arrange_quantity <![CDATA[<>]]> 0
          and pp.plan_status = 1
          and pp.origin_id is null
          and pp.purchase_no = #{purchaseNo,jdbcType=INTEGER}
          and pp.supplier_id = #{supplierId}
    </select>
    <select id="queryBySkuAndLatestArrivalDateAndWarehouseNo"
            resultType="net.summerfarm.model.domain.PurchasesPlan">
        SELECT pp.id,
               pp.arrange_quantity arrangeQuantity
        FROM purchases_plan pp
                 LEFT JOIN purchases p ON pp.purchase_no = p.purchase_no
        WHERE pp.sku = #{sku}
          AND pp.latest_arrival_date >= #{latestArrivalDate}
          AND pp.plan_status = 1
          AND p.area_no = #{warehouseNo}
          AND p.state = 1

    </select>
    <select id="selectSkuNumTotalQuantityTotalPriceByPurchaseNo" resultType="net.summerfarm.model.vo.PurchasesResultVO">
        select count(distinct sku) skuNums,sum(quantity) totalQuantity,sum(price) totalPrice
        from  purchases_plan where plan_status = 1 and purchase_no = #{purchaseNo}
    </select>

    <select id="selectPlanIds" resultType="java.lang.Integer">
         SELECT id
        FROM purchases_plan
        where purchase_no = #{purchaseNo}
        <if test="skuList != null and skuList.size() != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and origin_id is  null
    </select>

    <select id="selectByPurchasesNoList" resultType="net.summerfarm.model.vo.pms.PurchaseSkuDetailVO">
    select sku as sku,
        purchase_no as purchaseNo,
        supplier as supplier,
        tax_rate as taxRate,
        quantity as purchaseCount,
        price/quantity as unitPrice,
        price as totalPrice
        from purchases_plan
        where purchase_no in
        <foreach collection="purchaseNoList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and origin_id is null AND plan_status = 1
    </select>
    <select id="selectSupplierByPurchaseNoAndSku" resultType="java.lang.Long">
        select distinct supplier_id
        from purchases_plan
        where purchase_no = #{purchaseNo} and sku =#{sku}  and origin_id is  null
    </select>
    <update id="updateToNewPurchaseNoBySupplierId">
        update purchases_plan pp
        set purchase_no = #{purchaseNo}
        where purchase_no = #{beforePurchaseNo}
          and supplier_id = #{supplierId}
    </update>

    <delete id="deleteByPurchaseNo">
        delete from purchases_plan where purchase_no = #{purchaseNo}
    </delete>

    <update id="updateZeroToArrQuantity">
        update purchases_plan pp
        set arrange_quantity = 0
        where plan_status = 1
        and purchase_no = #{purchaseNo}
        and origin_id is null
    </update>

    <select id="getSupplierIdByBatchAndSku" resultType="java.lang.Integer">
        select supplier_id from purchases_plan where purchase_no = #{batch} and sku = #{sku} and origin_id is null and plan_status = 1 limit 1
    </select>

    <select id="selectSkuNetWeight" resultType="net.summerfarm.module.pms.model.vo.SkuWeightVO">
        SELECT p.sku, p.net_weight as netWeight
        FROM purchases_plan p
        JOIN (
        SELECT pp.sku, MAX(pp.id) AS max_id
        FROM purchases_plan pp
        inner join purchases pur on pur.purchase_no=pp.purchase_no and pur.area_no=#{warehouseNo} and pur.tenant_id=#{tenantId}
        WHERE   pp.sku IN
        <foreach collection="skuList" separator="," open="(" close=")" item="sku">
            #{sku}
        </foreach>
        GROUP BY pp.sku
        ) AS subquery
        ON p.sku = subquery.sku AND p.id = subquery.max_id
    </select>
</mapper>
