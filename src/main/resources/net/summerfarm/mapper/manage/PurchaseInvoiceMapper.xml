<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchaseInvoiceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchaseInvoice">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="billing_date" jdbcType="TIMESTAMP" property="billingDate" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
    <result column="excluding_tax" jdbcType="DECIMAL" property="excludingTax" />
    <result column="included_tax" jdbcType="DECIMAL" property="includedTax" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="invoice_form" jdbcType="INTEGER" property="invoiceForm" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="invoice_attachment" jdbcType="INTEGER" property="invoiceAttachment" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="purchaser" jdbcType="VARCHAR" property="purchaser" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="actual_tax_rate" jdbcType="DECIMAL" property="actualTaxRate" />
    <result column="actual_tax_amount" jdbcType="DECIMAL" property="actualTaxAmount" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="invoice_type_face" jdbcType="INTEGER" property="invoiceTypeFace" />
    <result column="wallets_id" jdbcType="BIGINT" property="walletsId" />
    <result column="creator_admin_id" jdbcType="INTEGER" property="creatorAdminId" />
    <result column="invoice_updater" jdbcType="BIGINT" property="invoiceUpdater" />
    <result column="invoice_commit" jdbcType="BIGINT" property="invoiceCommit" />
  </resultMap>

  <resultMap id="detail" type="net.summerfarm.model.vo.PurchaseInvoiceVO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="billing_date" jdbcType="TIMESTAMP" property="billingDate" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
    <result column="excluding_tax" jdbcType="DECIMAL" property="excludingTax" />
    <result column="included_tax" jdbcType="DECIMAL" property="includedTax" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="invoice_form" jdbcType="INTEGER" property="invoiceForm" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="purchaser" jdbcType="VARCHAR" property="purchaser" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="wallets_id" jdbcType="BIGINT" property="walletsId" />
    <result column="invoice_type_face" jdbcType="INTEGER" property="invoiceTypeFace" />
  </resultMap>
  <sql id="Base_Column_List">
    id, supplier_id, billing_date, invoice_code, invoice_number, excluding_tax,
    included_tax, tax_amount, tax_rate, invoice_type, invoice_form, `type`, invoice_attachment
    file_address, create_time, creator, delete_status, purchaser, supplier_name, actual_tax_rate, actual_tax_amount, tax_number, invoice_type_face, wallets_id,creator_admin_id,invoice_updater,invoice_commit
  </sql>

  <select id="queryByInvoiceId" parameterType="integer" resultMap="detail">
    select
    <include refid="Base_Column_List" />
    from purchase_invoice
    where id = #{id}
  </select>

  <select id="selectById" parameterType="integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchase_invoice
    where id = #{id} and delete_status = 0
  </select>
  <select id="selectByVO" parameterType="integer" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select
      pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.excluding_tax excludingTax,pi.included_tax includedTax,
      pi.tax_amount taxAmount,pi.tax_rate taxRate,pi.invoice_type invoiceType,pi.invoice_form invoiceForm,pi.supplier_name supplierName,pi.purchaser purchaser,
      pi.id purchaseInvoiceId,pi.invoice_attachment invoiceAttachment,pi.invoice_type_face invoiceTypeFace,pi.supplier_id supplierId,
      pi.invoice_updater invoiceUpdater,pi.invoice_commit invoiceCommit
    from purchase_invoice pi
    where id = #{id} and delete_status = 0
  </select>

  <select id="sumMatching" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceResultVO">
    select IFNULL(sum(pi.included_tax),0) as includedTaxAmount
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    left join purchase_invoice_time pit on pi.id = pit.purchase_invoice_id
    <where>
      pil.status = 1
      and pil.state = 0 and pi.wallets_id is null
      <if test="billingDateStart !=null and billingDateEnd != null">
        and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
        and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test="supplierName !=null">
        and pi.supplier_name = #{supplierName}
      </if>
      <if test="invoiceCode !=null ">
        and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
      </if>
      <if test="invoiceNumber !=null">
        and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
      </if>
      <if test="invoiceSearchKey !=null">
        and (pi.invoice_number LIKE CONCAT(#{invoiceSearchKey},'%') or pi.invoice_code LIKE CONCAT(#{invoiceSearchKey},'%'))
      </if>
      <if test="invoiceAttachment !=null">
        and pi.invoice_attachment = #{invoiceAttachment}
      </if>
      <if test="updateTimeStart !=null and updateTimeEnd !=null">
        and pil.create_time <![CDATA[>=]]> #{updateTimeStart}
        and pil.create_time <![CDATA[<=]]> #{updateTimeEnd}
      </if>
      <if test="updateTimeStart !=null and updateTimeEnd !=null and billingDateStart !=null and billingDateEnd != null">
        and pil.create_time <![CDATA[>=]]> #{updateTimeStart}
        and pil.create_time <![CDATA[<=]]> #{updateTimeEnd}
        and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
        and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test="invoiceTypeFace !=null">
        and pi.invoice_type_face = #{invoiceTypeFace}
      </if>
      <if test="taxNumber !=null">
        and pi.tax_number = #{taxNumber}
      </if>
    </where>
  </select>

  <select id="selectList" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.excluding_tax excludingTax,pi.included_tax includedTax,
           pi.tax_amount taxAmount,pi.tax_rate taxRate,pi.invoice_type invoiceType,pi.invoice_form invoiceForm,pi.supplier_name supplierName,
           pil.create_time updateTime,pit.remakes remakes,pi.id purchaseInvoiceId,pi.invoice_attachment invoiceAttachment,pit.actual_tax_amount actualTaxAmount,pi.invoice_type_face invoiceTypeFace,pi.supplier_id supplierId, pi.tax_number taxNumber
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    left join purchase_invoice_time pit on pi.id = pit.purchase_invoice_id
    <where>
      pil.status = 1
      and pil.state = 0 and pi.wallets_id is null
      <if test="billingDateStart !=null and billingDateEnd != null">
        and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
        and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test="supplierName !=null">
        and pi.supplier_name = #{supplierName}
      </if>
      <if test="invoiceCode !=null ">
        and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
      </if>
      <if test="invoiceNumber !=null">
        and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
      </if>
      <if test="invoiceSearchKey !=null">
        and (pi.invoice_number LIKE CONCAT(#{invoiceSearchKey},'%') or pi.invoice_code LIKE CONCAT(#{invoiceSearchKey},'%'))
      </if>
      <if test="invoiceAttachment !=null">
        and pi.invoice_attachment = #{invoiceAttachment}
      </if>
      <if test="updateTimeStart !=null and updateTimeEnd !=null">
        and pil.create_time <![CDATA[>=]]> #{updateTimeStart}
        and pil.create_time <![CDATA[<=]]> #{updateTimeEnd}
      </if>
      <if test="updateTimeStart !=null and updateTimeEnd !=null and billingDateStart !=null and billingDateEnd != null">
        and pil.create_time <![CDATA[>=]]> #{updateTimeStart}
        and pil.create_time <![CDATA[<=]]> #{updateTimeEnd}
        and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
        and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test="invoiceTypeFace !=null">
        and pi.invoice_type_face = #{invoiceTypeFace}
      </if>
      <if test="taxNumber !=null">
        and pi.tax_number = #{taxNumber}
      </if>
    </where>
    group by pi.id
    order by
        <choose>
          <when test="orderType == 1">pi.billing_date DESC</when>
          <when test="orderType == 2">pil.create_time DESC</when>
          <otherwise>pi.id</otherwise>
        </choose>
  </select>

  <select id="selectListWait" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.excluding_tax excludingTax,pi.included_tax includedTax,
           pi.tax_amount taxAmount,pi.tax_rate taxRate,pi.invoice_type invoiceType,pi.invoice_form invoiceForm,pi.supplier_name supplierName,pi.purchaser purchaser,
           pil.update_time updateTime,pi.id purchaseInvoiceId,pi.invoice_attachment invoiceAttachment,pi.invoice_type_face invoiceTypeFace,pi.creator creator
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    <where>
        pil.status = 0
        and pil.state = 0
        <if test="billingDateStart !=null and billingDateEnd != null">
          and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
          and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
        </if>
        <if test="purchaser != null">
          and pi.purchaser = #{purchaser}
        </if>
        <if test="supplierName !=null">
          and pi.supplier_name = #{supplierName}
        </if>
        <if test="invoiceCode !=null ">
          and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
        </if>
        <if test="invoiceNumber !=null">
          and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
        </if>
        <if test="invoiceAttachment !=null">
          and pi.invoice_attachment = #{invoiceAttachment}
        </if>
        <if test="invoiceTypeFace !=null">
            and pi.invoice_type_face = #{invoiceTypeFace}
        </if>
    </where>
    group by pi.id
    order by
        <choose>
          <when test="orderType == 1">pi.billing_date DESC</when>
          <when test="orderType == 2">pil.update_time DESC</when>
          <otherwise>pi.id</otherwise>
        </choose>
  </select>

  <select id="sumWaiting" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceResultVO">
    select IFNULL(sum(pi.included_tax),0) as includedTaxAmount
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    <where>
      pil.status = 0
      and pil.state = 0
      <if test="billingDateStart !=null and billingDateEnd != null">
        and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
        and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test="purchaser != null">
        and pi.purchaser = #{purchaser}
      </if>
      <if test="supplierName !=null">
        and pi.supplier_name = #{supplierName}
      </if>
      <if test="invoiceCode !=null ">
        and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
      </if>
      <if test="invoiceNumber !=null">
        and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
      </if>
      <if test="invoiceAttachment !=null">
        and pi.invoice_attachment = #{invoiceAttachment}
      </if>
      <if test="invoiceTypeFace !=null">
        and pi.invoice_type_face = #{invoiceTypeFace}
      </if>
    </where>
  </select>

  <select id="selectListSum" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceResultVO">
    select IFNULL(sum(pi.included_tax),0) as includedTaxAmount
    from purchase_invoice pi
    left join (select supplier_id,account_name from supplier_account  group by supplier_id) s on pi.supplier_id = s.supplier_id
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    <where>
      <if test="status == 0">
        and pil.status = 0
        and pil.state = 0
        <if test="billingDateStart !=null and billingDateEnd != null">
          and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
          and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
        </if>
        <if test="purchaser != null">
          and pi.purchaser = #{purchaser}
        </if>
        <if test="supplierName !=null">
          and s.account_name = #{supplierName}
        </if>
        <if test="invoiceCode !=null ">
          and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
        </if>
        <if test="invoiceNumber !=null">
          and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
        </if>
        <if test="invoiceAttachment !=null">
          and pi.invoice_attachment = #{invoiceAttachment}
        </if>
        <if test="invoiceTypeFace !=null">
          and pi.invoice_type_face = #{invoiceTypeFace}
        </if>
      </if>
      <if test="status == 1">
        and pil.status = 1
        and pil.state = 0
        <if test="billingDateStart !=null and billingDateEnd != null">
          and pi.billing_date <![CDATA[>=]]> #{billingDateStart}
          and pi.billing_date <![CDATA[<=]]> #{billingDateEnd}
        </if>
        <if test="purchaser != null">
          and pi.purchaser = #{purchaser}
        </if>
        <if test="supplierName !=null">
          and s.account_name = #{supplierName}
        </if>
        <if test="invoiceCode !=null ">
          and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
        </if>
        <if test="invoiceNumber !=null">
          and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
        </if>
        <if test="invoiceAttachment !=null">
          and pi.invoice_attachment = #{invoiceAttachment}
        </if>
        <if test="updateTimeStart !=null and updateTimeEnd !=null">
          and pil.create_time <![CDATA[>=]]> #{updateTimeStart}
          and pil.create_time <![CDATA[<=]]> #{updateTimeEnd}
        </if>
        <if test="invoiceTypeFace !=null">
          and pi.invoice_type_face = #{invoiceTypeFace}
        </if>
      </if>
    </where>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="integer">
    select count(*)
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    where pi.id = #{id,jdbcType=INTEGER} and pi.delete_status = 0 and (pil.status = 0 or (pil.status = 1 and pi.wallets_id is not null)) and pil.state = 0
  </select>

  <select id="selectByKey" parameterType="java.lang.Integer" resultType="integer">
    select count(*)
    from purchase_invoice
    where id = #{id,jdbcType=INTEGER} and delete_status = 0
  </select>

  <select id="selectDetailByFirst" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.type type,pi.supplier_name supplierName,pi.invoice_type invoiceType,
           pi.invoice_form invoiceForm,pi.excluding_tax excludingTax,pi.included_tax includedTax,pi.tax_amount taxAmount,pi.tax_rate taxRate,pi.invoice_supplier_type invoiceSupplierType,
           pil.update_time updateTime,pi.supplier_id supplierId,pi.invoice_type_face invoiceTypeFace,pi.tax_number taxNumber,pi.wallets_id walletsId
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id  = pil.purchase_invoice_id
    where pi.id = #{purchaseInvoiceId} and pil.status = #{status} and state = 0
    limit 1
  </select>

  <select id="selectDetailBySecond" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    /*FORCE_MASTER*/
    select pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.type type,pi.supplier_name supplierName,pi.invoice_type invoiceType,
           pi.invoice_form invoiceForm,pi.included_tax includedTax,pi.excluding_tax excludingTax ,pi.tax_amount taxAmount,pi.tax_rate taxRate,pi.invoice_type_face invoiceTypeFace,pi.invoice_supplier_type invoiceSupplierType,
           pi.tax_number taxNumber,pi.id id,pi.supplier_id supplierId,pi.wallets_id walletsId,ifnull(pi.actual_tax_rate,0) actualTaxRate, ifnull(pi.actual_tax_amount,0) actualTaxAmount
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id  = pil.purchase_invoice_id
    where pi.id = #{purchaseInvoiceId} and pil.status = #{status} and state = 0
    limit 1
  </select>

  <select id="fileAddress" resultType="string">
    select pif.file_address
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id  = pil.purchase_invoice_id
    left join purchase_invoice_file pif on pi.id = pif.purchase_invoice_id
    where pi.id = #{purchaseInvoiceId} and pil.status = #{status} and pif.delete_status = 0 and pil.state = 0 and pif.file_address is not null
    group by pif.id
  </select>

  <select id="selectInvoiceCodes" resultType="integer">
    select count(*)
    from purchase_invoice
    <where>
      delete_status = 0
      <if test="invoiceCode != null">
        and invoice_code = #{invoiceCode}
      </if>
      <if test="invoiceNumber != null">
        and invoice_number = #{invoiceNumber}
      </if>
    </where>

  </select>

  <select id="selectInvoiceId" resultMap="BaseResultMap">
    select id
    from purchase_invoice
    <where>
      delete_status = 0
      <if test="invoiceCode != null">
        and invoice_code = #{invoiceCode}
      </if>
      <if test="invoiceNumber != null">
        and invoice_number = #{invoiceNumber}
      </if>
        limit 100
    </where>
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.vo.PurchaseInvoiceVO" useGeneratedKeys="true">
    insert into purchase_invoice (supplier_id, billing_date,
      invoice_code, invoice_number, excluding_tax, 
      included_tax, tax_amount, tax_rate, 
      invoice_type, invoice_form, `type`, 
      invoice_attachment, create_time,
      creator, delete_status, purchaser)
    values (#{supplierId,jdbcType=INTEGER}, #{billingDate,jdbcType=TIMESTAMP},
      #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNumber,jdbcType=VARCHAR}, #{excludingTax,jdbcType=DECIMAL}, 
      #{includedTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{taxRate,jdbcType=INTEGER}, 
      #{invoiceType,jdbcType=INTEGER}, #{invoiceForm,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{invoiceAttachment,jdbcType=INTEGER},now(),
      #{creator,jdbcType=VARCHAR}, 0, #{purchaser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.vo.PurchaseInvoiceVO" useGeneratedKeys="true">
    insert into purchase_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="billingDate != null">
        billing_date,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNumber != null">
        invoice_number,
      </if>
      <if test="excludingTax != null">
        excluding_tax,
      </if>
      <if test="includedTax != null">
        included_tax,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceForm != null">
        invoice_form,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="invoiceAttachment != null">
        invoice_attachment,
      </if>
        create_time,
      <if test="creator != null">
        creator,
      </if>
        delete_status,
      <if test="purchaser != null">
        purchaser,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="invoiceTypeFace != null">
        invoice_type_face,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="creatorAdminId != null">
        creator_admin_id,
      </if>
      <if test="invoiceCommit != null">
        invoice_commit,
      </if>
      <if test="invoiceUpdater != null">
        invoice_updater,
      </if>
      <if test="invoiceSupplierType != null">
        invoice_supplier_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="billingDate != null">
        #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNumber != null">
        #{invoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="excludingTax != null">
        #{excludingTax,jdbcType=DECIMAL},
      </if>
      <if test="includedTax != null">
        #{includedTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceForm != null">
        #{invoiceForm,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="invoiceAttachment != null">
        #{invoiceAttachment,jdbcType=INTEGER},
      </if>
        now(),
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
        0,
      <if test="purchaser != null">
        #{purchaser,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTypeFace != null">
        #{invoiceTypeFace},
      </if>
      <if test="taxNumber != null">
        #{taxNumber},
      </if>
      <if test="creatorAdminId != null">
        #{creatorAdminId},
      </if>
      <if test="invoiceCommit != null">
        #{invoiceCommit},
      </if>
      <if test="invoiceUpdater != null">
        #{invoiceUpdater},
      </if>
      <if test="invoiceSupplierType != null">
        #{invoiceSupplierType}
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    update purchase_invoice
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="billingDate != null">
        billing_date = #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNumber != null">
        invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="excludingTax != null">
        excluding_tax = #{excludingTax,jdbcType=DECIMAL},
      </if>
      <if test="includedTax != null">
        included_tax = #{includedTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceForm != null">
        invoice_form = #{invoiceForm,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="purchaser != null">
        purchaser = #{purchaser},
      </if>
      <if test="invoiceAttachment != null">
        invoice_attachment = #{invoiceAttachment,jdbcType=INTEGER},
      </if>
      <if test="actualTaxRate != null">
        actual_tax_rate = #{actualTaxRate},
      </if>
      <if test="actualTaxAmount != null">
        actual_tax_amount = #{actualTaxAmount},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName},
      </if>
      <if test="invoiceTypeFace != null">
        invoice_type_face = #{invoiceTypeFace},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber},
      </if>
      <if test="walletsId != null">
        wallets_id = #{walletsId},
      </if>
      <if test="invoiceSupplierType != null">
       invoice_supplier_type = #{invoiceSupplierType}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateInvoiceEmptyByQuanDian">
    update purchase_invoice set invoice_code = null where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    update purchase_invoice
    set
      supplier_id = #{supplierId,jdbcType=INTEGER},
      billing_date = #{billingDate,jdbcType=TIMESTAMP},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
      excluding_tax = #{excludingTax,jdbcType=DECIMAL},
      included_tax = #{includedTax,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=INTEGER},
      invoice_type = #{invoiceType,jdbcType=INTEGER},
      invoice_form = #{invoiceForm,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      purchaser = #{purchaser},
      supplier_name = #{supplierName},
      invoice_attachment = #{invoiceAttachment,jdbcType=INTEGER},
      invoice_type_face = #{invoiceTypeFace},
      tax_number = #{taxNumber},
      invoice_supplier_type = #{invoiceSupplierType}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByActual" parameterType="net.summerfarm.model.domain.PurchaseInvoice">
    update purchase_invoice
    set
      actual_tax_amount = #{actualTaxAmount,jdbcType=DECIMAL},
      actual_tax_rate = #{actualTaxRate,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="delete">
    update purchase_invoice
    set delete_status  = 1
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTaxNumber" parameterType="net.summerfarm.model.input.PurchaseInvoiceQuery" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select pi.id,pi.billing_date billingDate,pi.invoice_code invoiceCode,pi.invoice_number invoiceNumber,pi.included_tax includedTax,pi.invoice_type_face invoiceTypeFace,pi.invoice_type invoiceType,pi.invoice_form invoiceForm
    ,pi.excluding_tax excludingTax ,pi.tax_amount taxAmount,pi.actual_tax_amount actualTaxAmount,pi.tax_rate taxRate,pi.invoice_commit invoiceCommit
    from purchase_invoice pi
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id
    <where>
      pi.tax_number = #{taxNumber} and pil.status = 1 and pil.state = 0 and pi.wallets_id is null
      <if test=" startTime != null and endTime != null">
        and pi.billing_date <![CDATA[>=]]> #{startTime}
        and pi.billing_date <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" invoiceCode != null">
        and pi.invoice_code LIKE CONCAT(#{invoiceCode},'%')
      </if>
      <if test=" invoiceNumber != null">
        and pi.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
      </if>
    </where>
    group by pi.id
  </select>

  <select id="selectWallets" parameterType="int" resultType="net.summerfarm.model.domain.PurchaseInvoice">
    select wallets_id walletsId,invoice_code invoiceCode,invoice_number invoiceNumber,id,billing_date billingDate,included_tax includedTax,invoice_type_face invoiceTypeFace
    from purchase_invoice
    where id = #{id} and delete_status = 0
  </select>

  <select id="select" parameterType="int" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select wallets_id walletsId,invoice_code invoiceCode,invoice_number invoiceNumber,id,billing_date billingDate,included_tax includedTax,invoice_type_face invoiceTypeFace,excluding_tax excludingTax,tax_amount taxAmount
    from purchase_invoice
    where id = #{id} and delete_status = 0
  </select>

  <update id="update" parameterType="net.summerfarm.model.domain.PurchaseInvoice">
    update purchase_invoice
    set
        wallets_id = #{walletsId}
    where id = #{id,jdbcType=INTEGER} and delete_status = 0 and wallets_id is null
  </update>
  
  <select id="selectSubmissionTime" parameterType="long" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select pi.update_time submissionTime,pi.updater submissionTimeMan
    from purchase_invoice p
    left join purchase_invoice_log pi on p.id = pi.purchase_invoice_id
    where p.wallets_id = #{walletsId} and p.delete_status = 0 and pi.status = 1 and pi.state = 0
    order by pi.update_time desc
    limit 1
  </select>

  <select id="selectAll" parameterType="long" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    SELECT
      p.id,
      p.supplier_id supplierId,
      p.billing_date billingDate,
      p.invoice_code invoiceCode,
      p.invoice_number invoiceNumber,
      p.excluding_tax excludingTax,
      p.included_tax includedTax,
      p.tax_amount taxAmount,
      p.tax_rate taxRate,
      p.invoice_type invoiceType,
      p.invoice_form invoiceForm,
      p.`type`,
      p.invoice_attachment invoiceAttachment,
      p.create_time createTime,
      p.creator,
      p.delete_status deleteStatus,
      p.purchaser,
      p.supplier_name supplierName,
      ifnull( p.actual_tax_rate, 0 ) actualTaxRate,
      ifnull( p.actual_tax_amount, 0 ) actualTaxAmount,
      p.tax_number taxNumber,
      p.invoice_type_face invoiceTypeFace,
      p.wallets_id walletsId,
      p.invoice_updater invoiceUpdater,
      p.invoice_commit invoiceCommit,
      p.type supplierType
    FROM	purchase_invoice p
    WHERE
      p.wallets_id = #{walletsId} and p.delete_status = 0
    GROUP BY
      p.id
    ORDER BY
      p.billing_date ASC
  </select>

  <select id="selectByAdd" parameterType="long" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select
      id, supplier_id supplierId, billing_date billingDate, invoice_code invoiceCode, invoice_number invoiceNumber, excluding_tax excludingTax,
      included_tax includedTax, tax_amount taxAmount, tax_rate taxRate, invoice_type invoiceType, invoice_form invoiceForm, `type`, invoice_attachment invoiceAttachment,
         create_time createTime, creator, delete_status deleteStatus, purchaser, supplier_name supplierName, ifnull(actual_tax_rate,0) actualTaxRate, ifnull(actual_tax_amount,0) actualTaxAmount,
           tax_number taxNumber , invoice_type_face invoiceTypeFace, wallets_id walletsId
    from purchase_invoice
    where wallets_id = #{walletsId} and delete_status = 0
    order by billing_date,id ASC
  </select>

  <update id="updateByWalletsId">
    update purchase_invoice
    set
      wallets_id = null
    where wallets_id = #{walletsId} and delete_status = 0
  </update>

  <update id="updateById">
    update purchase_invoice
    set
      wallets_id = null
    where id = #{id} and delete_status = 0
  </update>

  <select id="addDeductibleTax" parameterType="long" resultType="decimal">
    select sum(actual_tax_amount)
    from purchase_invoice
    where wallets_id = #{walletsId} and delete_status = 0 and invoice_type_face  = 2
  </select>

  <select id="addDeductibleTaxRed" parameterType="long" resultType="decimal">
    select sum(actual_tax_amount)
    from purchase_invoice
    where wallets_id = #{walletsId} and delete_status = 0 and invoice_type_face  = 1
  </select>

  <select id="selectData" parameterType="long" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select sum(actual_tax_amount) totalActualDeductibleTax,sum(excluding_tax) totalAmountExcludingTax,sum(included_tax) totalTaxIncluded
    from purchase_invoice
    where wallets_id = #{walletsId} and delete_status = 0 and invoice_type_face  = 2
  </select>

  <select id="selectRedInvoiceData" parameterType="long" resultType="net.summerfarm.model.vo.PurchaseInvoiceVO">
    select sum(actual_tax_amount) totalActualDeductibleTax,sum(excluding_tax) totalAmountExcludingTax,sum(included_tax) totalTaxIncluded
    from purchase_invoice
    where wallets_id = #{walletsId} and delete_status = 0 and  invoice_type_face  = 1
  </select>

  <select id="selectByIdList" resultMap="detail">
    select <include refid="Base_Column_List" /> from purchase_invoice where id in
<foreach collection="invoiceIdList" open="(" close=")" separator="," item="item">
#{item}
</foreach>
  </select>

</mapper>