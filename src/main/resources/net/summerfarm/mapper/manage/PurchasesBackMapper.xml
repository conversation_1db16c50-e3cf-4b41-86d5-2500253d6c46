<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesBackMapper">

    <select id="selectByNo" resultType="net.summerfarm.model.domain.PurchasesBack">
        SELECT id,
               purchases_back_no purchasesBackNo,
               store_no          storeNo,
               admin_id          adminId,
               status,
               expect_time       expectTime,
               add_time          addTime,
               remark,
               type,
               auditor,
               audit_time        auditTime,
               tenant_id         tenantId
        FROM purchases_back
        WHERE purchases_back_no = #{purchasesBackNo}
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.vo.PurchasesBackVO" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO purchases_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesBackNo != null">
                purchases_back_no,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="storeNo != null">
                status,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="auditor != null">
                auditor,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
        </trim>
        VALUE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesBackNo != null">
                #{purchasesBackNo} ,
            </if>
            <if test="storeNo != null">
                #{storeNo} ,
            </if>
            <if test="adminId != null">
                #{adminId} ,
            </if>
            <if test="storeNo != null">
                #{status} ,
            </if>
            <if test="expectTime != null">
                #{expectTime} ,
            </if>
            <if test="addTime != null">
                #{addTime} ,
            </if>
            <if test="remark != null">
                #{remark} ,
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="auditor != null">
                #{auditor},
            </if>
            <if test="auditTime != null">
                #{auditTime},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="adminName != null">
                #{adminName}
            </if>
        </trim>
    </insert>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.PurchasesBackVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchasesBackNo" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="storeNo" property="storeNo" jdbcType="INTEGER"/>
        <result column="adminId" property="adminId" jdbcType="INTEGER"/>
        <result column="adminName" property="adminName" jdbcType="VARCHAR"/>
        <result column="tenantName" property="tenantName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="expectTime" property="expectTime"/>
        <result column="addTime" property="addTime"/>
        <result column="updatetime" property="updatetime"/>
        <result column="supplier" property="supplier"/>
        <result column="warehouseName" property="warehouseName"/>
        <result column="stockState" property="stockState" jdbcType="INTEGER"/>
        <collection property="purchasesBackDetailVOS" column="purchasesBackNo" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.PurchasesBackDetailMapper.selectByNo"/>
    </resultMap>

    <select id="selectVOS" parameterType="net.summerfarm.model.vo.PurchasesBackVO" resultMap="VOMap">
        SELECT pb.id,
               pb.purchases_back_no purchasesBackNo,
               pb.store_no          storeNo,
               pb.admin_id          adminId,
               a.realname           adminName,
               pb.admin_name        tenantName,
               pb.status,
               pb.auditor,
               pb.type,
               pb.audit_time        auditTime,
               st.state             stockState,
               st.updatetime,
               pb.expect_time       expectTime,
               pb.add_time          addTime,
               wsc.warehouse_name   warehouseName,
               pb.store_no          warehouseNo,
               pp.supplier
        FROM purchases_back pb
        LEFT JOIN stock_task st ON pb.purchases_back_no = st.task_no
        LEFT JOIN admin a ON pb.admin_id = a.admin_id
        LEFT JOIN purchases_back_detail pbd ON pb.purchases_back_no = pbd.purchases_back_no
        LEFT join purchases_plan pp on pp.purchase_no = pbd.batch and pp.sku = pbd.sku and pp.plan_status=1 and pp.origin_id is null
        LEFT join purchases ps on ps.purchase_no = pbd.batch
        left join warehouse_storage_center wsc on wsc.warehouse_no = ps.area_no
        LEFT JOIN inventory i ON pbd.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        <where>
            <if test="expectTime!=null">
                AND pb.expect_time = #{expectTime}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() != 0">
                AND pb.store_no in
                <foreach collection="warehouseNoList" item="warehouseNo" open="(" close=")" separator=",">
                    #{warehouseNo}
                </foreach>
            </if>
            <if test="supplierId!=null">
                AND pp.supplier_id = #{supplierId}
            </if>
            <if test="supplierIdList!=null and supplierIdList.size!=0">
                AND pp.supplier_id in
                <foreach collection="supplierIdList" item="supplierId" separator="," open="(" close=")">
                    #{supplierId}
                </foreach>
            </if>
            <if test="storeNo != null">
                AND pb.store_no = #{storeNo}
            </if>
            <if test="purchasesBackNo != null">
                AND pb.purchases_back_no = #{purchasesBackNo}
            </if>
            <if test="status != null">
                AND pb.status = #{status}
            </if>
            <if test="type != null">
                AND pb.type = #{type}
            </if>
            <if test="stockState != null">
                AND st.state = #{stockState}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE concat('%', #{pdName} ,'%')
            </if>
            <if test="sku != null">
                AND pbd.sku = #{sku}
            </if>
            <if test="adminId != null">
                AND pb.admin_id = #{adminId}
            </if>
            <if test="tenantId == null and adminName != null">
                AND a.realname LIKE concat('%', #{adminName} , '%')
            </if>
            <if test="purchasesNo != null">
                AND pbd.batch = #{purchasesNo}
            </if>
            <if test="purchaser!=null ">
                AND ps.purchaser=#{purchaser}
            </if>
            <if test="tenantId != null">
                AND pb.tenant_id = #{tenantId}
            </if>
            <if test="tenantId == null">
                AND pb.tenant_id = 1
            </if>
        </where>
        GROUP BY pb.id
        ORDER BY pb.id DESC
    </select>

    <select id="selectVO" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.PurchasesBackVO">
        SELECT pb.id, pb.purchases_back_no purchasesBackNo, pb.store_no storeNo, pb.admin_id adminId, pb.status,
        pb.expect_time expectTime, pb.add_time addTime, pb.remark, pb.auditor, pb.type, pb.audit_time auditTime, st.state stockState,
               (select batch from purchases_back_detail where purchases_back_no =#{purchasesBackNo} limit 1) purchasesNo
        FROM purchases_back pb
        LEFT JOIN stock_task st ON pb.purchases_back_no = st.task_no
        WHERE pb.purchases_back_no = #{purchasesBackNo}
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.PurchasesBack">
        UPDATE purchases_back
        <set>
            <if test="status != null">
                status = #{status} ,
            </if>
            <if test="auditor != null">
                auditor = #{auditor},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="remark != null">
                remark = #{remark} ,
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="purchasesBackAmountInPlan" resultType="java.math.BigDecimal">
SELECT
	ifnull( sum( t2.out_cost ), 0 )
FROM
	(
	SELECT
		pp.purchase_no purchase_no,
		pp.sku sku
	FROM
		purchases_plan pp
		LEFT JOIN purchases p ON pp.purchase_no = p.purchase_no
		LEFT JOIN inventory i ON pp.sku = i.sku
		LEFT JOIN supplier s ON pp.supplier_id = s.id
	WHERE
		s.settle_form = 0
	    AND pp.plan_status = 1
		AND p.state != - 1
		AND i.type = 0
		AND pp.quantity != 0
		AND pp.price > 0
		AND pp.settle_flag = 0
	) t1
	INNER JOIN (
	SELECT
		pbd.batch purchase_no,
		pbd.sku sku,
		pbd.out_quantity AS out_quantity,
		pbd.total_cost AS out_cost
	FROM
		purchases_back pb
		LEFT JOIN purchases_back_detail pbd ON pb.purchases_back_no = pbd.purchases_back_no
	WHERE
		pb.STATUS = 2
	) t2 ON t1.purchase_no = t2.purchase_no
	AND t1.sku = t2.sku    </select>

    <select id="purchasesBackAmountInCash" resultType="java.math.BigDecimal">
SELECT
	ifnull( sum( t2.out_cost ), 0 )
FROM
	(
	SELECT
		pp.purchase_no purchase_no,
		pp.sku sku
	FROM
		purchases_plan pp
		LEFT JOIN purchases p ON pp.purchase_no = p.purchase_no
		LEFT JOIN inventory i ON pp.sku = i.sku
		LEFT JOIN supplier s ON pp.supplier_id = s.id
	WHERE
		s.settle_form IN ( 2, 3 )
        AND pp.plan_status = 1
		AND p.state != - 1
		AND i.type = 0
		AND pp.quantity != 0
		AND pp.price > 0
		AND pp.settle_flag = 0
	) t1
	INNER JOIN (
	SELECT
		pbd.batch purchase_no,
		pbd.sku sku,
		pbd.out_quantity AS out_quantity,
		pbd.total_cost AS out_cost
	FROM
		purchases_back pb
		LEFT JOIN purchases_back_detail pbd ON pb.purchases_back_no = pbd.purchases_back_no
	WHERE
		pb.STATUS = 2
	) t2 ON t1.purchase_no = t2.purchase_no
	AND t1.sku = t2.sku
    </select>


</mapper>
