<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CashToRpMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CashToRp" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="cash_id" property="cashId" jdbcType="INTEGER" />
    <result column="rp_id" property="rpId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, cash_id, rp_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from cash_to_rp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from cash_to_rp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.CashToRp" >
    insert into cash_to_rp (id, cash_id, rp_id
      )
    values (#{id,jdbcType=INTEGER}, #{cashId,jdbcType=INTEGER}, #{rpId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.CashToRp" >
    insert into cash_to_rp
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cashId != null" >
        cash_id,
      </if>
      <if test="rpId != null" >
        rp_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="cashId != null" >
        #{cashId,jdbcType=INTEGER},
      </if>
      <if test="rpId != null" >
        #{rpId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CashToRp" >
    update cash_to_rp
    <set >
      <if test="cashId != null" >
        cash_id = #{cashId,jdbcType=INTEGER},
      </if>
      <if test="rpId != null" >
        rp_id = #{rpId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CashToRp" >
    update cash_to_rp
    set cash_id = #{cashId,jdbcType=INTEGER},
      rp_id = #{rpId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>