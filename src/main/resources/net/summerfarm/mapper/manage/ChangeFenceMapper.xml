<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ChangeFenceMapper" >
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ChangeFence" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="area_no" property="areaNo" jdbcType="VARCHAR" />
        <result column="fence_id" property="fenceId" jdbcType="VARCHAR" />
        <result column="change_to_store_no" property="changeToStoreNo" jdbcType="INTEGER" />
        <result column="type" property="type" jdbcType="TIMESTAMP" />
        <result column="change_acm_id" property="changeAcmId"/>
        <result column="change_to_fence_id" property="changeToFenceId"/>
        <result column="status" property="status"/>
        <result column="exe_time" property="exeTime"/>
        <result column="operator" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List" >
    id, area_no, fence_id, change_to_store_no, `type`,change_acm_id,status,change_to_fence_id,exe_time,operator
    </sql>

    <!--auto code-->
    <select id="selectByAreaNo" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from change_fence
        where area_no = #{areaNo} and status = 0
    </select>

    <insert id="insertChangeFence" parameterType="net.summerfarm.model.domain.ChangeFence" >
    insert into change_fence (area_no, fence_id,change_to_store_no, `type`,change_acm_id,status,change_to_fence_id,exe_time,operator)
    values (#{areaNo}, #{fenceId}, #{changeToStoreNo}, #{type}, #{changeAcmId}, #{status}, #{changeToFenceId},#{exeTime},#{operator})
  </insert>

    <update id="updateChangeFence" parameterType="java.lang.Integer" >
      update change_fence set status = #{status} where id = #{id}
  </update>

    <update id="updateSelective">
        update change_fence
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByFenceId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from change_fence
        where fence_id = #{fenceId} and status = 0
    </select>

    <select id="selectByChangeToFenceId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from change_fence
        where change_to_fence_id = #{changToFenceId} and status = 0
    </select>


    <select id="selectByChangeToStoreNo" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from change_fence
        where change_to_store_no = #{storeNo} and status = 0
    </select>
    <select id="selectEffectByAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from change_fence
        where area_no = #{areaNo} and status = 0 order by exe_time desc limit 1

    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from change_fence
        where id = #{id}
    </select>

    <select id="selectSuccessTasks" resultMap="BaseResultMap" parameterType="java.time.LocalDate">
        SELECT * FROM change_fence WHERE DATE_FORMAT(exe_time,'%Y-%m-%d') = #{date} AND `status` = 2
    </select>

    <select id="selectHandlingTasksByStoreNo" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(*) FROM wnc_fence_change_task WHERE `status` IN (0,10,15) AND store_no = #{storeNo}
    </select>

    <select id="selectHandlingTasksByFenceId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT count(*) FROM wnc_fence_change_task WHERE `status` IN (0,10,15) AND fence_id = #{storeNo}
    </select>

    <select id="selectHandlingAreas" resultType="java.lang.String">
        SELECT GROUP_CONCAT(change_acm_id) AS areaStr FROM wnc_fence_change_task WHERE `status` IN (0,10)
    </select>
</mapper>
