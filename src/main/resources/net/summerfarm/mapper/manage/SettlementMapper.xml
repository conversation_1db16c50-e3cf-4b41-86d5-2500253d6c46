<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SettlementMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Settlement" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="pay_type" property="payType" jdbcType="INTEGER" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="account_bank" property="accountBank" jdbcType="VARCHAR" />
    <result column="account_ascription" property="accountAscription" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="pd_type" property="pdType" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="VOMap" type="net.summerfarm.model.vo.SettlementVO" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="supplier_id" property="supplierId" jdbcType="INTEGER" />
    <result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
    <result column="pay_type" property="payType" jdbcType="INTEGER" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="account_bank" property="accountBank" jdbcType="VARCHAR" />
    <result column="account_ascription" property="accountAscription" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="deduction_amount" property="deductionAmount" jdbcType="DECIMAL" />
    <result column="refund_settlement_amount" property="refundSettlementAmount" jdbcType="DECIMAL" />
    <collection property="detailList" ofType="net.summerfarm.model.vo.SettlementDetailVO" select="selectDetailList" column="id">
      <result column="id" property="id" jdbcType="INTEGER" />
      <result column="settlementId" property="settlementId" jdbcType="INTEGER" />
      <result column="purchase_no" property="purchaseNo" jdbcType="VARCHAR" />
      <result column="type" property="type" jdbcType="INTEGER" />
      <result column="purchasePlanId" property="purchasePlanId" jdbcType="INTEGER" />
      <result column="skuAmount" property="skuAmount" jdbcType="DECIMAL" />
      <result column="sku" property="sku" jdbcType="VARCHAR"/>
      <result column="extType" property="extType"  jdbcType="INTEGER"/>
      <result column="pdName" property="pdName" jdbcType="VARCHAR"/>
      <result column="weight" property="weight" jdbcType="VARCHAR"/>
      <result column="quantity" property="quantity" jdbcType="INTEGER"/>
      <result column="inPrice" property="inPrice" jdbcType="DECIMAL"/>
      <result column="purchases_type" property="purchasesType" jdbcType="INTEGER"/>
      <result column="state" property="state" jdbcType="INTEGER"/>
      <result column="in_price" property="inPrice" jdbcType="INTEGER"/>
    </collection>
    <collection property="recordList" ofType="net.summerfarm.model.vo.SettlementPaymentRecordVO" select="selectRecordList" column="id">
      <result column="id" property="id" jdbcType="INTEGER" />
      <result column="settlement_id" property="settlementId" jdbcType="INTEGER" />
      <result column="amount" property="amount" jdbcType="DECIMAL" />
      <result column="expected_time" property="expectedTime" jdbcType="TIMESTAMP" />
      <result column="remark" property="remark" jdbcType="VARCHAR" />
      <result column="settlement_count" property="settlementCount" jdbcType="INTEGER" />
      <result column="status" property="status" jdbcType="INTEGER" />
      <result column="creator" property="creator" jdbcType="VARCHAR" />
      <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
      <result column="auditor" property="auditor" jdbcType="VARCHAR" />
      <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
      <result column="approver" property="approver" jdbcType="VARCHAR" />
      <result column="approve_time" property="approverTime" jdbcType="TIMESTAMP" />
      <result column="payer" property="payer" jdbcType="VARCHAR" />
      <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
      <result column="payment_voucher" property="paymentVoucher" jdbcType="VARCHAR" />
    </collection>
  </resultMap>

  <select id="selectDetailList" parameterType="integer" resultType="net.summerfarm.model.vo.SettlementDetailVO">
    select sd.id id,
       settlement_id settlementId,
       sd.purchase_no purchaseNo,
       sd.type type,
       purchase_plan_id purchasePlanId,
       sku_amount skuAmount,
       pp.sku sku,
       pp.title pdName,
       pp.quantity quantity,
       pp.in_price inPrice,
       p.purchase_time purchaseTime,
       i.weight weight, pp.price,
       p.purchases_type purchasesType,
       p.process_state state,
       pp.in_quantity inQuantity,
       p.purchases_type purchasesType,
       i.ext_type extType
    from settlement_detail sd
             left join purchases_plan pp on sd.purchase_plan_id = pp.id and pp.plan_status = 1
             left join inventory i on i.sku = pp.sku
             left join purchases p on p.purchase_no = sd.purchase_no
    where sd.settlement_id = #{id}
  </select>


    <select id="selectConditionDetail" parameterType="net.summerfarm.model.vo.SettlementDetailVO" resultType="net.summerfarm.model.vo.SettlementDetailVO">
        select sd.id id,
        settlement_id settlementId,
        sd.purchase_no purchaseNo,
        sd.type type,
        purchase_plan_id purchasePlanId,
        sku_amount skuAmount,
        pp.sku sku,
        pp.title pdName,
        pp.quantity quantity,
        pp.in_price inPrice,
        p.purchase_time purchaseTime,
        i.weight weight, pp.price,
        p.purchases_type purchasesType,
        p.process_state state,
        pp.in_quantity inQuantity
        from settlement_detail sd
        left join purchases_plan pp on sd.purchase_plan_id = pp.id and pp.plan_status = 1
        left join inventory i on i.sku = pp.sku
        left join purchases p on p.purchase_no = sd.purchase_no
        <where>
            sd.settlement_id = #{id}
            <if test="state != null">
                and st.state = #{state}
            </if>
            <if test="inPrice != null">
                and pp.price > #{inPrice}
            </if>
        </where>
    </select>

  <select id="selectRecordList" parameterType="integer" resultType="net.summerfarm.model.vo.SettlementPaymentRecordVO">
    select id,
       settlement_id settlementId,
       amount,
       expected_time expectedTime,
       remark,
       settlement_count settlementCount,
       status,
       creator,
       create_time createTime,
       auditor,
       audit_time auditTime,
       approver,
       approver_time approverTime,
       payer,
       pay_time payTime,
       payment_voucher paymentVoucher,
       withdraw_time withdrawTime
    from settlement_payment_record where settlement_id = #{id}
  </select>

  <insert id="insert" parameterType="net.summerfarm.model.domain.Settlement" >
    insert into settlement (id, supplier_id, supplier_name,
      pay_type, account_name, account_bank,
      account_ascription, account, status,
      total_amount, creator, create_time
      )
    values (#{id,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, #{supplierName,jdbcType=VARCHAR},
      #{payType,jdbcType=INTEGER}, #{accountName,jdbcType=VARCHAR}, #{accountBank,jdbcType=VARCHAR},
      #{accountAscription,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{totalAmount,jdbcType=DECIMAL}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.Settlement" >
    insert into settlement
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="supplierId != null" >
        supplier_id,
      </if>
      <if test="supplierName != null" >
        supplier_name,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="accountName != null" >
        account_name,
      </if>
      <if test="accountBank != null" >
        account_bank,
      </if>
      <if test="accountAscription != null" >
        account_ascription,
      </if>
      <if test="account != null" >
        account,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="pdType != null" >
        pd_type,
      </if>
      <if test="deductionAmount != null" >
        deduction_amount,
      </if>
      <if test="refundSettlementAmount != null" >
        refund_settlement_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null" >
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null" >
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null" >
        #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null" >
        #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdType != null">
        #{pdType,jdbcType=INTEGER},
      </if>
      <if test="deductionAmount != null" >
        #{deductionAmount},
      </if>
      <if test="refundSettlementAmount != null" >
        #{refundSettlementAmount},
      </if>
    </trim>
  </insert>
  <select id="select" parameterType="net.summerfarm.model.input.SettlementQuery" resultType="net.summerfarm.model.vo.SettlementVO">
    select id,
       status,
       supplierName,
       totalAmount,
       sum(paidAmount) paidAmount,
       sum(unPidAmount) settlementProgress,
       createTime,
       creator,
       storeNo,
       deductionAmount,
       refundSettlementAmount
    from (
             select s.id,
                    s.status,
                    s.supplier_name                   supplierName,
                    s.total_amount                    totalAmount,
                    if(spr.status = 5, spr.amount, 0) paidAmount,
                    if(spr.status in (1,3,6), spr.amount, 0) unPidAmount,
                    s.create_time                     createTime,
                    s.creator,
                    p.area_no                         storeNo,
                    s.deduction_amount                deductionAmount,
                    s.refund_settlement_amount        refundSettlementAmount
             from settlement_payment_record spr
                      right join settlement s on spr.settlement_id = s.id
                      left join settlement_detail sd on sd.settlement_id = s.id
                      left join purchases_plan pp on pp.id = sd.purchase_plan_id and pp.plan_status = 1
                      left join purchases p on pp.purchase_no = p.purchase_no
            <where>
              <if test="status != null">
                and s.status = #{status}
              </if>
              <if test="storeNo != null">
                and p.area_no = #{storeNo}
              </if>
              <if test="settlementId != null">
                and s.id = #{settlementId}
              </if>
              <if test="supplierId != null">
                and s.supplier_id = #{supplierId}
              </if>
              <if test="purchaseNo != null">
                and p.purchase_no = #{purchaseNo}
              </if>
              <if test="creator != null">
                 and s.creator like concat('%',#{creator},'%')
              </if>
            </where>
             group by spr.id
         ) t
    group by t.id
    order by id desc
  </select>
  <select id="selectSettlementVO" parameterType="integer" resultMap="VOMap">
    select id,
       supplier_id,
       supplier_name,
       pay_type,
       account_name,
       account_bank,
       account_ascription,
       account,
       status,
       total_amount,
       creator,
       create_time,
       updater,
       update_time,
       deduction_amount,
       refund_settlement_amount
    from settlement where id = #{settlementId}
  </select>
  <update id="updateSelective" parameterType="net.summerfarm.model.domain.Settlement">
    update settlement
    <set>
      <if test="supplierId != null" >
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null" >
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null" >
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null" >
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null" >
        account_bank = #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null" >
        account_ascription = #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null" >
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>
  <select id="unFinishAmount" resultType="decimal">
    select ifnull(sum(unSettleAmount), 0)
    from (select ifnull(s.total_amount - t.amount, 0) unSettleAmount
          from settlement s
                   left join (select settlement_id, ifnull(sum(amount), 0) amount
                              from settlement_payment_record spr
                              where spr.status in (1, 3, 5)
                              group by settlement_id) t on s.id = t.settlement_id
                    left join supplier s2 on s.supplier_id = s2.id
          where s.status in (1, 2) and s2.settle_form = 0) t
  </select>
  <select id="selectLastRecord" resultMap="VOMap">
      select id,
       supplier_id,
       supplier_name,
       pay_type,
       account_name,
       account_bank,
       account_ascription,
       account,
       status,
       total_amount,
       creator,
       create_time,
       updater,
       update_time,
       deduction_amount,
       refund_settlement_amount
    from settlement order by id desc limit 1
  </select>
  <select id="unFinishCashAmount" resultType="decimal">
    select ifnull(sum(unSettleAmount), 0)
    from (select ifnull(s.total_amount - t.amount, 0) unSettleAmount
          from settlement s
                   left join (select settlement_id, ifnull(sum(amount), 0) amount
                              from settlement_payment_record spr
                              where spr.status in (1, 3, 5)
                              group by settlement_id) t on s.id = t.settlement_id
                    left join supplier s2 on s.supplier_id = s2.id
          where s.status in (1, 2) and s2.settle_form in (2,3)) t
  </select>

    <select id="selectById" resultMap="BaseResultMap">
        select id,
               supplier_id,
               supplier_name,
               pay_type,
               account_name,
               account_bank,
               account_ascription,
               account,
               status,
               total_amount,
               creator,
               create_time,
               pd_type
        from settlement where id=#{settlementId};
    </select>

    <select id="selectByNo" resultType="net.summerfarm.model.domain.Settlement">
        select s.id id,s.status status
        from  settlement s
        left join settlement_detail sd on  s.id = sd.settlement_id
        where sd.purchase_no = #{purchaseNo} and sd.purchase_plan_id = #{purchasePlanId} and s.status <![CDATA[<>]]> 4
    </select>

    <select id="selectSupplierMessage" resultType="net.summerfarm.model.domain.Settlement">
        select s.id id,s.name name,sa.pay_type payType,sa.account_name accountName,
               sa.account_bank accountBank,sa.account_ascription accountAscription, sa.account acccount
        from   settlement se
        LEFT JOIN supplier s on se.supplier_id = s.id and se.supplier_name = s.name
        LEFT JOIN supplier_account sa on s.id = sa.supplier_id
        where s.id = #{supplierId} and s.name = #{supplierName}
        limit 1
    </select>

    <select id="selectByPurchasesNo" resultMap="VOMap">
            select distinct s.id,
            supplier_id,
            supplier_name,
            pay_type,
            account_name,
            account_bank,
            account_ascription,
            account,
            total_amount,
            s.deduction_amount,
            refund_settlement_amount
            from settlement s  left join settlement_detail sd on s.id = sd.settlement_id
                                 left join settlement_payment_record spr on s.id = spr.settlement_id where sd.purchase_no=#{purchasesNo} and s.status != 4
    </select>

    <update id="updateRefundSettlementAmount">
        update settlement
        set refund_settlement_amount = refund_settlement_amount + #{refundSettlementAmount},
            updater = #{adminName},
            update_time = now()
        where id = #{settlementId}
    </update>


    <update id="updateSettlementRefundSettlementAmount">
        update settlement
        set refund_settlement_amount = refund_settlement_amount + #{refundSettlementAmount},
            total_amount = total_amount + #{refundSettlementAmount},
            updater = #{adminName},
            update_time = now()
        where id = #{settlementId}
    </update>

    <select id="selectPartSettle" resultType="net.summerfarm.model.domain.Settlement">
        select id
        from settlement
        where status = 2
    </select>
</mapper>
