<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskMapper">

    <resultMap id="withItem" type="net.summerfarm.model.domain.StockTask">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="expect_time" property="expectTime"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="updatetime" property="updatetime"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dimension" property="dimension" jdbcType="INTEGER"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="mismatch_reason" property="mismatchReason" jdbcType="VARCHAR"/>
        <result column="option_flag" property="optionFlag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="baseColumn">
        id
        ,task_no,area_no,type,expect_time,state,addtime,admin_id,remark,dimension,mismatch_reason,category,updatetime
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTask">
        INSERT INTO stock_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                task_no,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="outStoreNo != null">
                out_store_no,
            </if>
            <if test="outType != null">
                out_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="dimension != null">
                dimension,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="optionFlag != null">
                option_flag,
            </if>
            inventory_locked
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">
                #{taskNo},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="expectTime != null">
                #{expectTime},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="outStoreNo != null">
                #{outStoreNo},
            </if>
            <if test="outType != null">
                #{outType},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="dimension != null">
                #{dimension},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="category != null">
                #{category},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="optionFlag != null">
                #{optionFlag},
            </if>
            0
        </trim>
    </insert>

    <update id="update" parameterType="net.summerfarm.model.domain.StockTask">
        UPDATE stock_task
        SET state = #{state},
        <if test="processState != null">
            process_state = #{processState},
        </if>
        <if test="mismatchReason != null">
            mismatch_reason = #{mismatchReason},
        </if>
        <if test="adminId != null">
            admin_id = #{adminId},
        </if>
        updatetime = #{updatetime}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByTaskNo" parameterType="net.summerfarm.model.domain.StockTaking">
        UPDATE stock_task
        SET state       = #{state},
            updatetime  = #{updatetime},
            expect_time = #{expectTime}
        WHERE task_no = #{taskNo}
    </update>

    <update id="updateOptionFlagById" parameterType="net.summerfarm.model.domain.StockTask">
        UPDATE stock_task
        SET option_flag = #{optionFlag}
        WHERE id = #{id}
    </update>

    <select id="selectByPrimaryKey" resultType="net.summerfarm.model.domain.StockTask">
        /*FORCE_MASTER*/
        SELECT st.id,
               st.task_no            taskNo,
               st.out_store_no       outStoreNo,
               st.type,
               st.expect_time        expectTime,
               st.state,
               st.addtime,
               st.updatetime,
               st.area_no            areaNo,
               st.out_type           outType,
               st.remark,
               st.dimension,
               st.process_state      processState,
               st.remark,
               st.mismatch_reason as mismatchReason,
               task_type          as taskType,
               category,
               close_reason          closeReason,
               st.option_flag        optionFlag,
               st.system_source      systemSource,
               st.tenant_id          tenantId
        FROM stock_task st
        WHERE st.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectListByPrimaryKey" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id,
        st.task_no taskNo,
        st.out_store_no outStoreNo,
        st.type,
        st.expect_time expectTime,
        st.state,
        st.addtime,
        st.updatetime,
        st.area_no areaNo,
        st.out_type outType,
        st.remark,
        st.dimension,
        st.process_state processState,
        st.remark,
        st.mismatch_reason as mismatchReason,
        task_type as taskType,
        category,
        close_reason closeReason,
        st.option_flag optionFlag
        FROM stock_task st
        WHERE st.id IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectOptionFlagById" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.option_flag optionFlag
        FROM stock_task st
        WHERE st.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectOptionFlagByIdList" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.option_flag optionFlag
        FROM stock_task st
        WHERE st.id IN
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectOne" resultType="net.summerfarm.model.domain.StockTask">
        SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectAllStatus" resultType="integer">
        SELECT state
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectPurchasesTasks" resultType="net.summerfarm.model.domain.StockTask">
        SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <select id="selectWithItem" resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT t.id,t.task_no taskNo,t.area_no areaNo,t.out_store_no outStoreNo,t.type,t.expect_time
        expectTime,t.state,t.addtime,if(t.state in (1,2),updatetime,null) updatetime,t.process_state
        processState,t.category,t.option_flag optionFlag
        FROM stock_task t
        <if test="supplierId != null">
            LEFT JOIN purchases_plan pp on t.task_no = pp.purchase_no
        </if>
        <if test="sku != null or pdId != null">
            INNER JOIN stock_task_item sti ON t.id=sti.stock_task_id
            <choose>
                <when test="sku != null">
                    AND sti.sku LIKE concat('%',#{sku} ,'%')
                    INNER JOIN inventory i ON sti.sku=i.sku
                </when>
                <otherwise>
                    INNER JOIN inventory i ON sti.sku=i.sku
                    INNER JOIN products p ON i.pd_id=p.pd_id
                </otherwise>
            </choose>
        </if>
        WHERE
        t.type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="stateList != null and stateList.size!=0 ">
            AND t.state IN
            <foreach collection="stateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="typeList != null and typeList.size!=0 ">
            AND t.type IN
            <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null">
            AND t.id = #{id}
        </if>
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="areaNo != null">
            AND t.area_no = #{areaNo}
        </if>
        <if test="warehouseNoList != null and warehouseNoList.size!=0 ">
            AND t.area_no IN
            <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="state != null">
            AND t.state = #{state}
        </if>
        <if test="taskNo != null">
            AND t.task_no = #{taskNo}
        </if>
        <if test="storeNo != null">
            AND t.out_store_no = #{storeNo}
        </if>
        <if test=" pdId != null">
            AND i.pd_id = #{pdId}
        </if>
        <if test="processState != null">
            AND t.process_state = #{processState}
        </if>
        <if test="supplierId != null">
            AND pp.supplier_id = #{supplierId}
        </if>
        <if test="startTime != null">
            AND t.expect_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND t.expect_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="mailPushState != null">
            AND (t.option_flag &amp; -9223372036854775807) = #{mailPushState}
        </if>
        <if test="startAddTime != null">
            AND t.addtime <![CDATA[>=]]> #{startAddTime}
        </if>
        <if test="endAddTime != null">
            AND t.addtime <![CDATA[<=]]> #{endAddTime}
        </if>
        <!--AND t.tenant_id = IFNULL(#{tenantId},1)-->
        GROUP BY t.id
        ORDER BY t.id DESC
    </select>

    <select id="selectUnfinishTask" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT st.id,st.area_no areaNo,st.type,st.state,st.addtime,st.expect_time expectTime,a.realname adminName
        FROM stock_task st
        LEFT JOIN admin a ON st.admin_id = a.admin_id
        WHERE st.state IN (0,1)
        <if test="typeList != null and typeList.size!=0 ">
            AND st.type IN
            <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            AND st.type = #{type}
        </if>
        <if test="state != null">
            AND st.state = #{state}
        </if>
        <if test="areaNo != null">
            AND st.area_no = #{areaNo}
        </if>

    </select>

    <select id="unfinishTaskExcludeTransfer" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT st.id,st.area_no areaNo,st.type,st.state,st.addtime,st.expect_time expectTime,a.realname adminName
        FROM stock_task st
        LEFT JOIN admin a ON st.admin_id = a.admin_id
        WHERE st.state IN (0,1)
        and st.type != 82
        <if test="typeList != null and typeList.size!=0 ">
            AND st.type IN
            <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            AND st.type = #{type}
        </if>
        <if test="state != null">
            AND st.state = #{state}
        </if>
        <if test="areaNo != null">
            AND st.area_no = #{areaNo}
        </if>

    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockTask">
        UPDATE stock_task
        <set>
            <if test="taskNo != null">
                task_no = #{taskNo},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="closeReason != null">
                close_reason = #{closeReason},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectStoreingSku" resultType="net.summerfarm.model.domain.StockTaskItem">
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type IN (12, 53)
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type = 51
          AND st.out_type = 0
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type IN (82, 56)
          AND sti.sku = #{sku}
        UNION ALL
        SELECT sti.sku
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND st.type = 51
          AND st.out_type = 1
          AND sti.quantity != sti.actual_quantity
            AND sku = #{sku}
        UNION ALL
        select spi.sku
        from stock_task st
                 inner join stock_shipment_item spi on st.id = spi.stock_task_id
        where sku = #{sku}
          AND st.state IN (0, 1)
          and st.area_no = #{areaNo}
          and actual_quantity != quantity
        UNION ALL
        select sti.sku
        from stock_task st
                 inner join stock_storage_item sti on st.id = sti.stock_task_id
        where sku = #{sku}
          AND st.state IN (0, 1)
          and st.area_no = #{areaNo}
          and actual_quantity != quantity
    </select>

    <select id="selectPurchasingSku" resultType="net.summerfarm.model.domain.PurchasesPlan">
        SELECT pp.id,
               pp.purchase_no  purchaseNo,
               pp.title,
               pp.specification,
               pp.sku,
               pp.price,
               pp.quantity,
               pp.pack,
               pp.unit,
               pp.supplier,
               pp.supplier_id  supplierId,
               pp.check_report checkReport,
               pp.quality_date qualityDate,
               pp.in_quantity  inQuantity,
               pp.in_price     inPrice,
               pp.origin_id    originId
        FROM stock_task st
                 INNER JOIN purchases p ON st.task_no = p.purchase_no
                 INNER JOIN purchases_plan pp ON p.purchase_no = pp.purchase_no and pp.plan_status = 1
        WHERE st.area_no = #{areaNo}
          AND st.state IN (0, 1)
          AND pp.sku = #{sku}
    </select>

    <select id="selectVO" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT st.id,st.area_no areaNo,st.type,st.state,st.addtime,st.expect_time expectTime,st.updatetime,a.realname
        adminName,st.remark,st.dimension
        FROM stock_task st
        LEFT JOIN admin a ON st.admin_id=a.admin_id
        <if test="sku != null or pdName != null">
            INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
            <if test="sku != null">
                AND sti.sku = #{sku}
            </if>
            <if test="pdName != null">
                INNER JOIN inventory i ON sti.sku = i.sku
                INNER JOIN products p ON i.pd_id = p.pd_id
                AND p.pd_name LIKE concat('%',#{pdName} ,'%')
            </if>
        </if>
        <where>
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="state != null">
                AND st.state = #{state}
            </if>
            <if test="remark != null">
                AND st.remark = #{remark}
            </if>
            <if test="addtime != null">
                AND st.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="typeList != null and typeList.size!=0 ">
                AND st.type IN
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="selectUnFinishTask" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.state, st.type, st.area_no areaNo, st.out_store_no outStoreNo
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.type in (51,52,57,58)
          AND st.state IN (0, 1)
          AND st.area_no = #{areaNo}
          AND sti.sku = #{sku}
    </select>

    <select id="stockTaskList" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.state, st.type, st.area_no areaNo
        FROM stock_task st
        WHERE st.addtime <![CDATA[>=]]> #{startTime}
          AND st.addtime <![CDATA[<]]> #{endTime}
          AND st.area_no = #{areaNo}
    </select>

    <select id="finishTaskList" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.type, st.area_no areaNo
        FROM stock_task st
        WHERE st.state = 2
          AND st.updatetime <![CDATA[>=]]> #{startTime}
          AND st.updatetime <![CDATA[<]]> #{endTime}
          AND st.area_no = #{areaNo}
    </select>


    <select id="selectTaskMoney" resultType="java.math.BigDecimal">
        SELECT SUM(stid.quantity*t.cost)
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id AND st.type=51
        AND DATE(addtime) <![CDATA[>=]]> #{startDate} and DATE(addtime) <![CDATA[<]]> #{endDate} AND
        st.area_no=#{storeNo} AND task_no IS NULL
        INNER JOIN stock_task_item_detail stid ON sti.id=stid.stock_task_item_id
        INNER join inventory i on i.sku = sti.sku and i.type = 0
        inner join products p on i.pd_id = p.pd_id
        inner join category c on p.category_id = c.id
        <if test="type == null">
            and c.type != 4
        </if>
        <if test="type != null">
            and c.type = #{type}
        </if>
        INNER JOIN (
        SELECT batch,sku,quality_date,cost
        FROM store_record
        GROUP BY batch,sku,quality_date
        ) t ON stid.sku=t.sku AND stid.list_no=t.batch
        AND (
        (stid.quality_date IS NULL AND t.quality_date IS NULL)
        OR
        (stid.quality_date = t.quality_date)
        )
    </select>

    <select id="waitOutAllocationTask" resultType="net.summerfarm.model.vo.StockSubscribeVO">
        SELECT sai.sku, p.pd_name pdName, i.weight, i.unit, sai.out_quantity quantity
        FROM stock_allocation_list sal
                 INNER JOIN stock_allocation_item sai ON sal.list_no = sai.list_no
            AND sal.in_store = #{areaNo}
            AND sal.status = 3
            AND DATE (sal.expect_time) = #{expectTime}
            INNER JOIN inventory i
        ON sai.sku = i.sku
            INNER JOIN products p ON i.pd_id = p.pd_id
    </select>

    <select id="centerToStoreData" parameterType="net.summerfarm.model.vo.StockTaskItemVO"
            resultType="net.summerfarm.model.vo.StockTaskItemVO">
        SELECT st.area_no areaNo, st.out_store_no outStoreNo, p.pd_name pdName, stpd.sku, i.weight, sum(stpd.quantity)
        quantity, i.ext_type extType
        FROM stock_task st
        INNER JOIN stock_task_process stp ON st.id = stp.stock_task_id AND st.type = 51
        INNER JOIN stock_task_process_detail stpd ON stp.id = stpd.stock_task_process_id
        LEFT JOIN inventory i ON stpd.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        WHERE st.area_no = #{areaNo}
        AND st.out_store_no = #{outStoreNo}
        <if test="sku != null">
            AND stpd.sku = #{sku}
        </if>
        <if test="pdName != null">
            AND p.pd_name LIKE concat('%', #{pdName} ,'%')
        </if>
        <if test="startTime != null">
            AND stp.addtime <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND stp.addtime <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY stpd.sku
    </select>

    <select id="selectTaskState" resultType="integer">
        select st.state
        from stock_task_item sti
                 left join stock_task st on sti.stock_task_id = st.id
        where st.type = #{type}
          and sti.sku = #{sku}
          and st.task_no = #{purchaseNo}
    </select>

    <select id="selectUnStoreList" parameterType="net.summerfarm.model.vo.StoreRecordVO"
            resultType="net.summerfarm.model.vo.StoreRecordVO">
        select st.task_no batch, sti.sku, st.area_no areaNo, sti.quantity - sum(pp.in_quantity) storeQuantity,
        pp.production_date productionDate, pp.supplier,pp.quantity,pp.price
        totalCost,convert(pp.price/pp.quantity,decimal (15,8)) singleCost,i.weight,p.pd_name pdName
        from stock_task_item sti
        left join stock_task st on sti.stock_task_id = st.id
        left join purchases_plan pp on sti.sku = pp.sku and pp.purchase_no = st.task_no and pp.plan_status = 1
        left join inventory i on pp.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        where st.type = 11
        and st.state in (0,1)
        <if test="sku != null">
            and sti.sku = #{sku}
        </if>
        <if test="areaNo != null">
            AND st.area_no = #{areaNo}
        </if>
        <if test="batch != null">
            and pp.purchase_no = #{batch}
        </if>
        group by pp.purchase_no, pp.sku
        having storeQuantity > 0
    </select>

    <select id="selectQuantitySum" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StoreRecordVO">
        select *
        from (select i.sku,i.ext_type extType, sum(quantitySum) quantitySum, areaNo, p.pd_name pdName, i.weight,
        ad.name_remakes nameRemakes, sum(quantitySum)*i.weight_num totalWeight,
        i.type skuType, i.is_domestic isDomestic
        from (select sai.sku, sai.out_quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_allocation_list sal on st.task_no = sal.list_no
        left join stock_allocation_item sai on st.task_no = sai.list_no
        <where>
            and st.type = 50
            and st.state = 2
            <if test="areaNo != null">
                and sal.out_store = #{areaNo}
            </if>
            <if test="inStoreNo != null">
                <choose>
                    <when test="areaNo == inStoreNo">
                        and sal.in_store is null
                    </when>
                    <otherwise>
                        and sal.in_store = #{inStoreNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and sai.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and st.updatetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and st.updatetime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        union all
        select stpd.sku, stpd.quantity quantity, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            and st.type = 51
            and st.state in (1, 2)
            <if test="areaNo != null">
                <choose>
                    <when test="inStoreNo == null">
                        and st.area_no = #{areaNo}
                    </when>
                    <when test="areaNo == inStoreNo">
                        and st.area_no = #{areaNo}
                        and st.out_store_no is null
                    </when>
                    <otherwise>
                        and st.area_no = #{inStoreNo}
                        and st.out_store_no = #{areaNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        left join inventory i on t.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join admin ad on ad.admin_id = i.admin_id
        <where>
            <if test="pdName != null">
                AND p.pd_name LIKE CONCAT('%',#{pdName} ,'%')
            </if>
        </where>
        group by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectQuantitySumEx" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StoreRecordVO">
        select *
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (select sai.sku, sai.out_quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_allocation_list sal on st.task_no = sal.list_no
        left join stock_allocation_item sai on st.task_no = sai.list_no
        <where>
            and st.type = 50
            and st.state = 2
            <if test="areaNo != null">
                and sal.out_store = #{areaNo}
            </if>
            <if test="inStoreNo != null">
                <choose>
                    <when test="areaNo == inStoreNo">
                        and sal.in_store is null
                    </when>
                    <otherwise>
                        and sal.in_store = #{inStoreNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and sai.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and st.updatetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and st.updatetime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        union all
        select stpd.sku, stpd.quantity quantity, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            and st.type = 51
            and st.state in (1, 2)
            <if test="areaNo != null">
                <choose>
                    <when test="inStoreNo == null">
                        and st.area_no = #{areaNo}
                    </when>
                    <when test="areaNo == inStoreNo">
                        and st.area_no = #{areaNo}
                        and st.out_store_no is null
                    </when>
                    <otherwise>
                        and st.area_no = #{inStoreNo}
                        and st.out_store_no = #{areaNo}
                    </otherwise>
                </choose>
            </if>
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectUnFinishAllocationTask" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id, st.state, st.type, st.area_no areaNo, st.out_store_no outStoreNo
        FROM stock_task st
                 INNER JOIN stock_task_item sti ON st.id = sti.stock_task_id
        WHERE st.type = 10
          AND st.state IN (0, 1)
          AND st.area_no = #{areaNo}
    </select>

    <select id="selectSaleTaskQuantitySum" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StoreRecordVO">
        select *
        from (select i.sku, sum(quantitySum) quantitySum, areaNo, p.pd_name pdName, i.weight, i.ext_type extType,
        ad.name_remakes nameRemakes, sum(quantitySum)*i.weight_num totalWeight,
        i.type skuType, i.is_domestic isDomestic
        from (
        select stpd.sku, stpd.quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            st.state in (1, 2)
            and ( (st.area_no = #{areaNo} and st.out_store_no = #{storeNo}) or (st.area_no = #{areaNo} and
            st.out_store_no is null) )
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        left join inventory i on t.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join admin ad on ad.admin_id = i.admin_id
        <where>
            <if test="pdName != null">
                AND p.pd_name LIKE CONCAT('%',#{pdName} ,'%')
            </if>
        </where>
        group by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectSaleTaskQuantitySumEx" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StoreRecordVO">
        select *
        from (select t.sku, sum(quantitySum) quantitySum, areaNo
        from (
        select stpd.sku, stpd.quantity quantitySum, st.area_no areaNo
        from stock_task st
        left join stock_task_process stp on st.id=stp.`stock_task_id`
        left join stock_task_process_detail stpd on stp.id=stpd.stock_task_process_id
        <where>
            st.state in (1, 2)
            and ( (st.area_no = #{areaNo} and st.out_store_no = #{storeNo}) or (st.area_no = #{areaNo} and
            st.out_store_no is null) )
            <if test="sku != null">
                and stpd.sku = #{sku}
            </if>
            <if test="typeList != null and typeList.size != 0 ">
                and st.type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and stp.addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                and stp.addtime <![CDATA[<]]> #{endTime}
            </if>
        </where>
        ) t
        <where>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                t.sku in
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
        </where>
        group by sku
        order by sku
        ) tt
        order by quantitySum desc
    </select>

    <select id="selectSaleTaskMsg" resultType="net.summerfarm.model.domain.StockTask">
        select id, state, `type`, area_no areaNo
        from stock_task
        where DATE (addtime) = #{deliveryDate}
          and task_no is null
          and `type` = 51
          and out_store_no = #{storeNo}
          and state in (0
            , 1)
    </select>

    <select id="selectBySku" resultType="net.summerfarm.model.domain.StockTask">
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE
        st.type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_shipment_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        INNER JOIN stock_storage_item sti ON st.id=sti.stock_task_id
        AND sti.sku =#{sku} and sti.quantity!=sti.actual_quantity
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
        union
        SELECT
        st.id ,st.type
        FROM stock_task st
        inner join wms_damage_stock_task sti on sti.stock_task_id =st.id
        left JOIN wms_damage_stock_item stid ON sti.id=stid.damage_stock_task_id
        and stid.quantity!=stid.should_quantity AND stid.sku =#{sku}
        WHERE st.state in (0,1)
        AND st.area_no=#{areaNo}
        limit 1
    </select>

    <select id="selectbyTerm" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id,
               st.task_no     taskNo,
               st.area_no     areaNo,
               st.type,
               st.expect_time expectTime,
               st.state,
               st.addtime,
               st.updatetime
        FROM stock_task st
                 LEFT JOIN stocktaking s on s.stock_taking_no = st.task_no
        where s.id = #{id}
    </select>
    <select id="selectByPurchaseNo" parameterType="string" resultType="integer">
        SELECT IFNULL(count(1), 0)
        FROM stock_task
        WHERE task_no = #{purchaseNo}
    </select>

    <select id="selectBatchStockTask" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id,st.task_no taskNo,st.area_no areaNo,st.type,st.expect_time
        expectTime,st.state,st.addtime,st.updatetime
        FROM stock_task st
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByTemporaryTranf" resultType="net.summerfarm.model.domain.StockTask">
        select st.area_no areaNo, st.id
        from stock_task st
        where st.type = 82
          and st.remark = '拆包'
          and date (st.addtime) = #{startDate}
          and st.state = 2
    </select>

    <select id="selectByStockTakingListId" resultType="net.summerfarm.model.domain.StockTask">
        SELECT st.id,
               st.task_no     taskNo,
               st.area_no     areaNo,
               st.type,
               st.expect_time expectTime,
               st.state,
               st.addtime,
               st.updatetime
        FROM stock_task st
                 LEFT JOIN stocktaking s on s.stock_taking_no = st.task_no
                 LEFT JOIN stock_taking_list stl on stl.taking_id = s.id
        where stl.id = #{id}
    </select>

    <select id="selectUnOutStore" resultType="java.lang.Integer">
        select ifnull(sum(sti.quantity - sti.actual_quantity), 0)
        from stock_task st
                 left join stock_task_item sti on st.id = sti.stock_task_id
        where st.area_no = #{warehouseNo}
          and st.type in (51, 57, 52, 58)
          and sti.sku = #{sku}
          and st.state in (0, 1)
          and sti.quantity > sti.actual_quantity
    </select>

    <select id="selectUnFinish" resultType="net.summerfarm.model.domain.StockTask">
        select id,
               task_no     taskNo,
               area_no     areaNo,
               type,
               expect_time expectTime,
               state,
               addtime,
               admin_id,
               remark,
               dimension
        from stock_task
        where type = 11
          and addtime <![CDATA[<=]]> #{endTime}
          and addtime <![CDATA[>=]]> #{startTime}
    </select>

    <select id="selectByTaskId" resultType="net.summerfarm.model.domain.StockTask">
        select id,
               task_no     taskNo,
               area_no     areaNo,
               type,
               expect_time expectTime,
               state,
               addtime,
               admin_id,
               remark,
               dimension
        from stock_task
        where type = 11
          and state in (0, 1)
          and addtime <![CDATA[<=]]> #{endTime}
          and addtime <![CDATA[>=]]> #{startTime}
    </select>

    <select id="selectStockTaskNotice" resultType="net.summerfarm.model.vo.StockTaskItemDetailVO">
        SELECT ast.sku,
               ast.area_no          warehouseNo,
               st.type,
               st.task_no           listNo,
               st.expect_time       expectTime,
               sai.arrival_quantity storeQuantity,
               sai.actual_quantity,
               sai.pd_name          pdName,
               sai.weight           weight
        FROM area_store ast
                 INNER JOIN stock_task st on st.area_no = ast.area_no
                 INNER JOIN stock_arrange sa ON st.id = sa.stock_task_id
                 INNER JOIN stock_arrange_item sai ON sa.id = sai.stock_arrange_id AND sai.sku = ast.sku
        WHERE ast.online_quantity <![CDATA[<=]]> 0
          AND st.type = 11
          AND sa.`state` = 0
          AND sai.actual_quantity <![CDATA[<]]> sai.arrival_quantity
          and st.`addtime` BETWEEN #{startTime} AND #{endTime}
        ORDER BY st.id DESC
    </select>
    <select id="selectAllocationNextDayArrive" resultType="net.summerfarm.model.vo.StockTaskItemDetailVO">
        SELECT sai.sku,
               ast.area_no      warehouseNo,
               sai.pd_name      pdName,
               sai.weight,
               sai.out_quantity storeQuantity,
               sal.out_time     expectTime,
               sal.addtime      addTime,
               sal.in_store     inStore,
               sal.out_store    outStore
        FROM area_store ast
                 INNER JOIN stock_allocation_list sal on ast.`area_no` = sal.in_store
                 INNER JOIN stock_allocation_item sai ON sai.sku = ast.sku AND sai.list_no = sal.list_no
        WHERE ast.online_quantity BETWEEN 0 and sai.out_quantity
          and sal.`status` = 5
          and sal.`addtime` BETWEEN #{startTime} AND #{endTime}
        GROUP BY sai.`list_no`, sai.sku
        ORDER BY sal.id DESC
    </select>

    <select id="selectAllotIn" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT st.id,st.area_no areaNo,st.type,st.state,st.addtime,st.expect_time
        expectTime,st.updatetime,st.remark,st.dimension,st.category,st.task_no taskNo
        FROM stock_task st
        <if test="sku != null or pdId != null">
            INNER JOIN stock_storage_item sti ON st.id = sti.stock_task_id
            <choose>
                <when test="sku != null">
                    AND sti.sku = #{sku}
                    INNER JOIN inventory i ON sti.sku=i.sku
                </when>
                <otherwise>
                    INNER JOIN inventory i ON sti.sku=i.sku
                    INNER JOIN products p ON i.pd_id=p.pd_id
                </otherwise>
            </choose>
        </if>
        <where>
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="state != null">
                AND st.state = #{state}
            </if>
            <if test="remark != null">
                AND st.remark = #{remark}
            </if>
            <if test="addtime != null">
                AND st.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="states != null and states.size!=0 ">
                AND st.state IN
                <foreach collection="states" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" pdId != null">
                AND i.pd_id = #{pdId}
            </if>
            <if test="startTime != null">
                AND st.expect_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND st.expect_time <![CDATA[<]]> #{endTime}
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="selectAllotOut" parameterType="net.summerfarm.model.input.StockTaskReq"
            resultType="net.summerfarm.model.vo.StockTaskVO">
        SELECT st.id,st.task_no taskNo,st.area_no areaNo,st.out_store_no outStoreNo,st.type,st.expect_time
        expectTime,st.state,st.addtime,if(st.state in (1,2),updatetime,null) updatetime,st.process_state
        processState,st.category,st.option_flag optionFlag
        FROM stock_task st
        <if test="sku != null or pdId != null">
            INNER JOIN stock_shipment_item sti ON st.id = sti.stock_task_id
            <choose>
                <when test="sku != null">
                    AND sti.sku = #{sku}
                    INNER JOIN inventory i ON sti.sku=i.sku
                </when>
                <otherwise>
                    INNER JOIN inventory i ON sti.sku=i.sku
                    INNER JOIN products p ON i.pd_id=p.pd_id
                </otherwise>
            </choose>
        </if>
        <where>
            <!--st.tenant_id = IFNULL(#{tenantId},1)-->
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size!=0 ">
                AND st.area_no IN
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="state != null">
                AND st.state = #{state}
            </if>
            <if test="remark != null">
                AND st.remark = #{remark}
            </if>
            <if test="addtime != null">
                AND st.addtime <![CDATA[>=]]> #{addtime}
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="states != null and states.size!=0 ">
                AND st.state IN
                <foreach collection="states" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test=" pdId != null">
                AND i.pd_id = #{pdId}
            </if>
            <if test="startAddTime != null">
                AND st.addtime <![CDATA[>=]]> #{startAddTime}
            </if>
            <if test="endAddTime != null">
                AND st.addtime <![CDATA[<=]]> #{endAddTime}
            </if>
            <if test="taskNo != null">
                AND st.task_no = #{taskNo}
            </if>
            <if test="storeNo != null">
                AND st.out_store_no = #{storeNo}
            </if>
            <if test="mailPushState != null">
                AND (st.option_flag &amp; -9223372036854775807) = #{mailPushState}
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="getReturnRejectDataList" parameterType="net.summerfarm.model.input.ReturnRejectListReq"
            resultType="net.summerfarm.model.vo.ReturnRejectListVo">
        SELECT DISTINCT t.*
        FROM (
        SELECT
        a.mname,
        ad.realname userName,
        ad.admin_id,
        t.id,
        t.task_no taskNo,
        t.area_no areaNo,
        t.out_store_no outStoreNo,
        t.type,
        t.expect_time expectTime,
        t.state,
        t.addtime as addDataTime,
        IF
        ( t.state IN ( 1, 2 ), t.updatetime, NULL ) as addtime,
        t.process_state processState,
        t.task_type taskType
        FROM
        stock_task t
        LEFT JOIN orders s ON t.task_no = s.order_no
        LEFT JOIN merchant a ON a.m_id = s.m_id
        LEFT JOIN admin ad ON ad.admin_id = t.admin_id
        where
        t.type IN
        <foreach collection="types" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="taskType != null">
            AND t.task_type = #{taskType}
        </if>
        <if test="storeNo != null">
            AND t.out_store_no = #{storeNo}
        </if>
        ) t
        <if test="sku != null or pdName != null">
            INNER JOIN stock_storage_item sti ON t.id = sti.stock_task_id
            <if test="sku != null">
                AND sti.sku LIKE concat('%',#{sku} ,'%')
            </if>
            <if test="pdName != null">
                INNER JOIN inventory i ON sti.sku = i.sku
                INNER JOIN products p ON i.pd_id = p.pd_id
                AND p.pd_name LIKE concat('%',#{pdName} ,'%')
            </if>
        </if>
        WHERE 1=1
        <if test="stateList != null and stateList.size!=0 ">
            AND t.state IN
            <foreach collection="stateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="stockTaskId != null">
            AND t.id = #{stockTaskId}
        </if>
        <if test="areaNo != null">
            AND t.areaNo = #{areaNo}
        </if>
        <if test="taskNo != null">
            AND t.taskNo = #{taskNo}
        </if>
        <if test="processState != null">
            AND t.processState = #{processState}
        </if>
        <if test="startTime != null">
            AND t.expectTime <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND t.expectTime <![CDATA[<=]]> #{endTime}
        </if>
        <if test="userName != null">
            <if test="userName == '系统'">
                AND t.username is null
            </if>
            <if test="userName != '系统'">
                AND t.username LIKE concat('%',#{userName} ,'%')
            </if>
        </if>
        <if test="mname != null">
            AND t.mname LIKE concat('%',#{mname} ,'%')
        </if>
        ORDER BY t.id DESC
    </select>

    <select id="getOrderAndSendAboutInfo" parameterType="net.summerfarm.model.vo.StockTaskResult"
            resultType="net.summerfarm.model.vo.OrderAndSendVo">
        SELECT a.mname                  AS                                      mname,
               s.after_sale_order_no    AS                                      afterSaleOrderNo,
               dpl.delivery_time        AS                                      sendTime,
               dsdp.delivery_time       AS                                      giveTime,
               dp.id                    as                                      deliveryPathId,
               dp.path_status           AS                                      giveState,
               CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
               CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driverName,
               t.mismatch_reason        as                                      mismatchReason,
               s.after_sale_remark_type as                                      afterSaleRemarkType
        FROM stock_task t
                 left JOIN after_sale_order s ON t.task_no = s.order_no
                 LEFT JOIN after_sale_proof asp ON s.after_sale_order_no = asp.after_sale_order_no
                 left JOIN delivery_plan dpl ON dpl.order_no = t.task_no
                 left JOIN merchant a ON a.m_id = s.m_id
                 LEFT JOIN after_sale_delivery_path dsdp ON dsdp.after_sale_no = s.after_sale_order_no
                 LEFT JOIN delivery_path dp ON dsdp.concat_id = dp.contact_id
            AND dsdp.delivery_time = dp.delivery_time
                 LEFT JOIN delivery_car_path dcp ON dcp.delivery_time = dp.delivery_time
            AND dcp.path = dp.path
            and dcp.store_no = dp.store_no
                 LEFT JOIN delivery_car dc ON dcp.delivery_car_id = dc.id
                 LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        where t.id = #{id}
          and asp.handle_type IN (4, 5, 6)
    </select>


    <select id="getRejectOrderAndSendAboutInfo" parameterType="net.summerfarm.model.vo.StockTaskResult"
            resultType="net.summerfarm.model.vo.OrderAndSendVo">
        SELECT a.mname                  AS                                      mname,
               s.after_sale_order_no    AS                                      afterSaleOrderNo,
               dpl.delivery_time        AS                                      sendTime,
               dpl.delivery_time        AS                                      giveTime,
               dp.path_status           AS                                      giveState,
               CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
               CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driverName,
               t.mismatch_reason        as                                      mismatchReason,
               s.after_sale_remark_type as                                      afterSaleRemarkType
        FROM stock_task t
                 left JOIN after_sale_order s ON t.task_no = s.order_no
                 LEFT JOIN after_sale_proof asp ON s.after_sale_order_no = asp.after_sale_order_no
                 left JOIN delivery_plan dpl ON dpl.order_no = t.task_no
                 left JOIN merchant a ON a.m_id = s.m_id
                 LEFT JOIN delivery_path dp ON dpl.contact_id = dp.contact_id
            AND dpl.delivery_time = dp.delivery_time
                 LEFT JOIN delivery_car_path dcp ON dcp.delivery_time = dp.delivery_time
            AND dcp.path = dp.path
            and dcp.store_no = dp.store_no
                 LEFT JOIN delivery_car dc ON dcp.delivery_car_id = dc.id
                 LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        where t.id = #{id}
          and asp.handle_type IN (9, 10)
    </select>

    <select id="lackGoodsList" parameterType="net.summerfarm.model.input.LackGoodsListReq"
            resultType="net.summerfarm.model.vo.LackGoodsApprovedListVo">
        SELECT
        t.id,
        t.task_no AS taskNo,
        t.area_no AS areaNo,
        t.out_store_no AS storeNo,
        s.sku,
        p.pd_name AS pdName,
        t.expect_time AS expectTime,
        IF( t.state IN ( 1, 2 ), t.updatetime, NULL ) AS addtime,
        t.process_state AS processState,
        t.state,
        a.realname userName
        FROM
        stock_task t
        LEFT JOIN stock_storage_item s ON t.id = s.stock_task_id
        LEFT JOIN inventory i ON i.sku = s.sku
        LEFT JOIN products p ON p.pd_id = i.pd_id
        LEFT JOIN admin a ON a.admin_id = t.admin_id
        WHERE 1=1
        <if test="type != null">
            AND t.type = #{type}
        </if>
        <if test="stockTaskId != null">
            AND t.id = #{stockTaskId}
        </if>
        <if test="taskNo != null">
            AND t.task_no = #{taskNo}
        </if>
        <if test="areaNo != null">
            AND t.area_no = #{areaNo}
        </if>
        <if test="storeNo != null">
            AND t.out_store_no = #{storeNo}
        </if>
        <if test="sku != null and sku != ''">
            AND s.sku = #{sku}
        </if>
        <if test="pdName != null and pdName != ''">
            AND p.pd_name like concat(#{pdName},'%')
        </if>
        <if test="processState != null">
            AND t.process_state = #{processState}
        </if>
        <if test="stateList != null and stateList.size > 0">
            AND t.state IN
            <foreach collection="stateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY t.id DESC
    </select>

    <select id="selectByOrderNoAndTime" resultType="integer">
        SELECT count(*)
        from stock_task t
        where t.task_no = #{orderNo}
    </select>

    <select id="allData" resultMap="withItem">
        select
        <include refid="baseColumn"/>
        from stock_task
        where
        type = #{type}
        <if test="beginId != null">
            and id > #{beginId}
        </if>
        <if test="ids != null">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by id
    </select>

    <select id="selectExpectTask" resultType="net.summerfarm.model.domain.StockTask">
        SELECT id, task_no taskNo, area_no areaNo, type
        from stock_task
        where expect_time
                  between #{expectStartTime} and #{expectEndTime}
            and area_no = #{areaNo}
            and state in (0, 1)
            and type in (10, 11)
           or (expect_time between #{expectStartTime} and #{expectEndTime}
            and updateTime between #{expectStartTime} and #{expectEndTime}
            and area_no = #{areaNo}
            and state = 2
            and type in (10, 11))
        union
        SELECT st.id, st.task_no taskNo, st.area_no areaNo, st.type
        from stock_task st
                 left join stock_allocation_list sal on sal.list_no = st.task_no
        where sal.expect_out_time
                  between #{expectStartTime} and #{expectEndTime}
            and st.area_no = #{areaNo}
            and st.state in (0, 1)
            and st.type = 50
           or (sal.expect_out_time between #{expectStartTime} and #{expectEndTime}
            and st.updateTime between #{expectStartTime} and #{expectEndTime}
            and st.area_no = #{areaNo}
            and st.state = 2
            and st.type = 50)
    </select>


    <select id="selectWarehouseAndExpectTime" resultType="net.summerfarm.model.domain.StockTask">
        SELECT area_no areaNo,expect_time expectTime,type
        from stock_task where expect_time between #{expectStartTime} and #{expectEndTime}
        <if test="areaNo != null">
            AND area_no IN
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and state in(0,1)
        and type in(10,11)
        or (expect_time between #{expectStartTime} and #{expectEndTime}
        and updateTime between #{expectStartTime} and #{expectEndTime}
        <if test="areaNo != null and areaNo.size > 0">
            AND area_no IN
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and state =2
        and type in(10,11)) union
        SELECT st.area_No areaNo, sal.expect_out_time expectTime, st.type from stock_task st
        left join stock_allocation_list sal on sal.list_no=st.task_no where sal.expect_out_time
        between #{expectStartTime} and #{expectEndTime}
        <if test="areaNo != null and areaNo.size > 0">
            AND area_no IN
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and st.state in(0,1)
        and st.type =50
        or
        (sal.expect_out_time between #{expectStartTime} and #{expectEndTime}
        and st.updateTime between #{expectStartTime} and #{expectEndTime}
        <if test="areaNo != null and areaNo.size > 0">
            AND area_no IN
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and st.state =2
        and st.type =50)
        GROUP BY areaNo,expectTime
    </select>

    <select id="selectByTime" resultMap="withItem">
        select
        <include refid="baseColumn"/>
        from stock_task where updatetime = #{updatedAt} and addtime = #{createdAt} and type = 82
    </select>


    <select id="selectUnOutStoreByStoreNo" resultType="java.lang.Integer">
        select ifnull(sum(sti.quantity - sti.actual_quantity), 0)
        from stock_task st
                 left join stock_task_item sti on st.id = sti.stock_task_id
        where st.out_store_no = #{storeNo}
          and st.type in (51, 57, 52, 58)
          and sti.sku = #{sku}
          and st.state in (0, 1)
          and sti.quantity > sti.actual_quantity
    </select>
    <select id="selectUnFinishRoadQuantity" resultType="java.lang.Integer">
        select ifnull(sum(quantity - actual_quantity), 0)
        from stock_task st
                 left join stock_storage_item ssi on st.id = ssi.stock_task_id
        where st.type = 10
          and st.area_no = #{inStore}
          and ssi.sku = #{sku}
          and st.state in (0, 1)
    </select>

    <select id="selectByStoreNoAndTime" resultMap="withItem">
        select
        <include refid="baseColumn"/>
        from stock_task where date(addtime) = #{updatedAt} and out_store_no = #{storeNo} and type = 51
    </select>

    <select id="selectListByCondition" parameterType="net.summerfarm.model.input.StockTaskQuery"
            resultMap="withItem">
        select
        <include refid="baseColumn"/>
        from stock_task
        <where>
            <if test="storeNo != null">
                AND out_store_no = #{storeNo,jdbcType=INTEGER}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size > 0">
                AND area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                AND type = #{type,jdbcType=INTEGER}
            </if>
            <if test="deliveryTime != null">
                AND date(expect_time) = #{deliveryTime,jdbcType=DATE}
            </if>
        </where>
    </select>

    <select id="queryWaveConfig" resultType="net.summerfarm.model.domain.wms.StockTaskWaveConfig">
        select
            store_no as storeNo,
            warehouse_no as warehouseNo,
            wave_time as waveTime
        from wms_stock_task_wave_config
        where wave_status = 1
    </select>
</mapper>
