<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantExtMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="free_day" jdbcType="VARCHAR" property="freeDay" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="click_flag" jdbcType="TIMESTAMP" property="clickFlag" />
    <result column="group_buy_area_no" jdbcType="INTEGER" property="groupBuyAreaNo"/>
    <result column="group_head_flag" jdbcType="INTEGER" property="groupHeadFlag"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, free_day, creator, create_time, updater, update_time,click_flag,
    group_head_flag,group_buy_area_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_ext
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByMid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_ext
        where m_id = #{mId}
    </select>
    <select id="selectFreeDayIsNull" resultType="net.summerfarm.model.vo.MerchantExtVo">
      select me.id,me.m_id mId,a.delivery_rule deliveryRule,a.area_no areaNo from merchant_ext me
      inner join merchant m on me.m_id = m.m_id
      inner join area a on m.area_no = a.area_no
      where me.free_day is null

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_ext
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantExt" useGeneratedKeys="true">
    insert into merchant_ext (m_id, free_day, creator, 
      create_time, updater, update_time
      )
    values (#{mId,jdbcType=BIGINT}, #{freeDay,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantExt" useGeneratedKeys="true">
    insert into merchant_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="freeDay != null">
        free_day,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="clickFlag!=null">
        click_flag,
      </if>
      <if test="groupHeadFlag != null">
        group_head_flag,
      </if>
      <if test="groupBuyAreaNo != null">
        group_buy_area_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="freeDay != null">
        #{freeDay,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickFlag!=null">
        #{clickFlag},
      </if>
      <if test="groupHeadFlag != null">
        #{groupHeadFlag},
      </if>
      <if test="groupBuyAreaNo != null">
        #{groupBuyAreaNo},
      </if>
    </trim>
  </insert>
    <insert id="insertBatch">
        insert into merchant_ext (m_id, free_day, creator,
        updater,click_flag
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.mId},#{item.freeDay},#{item.creator},#{item.updater},#{item.clickFlag})
        </foreach>

    </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MerchantExt">
    update merchant_ext
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
     free_day = #{freeDay,jdbcType=VARCHAR},
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickFlag !=null">
        click_flag = #{clickFlag},
      </if>
      <if test="groupHeadFlag != null">
        group_head_flag = #{groupHeadFlag},
      </if>
      <if test="groupBuyAreaNo != null">
        group_buy_area_no = #{groupBuyAreaNo},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MerchantExt">
    update merchant_ext
    set m_id = #{mId,jdbcType=BIGINT},
      free_day = #{freeDay,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      group_head_flag = #{groupHeadFlag},
      group_buy_area_no = #{groupBuyAreaNo}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>