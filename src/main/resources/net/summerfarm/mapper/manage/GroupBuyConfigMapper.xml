<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.GroupBuyConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.GroupBuyConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="least_number" jdbcType="INTEGER" property="leastNumber" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="group_buy_name" jdbcType="VARCHAR" property="groupBuyName"/>
    <result column="group_buy_pic" jdbcType="VARCHAR" property="groupBuyPic"/>
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, area_no, `status`, least_number, end_time, delivery_time, 
    start_time,group_buy_name,group_buy_pic,actual_end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from group_buy_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_buy_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GroupBuyConfig" useGeneratedKeys="true">
    insert into group_buy_config (create_time, update_time, area_no, 
      `status`, least_number, end_time, 
      delivery_time, start_time,group_buy_name,group_buy_pic,actual_end_time)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{areaNo,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{leastNumber,jdbcType=INTEGER}, #{endTime,jdbcType=TIMESTAMP}, 
      #{deliveryTime,jdbcType=DATE}, #{startTime,jdbcType=TIMESTAMP}.#{groupBuyName},#{groupBuyPic},#{actualEndTime})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GroupBuyConfig" useGeneratedKeys="true">
    insert into group_buy_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="leastNumber != null">
        least_number,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="groupBuyName != null ">
        group_buy_name,
      </if>
      <if test="groupBuyPic != null">
        group_buy_pic,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="leastNumber != null">
        #{leastNumber,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBuyName != null ">
        #{groupBuyName},
      </if>
      <if test="groupBuyPic != null">
        #{groupBuyPic},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.GroupBuyConfig">
    update group_buy_config
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="leastNumber != null">
        least_number = #{leastNumber,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBuyName != null">
        group_buy_name = #{groupBuyName,jdbcType=VARCHAR},
      </if>
      <if test="groupBuyPic != null">
        group_buy_pic = #{groupBuyPic,jdbcType=VARCHAR},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.GroupBuyConfig">
    update group_buy_config
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      area_no = #{areaNo,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      least_number = #{leastNumber,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=DATE},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      group_buy_name = #{groupBuyName},
      group_buy_pic = #{groupBuyPic,jdbcType=VARCHAR},
      actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAreaNoAndEffectiveStatus" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from group_buy_config
    where area_no = #{areaNo}
            and status = 1 and actual_end_time >= now()
  </select>

  <resultMap id="GroupBuyConfigListDTOResultMap" type="net.summerfarm.model.GroupBuyConfigListDTO">
    <id column="id" property="id"/>
    <result column="area_no" property="areaNo"/>
    <result column="group_buy_name" property="groupBuyName"/>
    <result column="actual_end_time" property="actualEndTime"/>
    <result column="end_time" property="endTime"/>
    <result column="start_time" property="startTime"/>
    <result column="status" property="status"/>
    <result column="least_number" property="leastNumber"/>
    <result column="delivery_time" property="deliveryTime"/>
    <result column="area_name" property="areaName"/>
  </resultMap>
  <select id="selectList" resultMap="GroupBuyConfigListDTOResultMap">
    select gbc.id, gbc.area_no, group_buy_name, actual_end_time, start_time, gbc.status, least_number, delivery_time,area_name,end_time
    from group_buy_config gbc
        left join area on gbc.area_no = area.area_no
    <where>
      <if test="groupBuyName != null and groupBuyName != ''">
        and group_buy_name like concat(#{groupBuyName},'%')
      </if>
      <if test="areaNos != null and areaNos.size() > 0">
        and gbc.area_no in <foreach collection="areaNos" open="(" close=")" item="areaNo" separator=",">
                            #{areaNo}
                           </foreach>
      </if>
      <if test="startTime != null">
        and start_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and end_time <![CDATA[ <= ]]> #{endTime}
      </if>
      <if test="groupBuyName != null and groupBuyName != ''">
        and group_buy_name like concat(#{groupBuyName},'%')
      </if>
      <if test="status != null">
        <choose>
          <when test="status == 0">
            and (gbc.status = #{status} or (gbc.status = 1 and actual_end_time <![CDATA[ < ]]> now()))
          </when>
          <when test="status == 1">
            and gbc.status = 1 and start_time <![CDATA[ < ]]> now() and actual_end_time > now()
          </when>
          <otherwise>
            and gbc.status = 1 and start_time > now() and actual_end_time <![CDATA[ > ]]> now()
          </otherwise>
        </choose>
      </if>
    </where>
    order by gbc.id desc
  </select>

  <resultMap id="GroupBuyConfigDetailDTOResultMap" type="net.summerfarm.model.DTO.market.GroupBuyConfigDetailDTO">
    <id column="id" property="id"/>
    <result column="group_buy_name" property="groupBuyName"/>
    <result column="area_no" property="areaNo"/>
    <result column="area_name" property="areaName"/>
    <result column="least_number" property="leastNumber"/>
    <result column="end_time" property="endTime"/>
    <result column="delivery_time" property="deliveryTime"/>
    <result column="start_time" property="startTime"/>
    <result column="group_buy_pic" property="groupBuyPic"/>
  </resultMap>
  <select id="selectById" resultMap="GroupBuyConfigDetailDTOResultMap">
    select gbc.id, gbc.area_no, group_buy_name, end_time, start_time, gbc.status, least_number, delivery_time,group_buy_pic,
        area_name
    from group_buy_config gbc
        left join area on gbc.area_no = area.area_no
    where gbc.id = #{id}
  </select>

  <select id="selectEffectiveAreaNo" resultType="java.lang.Integer">
    select area_no
    from group_buy_config
    where status = 1 and actual_end_time > now()
  </select>

  <update id="updateActualEndTime">
    update group_buy_config
    set actual_end_time = now()
    where id = #{id}
  </update>

  <select id="selectByActualEndTime" resultMap="BaseResultMap">
    select id
    from group_buy_config
    where actual_end_time <![CDATA[ <= ]]> #{finishTime} and status = 1
  </select>

  <update id="updateStatusById">
    update group_buy_config
    set status = 0
    where id = #{id}
  </update>
</mapper>