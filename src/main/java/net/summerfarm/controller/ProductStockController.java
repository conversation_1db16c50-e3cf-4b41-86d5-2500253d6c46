package net.summerfarm.controller;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.input.BatchUpdateSkuWarehouseInput;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.StockVO;
import net.summerfarm.model.vo.inventory.AreaStoreSkuVO;
import net.summerfarm.service.ProductStockService;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.SEPARATING_SYMBOL;

/**
 * @Package: net.summerfarm.controller
 * @Description:    库存
 * @author: <EMAIL>
 * @Date: 2016/8/30
 */
@Api(tags = "库存管理")
@RestController
    @RequestMapping(value = "/product-stock")
public class ProductStockController {

    @Resource
    private ProductStockService productStockService;

    private static final Lock lock = new ReentrantLock();

    /**
     * 查询库存数据
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @param orderBy
     * @return
     */
    @ApiOperation(value = "查询库存数据",httpMethod = "GET",tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "orderBy",value = "排序依据",paramType = "query")
    })
    @RequiresPermissions(value = {"product-stock:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/stock/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult<PageInfo<StockVO>> selectStock(@PathVariable int pageIndex, @PathVariable int pageSize, StockVO selectKeys, String orderBy){
        return productStockService.selectStock(pageIndex, pageSize, selectKeys, orderBy);
    }

    /**
     * 查询库存详细信息
     *
     * @return
     */
    @ApiOperation(value = "查询库存详细信息",httpMethod = "GET",tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query"),
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query")
    })
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    @RequiresPermissions(value = {"product-stock:select", Global.SA}, logical = Logical.OR)
    public AjaxResult<AreaStoreSkuVO> selectAll(String sku, Integer areaNo) {
        return productStockService.selectBySku(sku,areaNo);
    }

    /**
     * 用于:价格调整->查看->城市价格毛利率信息
     * 查询使用同一sku且使用同一个仓的城市信息
     * @param sku
     * @param areaNo
     * @return
     */
    @ApiOperation(value = "查询使用同一sku且使用同一个仓的城市信息",httpMethod = "GET",tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query"),
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query")
    })
    @RequestMapping(value = "/select2", method = RequestMethod.GET)
    @RequiresPermissions(value = {"product-stock:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectSkuStoreCityInfo(String sku, Integer areaNo) {
        return productStockService.selectSkuStoreCityInfo(sku, areaNo);
    }

    /**
     * 修改虚拟库存
     * @param sku
     * @return
     */
    @ApiOperation(value = "修改虚拟库存",httpMethod = "POST",tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query"),
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query"),
            @ApiImplicitParam(name = "quantity",value = "新的库存数量",paramType = "query")
    })
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @RequiresPermissions(value = {"inventory:update", Global.SA},logical = Logical.OR)
    public AjaxResult updateQuantity(String sku, Integer areaNo,int quantity){
        return productStockService.updateQuantity(sku,areaNo ,quantity);
    }

    /**
     * 修改二级城市库存
     * @return
     */
    @ApiOperation(value = "修改二级城市库存",httpMethod = "POST",tags = "库存管理")
    @RequestMapping(value = "/update/two", method = RequestMethod.POST)
    @RequiresPermissions(value = {"areasku:updateone", Global.SA}, logical = Logical.OR)
    public AjaxResult updateTwo(@RequestBody AreaStoreVO areaStoreVO) {
        return productStockService.updateTwo(areaStoreVO);
    }

    @ApiOperation(value = "新版 修改库存使用仓",httpMethod = "PUT",tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "warehouseNo",value = "仓库编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "storeNo",value = "配送中心编号",paramType = "query",required = true)
    })
    @PostMapping(value = "/trust-change/new")
    @RequiresPermissions(value = {"trustNo:change",Global.SA},logical = Logical.OR)
    public AjaxResult trustWarehouseNoChange(Integer warehouseNo,Integer storeNo,String sku){
        AjaxResult result;
        lock.lock();
        try {
            result = productStockService.trustWarehouseNoChange(warehouseNo,storeNo,sku);
        }finally {
            lock.unlock();
        }
        return result;
    }

    /**
     * 批量更新sku库存使用仓
     * @param batchUpdateSkuWarehouseInput 参数
     * @return 响应结果
     */
    @PostMapping(value = "/import/batch-update-sku-warehouse")
    @RequiresPermissions(value = {"trustNo:change",Global.SA},logical = Logical.OR)
    public CommonResult<Void> batchUpdateSkuWarehouse(@RequestBody @Validated BatchUpdateSkuWarehouseInput batchUpdateSkuWarehouseInput){
        productStockService.batchUpdateSkuWarehouse(batchUpdateSkuWarehouseInput.getExcelObjectOssKey());
        return CommonResult.ok();
    }

    /**
     * 批量更新sku库存使用仓 时间限制开关
     * @return 响应结果
     */
    @PostMapping(value = "/import/batch-update-sku-warehouse/switch")
    public CommonResult<Void> batchUpdateSkuWarehouseSwitch(){
        productStockService.batchUpdateSkuWarehouseSwitch();
        return CommonResult.ok();
    }

    /**
     * 查询库存详细信息
     *
     * @return
     */
    @ApiOperation(value = "修改预留库存上限,下限值", httpMethod = "POST", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query",required = true),
    })
    @RequestMapping(value = "/queryAreaSku", method = RequestMethod.GET)
    public AjaxResult queryAreaStore(String sku,Integer areaNo) {
        return productStockService.queryAreaStore(sku,areaNo);
    }

    /**
     * 修改上限值,下限值触发 省心送库存冻结
     *  0 上限 1 下限
     */
    @ApiOperation(value = "修改预留库存上限,下限值", httpMethod = "POST", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNo",value = "城市编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "reserveQuantity",value = "修改数量",paramType = "query",required = true),
            @ApiImplicitParam(name = "type",value = "0 上限 1 下限",paramType = "query",required = true)
    })
    @RequiresPermissions(value = {"area-store:purchaser", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update-reserve", method = RequestMethod.POST)
    public AjaxResult updateReserveQuantity(Integer storeNo, String sku, Integer reserveQuantity,Integer type) {
        if (storeNo == null || StringUtils.isBlank(sku) || reserveQuantity == null || type == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        return productStockService.updateReserveQuantity( storeNo,  sku,  reserveQuantity, type);

    }

    /**
    * 切换 是否支持预留库存
    */
    @ApiOperation(value = "切换 是否支持预留库存", httpMethod = "POST", tags = "库存管理")
    @RequiresPermissions(value = {"area-store:purchaser", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update-support", method = RequestMethod.POST)
    public AjaxResult trustReserveQuantity(@RequestBody AreaStore areaStore) {
        return productStockService.trustReserveQuantity(areaStore);

    }

    /**
     * 查询库存详细信息
     *
     * @return
     */
    @ApiOperation(value = "查询库存详细信息", httpMethod = "GET", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "warehouseNo",value = "仓库编号",paramType = "query",required = true)
    })
    @RequestMapping(value = "/select/queryAreaSku", method = RequestMethod.GET)
    public AjaxResult queryAreaStoreNew(String sku,Integer warehouseNo) {
        return productStockService.queryAreaStoreNew(sku,warehouseNo);
    }

    /**
    * 查询仓库信息
    */
    @ApiOperation(value = "修改预留库存上限,下限值", httpMethod = "POST", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "warehouseName",value = "仓库名称",paramType = "query")
    })
    @GetMapping("/select/warehouse/{pageIndex}/{pageSize}")
    public AjaxResult selectProductsWarehouse(@PathVariable int pageIndex, @PathVariable int pageSize,String warehouseName){
        return productStockService.selectProductsWarehouse(pageIndex,pageSize,warehouseName);
    }

    @ApiOperation(value = "分页配送仓编号", httpMethod = "GET", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @GetMapping("/select/store/sku/{pageIndex}/{pageSize}")
    public AjaxResult selectStoreSkuMsg(@PathVariable int pageIndex, @PathVariable int pageSize,
                                        WarehouseInventoryMappingVO mappingVO){
        return productStockService.selectStoreMsg(pageIndex,pageSize,mappingVO);
    }

    @ApiOperation(value = "配送仓编号", httpMethod = "GET", tags = "库存管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @GetMapping("/select/store/sku")
    public AjaxResult selectStoreSkuMsg(WarehouseInventoryMappingVO mappingVO){
        return productStockService.selectAllStoreMsg(mappingVO);
    }

    @ApiOperation(value = "批量修改城配仓的库存使用仓", httpMethod = "POST", tags = "库存管理")
    @PostMapping("/trust-change/batch")
    @RequiresPermissions(value = {"trustNo:change", Global.SA}, logical = Logical.OR)
    public AjaxResult batchTrustChange(String storeNos, Integer fromStoreNo,Integer type) {
        if(1 == 1){
            throw new BizException("此业务已下线,如需使用请联系产品开发");
        }
        if (storeNos == null || fromStoreNo == null){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        LocalDateTime now = LocalDateTime.now();
        if(!now.toLocalDate().isEqual(LocalDate.of(2021, 07, 21))
                && now.isAfter(LocalDateTime.of(LocalDate.now(), LocalTime.of(17, 45)))
                && now.isBefore(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 0)))){
            return AjaxResult.getErrorWithMsg("17:45-23:00该功能不支持使用");
        }

        List<String> storeNoList = Arrays.stream(storeNos.split(SEPARATING_SYMBOL)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeNoList) || storeNoList.size() > 1){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT, "城配仓批量切仓会导致系统异常，请操作完一个城配仓，再操作下一个");
        }

        return productStockService.batchTrustChange(storeNos, fromStoreNo,type);
    }

    @ApiOperation(value = "批量修改城配仓的库存使用仓", httpMethod = "POST", tags = "库存管理")
    @PostMapping("/trust-change/batch/temp")
    @RequiresPermissions(value = {"trustNo:change", Global.SA}, logical = Logical.OR)
    public AjaxResult batchTrustChangeV2(String storeNos, Integer fromStoreNo,Integer type) {
        if (storeNos == null || fromStoreNo == null){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
//        LocalDateTime now = LocalDateTime.now();
//        if(!now.toLocalDate().isEqual(LocalDate.of(2021, 07, 21))
//                && now.isAfter(LocalDateTime.of(LocalDate.now(), LocalTime.of(17, 45)))
//                && now.isBefore(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 0)))){
//            return AjaxResult.getErrorWithMsg("17:45-23:00该功能不支持使用");
//        }

        List<String> storeNoList = Arrays.stream(storeNos.split(SEPARATING_SYMBOL)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeNoList) || storeNoList.size() > 1){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT, "城配仓批量切仓会导致系统异常，请操作完一个城配仓，再操作下一个");
        }


        return productStockService.batchTrustChange(storeNos, fromStoreNo,type);
    }

    @ApiOperation(value = "查询切换任务状态", httpMethod = "GET", tags = "库存管理")
    @GetMapping("/cut-task/state")
    @RequiresPermissions(value = {"trustNo:select", Global.SA}, logical = Logical.OR)
    public AjaxResult cutTaskState(String cutTaskId) {
        return productStockService.cutTaskState(cutTaskId);
    }

    @ApiOperation(value = "终止切换任务", httpMethod = "GET", tags = "库存管理")
    @GetMapping("/cut-task/stop")
    @RequiresPermissions(value = {"trustNo:update", Global.SA}, logical = Logical.OR)
    public AjaxResult stopCutTask(Integer taskId) {
        return productStockService.stopCutTask(taskId);
    }

    @ApiOperation(value = "查询备份城配仓", httpMethod = "GET", tags = "库存管理")
    @GetMapping("/back-up/logistics")
    @RequiresPermissions(value = {"trustNo:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectLogisticsCenter(Integer type) {
        return productStockService.selectLogisticsCenter(type);
    }

    @ApiOperation(value = "批量修改单个sku库存使用仓", httpMethod = "GET", tags = "库存管理")
    @GetMapping("/batch/trust-change/new")
    @RequiresPermissions(value = {"trustNo:update", Global.SA}, logical = Logical.OR)
    public AjaxResult batchTrustWarehouseNoChange(Integer warehouseNo,String storeNos,String sku) {
        AjaxResult result;
        lock.lock();
        try {
            result = productStockService.batchTrustWarehouseNoChange(warehouseNo,storeNos,sku);
        }finally {
            lock.unlock();
        }
        return result;
    }
}
