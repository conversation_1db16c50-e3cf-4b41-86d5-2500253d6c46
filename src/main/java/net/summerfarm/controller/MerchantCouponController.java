package net.summerfarm.controller;

import cn.hutool.core.util.CharUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.OrderRedissonLockKey;
import net.summerfarm.model.DTO.CouponSendTemplateDTO;
import net.summerfarm.model.input.CouponSendTemplateIssueReq;
import net.summerfarm.model.input.CouponSendTemplateUploadReq;
import net.summerfarm.model.input.TaskInput;
import net.summerfarm.model.vo.MerchantCouponVO;
import net.summerfarm.service.MerchantCouponService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Package: net.summerfarm.controller
 * @Description: 用户优惠券发放
 * @author: <EMAIL>
 * @Date: 2016/10/11
 */
@Api(tags = "用户优惠券管理")
@RestController
@RequestMapping(value = "/merchant-coupon")
@Slf4j
public class MerchantCouponController {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(MerchantCouponController.class);

    @Resource
    private MerchantCouponService merchantCouponService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BaseService baseService;

    /**
     * 给用户发放优惠券
     * @param phone
     * @param couponId
     * @return
     */
    @ApiOperation(value = "发放优惠券",httpMethod = "POST",tags = "用户优惠券管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone",value = "商户手机号",paramType = "query"),
            @ApiImplicitParam(name = "couponId",value = "优惠券id",paramType = "query"),
            @ApiImplicitParam(name = "sendMsg",value = "是否发送短信",paramType = "query",dataType = "boolean"),
            @ApiImplicitParam(name = "reason",value = "发放理由",paramType = "query")
    })
    @RequiresPermissions(value = {"merchant-coupon:save", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult issueCoupen(String phone ,Integer couponId,boolean sendMsg,String reason,Long mId) {
        return merchantCouponService.issueCoupon(phone, couponId,sendMsg,true,reason,mId);
    }

    @ApiOperation(value = "用户优惠券模板下载",httpMethod = "GET",tags = "用户优惠券管理")
    @RequiresPermissions(value = {"merchant-coupon:save", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/template", method = RequestMethod.GET)
    public void majortemplate(HttpServletResponse response) throws IOException {
        Map<String, List<List<String>>> data = new HashMap<>();
        List<String> rowData = new ArrayList<>();
        rowData.add("手机号");
        List<List<String>> sheetData = new ArrayList<>();
        sheetData.add(rowData);
        data.put("发券", sheetData);
        ExcelUtils.outputExcel(data, "发券模板.xls", response);
    }

    /**
     * 给用户发放优惠券
     * @param couponId
     * @return
     */
    @ApiOperation(value = "发送优惠券（通过模板）",httpMethod = "POST",tags = "用户优惠券管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "couponId",value = "优惠券id",paramType = "query"),
            @ApiImplicitParam(name = "sendMsg",value = "是否发送短信",paramType = "query",dataType = "boolean"),
            @ApiImplicitParam(name = "reason",value = "发放理由",paramType = "query",dataType = "query")
    })
    @RequiresPermissions(value = {"merchant-coupon:save", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/excel-coupon",method = RequestMethod.POST)
    public AjaxResult excelCoupon(@RequestParam("file") MultipartFile file, Integer couponId, boolean sendMsg,String reason) {
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            ExcelUtils eh = new ExcelUtils(workbook);
            List<List<String>> data = eh.getListData();
            List<String> phoneList=new ArrayList<>();
            for (List<String> list : data) {
                if (list.size() < 1) {
                    continue;
                }
                String phone = list.get(0);
                if (StringUtils.isBlank(phone) || phone.contains("手机号")) {
                    continue;
                }
                phone = removeSpecialCharacter(phone);
                if (StringUtils.isNotBlank(phone)) {
                    phoneList.add(phone);
                }
            }
            if (data.size() == 0 || phoneList.size() == 0){
                return AjaxResult.getErrorWithMsg("模板数据手机号暂无，请重新导入！");
            }

            return merchantCouponService.excelCoupon(phoneList, couponId, sendMsg,reason);
        } catch (Exception e) {
            logger.error("发送优惠券（通过模板）异常，", e);
            return AjaxResult.getErrorWithMsg("模板数据导入异常，请重新导入！");
        }

    }

    private String removeSpecialCharacter(String phone){
        char[] chars = phone.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (char c : chars){
            if (CharUtil.isNumber(c)) {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 删除优惠券
     * @param id 商户优惠券主键id
     * @return
     */
    @ApiOperation(value = "删除优惠券",httpMethod = "GET",tags = "用户优惠券管理")
    @ApiImplicitParam(name = "couponId",value = "优惠券id",paramType = "query")
    @RequiresPermissions(value = {"merchant-coupon:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteCoupon",method = RequestMethod.GET)
    public AjaxResult deleteCoupon(Integer id) {
        return merchantCouponService.deleteCoupon(id);
    }

    /**
     * 用户优惠券展示
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @ApiOperation(value = "用户优惠券展示",httpMethod = "GET",tags = "用户优惠券管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"merchant-coupon:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantCouponVO selectKeys) {
        return merchantCouponService.select(pageIndex, pageSize, selectKeys);
    }

    @RequestMapping(value = "/taskCoupon/{id}",method = RequestMethod.GET)
    public AjaxResult taskCoupon(@PathVariable int id){
        return merchantCouponService.taskCoupon(id);
    }

    @RequestMapping(value = "/taskDetail/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult taskDetail(@PathVariable int pageIndex, @PathVariable int pageSize,TaskInput taskInput){
        return merchantCouponService.taskDetail(pageIndex,pageSize,taskInput);
    }

    /**
     * 上传卡劵发送模版
     * @param couponSendTemplateUploadReq
     * @return List<CouponSendTemplateDTO>
     */
    @RequestMapping(value = "/upset/upload/send-coupon", method = RequestMethod.POST)
    public CommonResult<List<CouponSendTemplateDTO>> uploadSendCoupon(@RequestBody @Validated CouponSendTemplateUploadReq couponSendTemplateUploadReq) {
        return CommonResult.ok(merchantCouponService.uploadSendCoupon(couponSendTemplateUploadReq));
    }

    /**
     * 人工批量发送卡劵
     * @param couponSendTemplateIssueReq
     * @return Boolean
     */
    @RequestMapping(value = "/upset/batch/send-coupon", method = RequestMethod.POST)
    public CommonResult<Boolean> batchSendCoupon(@RequestBody @Validated CouponSendTemplateIssueReq couponSendTemplateIssueReq) {
        RLock redissonLock = redissonClient.getLock(OrderRedissonLockKey.BATCH_SEND_COUPON
                + couponSendTemplateIssueReq.getCouponId() + baseService.getAdminId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            return CommonResult.ok(merchantCouponService.batchSendCoupon(couponSendTemplateIssueReq));
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException(1, "请重试");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }
}
