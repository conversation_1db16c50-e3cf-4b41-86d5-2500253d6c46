package net.summerfarm.controller;


import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.ArrivalNotice;
import net.summerfarm.model.vo.ArrivalNoticeVO;
import net.summerfarm.service.ArrivalNoticeService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/arrival-notice")
public class ArrivalNoticeController {

    @Resource
    private ArrivalNoticeService arrivalNoticeService;


    @ApiOperation(value = "分页查询",httpMethod = "GET",tags = "到货通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    @RequiresPermissions(value = {"arrival-notice:select", Global.SA}, logical = Logical.OR)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, ArrivalNoticeVO arrivalNoticeVO) {
        return arrivalNoticeService.select(pageIndex, pageSize, arrivalNoticeVO);
    }

    @RequestMapping(value = "/select-sku", method = RequestMethod.GET)
    @RequiresPermissions(value = {"arrival-notice:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectBySku(ArrivalNotice arrivalNotice) {
        return arrivalNoticeService.selectBySku(arrivalNotice);
    }


}
