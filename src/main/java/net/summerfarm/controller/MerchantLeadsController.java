package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.MerchantLeads;
import net.summerfarm.model.vo.MerchantLeadsVO;
import net.summerfarm.service.MerchantLeadsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-09
 */

@Api(tags = "用户例子库")
@RestController
@RequestMapping(value = "/merchant-leads")
public class MerchantLeadsController {

    @Resource
    private MerchantLeadsService merchantLeadsService;


    @ApiOperation(value = "用户例子库展示",httpMethod = "GET",tags = "用户例子库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"merchant-leads:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantLeads selectKeys) {
        return merchantLeadsService.select(pageIndex, pageSize, selectKeys);
    }

    @ApiOperation(value = "用户例子新增",httpMethod = "POST",tags = "用户例子库")
    @RequiresPermissions(value = {"merchant-leads:update", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated @RequestBody MerchantLeadsVO merchantLeads, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return merchantLeadsService.save(merchantLeads);
    }

}
