package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.model.input.IndustryAndWealthInput;
import net.summerfarm.service.IndustryAndWealthService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: IndustryAndWealthController
 * @date 2022/3/2916:31
 */
@Api(tags = "业财一体")
@RestController
@RequestMapping(value = "/industryAndWealth")
public class IndustryAndWealthController {

    @Resource
    private IndustryAndWealthService industryAndWealthService;

    @ApiOperation(value = " 收入模块信息", httpMethod = "GET", tags = "业财一体")
    @RequiresPermissions(value = {"industryAndWealth:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/income", method = RequestMethod.GET)
    public AjaxResult selectIncome(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.selectIncome(industryAndWealthInput);
    }

    @ApiOperation(value = " 收入确认额趋势", httpMethod = "GET", tags = "业财一体")
    @RequiresPermissions(value = {"industryAndWealth:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/incomeTrend", method = RequestMethod.GET)
    public AjaxResult selectIncomeTrend(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.selectIncomeTrend(industryAndWealthInput);
    }

    @ApiOperation(value = "资金情况", httpMethod = "GET", tags = "业财一体")
    @RequiresPermissions(value = {"industryAndWealth:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/capital", method = RequestMethod.GET)
    public AjaxResult selectCapital(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.selectCapital(industryAndWealthInput);
    }

    @ApiOperation(value = " 资金确认额趋势", httpMethod = "GET", tags = "业财一体")
    @RequiresPermissions(value = {"industryAndWealth:data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/capitalTrend", method = RequestMethod.GET)
    public AjaxResult selectCapitalTrend(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.selectCapitalTrend(industryAndWealthInput);
    }

    @ApiOperation(value = " 提交计算", httpMethod = "GET", tags = "业财一体")
    @RequiresPermissions(value = {"industryAndWealth:download ", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/submit", method = RequestMethod.GET)
    public AjaxResult submitDate(FileDownloadRecord fileDownloadRecord) {
        return industryAndWealthService.submitDate(fileDownloadRecord);
    }

    @GetMapping("/cost")
    public AjaxResult cost(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.cost(industryAndWealthInput);
    }

    @GetMapping("/costTrend")
    public AjaxResult costTrend(IndustryAndWealthInput industryAndWealthInput) {
        return industryAndWealthService.costTrend(industryAndWealthInput);
    }

}
