package net.summerfarm.controller.srm;


import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.param.srm.SrmSupplierOfferDetailParam;
import net.summerfarm.model.param.srm.SrmSupplierOfferDetailUpdateParam;
import net.summerfarm.service.srm.SrmSupplierOfferDetailService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@RestController
@RequestMapping("/srm-supplier-offer-detail")
public class SrmSupplierOfferDetailController {

    @Resource
    SrmSupplierOfferDetailService srmSupplierOfferDetailService;

    /**
     * 新增报价详情
     */
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public AjaxResult save(@RequestBody SrmSupplierOfferDetailParam srmSupplierOfferParam, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return srmSupplierOfferDetailService.save(srmSupplierOfferParam);
    }

    /**
     * 更新报价详情
     */
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    public AjaxResult update(@RequestBody SrmSupplierOfferDetailUpdateParam srmSupplierOfferDetailUpdateParam, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return srmSupplierOfferDetailService.update(srmSupplierOfferDetailUpdateParam);
    }

    /**
     * 关闭报价详情
     */
    @RequestMapping(value = "/close/{id}",method = RequestMethod.POST)
    public AjaxResult close(@PathVariable("id") Long id) {
        return srmSupplierOfferDetailService.close(id);
    }

    @RequestMapping(value = "/switchOfferStatus",method = RequestMethod.POST)
    public void switchOfferStatus(){
        srmSupplierOfferDetailService.switchOfferStatus();
    }
    @RequestMapping(value = "/expireRemind",method = RequestMethod.POST)
    public void expireRemind(){
        srmSupplierOfferDetailService.expireRemind();
    }

}

