package net.summerfarm.controller.srm;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.DTO.srm.WxEeventType;
import net.summerfarm.model.DTO.srm.WxMessageType;
import net.summerfarm.model.DTO.srm.WxServiceMsgDto;
import net.summerfarm.service.srm.impl.WechatService;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

@Slf4j
@RestController
public class WechatController {

    // xml转义符
    private static final String CDATA_CHARACTER = "<![CDATA[";
    private static final String LEFT_CHARACTER = "]]>";
    @Resource
    WechatService wechatService;

    @GetMapping(path = "/check", produces = "text/plain;charset=utf-8")
    public String authGet(@RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        log.info("\n接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature,
                timestamp, nonce, echostr);
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        if (wechatService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        return "非法请求";
    }

//    @PostMapping(value = "/check", produces = "text/xml;charset=UTF-8")
//    public String checkWxToken(@RequestParam(value = "signature", required = false) String signature,
//                               @RequestParam(value = "timestamp", required = false) String timestamp,
//                               @RequestParam(value = "echostr", required = false) String echostr,
//                               @RequestParam(value = "openid", required = false) String openid,
//                               @RequestBody(required = false) WxServiceMsgDto wxServiceMsgDto,
//                               HttpServletResponse response) {
//        log.info("接收微信请求：[openid=[{}], 消息请求体:{}", openid, JSON.toJSONString(wxServiceMsgDto));
//        // 只处理订阅与取消订阅消息
//        String returnMsg = null;
//        if (null != wxServiceMsgDto && StringUtils.isNotEmpty(wxServiceMsgDto.getMsgType()) &&
//                StringUtils.isNotEmpty(wxServiceMsgDto.getEvent())) {
//            String msgType = wxServiceMsgDto.getMsgType();
//            String event = wxServiceMsgDto.getEvent();
//            boolean isSubcribe = WxMessageType.EVENT.getCode().equals(msgType) &&
//                    (WxEeventType.SUBSCRIBE.getCode().equals(event)
//                            || WxEeventType.UNSUBSCRIBE.getCode().equals(event));
//            if (isSubcribe) {
//                WxEeventType wxEeventType = WxEeventType.SUBSCRIBE.getCode().equals(event) ? WxEeventType.SzUBSCRIBE : WxEeventType.UNSUBSCRIBE;
//                returnMsg = wechatService.onFollowCallback(wxServiceMsgDto, wxEeventType);
//            }
//        }
//        // 将文本消息对象转换成xml
//        return returnMsg;
//    }

    @PostMapping(value = "/check")
    public String checkV2(@RequestParam(value = "signature", required = false) String signature,
                          @RequestParam(value = "timestamp", required = false) String timestamp,
                          @RequestParam(value = "echostr", required = false) String echostr,
                          @RequestParam(value = "openid", required = false) String openid,
                          HttpServletRequest httpServletRequest) {
        String xml;
        try {
            xml = IOUtils.toString(httpServletRequest.getInputStream(), httpServletRequest.getCharacterEncoding());
            log.info("接收微信请求：[openid=[{}], 消息请求体:{}", openid, xml);
        } catch (IOException ioe) {
            log.error("Wechat callback /check occur io error,msg:{}", ioe.getMessage(), ioe);
            throw new RuntimeException(ioe);
        }
        WxServiceMsgDto wxServiceMsgDto = analysisXmlToMsgDTO(xml);
        if (wxServiceMsgDto == null || StrUtil.isEmpty(wxServiceMsgDto.getMsgType()) || StrUtil.isEmpty(wxServiceMsgDto.getEvent())) {
            return null;
        }
        WxEeventType eventType = WxEeventType.wrap(wxServiceMsgDto.getEvent());
        if (!Objects.equals(WxMessageType.EVENT.getCode(), wxServiceMsgDto.getMsgType()) || Objects.isNull(eventType)) {
            return null;
        }
        return wechatService.onFollowCallback(wxServiceMsgDto, eventType);
    }


    /**
     * 解析xml 返回dto
     *
     * @param xml io=>xml
     * @return dto
     */
    private WxServiceMsgDto analysisXmlToMsgDTO(String xml) {
        if (StrUtil.isEmpty(xml)) {
            return null;
        }
        String toUserName = xmlSubBetween(xml, "<ToUserName>", "</ToUserName>");
        String fromUserName = xmlSubBetween(xml, "<FromUserName>", "</FromUserName>");
        String createTime = xmlSubBetween(xml, "<CreateTime>", "</CreateTime>");
        String msgType = xmlSubBetween(xml, "<MsgType>", "</MsgType>");
        String event = xmlSubBetween(xml, "<Event>", "</Event>");
        String content = xmlSubBetween(xml, "<Content>", "</Content>");

        return WxServiceMsgDto.builder().toUserName(toUserName)
                .fromUserName(fromUserName)
                .createTime(StrUtil.isEmpty(createTime) ? 0L : Long.parseLong(createTime))
                .content(content)
                .msgType(msgType)
                .event(event)
                .build();
    }


    /**
     * 截取xml标签，注意：标签内容可能包含CDATA转义府，需要替换
     *
     * @param xml    xml
     * @param before 前符号
     * @param after  后符号
     * @return xml标签内容
     */
    private String xmlSubBetween(String xml, String before, String after) {
        String xmlContent = StrUtil.subBetween(xml, before, after);
        if (xmlContent == null || !xmlContent.contains(LEFT_CHARACTER)) {
            return xmlContent;
        }
        return StrUtil.subBetween(xmlContent, CDATA_CHARACTER, LEFT_CHARACTER);
    }
}
