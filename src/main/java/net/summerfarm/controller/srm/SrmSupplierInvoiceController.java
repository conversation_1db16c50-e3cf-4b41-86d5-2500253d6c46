package net.summerfarm.controller.srm;

import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.PurchaseInvoice;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.service.srm.SrmSupplierInvoiceService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: SrmSupplierInvoiceController
 * SRM发票管理
 * @date 2022/9/2114:00
 */
@RestController
@RequestMapping("/srm-supplier-invoice")
public class SrmSupplierInvoiceController {

    @Resource
    private SrmSupplierInvoiceService srmSupplierInvoiceService;

    @ApiOperation(value = "SRM供应商提交发票（提交的发票为待匹配状态）", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/upsert-supplier-invoice", method = RequestMethod.POST)
    public AjaxResult upsertSupplierInvoice(@RequestBody PurchaseInvoiceVO purchaseInvoice) {
        return srmSupplierInvoiceService.upsertSupplierInvoice(purchaseInvoice);
    }

    @ApiOperation(value = "SRM供应商发票详情（发票池发票详情）", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/query-invoice-detail", method = RequestMethod.POST)
    public AjaxResult queryInvoiceDetail(@RequestBody PurchaseInvoiceVO purchaseInvoice) {
        return srmSupplierInvoiceService.queryInvoiceDetail(purchaseInvoice.getId());
    }

    @ApiOperation(value = "SRM供应商删除发票（发票池））", httpMethod = "POST", tags = "srm")
    @RequestMapping(value = "/delete-invoice", method = RequestMethod.POST)
    public AjaxResult deleteInvoice(@RequestBody PurchaseInvoiceVO purchaseInvoice) {
        return srmSupplierInvoiceService.deleteInvoice(purchaseInvoice.getId());
    }

}
