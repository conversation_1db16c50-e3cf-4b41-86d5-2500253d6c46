package net.summerfarm.controller;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import javax.annotation.Resource;
import net.summerfarm.common.base.BaseController;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.market.resource.PageQueryInput;
import net.summerfarm.model.vo.BannerVO;
import net.summerfarm.model.vo.LinkDetailVO;
import net.summerfarm.service.BannerService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Package: net.summerfarm.controller
 * @Description: banner配置
 * @author: <EMAIL>
 * @Date: 2016/10/27
 */
@Api(tags = "banner配置")
@RestController
@RequestMapping("/banner")
public class BannerController extends BaseController {

    @Resource
    private BannerService bannerService;

    /**
     * 新增 修改 banner
     * @param bannerVO
     * @return
     */
    @RequiresPermissions(value = {"banner:save", Global.SA}, logical = Logical.OR)
    @PostMapping
    public CommonResult<Boolean> save(@Validated @RequestBody BannerVO bannerVO) {
        Integer adminId = getAdminId();
        bannerVO.setCreatorId(adminId);
        return bannerService.save(bannerVO);
    }

    /**
     * 列表页分页查询
     * @param queryInput
     * @return
     */
    @RequiresPermissions(value = {"banner:select", Global.SA},logical = Logical.OR)
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<BannerVO>> page(@RequestBody PageQueryInput queryInput) {
        return bannerService.page(queryInput);
    }

    /**
     * 校验跳转信息是否存在
     * @return
     */
    @PostMapping(value = "/query/check")
    public CommonResult<LinkDetailVO> getLinkDetail(@RequestParam String bizId, @RequestParam Integer type) {
        return bannerService.getLinkDetail(bizId, type);
    }


}
