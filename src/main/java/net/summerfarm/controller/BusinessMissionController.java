package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.BusinessMission;
import net.summerfarm.service.BusinessMissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/3/23
 */
@Api(tags = "BD团队GMV指标管理")
@RestController
@RequestMapping(value = "/business-mission")
public class BusinessMissionController {

    @Resource
    private BusinessMissionService businessMissionService;

    @ApiOperation(value = "GMV指标查询",httpMethod = "GET",tags = "BD团队GMV指标管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"business-mission:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, BusinessMission selectKeys) {
        return businessMissionService.select(pageIndex, pageSize, selectKeys);
    }

    @ApiOperation(value = "GMV指标修改",httpMethod = "PUT",tags = "BD团队GMV指标管理")
    @ApiImplicitParam(name = "id",value = "id",paramType = "path",required = true)
    @RequiresPermissions(value = {"business-mission:value", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public AjaxResult update(@PathVariable int id, @Validated(Update.class) @RequestBody BusinessMission businessMission, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        businessMission.setId(id);
        return businessMissionService.update(businessMission);
    }

    @ApiOperation(value = "新增GMV指标",httpMethod = "POST",tags = "BD团队GMV指标管理")
    @RequiresPermissions(value = {"business-mission:save", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated(Add.class) BusinessMission businessMission, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return businessMissionService.save(businessMission);

    }
}
