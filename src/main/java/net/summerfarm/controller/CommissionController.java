package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.CrmCommissionCategory;
import net.summerfarm.model.domain.CrmCommissionCoreMerchant;
import net.summerfarm.model.domain.CrmCommissionMerchantLevel;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.CrmBdConfigVo;
import net.summerfarm.model.vo.CrmCommissionMerchantVo;
import net.summerfarm.model.vo.CrmCommissionSkuVo;
import net.summerfarm.model.vo.GmvTargetVO;
import net.summerfarm.service.CommissionService;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.ProductsService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "佣金机制管理")
@RestController
@RequestMapping(value = "/commission")
public class CommissionController {

    @Resource
    CommissionService commissionService;
    @Resource
    private ProductsService productsService;
    @Resource
    private InventoryService inventoryService;

    @ApiOperation(value = "新城市新销售定义查询",httpMethod = "GET",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-commission:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectAreaBd", method = RequestMethod.GET)
    public AjaxResult selectAreaBd(){
        return commissionService.selectAreaBd();
    }


    @ApiOperation(value = "保存配置值",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-newcitybd:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    public AjaxResult saveAreaBd(@RequestBody Config config){
        return commissionService.saveAreaBd(config);
    }

    @ApiOperation(value = "按件奖励查询",httpMethod = "GET",tags = "佣金机制管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "zoneName",value = "区域名称",paramType = "query"),
            @ApiImplicitParam(name = "sku",value = "sku",paramType = "query"),
            @ApiImplicitParam(name = "pdName",value = "商品名称",paramType = "query")
    })
    @RequiresPermissions(value = {"crm-commission:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectAreasku/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectsku(@PathVariable int pageIndex,@PathVariable int pageSize,String zoneName,String sku,String pdName){
        return commissionService.selectsku(pageIndex,pageSize,zoneName,sku,pdName);
    }

    @ApiOperation(value = "按件奖励删除",httpMethod = "DELETE",tags = "佣金机制管理")
    @ApiImplicitParam(name = "id",value = "佣金sku管理id",paramType = "query",required = true)
    @RequiresPermissions(value = {"crm-areasku:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteAreasku", method = RequestMethod.DELETE)
    public AjaxResult deleteAreasku(int id){
        return commissionService.deleteAreasku(id);
    }

    @ApiOperation(value = "按件奖励新增",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-areasku:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveAreaSku", method = RequestMethod.POST)
    public AjaxResult saveAreaSku(@RequestBody CrmCommissionSkuVo crmCommissionSkuVo){
        return commissionService.saveAreaSku(crmCommissionSkuVo);
    }

    @ApiOperation(value = "按件奖励批量复制",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-areasku:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyAreaSku", method = RequestMethod.POST)
    public AjaxResult copyAreaSku(@RequestBody CopyInfoInput copyInfoInput){
        return commissionService.copyAreaSku(copyInfoInput);
    }

    @ApiOperation(value = "拉新奖励查询",httpMethod = "GET",tags = "佣金机制管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "zoneName",value = "区域名称",paramType = "query")
    })
    @RequiresPermissions(value = {"crm-commission:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectMerchant/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectMerchant(@PathVariable int pageIndex,@PathVariable int pageSize,String zoneName) {
        return commissionService.selectMerchant(pageIndex,pageSize,zoneName);
    }


    @ApiOperation(value = "拉新奖励删除",httpMethod = "DELETE",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-newmerchant:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteMerchant", method = RequestMethod.DELETE)
    public AjaxResult deleteMerchant(int id) {
        return commissionService.deleteMerchant(id);
    }


    @ApiOperation(value = "拉新奖励编辑",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-newmerchant:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveMerchant", method = RequestMethod.POST)
    public AjaxResult saveMerchant(@RequestBody CrmCommissionMerchantVo crmCommissionMerchantVo) {
        return commissionService.saveMerchant(crmCommissionMerchantVo);
    }


    @ApiOperation(value = "拉新奖励复制",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-newmerchant:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyMerchant", method = RequestMethod.POST)
    public AjaxResult copyMerchant(@RequestBody CopyInfoInput copyInfoInput) {
        return commissionService.copyMerchant(copyInfoInput);
    }


    @ApiOperation(value = "拉新奖励批量修改",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-newmerchant:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/batchModifyMerchant", method = RequestMethod.POST)
    public AjaxResult batchModifyMerchant(@RequestBody BatchModifyMerchantInput batchModifyMerchantInput) {
        return commissionService.batchModifyMerchant(batchModifyMerchantInput);
    }

    @ApiOperation(value = "激励指标查询",httpMethod = "GET",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-commission:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectIncentiveIndex/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectIncentiveIndex(@PathVariable int pageIndex, @PathVariable int pageSize,@RequestParam(value = "area",required = false) List<Integer> area,@RequestParam(value = "adminName",required = false) String adminName) {
        return commissionService.selectIncentiveIndex(pageIndex,pageSize,area,adminName);
    }


    @ApiOperation(value = "激励指标删除",httpMethod = "DELETE",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-incentiveindex:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteIncentiveIndex", method = RequestMethod.DELETE)
    public AjaxResult deleteIncentiveIndex(int id) {
        return commissionService.deleteIncentiveIndex(id);
    }


    @ApiOperation(value = "激励指标编辑",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-incentiveindex:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult saveIncentiveIndex(@RequestBody CrmBdConfigVo crmBdConfigVo) {
        return commissionService.saveIncentiveIndex(crmBdConfigVo);
    }

    @ApiOperation(value = "激励指标复制",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-incentiveindex:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult copyIncentiveIndex(@RequestBody CopyIntInfoInput copyIntInfoInput) {
        return commissionService.copyIncentiveIndex(copyIntInfoInput);
    }

    @ApiOperation(value = "激励指标批量修改",httpMethod = "POST",tags = "佣金机制管理")
    @RequiresPermissions(value = {"crm-incentiveindex:edit", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/batchModifyIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult batchModifyIncentiveIndex(@RequestBody BatchModifyIncentiveIndexInput batchModifyIncentiveIndexInput) {
        return commissionService.batchModifyIncentiveIndex(batchModifyIncentiveIndexInput);
    }

    @ApiOperation(value = "模糊查询表格中存在和不存在区域",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/queryZoneNameCondition", method = RequestMethod.GET)
    public AjaxResult queryZoneNameCondition(Boolean isExist,String flag,String zoneName) {
        return commissionService.queryZoneNameCondition(isExist,flag,zoneName);
    }


    @ApiOperation(value = "模糊查询商品名称+联动sku",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/queryProductName", method = RequestMethod.GET)
    public AjaxResult queryProductName(String pdname,Integer id) {
        return productsService.queryProductName(pdname,id);
    }

    @ApiOperation(value = "模糊查询sku+联动商品",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/querySku", method = RequestMethod.GET)
    public AjaxResult querySku(String skuName,Integer id) {
        return inventoryService.querySku(skuName,id);
    }

    @ApiOperation(value = "模糊查询表格中存在和不存在名称",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/queryBdName", method = RequestMethod.GET)
    public AjaxResult queryBdName(Boolean isExist,String bdName) {
        return commissionService.queryBdName(isExist,bdName);
    }

    @ApiOperation(value = "查询区域选择",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/selectZoneNameList", method = RequestMethod.GET)
    public AjaxResult selectZoneNameList() {
        return commissionService.selectZoneNameList();
    }

    @ApiOperation(value = "查询上月核心客户数",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/selectNumLastMonth", method = RequestMethod.GET)
    public AjaxResult selectNumLastMonth(Integer adminId){
        return commissionService.selectNumLastMonth(adminId);
    }

    @ApiOperation(value = "查询数据",httpMethod = "GET",tags = "佣金机制管理")
    @RequestMapping(value = "/selectWarehouse", method = RequestMethod.GET)
    public AjaxResult selectWarehouse() {
        return commissionService.selectWarehouse();
    }

    /**
     * 获取销售按gmv奖励数据
     * @return 销售按gmv奖励数据
     */
    @GetMapping(value = "/selectGmvTarget")
    public AjaxResult selectGmvTarget(){
        return commissionService.selectGmvTarget();
    }
    /**
     * 编辑销售按gmv奖励系数
     * @param config 变更字段
     * @return 0|1
     */
    @PostMapping(value = "/saveGmvTarget")
    public AjaxResult saveGmvTarget(@RequestBody Config config){
        return commissionService.saveGmvTarget(config);
    }
    /**
     * 查询按品类提成奖励
     * @return 查询按品类提成奖励
     */
    @GetMapping(value = "/selectCategoryAward")
    public AjaxResult selectCategoryAward(){
        return commissionService.selectCategoryAward();
    }
    /**
     * 更新品类提成奖励
     * @param crmCommissionCategory 品类提成奖励
     * @return 0|1
     */
    @PostMapping(value = "/saveCategoryAward")
    public AjaxResult saveCategoryAward(@RequestBody CrmCommissionCategory crmCommissionCategory){
        return commissionService.saveCategoryAward(crmCommissionCategory);
    }
    /**
     * 查询客户等级
     * @return 客户等级系数列表
     */
    @GetMapping(value = "/selectCoreMerchant/{merchantLevelType}")
    public AjaxResult selectCoreMerchant(@PathVariable int merchantLevelType,String grade){
        return commissionService.selectCoreMerchant(merchantLevelType,grade);
    }
    /**
     * 更新客户等级系数
     * @param crmCommissionMerchantLevel 客户等级系数
     * @return 0|1
     */
    @PostMapping(value = "/saveCoreMerchant")
    public AjaxResult saveCoreMerchant(@RequestBody CrmCommissionMerchantLevel crmCommissionMerchantLevel){
        return commissionService.saveCoreMerchant(crmCommissionMerchantLevel);
    }
    /**
     * 核心客户净增长系数查询
     * @return 核心客户净增长系数
     */
    @GetMapping(value = "/selectCoreMerchantsNetGrowth")
    public AjaxResult selectCoreMerchantsNetGrowth(){
        return commissionService.selectCoreMerchantsNetGrowth();
    }
    /**
     * 核心客户净增长系数编辑
     * @param crmCommissionCoreMerchant 核心客户净增长系数信息
     * @return 核心客户净增长系数
     */
    @PostMapping(value = "/saveCoreMerchantsNetGrowth")
    public AjaxResult saveCoreMerchantsNetGrowth(@RequestBody CrmCommissionCoreMerchant crmCommissionCoreMerchant){
        return commissionService.saveCoreMerchantsNetGrowth(crmCommissionCoreMerchant);
    }

    @GetMapping(value = "/selectMonthLivingCouponQuota")
    public AjaxResult selectMonthLivingCouponQuota(){
        return commissionService.selectMonthLivingCouponQuota();
    }

    @PostMapping(value = "/updateMonthLivingCouponQuota/{monthLivingCouponQuota}")
    public AjaxResult updateMonthLivingCouponQuota(@PathVariable String monthLivingCouponQuota){
        return commissionService.updateMonthLivingCouponQuota(monthLivingCouponQuota);
    }
}
