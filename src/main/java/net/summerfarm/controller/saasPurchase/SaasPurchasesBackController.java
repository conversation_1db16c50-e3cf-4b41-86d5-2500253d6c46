package net.summerfarm.controller.saasPurchase;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.annotation.CheckSaasToken;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.redis.PurchasesBackLock;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.enums.PurchasesBackTypeEnum;
import net.summerfarm.model.domain.PurchasesBack;
import net.summerfarm.model.domain.PurchasesBackDetail;
import net.summerfarm.model.vo.PurchasesBackVO;
import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.service.PurchasesBackService;
import net.summerfarm.service.StockTaskService;
import net.summerfarm.service.StoreRecordService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chenjie
 * @date : 2022-12-14 15:28
 * @describe :
 */
@Api(tags = "SAAS采购退货管理")
@CheckSaasToken
@RestController
@RequestMapping(value = "/summerfarm-manage/saas-purchases-back")
public class SaasPurchasesBackController {
    @Resource
    PurchasesBackService purchasesBackService;
    @Resource
    private PurchasesBackLock purchasesBackLock;
    @Resource
    private StoreRecordService storeRecordService;
    @Resource
    private StockTaskService stockTaskService;

    @ApiOperation(value = "新增采购退货", httpMethod = "POST", tags = "采购退货管理")
    @RequestMapping(value = "upsert/save", method = RequestMethod.POST)
    public AjaxResult save(@Validated(value = {Add.class}) @RequestBody PurchasesBackVO purchasesBackVO, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return purchasesBackService.save(purchasesBackVO);
    }


    @ApiOperation(value = "修改采购退货单", httpMethod = "POST", tags = "采购退货管理")
    @RequestMapping(value = "upsert/update", method = RequestMethod.PUT)
    public AjaxResult updateDetail(@RequestBody @Validated(value = {Update.class}) PurchasesBackDetail detail, BindingResult result) {
        if(result.hasFieldErrors()) {
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        Long lockId = purchasesBackLock.nextId();
        if (!purchasesBackLock.tryLock(detail.getPurchasesBackNo(), lockId, 1L, TimeUnit.SECONDS)) {
            return AjaxResult.getError("REPEAT_COMMIT");
        }
        try {
            return purchasesBackService.update(detail);
        } finally {
            purchasesBackLock.unLock(detail.getPurchasesBackNo(), lockId);
        }
    }


    @ApiOperation(value = "删除采购退货", httpMethod = "DELETE", tags = "采购退货管理")
    @ApiImplicitParam(name = "id", value = "采购退货详情id", paramType = "path",required = true)
    @RequestMapping(value = "upsert/delete/{id}", method = RequestMethod.DELETE)
    public AjaxResult deleteDetail(@PathVariable Integer id) {
        Long lockId = purchasesBackLock.nextId();
        if (!purchasesBackLock.tryLock("delete" + id, lockId, 1L, TimeUnit.SECONDS)) {
            return AjaxResult.getError("REPEAT_COMMIT");
        }
        try {
            return purchasesBackService.delete(id);
        } finally {
            purchasesBackLock.unLock("delete" + id, lockId);
        }
    }


    @ApiOperation(value = "新增采购退货项", httpMethod = "POST", tags = "采购退货管理")
    @RequestMapping(value = "upsert/save-detail", method = RequestMethod.POST)
    public AjaxResult saveDetail(@RequestBody @Validated(value = {Add.class}) PurchasesBackDetail detail, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        Long lockId = purchasesBackLock.nextId();
        if (!purchasesBackLock.tryLock(detail.getPurchasesBackNo(), lockId, 1L, TimeUnit.SECONDS)) {
            return AjaxResult.getError("REPEAT_COMMIT");
        }
        try {
            return purchasesBackService.saveDetail(detail);
        } finally {
            purchasesBackLock.unLock(detail.getPurchasesBackNo(), lockId);
        }
    }


    @ApiOperation(value = "查询采购退货单", httpMethod = "GET", tags = "采购退货管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path",required = true),
    })
    @RequestMapping(value = "query/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<PurchasesBackVO>> select(@RequestBody PurchasesBackVO purchasesBackVO) {
        return CommonResult.ok(purchasesBackService.select(purchasesBackVO.getPageIndex(), purchasesBackVO.getPageSize(), purchasesBackVO));
    }

    @ApiOperation(value = "查询采购退货单", httpMethod = "GET", tags = "采购退货管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path",required = true),
    })
    @RequestMapping(value = "query/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public CommonResult<PageInfo<PurchasesBackVO>> selectList(@PathVariable int pageIndex, @PathVariable int pageSize, PurchasesBackVO purchasesBackVO) {
        return CommonResult.ok(purchasesBackService.select(pageIndex, pageSize, purchasesBackVO));
    }


    @ApiOperation(value = "查询采购退货单详情", httpMethod = "GET", tags = "采购退货管理")
    @ApiImplicitParam(name = "purchasesBackNo", value = "退货单号", paramType = "path",required = true)
    @RequestMapping(value = "query/detail/{purchasesBackNo}", method = RequestMethod.GET)
    public CommonResult<PurchasesBackVO> selectDetail(@PathVariable String purchasesBackNo) {
        return CommonResult.ok(purchasesBackService.selectDetail(purchasesBackNo));
    }

    @ApiOperation(value = "审核采购退货单", httpMethod = "PUT", tags = "采购退货管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "purchasesBackNo", value = "退货单号", paramType = "path",required = true),
    })
    @RequestMapping(value = "upsert/handle/{purchasesBackNo}", method = RequestMethod.PUT)
    public AjaxResult handle(@PathVariable String purchasesBackNo,@RequestBody PurchasesBack purchasesBack) {
        long lockId = purchasesBackLock.nextId();
        if (!purchasesBackLock.tryLock(purchasesBackNo, lockId, 1L, TimeUnit.SECONDS)) {
            return AjaxResult.getError("REPEAT_COMMIT");
        }
        try {
            purchasesBack.setAuditor(null);
            return purchasesBackService.handle(purchasesBackNo, purchasesBack);
        } finally {
            purchasesBackLock.unLock(purchasesBackNo, lockId);
        }
    }

    @ApiOperation(value = "修改采购退货单备注", httpMethod = "POST", tags = "采购退货管理")
    @RequestMapping(value = "upsert/update-remark", method = RequestMethod.PUT)
    public AjaxResult updateRemark(@RequestBody PurchasesBack purchasesBack) {
        return purchasesBackService.updateRemark(purchasesBack);
    }

    @ApiOperation(value = "查询退货单对应的退款单", httpMethod = "GET", tags = "采购退货管理")
    @RequestMapping(value = "query/refund", method = RequestMethod.GET)
    public AjaxResult refund(String purchasesBackNo) {
        return purchasesBackService.selectRefund(purchasesBackNo);
    }

    @ApiOperation(value = "查询退货校验金额", httpMethod = "GET", tags = "采购退货管理")
    @RequestMapping(value = "query/amount", method = RequestMethod.GET)
    public AjaxResult queryReturnLimit(String purchasesNo) {
        return purchasesBackService.selectLeftAmount(purchasesNo);
    }

    @ApiOperation(value = "批次列表查询",httpMethod = "GET",tags = "库存管理（仓库）")
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public AjaxResult selectBatchList(StoreRecordVO storeRecord){
        if(Objects.equals(storeRecord.getPurchaseBackType(), PurchasesBackTypeEnum.UN_IN_BACK.ordinal())){
            return stockTaskService.selectUnStoreList(storeRecord);
        }
        return storeRecordService.selectBatchList(storeRecord);
    }

    /**
     * 导出当前筛选结果退货单
     *
     * @param purchasesBackVO 查询入参
     * @return 是否成功
     */
    @PostMapping("/export")
    public CommonResult<Boolean> export(@RequestBody PurchasesBackVO purchasesBackVO) {
        purchasesBackService.export4Saas(purchasesBackVO);
        return CommonResult.ok(true);
    }
}
