package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.ConversionSkuConfig;
import net.summerfarm.model.vo.ConversionSkuConfigVO;
import net.summerfarm.service.ConversionSkuConfigService;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> ct
 * create at:  2021/10/21  10:01
 */
@RestController
@RequestMapping("/conversion")
public class ConversionSkuConfigController {

    @Resource
    ConversionSkuConfigService conversionSkuConfigService;


    /**
     * 列表查询
     * @Author: ct
     * @param pageSize  size
     * @param pageIndex   index
     * @param vo 查询
     * @return
     **/
    @GetMapping("/select/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"conversion:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectConfigVO(@PathVariable("pageIndex") Integer pageIndex, @PathVariable("pageSize") Integer pageSize, ConversionSkuConfigVO vo){
        AjaxResult result = conversionSkuConfigService.selectConfigVO(pageIndex, pageSize, vo);
        return result;
    }


    /**
     * 新增
     * @Author: ct
     * @param config 配置
     * @return
     *
     **/
    @PostMapping("/save")
    @RequiresPermissions(value = {"conversion:save", Global.SA}, logical = Logical.OR)
    public AjaxResult saveConfigVO(ConversionSkuConfig config){
        AjaxResult result = conversionSkuConfigService.saveConfigVO(config);
        return result;
    }

    /**
     * 批量导入
     * @Author: ct
     * @param file 文件
     * @return
     **/
    @PostMapping("/import")
    @RequiresPermissions(value = {"conversion:import", Global.SA}, logical = Logical.OR)
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file){
        return conversionSkuConfigService.importExcel(file);
    }

    /**
     * 更新
     * @Author: ct
     * @param conversionSkuConfig 配置信息
     * @return
     **/
    @PostMapping("/update")
    @RequiresPermissions(value = {"conversion:update", Global.SA}, logical = Logical.OR)
    public AjaxResult updateConfig(ConversionSkuConfig conversionSkuConfig){
        AjaxResult result = conversionSkuConfigService.updateConfig(conversionSkuConfig);
        return result;
    }

    /**
     * 模版导出
     * @Author: ct
     * @param response 返回
     * @return
     **/
    @GetMapping("/template")
    @RequiresPermissions(value = {"conversion:template", Global.SA}, logical = Logical.OR)
    public AjaxResult templateDown(HttpServletResponse response){
        conversionSkuConfigService.templateDown(response);
        return AjaxResult.getOK();
    }
}
