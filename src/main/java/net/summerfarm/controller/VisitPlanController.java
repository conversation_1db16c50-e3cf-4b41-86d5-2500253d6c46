package net.summerfarm.controller;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.VisitPlan;
import net.summerfarm.model.input.VisitPlanInput;
import net.summerfarm.model.vo.VisitPlanVO;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.VisitPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2019/8/5  4:30 PM
 */
@Api(tags = "拜访计划管理")
@RestController
@RequestMapping(value = "/visitPlan")
public class VisitPlanController {
    @Resource
    private VisitPlanService visitPlanService;
    @Resource
    private MerchantService merchantService;

    @ApiOperation(value = "新增拜访计划", httpMethod = "POST", tags = "拜访计划管理类")
    @RequiresPermissions(value = {"visitPlan:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insertVisitPlan(VisitPlan visitPlan) {
        return visitPlanService.insertVisitPlan(visitPlan);
    }

    @ApiOperation(value = "更新拜访计划", httpMethod = "PUT", tags = "拜访计划管理类")
    @RequiresPermissions(value = {"visitPlan:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@Validated(Update.class) @RequestBody VisitPlan visitPlan) {
        return visitPlanService.updateVisitPlan(visitPlan);
    }

    @ApiOperation(value = "查询拜访计划", httpMethod = "POST", tags = "拜访计划管理类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"visitPlan:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult queryVisitPlanList(@PathVariable int pageIndex, @PathVariable int pageSize, VisitPlanInput visitPlanInput) {
        return visitPlanService.queryVisitPlanList(pageIndex,pageSize,visitPlanInput);
    }

    @GetMapping("/{visitPlanId}")
    public AjaxResult selectVisitPlanDetail(@PathVariable Integer visitPlanId){
        return visitPlanService.queryVisitPlan(visitPlanId);
    }

    @ApiOperation(value = "查询 日拜访和月拜访计划个数", httpMethod = "POST", tags = "拜访计划管理类")
    @RequestMapping(value = "/select-day", method = RequestMethod.GET)
    public AjaxResult selectDayAndMonth() {
        return visitPlanService.queryDayAndMonth();
    }

    @ApiOperation(value = "拜访计划转换拜访记录", httpMethod = "POST", tags = "拜访计划管理类")
    @RequestMapping(value = "/compare-record", method = RequestMethod.POST)
    public AjaxResult compareFollowUpRecord( VisitPlanVO visitPlanVO) {
        return visitPlanService.compareToFollowUpRecord(visitPlanVO);
    }

    @ApiOperation(value = "取消拜访计划", httpMethod = "POST", tags = "拜访计划管理类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "拜访计划id", paramType = "query"),
            @ApiImplicitParam(name = "cancelContent", value = "取消原因", paramType = "query")
    })
    @RequiresPermissions(value = {"visitPlan:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public AjaxResult cancel(Integer id, String cancelContent) {
        return visitPlanService.cancelPlan(id, cancelContent);
    }

    @ApiOperation(value = "按日期查询任务", httpMethod = "GET", tags = "拜访计划管理类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "date", value = "查询日期，格式：" + DateUtils.DEFAULT_DATE_FORMAT, paramType = "query")
    })
    @RequiresPermissions(value = {"visitPlan:select", Global.SA}, logical = Logical.OR)
    @GetMapping("/day")
    public AjaxResult queryVisitPlan(Integer areaNo, String date) {
        return visitPlanService.queryPlanByDate(areaNo, DateUtils.date2LocalDate(DateUtils.string2Date(date, DateUtils.DEFAULT_DATE_FORMAT)));
    }

    @ApiOperation(value = "按日期统计任务完成情况", httpMethod = "GET", tags = "拜访计划管理类")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
        @ApiImplicitParam(name = "date", value = "查询日期，格式：" + DateUtils.DEFAULT_DATE_FORMAT, paramType = "query")
    })
    @RequiresPermissions(value = {"visitPlan:select", Global.SA}, logical = Logical.OR)
    @GetMapping("/count")
    public AjaxResult count(Integer areaNo, String date) {
        return visitPlanService.count(areaNo, date);
    }

    @ApiOperation(value = "批量创建拉新任务", httpMethod = "POST", tags = "拜访计划管理类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "count", value = "数量", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "date", value = "查询日期，格式：" + DateUtils.DEFAULT_DATE_FORMAT, paramType = "query")
    })
    @RequiresPermissions(value = {"visitPlan:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/create-batch", method = RequestMethod.POST)
    public AjaxResult createBatch(Integer count, Integer areaNo, String date) {
        return visitPlanService.createBatch(count, areaNo, date);
    }

    @GetMapping(value = "/select-contact")
    public AjaxResult selectContact(Long contactId) {
        return merchantService.selectContact(contactId);
    }
}
