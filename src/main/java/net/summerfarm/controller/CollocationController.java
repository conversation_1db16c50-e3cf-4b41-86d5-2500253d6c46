package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.CollocationAddReq;
import net.summerfarm.model.input.CollocationQuery;
import net.summerfarm.service.CollocationService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 搭配购管理
 */
@RestController
@RequestMapping("/sku/collocation")
public class CollocationController {

    @Resource
    private CollocationService collocationService;


    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param collocationQuery
     * @return
     */
    @RequiresPermissions(value = {"sku-collocation:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, CollocationQuery collocationQuery){
        return collocationService.select(pageIndex, pageSize, collocationQuery);
    }

    /**
     *
     * @param collocationAddReq
     * @param bindingResult
     * @return
     */
    @RequiresPermissions(value = {"sku-collocation:save", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated @RequestBody CollocationAddReq collocationAddReq, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return collocationService.save(collocationAddReq);
    }

//    @RequiresPermissions(value = {"config:save", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/config/{days}",method = RequestMethod.GET)
    public AjaxResult updateCollocationDays(@PathVariable Integer days){
        return collocationService.updateCollocationDays(days);
    }

    @RequestMapping(value = "/config",method = RequestMethod.GET)
    public AjaxResult selectCollocationDays(){
        return collocationService.selectCollocationDays();
    }

    /**
     * 删除搭配购sku
     * @return
     */
    @RequiresPermissions(value = {"sku-collocation:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/item/{collocationItemId}", method = RequestMethod.POST)
    public AjaxResult deleteCollocationItem(@PathVariable String collocationItemId,Integer collocationId) {
        return collocationService.deleteCollocationItem(collocationItemId,collocationId);
    }


    /**
     * 查询详情
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"sku-collocation:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult selectDetail(@PathVariable int id) {
        return collocationService.selectDetail(id);
    }

    @RequestMapping(value = "/discount/{sku}", method = RequestMethod.GET)
    public AjaxResult selectDiscount(@PathVariable String sku) {
        return collocationService.selectDiscount(sku);
    }


}
