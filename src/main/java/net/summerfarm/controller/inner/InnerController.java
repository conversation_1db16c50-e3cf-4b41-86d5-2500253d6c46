package net.summerfarm.controller.inner;


import com.google.common.base.Splitter;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.PartnershipBuy;
import net.summerfarm.model.param.BatchProveParam;
import net.summerfarm.model.vo.DeliveryOrdersVO;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.BatchProveService;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.PurchasePredictionService;
import net.summerfarm.service.StoreRecordService;
import net.summerfarm.service.bms.BmsSettleAccountService;
import net.summerfarm.service.inner.InnerService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 后门接口
 * <AUTHOR>
 * @Create 2020-09-21
 */
@RestController
@RequestMapping("/inccddaa")
public class InnerController {

    @Autowired
    private InnerService innerService;

    @Resource
    private MerchantService merchantService;
    @Resource
    private MerchantMapper merchantMapper;

    @Autowired
    private PurchasePredictionService purchasePredictionService;

    @Resource
    private StoreRecordService storeRecordService;

    @Resource
    private BatchProveService batchProveService;

    @Resource
    private AreaStoreService areaStoreService;

    @Resource
    private BmsSettleAccountService bmsSettleAccountService;

    /**
     * 茶百道账号数据导入
     * @param file       导入的excel文件
     * @param allowCity  允许导入的城市，用【中文逗号分隔】
     * @return
     */
    @PostMapping("/cabaidao")
    public String cabaidaoAccount(@RequestParam("file") MultipartFile file, String allowCity){
        List<String> allowCityList = null;
        if (StringUtils.isNotBlank(allowCity)) {
            allowCityList = Splitter.on("，").splitToList(allowCity);
        }
        innerService.cabaidaoAccount(file, allowCityList);
        return "finished";
    }

    /**
     * 初始化售后单位
     * @return
     */
    @GetMapping("/ia")
    public String initAfterSaleUnit(){
        return innerService.initAfterSaleUnit();
    }

    /**
    * 批量更新客户经营类型
    */
    @PostMapping("/updateType")
    public String merchantUpdateType(@RequestParam("file") MultipartFile file){
        innerService.merchantUpdateType(file);
        return "finished";
    }

    /**
     * 水果采购预警
     * @return
     */
    @GetMapping("/fpr")
    public String exePurchaseRemind(){
        purchasePredictionService.fruitPurchaseRemind();
        return "finished";
    }

    @GetMapping("/esf")
    public String exeSyncFruitData(){
        purchasePredictionService.syncFruitData();
        return "finished";
    }

    @GetMapping("/warehouse")
    public AjaxResult warehouseMapping(@RequestParam("file") MultipartFile file){
        innerService.warehouseMapping(file);
        return AjaxResult.getOK();
    }

    @GetMapping("/add/follow")
    public AjaxResult followUpRelation(Integer adminId){
        return innerService.followUpRelation(adminId);
    }

    @GetMapping("/update/contact")
    public AjaxResult updateContact(){
        return innerService.updateContact();
    }

    @GetMapping("/update/storeRecord")
    public AjaxResult updateStoreRecord(Integer id, Integer quantity ,Integer type){
        return storeRecordService.updateStoreRecordQuantity(id,quantity,type);
    }

    /**
     * 处理冻结库存
     * @param id
     * @param quantity
     * @param type
     * @return
     */
    @GetMapping("/update/areaStore")
    public AjaxResult updateAreaStore(Integer id, Integer quantity ,Integer type){
        return areaStoreService.updateAreaStoreQuantity(id,quantity,type);
    }


    /**
     * 生成城配明细结算单
     *
     * @param storeNo
     * @param deliveryDate
     * @return
     */
    @GetMapping("/bms/create/settle-account")
    public AjaxResult updateAreaStore(Integer storeNo, @DateTimeFormat(pattern = "yyyy-MM-dd") Date deliveryDate) {
        LocalDate date = DateUtils.date2LocalDate(deliveryDate);
        bmsSettleAccountService.manualSettleAccountSingle(storeNo, date);
        return AjaxResult.getOK();
    }


    /**
     * 审核用户
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"merchant:review", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/review/{id}", method = RequestMethod.POST)
    public AjaxResult reviewMerchant(@RequestBody MerchantVO inputVo, @PathVariable int id) throws IOException {
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid((long) id);
        merchantVO.setOperateStatus(inputVo.getOperateStatus());
        merchantVO.setState(inputVo.getState());
        return merchantService.reviewMerchant(id, merchantVO, false);
    }
}
