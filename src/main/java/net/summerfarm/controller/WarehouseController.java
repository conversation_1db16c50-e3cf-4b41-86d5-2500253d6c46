package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.vo.WarehouseCenterVO;
import net.summerfarm.model.vo.WarehouseStorageCenterExtVO;
import net.summerfarm.service.WarehouseService;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓库管理
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-03-15
 * @description
 */
@Api(tags = "仓库管理")
@RestController
@RequestMapping("/warehouse")
public class WarehouseController {
    @Resource
    private WarehouseService warehouseService;

    /**
     * 分页查询仓库数据
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param name 仓库名称
     * @param type 类型 0-本部仓 1-分部仓 2-合伙人仓
     * @return
     */
    @ApiOperation(value = "分页查询仓库数据", httpMethod = "GET", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "name",value = "仓库名称",paramType = "query",required = true),
            @ApiImplicitParam(name = "type",value = "类型 0-本部仓 1-分部仓 2-合伙人仓",paramType = "query",required = true)
    })
    @GetMapping("/storage/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"warehouse-storage:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectPage(@PathVariable int pageIndex, @PathVariable int pageSize, String name, Integer type) {
        return warehouseService.listWarehouseStoragePage(pageIndex, pageSize, name, type);
    }

    /**
     * 查询所有仓库
     * @param type 0-无效  1-有效
     * @param status 类型 0-本部仓 1-分部仓 2-合伙人仓
     * @return
     */
    @ApiOperation(value = "查询所有仓库", httpMethod = "GET", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status",value = "0-无效  1-有效",paramType = "query",required = true),
            @ApiImplicitParam(name = "type",value = "类型 0-本部仓 1-分部仓 2-合伙人仓",paramType = "query",required = true)
    })
    @GetMapping("/storage")
    public AjaxResult<List<WarehouseStorageCenterExtVO>> selectAllStorage(Integer type, Integer status){
        return warehouseService.selectWarehouseStorageExt(type, status);
    }

    /**
     * 新增仓库
     * @param instance
     * @return
     */
    @ApiOperation(value = "新增仓库", httpMethod = "PUT", tags = "仓库管理")
    @PutMapping("/storage")
    @RequiresPermissions(value = {"warehouse-storage:add", Global.SA}, logical = Logical.OR)
    public AjaxResult addWarehouse(@RequestBody WarehouseStorageCenterVO instance) {
        return warehouseService.insertWarehouseStorage(instance);
    }

    /**
     * 修改仓库
     * @param instance
     * @return
     */
    @ApiOperation(value = "修改仓库", httpMethod = "POST", tags = "仓库管理")
    @PostMapping("/storage")
    @RequiresPermissions(value = {"warehouse-storage:update", Global.SA}, logical = Logical.OR)
    public AjaxResult updateWarehouse(@RequestBody WarehouseStorageCenterVO instance) {
        return warehouseService.updateWarehouseStorage(instance);
    }

    /**
     * 分页查询物流中心
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param logisticsCenter
     * @return
     */
    @ApiOperation(value = "查询所有物流中心", httpMethod = "GET", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @GetMapping("/logistics/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"warehouse-logistics:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectLogisticsCenterPage(@PathVariable int pageIndex, @PathVariable int pageSize, WarehouseLogisticsCenter logisticsCenter){
        return warehouseService.selectWarehouseLogisticsPage(pageIndex, pageSize,logisticsCenter);
    }

    /**
     * 查询所有物流中心
     * @param status
     * @return
     */
    @ApiOperation(value = "查询所有物流中心", httpMethod = "GET", tags = "仓库管理")
    @GetMapping("/logistics")
    public AjaxResult<List<WarehouseCenterVO>> selectAllLogistics(Integer status){
        return warehouseService.selectWarehouseLogistics(status);
    }

    /**
     * 添加配送中心
     * @param instance
     * @return
     */
    @ApiOperation(value = "添加配送中心", httpMethod = "PUT", tags = "仓库管理")
    @PutMapping("/logistics")
    @RequiresPermissions(value = {"warehouse-logistics:add", Global.SA}, logical = Logical.OR)
    public AjaxResult addLogisticsCenter(@RequestBody WarehouseCenterVO instance){
        return warehouseService.addWarehouseLogistics(instance);
    }

    /**
     * 修改配送中心
     *
     * @param instance
     * @return
     */
    @ApiOperation(value = "修改配送中心", httpMethod = "POST", tags = "仓库管理")
    @PostMapping("/logistics")
    @RequiresPermissions(value = {"warehouse-logistics:update", Global.SA}, logical = Logical.OR)
    public AjaxResult updateLogisticsCenter(@RequestBody WarehouseCenterVO instance){
        return warehouseService.updateWarehouseLogistics(instance);
    }

    /**
     * 删除配送中心使用库存仓
     * @param storeNo 物流中心编号
     * @param warehouseNo 仓库编号
     * @return
     */
    @ApiOperation(value = "删除配送中心使用库存仓", httpMethod = "DELETE", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNo",value = "物流中心编号",paramType = "path",required = true),
            @ApiImplicitParam(name = "warehouseNo",value = "仓库编号",paramType = "query",required = true)
    })
    @DeleteMapping("/logistics/{storeNo}")
    @RequiresPermissions(value = {"warehouse-logistics:delete", Global.SA}, logical = Logical.OR)
    public AjaxResult deleteLogisticsUseStorage(@PathVariable Integer storeNo, Integer warehouseNo){
        return warehouseService.deleteLogisticsUseStorage(storeNo, warehouseNo);
    }

    /**
     * 查询城市可用库存城市
     * @param areaNo 城市编号
     * @param sku sku
     * @return
     */
    @ApiOperation(value = "查询城市可用库存城市", httpMethod = "GET", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "sku",value = "sku",paramType = "query",required = true)
    })
    @GetMapping("/logistics/usable-storage")
    public AjaxResult selectUsableStorage(Integer areaNo, String sku){
        return AjaxResult.getOK(warehouseService.selectUsableStorageList(areaNo, sku));
    }

    /**
     * 物流中心取消更改截单时间
     * @param storeNo 城市编号
     * @return
     */
    @ApiOperation(value = "物流中心取消更改截单时间", httpMethod = "GET", tags = "仓库管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNo",value = "城市编号",paramType = "query",required = true),
            @ApiImplicitParam(name = "sku",value = "sku",paramType = "query",required = true)
    })
    @GetMapping("/logistics/cancel/closeTime")
    public AjaxResult selectUsableStorage(Integer storeNo){
        return warehouseService.cancelUpdateCloseTime(storeNo);
    }


}
