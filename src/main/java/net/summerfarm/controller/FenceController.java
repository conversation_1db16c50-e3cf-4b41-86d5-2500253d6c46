package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.RedissonLockUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.vo.FenceVO;
import net.summerfarm.module.wnc.common.exception.WncBizErrorEnum;
import net.summerfarm.service.FenceService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static net.summerfarm.module.wnc.common.exception.WncBizErrorEnum.FENCE_ADD_LOCK;

/**
 * <AUTHOR> ct
 * create at:  2021/8/10  17:00
 */
@RestController
@RequestMapping("/fence")
public class FenceController {

    @Resource
    private FenceService fenceService;
    private final String FENCE_ADD_LOCK = "fence:add";

    /**
    * 批量插入
    */
    @RequiresPermissions(value = {"fence:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult insertBatch(@RequestBody FenceVO fenceVO) {
        RLock lock = RedissonLockUtil.tryLock(FENCE_ADD_LOCK,10);
        try {
            return fenceService.insertFence(fenceVO);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    /**
     * 围栏组修复
     */
    @PostMapping(value = "/upsert/pack-handle")
    public CommonResult<Void> packHandle() {
        RLock lock = RedissonLockUtil.tryLock(FENCE_ADD_LOCK, 5, WncBizErrorEnum.FENCE_ADD_LOCK);
        try {
            fenceService.packHandleFix();
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
        return CommonResult.ok();
    }

    /**
    * 列表查询
    */
    @RequiresPermissions(value = {"fence:select-list", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/select/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectList(@PathVariable int pageIndex, @PathVariable int pageSize, FenceVO fenceVO) {
        return fenceService.selectFence(fenceVO,pageIndex,pageSize);
    }

    /**
    * 查询详情
    */
    @RequiresPermissions(value = {"fence:select-detail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/select/detail", method = RequestMethod.GET)
    public AjaxResult selectDetail(Integer id) {

        FenceVO fenceVO = fenceService.selectFenceById(id);
        return AjaxResult.getOK(fenceVO);
    }

    /**
    * 失效
    */
    @RequiresPermissions(value = {"fence:delete", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public AjaxResult deleteFence (Integer id) {
        return fenceService.deleteFenceById(id);
    }

    /**
    * 更新
    */
    @RequiresPermissions(value = {"fence:updateFence", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/updateFence", method = RequestMethod.POST)
    public AjaxResult updateFence (@RequestBody  FenceVO fenceVO) {
        return fenceService.updateFence(fenceVO);
    }

    /**
    * 新增service
    */
    @RequestMapping(value = "/insert/gd", method = RequestMethod.GET)
    public AjaxResult insertGdFence () {
        return fenceService.insertGdService();
    }

    /**
     * 查询已有的城市信息
     */
    @RequestMapping(value = "/select/city", method = RequestMethod.GET)
    public AjaxResult selectCityArea () {
        return fenceService.selectCityArea();
    }

    @RequestMapping(value = "/delete/All", method = RequestMethod.GET)
    public AjaxResult deleteAll () {
        return fenceService.deleteAll();
    }


    /**
    * 获取对应的区域
    */
    @GetMapping("/select/areaNo")
    public AjaxResult selectAreaNo(Long mId){
       return AjaxResult.getOK(fenceService.selectAreaByStoreNo(mId));
    }

    /**
    * 围栏暂停
    */
    @PostMapping("/stop")
    public AjaxResult stopFence (@RequestBody  FenceVO fenceVO) {
        return fenceService.stopFence(fenceVO);
    }

    /**
     * 围栏配送时间、首配初始化
     * @return
     */
//    @GetMapping("/initFenceDelivery")
//    public AjaxResult initFenceDelivery () {
//         fenceService.initFenceDelivery();
//         return null;
//    }

    /**
     * 获取围栏信息
     * @return
     */
    @GetMapping("/getAllEfficientFence")
    public AjaxResult getAllEfficientFence () {
         return AjaxResult.getOK(fenceService.getAllEfficientFence());
    }
}
