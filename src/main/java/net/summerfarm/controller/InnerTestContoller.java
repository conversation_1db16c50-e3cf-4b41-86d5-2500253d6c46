package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.model.vo.AreaSkuVO;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.impl.ActivityNewServiceImpl;
import net.summerfarm.task.ActivityLadderPriceInitProcessor;
import net.summerfarm.task.ActivityPriceExpireProcessor;
import net.summerfarm.task.ActivityPriceInitProcessor;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/18 13:49
 */

@RestController
@RequestMapping(value = "/inner/test")
public class InnerTestContoller {

    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private ActivityNewServiceImpl activityNewService;
    @Resource
    private ActivityPriceExpireProcessor processor;
    @Resource
    private ActivityPriceInitProcessor activityPriceInitProcessor;
    @Resource
    private ActivityLadderPriceInitProcessor activityLadderPriceInitProcessor;

    @RequestMapping("/selectCycleCost")
    public AjaxResult selectCycleCost(@RequestBody Map map) {
        String sku = (String) map.get("sku");
        Integer warehouseNo = (Integer) map.get("warehouseNo");
        return AjaxResult.getOK(cycleInventoryCostRepository.selectCycleCost(sku, warehouseNo));
    }

    @RequestMapping("/selectBySku")
    public AjaxResult selectBySku(@RequestBody Map map) {
        String sku = (String) map.get("sku");
        Integer warehouseNo = (Integer) map.get("warehouseNo");
        return AjaxResult.getOK(cycleInventoryCostRepository.selectBySku(sku, warehouseNo));
    }

    @RequestMapping("/selectAdmin")
    public AjaxResult selectAdmin(@RequestBody Map map) {
        Integer id = (Integer) map.get("id");
        return AjaxResult.getOK(adminMapper.selectByPrimaryKey(id));
    }

    @RequestMapping("/activityPriceExpireProcessor")
    public AjaxResult activityPriceExpireProcessor() {
        try {
            return AjaxResult.getOK(processor.processResult(null));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK();
    }

    @RequestMapping("/getActivitySku")
    public AjaxResult getActivitySku(@RequestBody AreaSkuVO vo) {
        try {
            final String sku = vo.getSku();
            final Integer areaNo = vo.getAreaNo();
            return AjaxResult.getOK(activityNewService.getActivitySku(sku, areaNo));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK();
    }

    @PostMapping("/activityPriceInit")
    public AjaxResult activityPriceInit(@RequestBody XmJobInput xmJobInput) {
        try {
            return AjaxResult.getOK(activityPriceInitProcessor.processResult(xmJobInput));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK();
    }

    @RequestMapping("/activityLadderPriceInit")
    public AjaxResult activityLadderPriceInit() {
        try {
            XmJobInput input = new XmJobInput();
            input.setInstanceParameters("1");
            return AjaxResult.getOK(activityLadderPriceInitProcessor.processResult(input));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK();
    }
}
