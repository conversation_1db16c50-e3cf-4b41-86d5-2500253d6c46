package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseController;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.param.QuantityChangeRecordQueryVO;
import net.summerfarm.model.vo.QuantityChangeRecordVO;
import net.summerfarm.model.vo.QuantityRecordVO;
import net.summerfarm.service.QuantityRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by wjd on 2018/11/24.
 */

@Api(tags = "库存变动记录")
@RestController
@RequestMapping(value = "/quantityrecord")
public class QuantityRecordController extends BaseController {


    @Resource
    private QuantityRecordService quantityRecordService;

    @ApiOperation(value = "库存变动记录查询",httpMethod = "GET",tags = "库存变动记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
    })
    @RequiresPermissions(value = {"quantityrecord:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/change/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectChangeList(@PathVariable int pageIndex, @PathVariable int pageSize, QuantityChangeRecordQueryVO selectKeys) {
        return quantityRecordService.selectChangeList(pageIndex, pageSize, selectKeys, getTenantId());
    }



}
