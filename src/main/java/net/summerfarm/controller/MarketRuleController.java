package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.MarketRule;
import net.summerfarm.model.input.MarketRuleReq;
import net.summerfarm.model.vo.MarketRuleVO;
import net.summerfarm.service.MarketRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/9/28
 */
@Api(tags = "营销规则管理")
@RestController
@RequestMapping(value = "/market-rule")
public class MarketRuleController {

    @Resource
    private MarketRuleService marketRuleService;


    @ApiOperation(value = "营销规格分页查找",httpMethod = "GET",tags = "营销规则管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"market-rule:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, MarketRule selectKeys){
        return marketRuleService.select(pageIndex, pageSize, selectKeys);
    }


    @RequiresPermissions(value = {"market-rule:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/exist", method = RequestMethod.GET)
    public AjaxResult exist(MarketRule selectKeys){
        return marketRuleService.exist(selectKeys);
    }

    @ApiOperation(value = "新增营销规则",httpMethod = "POST",tags = "营销规则管理")
    @RequiresPermissions(value = {"market-rule:save", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated(value = Add.class) @RequestBody MarketRule marketRule, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return marketRuleService.save(marketRule);
    }

    @ApiOperation(value = "修改营销规则",httpMethod = "POST",tags = "营销规则管理")
    @ApiImplicitParam(name = "id",value = "营销规则id",paramType = "path",required = true)
    @RequiresPermissions(value = {"market-rule:update", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "{id}", method = RequestMethod.POST)
    public AjaxResult update(@PathVariable int id , @Validated(value = Update.class) @RequestBody MarketRule marketRule, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        marketRule.setId(id);
        return marketRuleService.update(marketRule);
    }

    /**
     * 查询详情信息
     * @param marketRuleReq
     * @return MarketRuleVO
     */
    @RequiresPermissions(value = {"market-rule:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "query/detail", method = RequestMethod.POST)
    public CommonResult<MarketRuleVO> detail(@RequestBody @Validated MarketRuleReq marketRuleReq){
        return CommonResult.ok(marketRuleService.detail(marketRuleReq));
    }

}
