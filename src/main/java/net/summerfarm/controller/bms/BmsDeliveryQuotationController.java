package net.summerfarm.controller.bms;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.bms.BmsDeliveryQuotationQuery;
import net.summerfarm.model.input.bms.BmsQuotationConflictQuery;
import net.summerfarm.model.param.bms.BmsDeliveryQuotationParam;
import net.summerfarm.model.param.bms.BmsQuotationDetailParam;
import net.summerfarm.module.bms.inbound.controller.QuotationCommandController;
import net.summerfarm.module.bms.inbound.controller.QuotationQueryController;
import net.summerfarm.module.bms.model.input.QuotationDeleteInput;
import net.summerfarm.module.bms.model.input.QuotationQueryInput;
import net.summerfarm.module.bms.model.input.QuotationUpsertInput;
import net.summerfarm.service.bms.BmsDeliveryQuotationService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022/8/18
 */
@RestController
@Slf4j
@RequestMapping("/bms")
@Deprecated
public class BmsDeliveryQuotationController {

    @Resource
    private BmsDeliveryQuotationService bmsDeliveryQuotationService;

    @Resource
    private QuotationQueryController quotationQueryController;

    @Resource
    private QuotationCommandController quotationCommandController;

    @Resource
    private RedissonClient redissonClient;


    @ApiOperation(value = "查询城配报价单列表",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"delivery-quotation:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/delivery-quotation/list",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult selectDeliveryQuotation(@RequestBody BmsDeliveryQuotationQuery param){
        log.info("[upgrade quote]select");
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(quotationQueryController.queryQuotationPage( JSON.parseObject(JSON.toJSONString(param), QuotationQueryInput.class)).getData());
        }
        return bmsDeliveryQuotationService.selectDeliveryQuotation(param);
    }

    @ApiOperation(value = "查询城配报价单详情",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"delivery-quotation:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/delivery-quotation/detail",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult selectDeliveryQuotationDetail(@RequestBody BmsDeliveryQuotationParam param){
        log.info("[upgrade quote]detail");
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(quotationQueryController.queryQuotationDetail( JSON.parseObject(JSON.toJSONString(param), QuotationQueryInput.class)).getData());
        }
        return bmsDeliveryQuotationService.selectDeliveryQuotationDetail(param.getId());
    }

    @ApiOperation(value = "新增城配报价单",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"delivery-quotation:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delivery-quotation/save",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult saveDeliveryQuotation(@RequestBody @Validated BmsDeliveryQuotationParam param, BindingResult result){
        log.info("[upgrade quote]save");
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(quotationCommandController.saveQuotation( JSON.parseObject(JSON.toJSONString(param), QuotationUpsertInput.class)).getData());
        }

        RLock redissonLock = redissonClient.getLock(param.getQuotationAreas().get(NumberUtils.INTEGER_ZERO).getCity());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getError("REPEAT_COMMIT");
            }
            return bmsDeliveryQuotationService.saveDeliveryQuotation(param);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException(1, "请重试");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    @ApiOperation(value = "更新城配报价单",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"delivery-quotation:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delivery-quotation/update",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult updateDeliveryQuotation(@RequestBody @Validated BmsDeliveryQuotationParam param, BindingResult result){
        log.info("[upgrade quote]update");
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(quotationCommandController.updateQuotation( JSON.parseObject(JSON.toJSONString(param), QuotationUpsertInput.class)).getData());
        }
        RLock redissonLock = redissonClient.getLock(param.getQuotationAreas().get(NumberUtils.INTEGER_ZERO).getCity());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getError("REPEAT_COMMIT");
            }
            return bmsDeliveryQuotationService.updateDeliveryQuotation(param);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException(1, "请重试");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    @ApiOperation(value = "作废报价单",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"delivery-quotation:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delivery-quotation/delete",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult invalidDeliveryQuotation(@RequestBody BmsDeliveryQuotationParam param, BindingResult result){
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        log.info("[upgrade quote]delete");
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(quotationCommandController.deleteQuotation( JSON.parseObject(JSON.toJSONString(param), QuotationDeleteInput.class)).getData());
        }
        return bmsDeliveryQuotationService.invalidDeliveryQuotation(param);
    }

    @ApiOperation(value = "新增报价类型",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"quotation-detail:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delivery-quotation/type",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult saveQuotationDetail(@RequestBody @Validated BmsQuotationDetailParam param, BindingResult result){
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        log.info("[upgrade quote]save item");
        return bmsDeliveryQuotationService.saveQuotationDetail(param);
    }

    @ApiOperation(value = "查询报价类型",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"quotation-detail:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/delivery-quotation/type/detail",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult selectQuotationDetail(@RequestBody BmsQuotationDetailParam param){
        log.info("[upgrade quote]select item");
        return bmsDeliveryQuotationService.selectQuotationDetail(param);
    }

    @ApiOperation(value = "查询服务区域",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"quotation-detail:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/delivery-quotation/area",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult selectQuotationArea(){
        log.info("[upgrade quote]select area");
        return bmsDeliveryQuotationService.selectQuotationArea();
    }

    @ApiOperation(value = "查询报价单已覆盖区县",httpMethod = "POST",tags = "报价管理")
    @RequiresPermissions(value = {"quotation-detail:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/quotation-area/exist",method = RequestMethod.POST)
    @Deprecated
    public AjaxResult selectQuotationAreaExist(@RequestBody BmsDeliveryQuotationParam param){
        log.info("[upgrade quote]exist");
        return bmsDeliveryQuotationService.selectQuotationAreaExist(param);
    }

    @ApiOperation(value = "查询是否存在冲突的计费模型", httpMethod = "GET", tags = "结算管理")
    @RequiresPermissions(value = {"cost-adjustment:import", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/quotation-conflict", method = RequestMethod.POST)
    /**
     * 查询是否存在冲突的计费模型
     */
    @Deprecated
    public AjaxResult queryQuotationConflict(@RequestBody BmsQuotationConflictQuery param, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        log.info("[upgrade quote]conflict");
        return bmsDeliveryQuotationService.queryQuotationConflict(param);
    }

}
