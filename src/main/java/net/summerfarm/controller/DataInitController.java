package net.summerfarm.controller;

import cn.hutool.json.JSONUtil;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.service.InventoryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * @Date 2023/4/6 16:07
 * @<AUTHOR>
 */
@RestController
@RequestMapping("/data-init")
@Slf4j
public class DataInitController {

    @Resource
    private InventoryMapper inventoryMapper;
    @DubboReference
    private TenantProvider tenantProvider;

    /**
     * 初始化saas 租户ID
     *
     * <AUTHOR>
     * @date 2023/4/6 16:09
     * @return net.xianmu.common.result.CommonResult 返回
     */
    @RequestMapping("/initSaasTenantId")
    public CommonResult<Boolean> initSaasTenantId() {
        // 查询inventory createType为代仓的adminId
        List<Integer> adminIdList = inventoryMapper.selectSaasAdminIdList();
        log.info("adminIdList:{}", JSONUtil.toJsonStr(adminIdList));
        adminIdList.forEach(adminId -> {
            TenantQueryReq queryReq = new TenantQueryReq();
            queryReq.setAdminId(adminId.longValue());
            DubboResponse<List<TenantResp>> response = tenantProvider.list(queryReq);
            if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                log.error("invoke tenantProvider.list fail request:{}, msg:{}", JSONUtil.toJsonStr(queryReq), response.getMsg());
                return;
            }
            List<TenantResp> tenantList = response.getData();
            TenantResp tenantResp = tenantList.get(0);
            // 根据adminId更新租户ID
            int effectRow = inventoryMapper.updateTenantIdByAdminId(adminId, tenantResp.getId());
            log.info("adminId:{}, tenantId:{}, 初始化成功, 影响行数:{}", adminId, tenantResp.getId(), effectRow);
        });
        return CommonResult.ok();
    }

}
