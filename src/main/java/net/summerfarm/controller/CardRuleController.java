package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.CardRule;
import net.summerfarm.model.vo.CardRuleVO;
import net.summerfarm.service.CardRuleService;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "优惠卡规则")
@RestController
@RequestMapping(value = "/card-rule")
public class CardRuleController {
    @Resource
    private CardRuleService cardRuleService;

    @ApiOperation(value = "新增规则", httpMethod = "POST", tags = "优惠卡规则")
    @RequiresPermissions(value = {"card-rule:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult save(@Validated(value = {Add.class}) CardRule cardRule, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return cardRuleService.save(cardRule);
    }

    @ApiOperation(value = "修改规则", httpMethod = "PUT", tags = "优惠卡规则")
    @RequiresPermissions(value = {"card-rule:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody @Validated(value = {Update.class}) CardRule cardRule, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return cardRuleService.update(cardRule);
    }

    @ApiOperation(value = "规则列表", httpMethod = "GET", tags = "优惠卡规则")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"card-rule:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, CardRuleVO cardRuleVO) {
        return cardRuleService.select(pageIndex, pageSize, cardRuleVO);
    }

}
