package net.summerfarm.controller;

import net.summerfarm.service.DistributionRulesService;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 运费规则
 * @date 2023/10/7 18:43:41
 */
@RequestMapping(value = "/freight")
@RestController
public class DistributionRulesController {

    @Resource
    private DistributionRulesService distributionRulesService;

    /**
     * 初始化门店-运费规则
     * @param
     * @return List<Long>
     */
    @RequestMapping(value = "/init-merchant",method = RequestMethod.POST)
    public CommonResult<List<Long>> initMerchant() {
        return CommonResult.ok(distributionRulesService.initMerchant());
    }

    /**
     * 初始化区域-运费规则
     * @param
     * @return List<Long>
     */
    @RequestMapping(value = "/init-area",method = RequestMethod.POST)
    public CommonResult<List<Long>> initArea() {
        return CommonResult.ok(distributionRulesService.initArea());
    }

    /**
     * 初始化品牌-运费规则
     * @param
     * @return List<Long>
     */
    @RequestMapping(value = "/init-admin",method = RequestMethod.POST)
    public CommonResult<Set<Long>> initAdmin() {
        return CommonResult.ok(distributionRulesService.initAdmin());
    }
}
