package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.MajorRebate;
import net.summerfarm.model.domain.SkuMapping;
import net.summerfarm.model.input.MajorMerchantQuery;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.vo.MajorAreaStoreVO;
import net.summerfarm.model.vo.MajorPriceVO;
import net.summerfarm.model.vo.SkuMappingVO;
import net.summerfarm.service.MajorPriceService;
import net.summerfarm.service.MajorRebateService;
import net.summerfarm.service.SkuMappingService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by wjd on 2018/7/16.
 */

@Api(tags = "大客户管理")
@RestController
@RequestMapping(value = "/major")
public class MajorController {

    @Resource
    private MajorPriceService majorPriceService;


    @Resource
    private MajorRebateService majorRebateService;

    @Resource
    private SkuMappingService skuMappingService;

    @ApiOperation(value = "大客户数据看板查询", httpMethod = "GET", tags = "大客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "year", value = "年份", paramType = "path", required = true),
            @ApiImplicitParam(name = "month", value = "月份", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/total/{year}/{month}", method = RequestMethod.GET)
    public AjaxResult selectTotalByDate(@PathVariable Integer year, @PathVariable Integer month) {
        LocalDate startTime = LocalDate.of(year, month, 1);
        LocalDate endTime = null;
        if (month == 12) {
            endTime = LocalDate.of(year + 1, 1, 1);
        } else {
            endTime = LocalDate.of(year, month + 1, 1);
        }
        return majorPriceService.selectTotal(startTime, endTime);
    }


    @ApiOperation(value = "大客户账单管理", httpMethod = "GET", tags = "大客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "year", value = "年份", paramType = "query"),
            @ApiImplicitParam(name = "month", value = "月份", paramType = "query"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/bill/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectBill(@PathVariable int pageIndex, @PathVariable int pageSize, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate startTime, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate endTime) {
        return majorPriceService.selectBill(pageIndex, pageSize, startTime, endTime);
    }

    @ApiOperation(value = "大客户报价单查询", httpMethod = "GET", tags = "大客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path",required = true),
    })
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/majorPrice/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectMajorPrice(@PathVariable int pageIndex, @PathVariable int pageSize,MajorPriceInput majorPriceInput) {
        return majorPriceService.selectMajorPrice(pageIndex,pageSize,majorPriceInput);
    }

    @ApiOperation(value = "大客户门店查询", httpMethod = "GET", tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/store/manage", method = RequestMethod.GET)
    public AjaxResult selectStoreManage() {
        return majorPriceService.selectStoreManage();
    }

    @ApiOperation(value = "大客户门店详情", httpMethod = "GET", tags = "大客户管理")
    @ApiImplicitParam(name = "adminId", value = "用户id", paramType = "path", required = true)
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/detail/{adminId}", method = RequestMethod.GET)
    public AjaxResult majorMerchantDetail(@PathVariable Integer adminId,MajorMerchantQuery majorMerchantQuery) {
        majorMerchantQuery.setAdminId(adminId);
        return majorPriceService.majorMerchantDetail(majorMerchantQuery);
    }

    @PostMapping("/query/merchant-list")
    public AjaxResult merchantList(@RequestBody MajorMerchantQuery majorMerchantQuery){
        return majorPriceService.majorMerchantDetail(majorMerchantQuery);
    }

    @ApiOperation(value = "大客户价格策略查询", httpMethod = "GET", tags = "大客户管理")
    @RequiresPermissions(value = {"rebate:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/rebate/select", method = RequestMethod.GET)
    public AjaxResult majorRebateSelect(MajorRebate majorRebate) {
        return majorRebateService.select(majorRebate);
    }

    @ApiOperation(value = "大客户价格策略详情查询", httpMethod = "GET", tags = "大客户管理")
    @RequiresPermissions(value = {"rebate:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/rebate/detail", method = RequestMethod.GET)
    public AjaxResult majorRebateDetial(MajorRebate majorRebate) {
        return majorRebateService.selectDetail(majorRebate);
    }

    @ApiOperation(value = "大客户价格策略新增", httpMethod = "POST", tags = "大客户管理")
    @RequiresPermissions(value = {"rebate:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/rebate/save", method = RequestMethod.POST)
    public AjaxResult majorRebateSave(@RequestBody MajorRebate majorRebate) {
        return majorRebateService.save(majorRebate);
    }

    @ApiOperation(value = "大客户价格策略移除", httpMethod = "POST", tags = "大客户管理")
    @RequiresPermissions(value = {"rebate:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/rebate/delete", method = RequestMethod.POST)
    public AjaxResult majorRebateDelete(MajorRebate majorRebate) {
        return majorRebateService.delete(majorRebate);
    }

    @ApiOperation(value = "大客户sku映射查询",httpMethod = "GET",tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-mapping/select",method = RequestMethod.GET)
    public AjaxResult skuMappingSelect(SkuMappingVO skuMappingVO){
        return skuMappingService.selectList(skuMappingVO);
    }

    @ApiOperation(value = "大客户sku映射新增",httpMethod = "POST",tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-mapping/insert",method = RequestMethod.POST)
    public AjaxResult skuMappingInsert(@RequestBody @Validated({Add.class}) SkuMapping skuMapping, BindingResult result){
        if (result.hasErrors()){
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return skuMappingService.insert(skuMapping);
    }

    @ApiOperation(value = "大客户sku映射删除",httpMethod = "DELETE",tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @ApiImplicitParam(name = "id",value = "sku映射id",type = "path",required = true)
    @RequestMapping(value = "/sku-mapping/del/{id}",method = RequestMethod.DELETE)
    public AjaxResult skuMappingDel(@PathVariable Integer id){
        return skuMappingService.delete(id);
    }

    @ApiOperation(value = "大客户sku映射修改",httpMethod = "PUT",tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-mapping/update",method = RequestMethod.PUT)
    public AjaxResult skuMappingUpdate(@RequestBody @Validated({Update.class}) SkuMapping skuMapping, BindingResult result){
        if (result.hasErrors()){
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return skuMappingService.update(skuMapping);
    }

    @ApiOperation(value = "大客户sku映射模板下载",httpMethod = "GET",tags = "大客户管理")
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-mapping/download",method = RequestMethod.GET)
    public AjaxResult skuMappingTemplateDownload(HttpServletResponse response){
        return skuMappingService.templateDownload(response);
    }

    @GetMapping("/sku-mapping/url")
    public AjaxResult skuMappingTemplateUrl(){
        return skuMappingService.skuMappingTemplateUrl();
    }

    @ApiOperation(value = "大客户sku映射模板上传",httpMethod = "POST",tags = "大客户管理")
    @ApiImplicitParam(name = "adminId", value = "用户id", paramType = "query",required = true)
    @RequiresPermissions(value = {"major:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-mapping/upload",method = RequestMethod.POST)
    public AjaxResult skuMappingTemplateUpload(@RequestParam("file") MultipartFile file,Integer adminId){
        return skuMappingService.templateUpload(file,adminId);
    }

    @ApiOperation(value = "大客户代仓商品库存查询",httpMethod = "GET",tags = "大客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path",required = true),
    })
    @RequiresPermissions(value = {"major:productStock", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/majorPrice/productStock/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult selectTotalProduct(@PathVariable int pageIndex, @PathVariable int pageSize, MajorAreaStoreVO majorAreaStoreVO){
        return majorPriceService.selectTotalProductStock(pageIndex, pageSize, majorAreaStoreVO);

    }

    @ApiOperation(value = "大客户代仓商品库存导出",httpMethod = "GET",tags = "大客户管理")
    @RequiresPermissions(value = {"major:productStock", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/majorPrice/productStock/export",method = RequestMethod.GET)
    public AjaxResult exportTotalProduct(MajorAreaStoreVO majorAreaStoreVO, HttpServletResponse response){
        return majorPriceService.exportTotalProductStock(majorAreaStoreVO, response);
    }

    @ApiOperation(value = "大客户报价单失败数据校验", httpMethod = "POST", tags = "大客户管理")
    @RequestMapping(value = "/majorPrice/failCheck", method = RequestMethod.POST)
    public AjaxResult failCheck(@RequestBody List<MajorPriceVO> majorPriceList){
        return  majorPriceService.failCheck(majorPriceList);
    }

    /**
     * 大客户账单管理-门店汇总导出
     * @param startTime
     * @param endTime
     * @param response
     * @return
     */
    @ApiOperation(value = "大客户账单管理-门店汇总导出",httpMethod = "GET",tags = "大客户管理")
    @RequestMapping(value = "/storeBill/export", method = RequestMethod.GET)
    public void downloadMajorOrder(@DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate startTime, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate endTime, HttpServletResponse response) {
        majorPriceService.storeBillExport(startTime,endTime, response);
    }

}
