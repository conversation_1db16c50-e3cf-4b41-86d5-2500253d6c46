package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.CirclePeopleVo;
import net.summerfarm.service.CirclePeopleService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-10-25
 * @description 圈人平台
 */
@Api(tags = "圈人平台")
@RestController
@RequestMapping(value = "/circle-people")
public class CirclePeopleController {
    @Resource
    private CirclePeopleService circlePeopleService;

    @ApiOperation(value = "查询",httpMethod = "GET",tags = "圈人平台")
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize,String name){
        return circlePeopleService.select(pageIndex,pageSize,name);
    }

    @ApiOperation(value = "根据名称或者id查询",httpMethod = "GET",tags = "圈人平台")
    @RequestMapping(value = "/selectByNameId", method = RequestMethod.GET)
    public AjaxResult selectByNameId(String nameOrId){
        return circlePeopleService.selectByNameId(nameOrId);
    }

    @ApiOperation(value = "下载模板",httpMethod = "GET",tags = "圈人平台")
    @RequestMapping(value = "/downtemplate", method = RequestMethod.GET)
    public AjaxResult downtemplate(String fileName){
        return circlePeopleService.downtemplate(fileName);
    }

    @ApiOperation(value = "保存",httpMethod = "POST",tags = "圈人平台")
    @PostMapping(value ="/save")
    public AjaxResult save(CirclePeopleVo circlePeopleVo,@RequestParam(value = "file",required = false) MultipartFile file){
        return circlePeopleService.save(circlePeopleVo,file);
    }


}
