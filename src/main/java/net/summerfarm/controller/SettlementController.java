package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.SettlementPaymentRecord;
import net.summerfarm.model.input.SettlementQuery;
import net.summerfarm.model.vo.RefundSlipVO;
import net.summerfarm.model.vo.SettlementDetailVO;
import net.summerfarm.model.vo.SettlementVO;
import net.summerfarm.service.RefundSlipService;
import net.summerfarm.service.SettlementService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-11-13
 * @description
 */
@Api(tags = "结算单")
@RestController
@RequestMapping("/settlement")
@Deprecated
public class SettlementController {

    @Resource
    private SettlementService settlementService;

    @Resource
    private RefundSlipService refundSlipService;

    @ApiOperation(value = "结算单列表查询", httpMethod = "GET", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"settlement:list", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, SettlementQuery query) {
        return settlementService.select(pageIndex, pageSize, query);
    }

    @ApiOperation(value = "未结算采购项查询", httpMethod = "GET", tags = "结算单")
    @ApiImplicitParam(name = "purchaseNo", value = "采购单号", paramType = "path", required = true)
    @RequiresPermissions(value = {"settlement:list", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/un-settle/{purchaseNo}", method = RequestMethod.GET)
    public AjaxResult selectUnSettleByPurchaseNo(@PathVariable String purchaseNo) {
        return settlementService.selectUnSettleByPurchaseNo(purchaseNo);
    }

    @ApiOperation(value = "未结算采购项查询", httpMethod = "GET", tags = "结算单")
    @ApiImplicitParam(name = "supplierId", value = "供应商id", paramType = "query", required = true)
    @RequiresPermissions(value = {"settlement:list", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/un-settle", method = RequestMethod.GET)
    public AjaxResult selectUnSettleBySupplierId(Integer supplierId,
                                                 @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate startTime,
                                                 @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate endTime) {
        return settlementService.selectUnSettleBySupplierId(supplierId,startTime,endTime);
    }

    @ApiOperation(value = "创建采购单", httpMethod = "POST", tags = "结算单")
    @RequiresPermissions(value = {"settlement:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult save(@Validated @RequestBody SettlementVO instance) {
        return settlementService.save(instance);
    }

    @ApiOperation(value = "手动关闭结算单", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParam(name = "settlementId", value = "结算单号", paramType = "query", required = true)
    @RequiresPermissions(value = {"settlement:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    public AjaxResult close(Integer settlementId) {
        return settlementService.close(settlementId);
    }

    @ApiOperation(value = "查看结算单详情", httpMethod = "GET", tags = "结算单")
    @ApiImplicitParam(name = "id", value = "结算单id", paramType = "query", required = true)
    @RequiresPermissions(value = {"settlement:detail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult detail(@PathVariable Integer id) {
        return settlementService.detail(id);
    }

    @ApiOperation(value = "根据条件查看结算单详情", httpMethod = "GET", tags = "结算单")
    @RequiresPermissions(value = {"settlement:detail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    public AjaxResult selectDetail(SettlementDetailVO settlementDetailVO) {
        return settlementService.selectDetail(settlementDetailVO);
    }

    @ApiOperation(value = "创建单次结算", httpMethod = "POST", tags = "结算单")
    @RequiresPermissions(value = {"settlement:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/save-record", method = RequestMethod.POST)
    public AjaxResult saveRecord(@Validated SettlementPaymentRecord instance) {
        return settlementService.saveRecord(instance);
    }

    @ApiOperation(value = "修改结算单打款记录备注", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
            @ApiImplicitParam(name = "remark", value = "备注", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"settlement:insert", "settlement:audit", "settlement:pay", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update-remark", method = RequestMethod.POST)
    public AjaxResult updateRemark(Integer recordId, String remark) {
        return settlementService.updateRemark(recordId, remark);
    }

    @ApiOperation(value = "结算单审核", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
            @ApiImplicitParam(name = "auditFlag", value = "0、fail 1、pass", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"settlement:audit", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public AjaxResult audit(Integer recordId, Integer auditFlag) {
        return settlementService.audit(recordId, auditFlag);
    }

    @ApiOperation(value = "结算单审批", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
            @ApiImplicitParam(name = "auditFlag", value = "0、fail 1、pass", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"settlement:approve", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/approve", method = RequestMethod.POST)
    public AjaxResult approve(Integer recordId, Integer approveFlag) {
        return settlementService.approve(recordId, approveFlag);
    }

    @ApiOperation(value = "付款单撤回", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
    })
    @RequestMapping(value = "/withdraw", method = RequestMethod.POST)
    public AjaxResult withdraw(Integer recordId) {
        return settlementService.withdraw(recordId);
    }

    @ApiOperation(value = "结算打款", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
            @ApiImplicitParam(name = "auditFlag", value = "0、fail 1、pass", paramType = "query", required = true),
            @ApiImplicitParam(name = "paymentVoucher", value = "打款凭证", paramType = "query", required = false)
    })
    @RequiresPermissions(value = {"settlement:pay", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/pay", method = RequestMethod.POST)
    public AjaxResult pay(Integer recordId, Integer auditFlag, String paymentVoucher) {
        return settlementService.pay(recordId, auditFlag, paymentVoucher);
    }

    @ApiOperation(value = "上传打款凭证", httpMethod = "POST", tags = "结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "打款记录id", paramType = "query", required = true),
            @ApiImplicitParam(name = "voucher", value = "凭证", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"settlement:pay", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/up-voucher", method = RequestMethod.POST)
    public AjaxResult upVoucher(Integer recordId, String voucher) {
        return settlementService.upVoucher(recordId, voucher);
    }

    @ApiOperation(value = "结算数据统计", httpMethod = "GET", tags = "结算单")
    @RequiresPermissions(value = {"settlement:list", Global.SA}, logical = Logical.OR)
    @GetMapping("/data")
    public AjaxResult data() {
        return settlementService.data();
    }

    @ApiOperation(value = "结算单导出", httpMethod = "GET", tags = "结算单")
    @ApiImplicitParam(name = "id", value = "结算单编号", paramType = "path", required = true)
    @RequiresPermissions(value = {"settlement:detail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/export/{id}/{recordId}", method = RequestMethod.GET)
    public void export(@PathVariable Integer id,@PathVariable Integer recordId) {
        settlementService.export(id,recordId);
    }

    @ApiOperation(value = "未发起结算明细导出", httpMethod = "GET", tags = "结算单")
    @RequiresPermissions(value = {"settlement:unsettled:export", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/unsettled/export", method = RequestMethod.GET)
    public void export(){settlementService.unSettlePurchaseExport();}

    @ApiOperation(value = "退款单列表查询", httpMethod = "GET", tags = "结算单")
    @RequiresPermissions(value = {"settlement:list", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectRefundSlip/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectRefundSlip(@PathVariable int pageIndex, @PathVariable int pageSize, RefundSlipVO query) {
        return refundSlipService.selectRefundSlip(pageIndex, pageSize, query);
    }

    @ApiOperation(value = "待退款退款单确认退款上传信息", httpMethod = "POST", tags = "结算单")
    @RequiresPermissions(value = {"settlement:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/adoptRefundSlip", method = RequestMethod.POST)
    public AjaxResult adoptRefundSlip(RefundSlipVO query) {
        return refundSlipService.adoptRefundSlip(query);
    }
}
