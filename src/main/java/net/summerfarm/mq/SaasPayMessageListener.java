package net.summerfarm.mq;

import com.alibaba.fastjson.JSON;
import com.cosfo.summerfarm.enums.OrderDeliveryResultEnums;
import com.cosfo.summerfarm.enums.ResultCodeEnums;
import com.cosfo.summerfarm.model.dto.SummerFarmOrderPayResultDTO;
import com.cosfo.summerfarm.model.input.SummerFarmOrderPayResultInput;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMqTag;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.saas.OutsideOrderService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2023/3/21 10:37<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
@MqListener(
        consumerGroup = "GID_manage_saas_order_change",
        topic =  SummerfarmMQTopic.SAAS_ORDER_CHANGE,
        tag = SummerfarmMqTag.ORDER_PAY_CHANGE)
public class SaasPayMessageListener extends AbstractMqListener<SummerFarmOrderPayResultInput> {

    @Resource
    private OutsideOrderService outsideOrderService;
    @Resource
    private MqProducer mqProducer;

    @Override
    public void process(SummerFarmOrderPayResultInput summerFarmOrderPayResultInput) {
        try {
            log.info("接收到saas订单支付消息:{}", JSON.toJSONString(summerFarmOrderPayResultInput));
            OrderDeliveryResultEnums resultEnums = outsideOrderService.payConfirm(summerFarmOrderPayResultInput);

            SummerFarmOrderPayResultDTO summerFarmOrderPayResultDTO = new SummerFarmOrderPayResultDTO();
            summerFarmOrderPayResultDTO.setOrderNo(summerFarmOrderPayResultInput.getOrderNo());
            summerFarmOrderPayResultDTO.setDeliveryTime(summerFarmOrderPayResultInput.getDeliveryTime());
            summerFarmOrderPayResultDTO.setStatus(resultEnums.getCode());
            summerFarmOrderPayResultDTO.setCode(ResultCodeEnums.SUCCESS.getCode());
            summerFarmOrderPayResultDTO.setCodeMsg(ResultCodeEnums.SUCCESS.getDesc());
            mqProducer.send(SummerfarmMQTopic.SAAS_ORDER_CHANGE_RESULT, SummerfarmMqTag.ORDER_PAY_CHANGE, summerFarmOrderPayResultDTO);
        } catch (Exception e) {
            SummerFarmOrderPayResultDTO summerFarmOrderPayResultDTO = new SummerFarmOrderPayResultDTO();
            summerFarmOrderPayResultDTO.setOrderNo(summerFarmOrderPayResultInput.getOrderNo());
            summerFarmOrderPayResultDTO.setDeliveryTime(summerFarmOrderPayResultInput.getDeliveryTime());
            summerFarmOrderPayResultDTO.setCode(ResultCodeEnums.FAILED.getCode());
            if(e instanceof BizException){
                summerFarmOrderPayResultDTO.setCodeMsg(e.getMessage());
            }else if(e instanceof ProviderException){
                log.error("Saas支付消息确认异常",e);
                summerFarmOrderPayResultDTO.setCodeMsg("调用第三方接口异常");
            }else{
                log.error("Saas支付消息确认异常",e);
                summerFarmOrderPayResultDTO.setCodeMsg("处理异常");
            }
            mqProducer.send(SummerfarmMQTopic.SAAS_ORDER_CHANGE_RESULT,SummerfarmMqTag.ORDER_PAY_CHANGE, summerFarmOrderPayResultDTO);
        }
    }
}
