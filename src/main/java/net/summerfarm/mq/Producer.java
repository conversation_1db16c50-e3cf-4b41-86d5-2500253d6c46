//package net.summerfarm.mq;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.openservices.ons.api.bean.ProducerBean;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.common.util.SpringContextUtil;
//import net.summerfarm.contexts.MQDelayConstant;
//import net.summerfarm.tms.event.TmsDeliveryEvent;
//
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//
///**
// * @Package: net.summerfarm.mq
// * @Description:
// * @author: <EMAIL>
// * @Date: 2017/3/13
// */
//@Service
//@Slf4j
//public class Producer {
//
//    @Resource
//   // private RocketMQTemplate rocketMQTemplate;
//
//    public void sendDataToQueue(String queueKey, String data) {
//       // rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    public void sendDataToQueue(String queueKey, Object data) {
//       // rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    /**
//     * 发送延时消息
//     * @param queueKey
//     * @param data
//     * @param delayLevel
//     * @param startDeliverTime 延迟多久投递 单位ms
//     */
//    public void sendDelayDataToQueue(String queueKey, String data, int delayLevel, long startDeliverTime) {
////        if(SpringContextUtil.isProduct()){
////            // 开发测试环境不存在这个bean所以不才用字段注入的方式
////            ProducerBean producerBean = SpringContextUtil.getApplicationContext().getBean(ProducerBean.class);
////            com.aliyun.openservices.ons.api.Message message =
////                    new com.aliyun.openservices.ons.api.Message(queueKey, "", data.getBytes(StandardCharsets.UTF_8));
////            message.setStartDeliverTime(startDeliverTime);
////            producerBean.send(message);
////        }else{
////            Message message = MessageBuilder.withPayload(data).build();
////            this.rocketMQTemplate.syncSend(queueKey, message, MQDelayConstant.TIMEOUT, delayLevel);
////        }
//    }
//
//    /**
//     * tms消息订单拦截发送
//     * @param tmsDeliveryEvent
//     * @param MQ_QUEUE_KEY
//     */
//    public void notify(TmsDeliveryEvent tmsDeliveryEvent,String MQ_QUEUE_KEY) {
////        log.info("配送消息通知：{}", JSONObject.toJSONString(tmsDeliveryEvent));
////        rocketMQTemplate.convertAndSend(MQ_QUEUE_KEY, tmsDeliveryEvent);
//    }
//}
