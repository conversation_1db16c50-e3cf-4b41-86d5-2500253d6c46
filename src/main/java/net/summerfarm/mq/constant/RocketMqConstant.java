package net.summerfarm.mq.constant;

import net.summerfarm.table.enums.DBTableName;

public class RocketMqConstant {

    public interface Topic {
        String MYSQL_BINLOG_ORDERLY_SUMMERFARM = "mysql-binlog-orderly";
    }

    public interface ConsumerGroup {
        String MYSQL_BINLOG_ORDERLY_SUMMERFARM_MANAGE = "GID_mysql_binlog_orderly_manage";

        /**
         * delivery_plan 表的监听group
         */
        String MYSQL_BINLOG_ORDERLY_DELIVERY_PLAN_SUMMERFARM_MANAGE = "GID_mysql_binlog_delivery_plan_orderly_manage";

    }
}
