package net.summerfarm.mq.item.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/9/1
 */
@Data
public class PriceAdjustmentMsgDTO implements Serializable {

    private static final long serialVersionUID = 525589656313674745L;

    /**
     * 调价单id
     */
    private Integer id;

    /**
     * 生效时间
     */
    private LocalDateTime upTime;

    /**
     * 组id
     */
    private Long businessId;
}
