package net.summerfarm.mq.business.handler;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.mq.business.Business;
import net.summerfarm.service.AdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 20:35
 */
@Service
public class DefaultHandler implements Business {

    private static final Logger logger = LoggerFactory.getLogger(DefaultHandler.class);

    @Resource
    private AdminService adminService;

    @Override
    public void consumptionMessage(JSONObject jsonObject, String messageType) {
        Integer id = jsonObject.getInteger("id");
        Admin select = adminService.select(id);
        logger.info("看看链路通了么!{}----{}" ,jsonObject.toJSONString(),messageType);
        logger.info("看看能不能使用spring容器!{}" ,select.toString());
    }
}
