package net.summerfarm.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.FileDownloadRecordEnum;
import net.summerfarm.mapper.manage.FileDownloadRecordMapper;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.model.input.TeamDataInput;
import net.summerfarm.service.SalesDataService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 任务式调度导出销售团队数据
 */
@Component
@Slf4j
public class SaleDataExportTaskProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;

    @Resource
    private SalesDataService salesDataService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("销售团队数据导出开始:{}", context);
        Set<Long> fileDownloadRecordIdSet = getFileDownloadRecordIdFromParams(context);
        List<FileDownloadRecord> fileDownloadRecordList = Collections.emptyList();
        final FileDownloadRecordEnum type = FileDownloadRecordEnum.SALE_DATA_EXCEL;
        if (CollectionUtils.isEmpty(fileDownloadRecordIdSet)) {
            log.info("没有指定要执行的ID，将从数据库捞出来一批顺序执行");
            fileDownloadRecordList = fileDownloadRecordMapper.selectPendingTasks(type.getType());
        } else {
            log.info("获取指定要执行的ID:{}", fileDownloadRecordIdSet);
            fileDownloadRecordList = fileDownloadRecordMapper.selectByIdCollection(fileDownloadRecordIdSet, type.getType());
        }

        if (CollectionUtils.isEmpty(fileDownloadRecordList)) {
            log.info("没有需要执行的任务");
            return new ProcessResult(InstanceStatus.SUCCESS, "没有需要执行的任务");
        }

        Set<Long> finishedTaskIds = new HashSet<>();
        Set<Long> failedTaskIds = new HashSet<>();
        for (FileDownloadRecord exportTask : fileDownloadRecordList) {
            boolean isSuccess = downloadForRecord(exportTask);
            if (isSuccess) {
                finishedTaskIds.add(exportTask.getId());
            } else {
                log.warn("导出销售数据任务运行失败:{}", exportTask, new Exception("导出销售数据任务运行失败:" + JSON.toJSONString(exportTask)));
                failedTaskIds.add(exportTask.getId());
            }
        }

        return new ProcessResult(InstanceStatus.SUCCESS,
            String.format("执行成功的任务ID:%s, 失败的任务ID:%s", JSON.toJSONString(finishedTaskIds), JSON.toJSONString(failedTaskIds)));
    }

    private boolean downloadForRecord(FileDownloadRecord fileDownloadRecord) {
        log.info("即将开始导出销售数据:{}", fileDownloadRecord);
        if (StringUtils.isBlank(fileDownloadRecord.getParams())) {
            log.warn("任务参数为空:{}", fileDownloadRecord);
            return true;
        }
        TeamDataInput teamDataInput = JSON.parseObject(fileDownloadRecord.getParams(), TeamDataInput.class);
        try {
            salesDataService.orderDataExcelForTask(teamDataInput, fileDownloadRecord.getAdminId(), fileDownloadRecord.getUId());
            return true;
        } catch (Exception e) {
            log.error("导出销售数据失败:{}", fileDownloadRecord, e);
            return false;
        }
    }

    private static Set<Long> getFileDownloadRecordIdFromParams(XmJobInput context) throws Exception {
        if (null == context || StringUtils.isBlank(context.getInstanceParameters())) {
            return Collections.emptySet();
        }
        return Arrays.stream(context.getInstanceParameters().split(",")).map(Long::valueOf).collect(Collectors.toSet());
    }
}
