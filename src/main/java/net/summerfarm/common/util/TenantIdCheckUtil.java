package net.summerfarm.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.contexts.BaseConstant;
import net.xianmu.common.exception.BizException;

import java.util.Objects;

/**
 * @Description
 * @Date 2024/2/2 13:17
 * @<AUTHOR>
 */
@Slf4j
public class TenantIdCheckUtil {

    public static void checkTenantMatch(Long loginTenantId, Long taskTenantId) {
        log.info("租户信息 loginTenantId:{}, taskTenantId:{} ", loginTenantId, taskTenantId);
        if (Objects.isNull(loginTenantId) || Objects.isNull(taskTenantId)) {
            log.error("未获取到租户信息 loginTenantId:{}, taskTenantId:{} \n", loginTenantId, taskTenantId);
            return;
        }
        // 鲜沐后台登录不做校验
        if (BaseConstant.XIANMU_TENANT_ID.equals(loginTenantId)) {
            return;
        }
        if (!loginTenantId.equals(taskTenantId)) {
            throw new BizException("请求单据不存在");
        }

    }
}
