package net.summerfarm.common.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.ArrayUtils;
import com.google.common.collect.Sets;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

import net.summerfarm.common.util.es.dto.EsInsert;
import net.summerfarm.common.util.es.dto.EsUpdate;
import net.summerfarm.es.EsClientPoolUtil;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.BulkByScrollTask;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: <EMAIL>
 * @create: 2022/8/25
 */
public class EsUtil {

    private static final Logger logger = LoggerFactory.getLogger(EsUtil.class);

    private static final long SCROLL_TIMEOUT = 180000;

    private static int SIZE = 1000;

    private static int MAX_BUFFER = 209715200;

    /**
     * 构建SearchResponse
     *
     * @param client     restHighLevelClient
     * @param indices    索引
     * @param query      queryBuilder
     * @param includes   包含的字段
     * @param orderField 排序字段
     * @param order      排序类型
     * @param fun        返回函数
     * @param <T>        返回类型
     * @return List, 可以使用fun转换为T结果
     * @throws Exception
     */
    public static <T> Set<T> searchResponse(RestHighLevelClient client, String[] indices,
            QueryBuilder query, String[] includes, String orderField, SortOrder order,
            Function<SearchHit, T> fun) throws Exception {
        //滚动查询的Scroll
        Scroll scroll = new Scroll(TimeValue.timeValueMillis(SCROLL_TIMEOUT));

        //构建searchRequest
        SearchRequest request = new SearchRequest(indices);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        if (includes != null) {
            //构造器加入需要查找的字段
            sourceBuilder.fetchSource(includes, null);
        }
        //加入query语句
        sourceBuilder.query(query);
        //每次滚动的长度
        sourceBuilder.size(SIZE);
        //加入排序字段
        if (orderField != null && !"".equals(orderField.trim())) {
            sourceBuilder.sort(orderField, order);
        }
        //加入scroll和构造器
        request.scroll(scroll);
        request.source(sourceBuilder);
        //存储scroll的list
        List<String> scrollIdList = new ArrayList<>();
        //返回结果
        SearchResponse searchResponse = client.search(request, RequestOptions.DEFAULT);
        //拿到第一个ScrollId（游标）
        String scrollId = searchResponse.getScrollId();
        //拿到hits结果
        SearchHit[] hits = searchResponse.getHits().getHits();
        //保存返回结果List
        Set<T> result = Sets.newHashSet();
        scrollIdList.add(scrollId);

        try {
            //滚动查询将SearchHit封装到result中
            while (ArrayUtils.isNotEmpty(hits)) {
                for (SearchHit hit : hits) {
                    //Function<SearchHit, T>, 输入SearchHit，经过操作后，返回T结果
                    result.add(fun.apply(hit));
                }
                //说明滚动完了，返回结果即可
                if (hits.length < SIZE) {
                    break;
                }
                //继续滚动，根据上一个游标，得到这次开始查询位置
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                //得到结果
                SearchResponse searchScrollResponse = client.scroll(searchScrollRequest,
                        RequestOptions.DEFAULT);
                //定位游标
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
                scrollIdList.add(scrollId);
            }
        } finally {
            //清理scroll,释放资源
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.setScrollIds(scrollIdList);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        }
        return result;
    }

    /**
     * 聚合查询的SearchResponse
     *
     * @param client
     * @param indices      索引
     * @param query        QueryBuilder
     * @param aggregations AggregationBuilder
     * @return SearchResponse
     * @throws Exception
     */

    public static SearchResponse searchResponse(RestHighLevelClient client, String[] indices,
            QueryBuilder query, AggregationBuilder... aggregations) throws Exception {
        //构建request请求
        SearchRequest request = new SearchRequest(indices);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(query);
        //加入Agg
        if (aggregations != null && aggregations.length > 0) {
            for (AggregationBuilder aggregation : aggregations) {
                sourceBuilder.aggregation(aggregation);
            }
        }
        sourceBuilder.size(0);
        //忽略不可用索引，只用于开放索引
        request.indicesOptions(IndicesOptions.lenientExpandOpen());
        request.source(sourceBuilder);
        return client.search(request, RequestOptions.DEFAULT);
    }

    /**
     * 根据文档id删除文档
     * @param esUpdate 索引名,文档id
     */
    public static boolean delDoc(EsUpdate esUpdate) throws Exception {
        esUpdate = Optional.ofNullable(esUpdate).orElse(new EsUpdate());

        logger.info("开始删除es文档数据,索引:{},文档id:{}",esUpdate.getIndexName(),esUpdate.getDocumentId());
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
            DeleteRequest request = new DeleteRequest(esUpdate.getIndexName(), esUpdate.getDocumentId());
            DeleteResponse deleteResponse = client.delete(request, RequestOptions.DEFAULT);
            RestStatus status = deleteResponse.status();
            logger.info("删除结束,索引:{},文档id:{},状态码:{}",esUpdate.getIndexName(),esUpdate.getDocumentId(),status.getStatus());
            return true;
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
    }

    /**
     * 删除索引
     * @param indexName 索引名
     */
    public static void delAllDoc(String indexName) throws Exception {
        logger.info("开始删除es文档数据,索引:{}",indexName);
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
            DeleteByQueryRequest deleteRequest = new DeleteByQueryRequest(indexName);
            deleteRequest.setQuery(new MatchAllQueryBuilder());
            BulkByScrollResponse deleteByQuery = client.deleteByQuery(deleteRequest, RequestOptions.DEFAULT);
            BulkByScrollTask.Status status = deleteByQuery.getStatus();
            logger.info("删除结束,索引:{},总数:{}",indexName,status.getTotal());
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
    }

    /**
     * 批量新增文档
     * @param esInsert 新增内容
     * @return 成功与否
     */
    public static void batchAddDoc(EsInsert esInsert) throws Exception {
        esInsert = Optional.ofNullable(esInsert).orElse(new EsInsert());
        String indexName = esInsert.getIndexName();
        logger.info("开始批量新增文档,索引:{}",esInsert.getIndexName());
        RestHighLevelClient client = null;

        try {
            client = EsClientPoolUtil.getClient();
            BulkRequest request = new BulkRequest();

            for (Map.Entry<String, Object> entry : esInsert.getJsonList().entrySet()){
                String merchantIndexInfoJsonStr = JSON.toJSONString(entry.getValue());

                request.add(new IndexRequest(indexName)
                        .id(entry.getKey())
                        .source(merchantIndexInfoJsonStr, XContentType.JSON));
            }

            BulkResponse bulk = client.bulk(request, RequestOptions.DEFAULT);
            // 是否失败
            // 是否失败
            if(bulk.hasFailures()){
                logger.error("批量插入文档失败,失败信息:{}",bulk.buildFailureMessage());
            }
            logger.info("批量插入文档结束,索引:{},更新数量:{}",esInsert.getIndexName(),esInsert.getJsonList().size());
        }  finally {
            EsClientPoolUtil.returnClient(client);
        }
    }

    /**
     * 实体类转Map
     * @param object 对象
     * @return map
     */
    public static Map<String, Object> entityToMap(Object object) {
        Map<String, Object> map = new HashMap<>(1);

        Field[] declaredFields = object.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(), o);
                field.setAccessible(flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return map;
    }

}
