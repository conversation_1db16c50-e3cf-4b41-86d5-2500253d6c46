package net.summerfarm.common.util;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.Global;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class MathUtil {
    private static final Logger logger = LoggerFactory.getLogger(MathUtil.class);

    /**
     * 计算标准差
     */
    public static BigDecimal stDev(List<BigDecimal> nums){
        return BigDecimal.valueOf(Math.sqrt(variance(nums).doubleValue()));
    }

    /**
     * 计算方差
     */
    public static BigDecimal variance(List<BigDecimal> nums){
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal num: nums){
            sum = sum.add(num);
        }
        BigDecimal avg = sum.divide(BigDecimal.valueOf(nums.size()),2,BigDecimal.ROUND_HALF_EVEN);
        BigDecimal powSum = BigDecimal.ZERO; //平方之和
        for (BigDecimal num: nums){
            double avgDiff = Math.pow(avg.subtract(num).doubleValue(),2);
            powSum = powSum.add(BigDecimal.valueOf(avgDiff));
        }
        return powSum.divide(BigDecimal.valueOf(nums.size()),2,BigDecimal.ROUND_HALF_EVEN);
    }

    //正态分布反函数
    public static BigDecimal inverseCumulativeProbability(BigDecimal rate){
        List<String> params = new ArrayList<>();
        params.add("ppf");
        params.add(rate.toString());
        return invoke(params);
    }

    public static BigDecimal invoke(List<String> params) {
        try {
            String[] cmdarray = new String[params.size() + 2];
            cmdarray[0] = "python3";
            cmdarray[1] = "/root/mathUtil.py";
            for (int i = 0; i < params.size(); i++) {
                cmdarray[i + 2] = params.get(i);
            }
            Process process = Runtime.getRuntime().exec(cmdarray);
            BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
            StringBuffer data = new StringBuffer();
            String line;
            while ((line = in.readLine()) != null) {
                data.append(line);
            }
            in.close();
            return BigDecimal.valueOf(Double.valueOf(data.toString()));
        } catch (Exception e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("invoke python error");
        }
    }

    //计算和
    public static double sum(List<Integer> list){
        double sum = 0;
        for (Integer integer : list) {
            sum += integer;
        }
        return sum;
    }

    //求平均值
    public static double avg(List<Integer> list){
        return sum(list) / list.size();
    }

    //求标准差
    public static double standardDeviation(List<Integer> list){
        double sum = 0;
        double meanValue = avg(list);                //平均数
        for (Integer integer : list) {
            sum += Math.pow(integer - meanValue, 2);
        }
        return Math.sqrt(sum/list.size());
    }

    //求中位数
    public static double median(List<Double> total) {
        double j = 0;
        //集合排序
        Collections.sort(total);
        int size = total.size();
        if (size % 2 == 1) {
            j = total.get((size - 1) / 2);
        } else {
            j = (total.get(size / 2 - 1) + total.get(size / 2) + 0.0) / 2;
        }
        return j;
    }

    /**
     * 计算
     * @param quantity
     * @param oldQuantity
     * @return
     */
    public static Integer sumQuantity(Integer quantity, Integer oldQuantity){
        Integer addend = Objects.isNull(quantity) ? 0 : quantity;
        Integer oldAddend = Objects.isNull(oldQuantity) ? 0 : oldQuantity;
        return addend + oldAddend;
    }

    /**
     * 判断当前值是否在指定区间
     * before<=current<=after
     *
     * @param current 当前值
     * @param before  前区间
     * @param after   后区间
     * @return 是/否
     */
    public static boolean between(Integer current, Integer before, Integer after) {
        return Math.max(before, current) == Math.min(current, after);
    }
}
