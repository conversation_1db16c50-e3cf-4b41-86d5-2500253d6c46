package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductPriceAdjustmentDO {

    private Long id;

    private String pdName;

    private String sku;

    /**
     * 规格
     */
    private String weight;

    /**
     * 生效时间
     */
    private LocalDateTime upTime;

    /**
     * 理由
     */
    private String reason;

    /**
     * 状态，1待处理，2待审批，3待执行，4已执行，5不调整，6超时未审批，7审核不通过
     */
    private Integer status;

    /**
     * 发起人name，系统记为自动
     */
    private String createAdminName;

    /**
     * 审核人
     */
    private String auditAdminName;
}
