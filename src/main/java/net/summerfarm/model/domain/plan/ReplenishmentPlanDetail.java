package net.summerfarm.model.domain.plan;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ReplenishmentPlanDetail {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 补货计划id
     */
    private Long replenishmentPlanId;

    /**
     * 补货计划编号
     */
    private String replenishmentPlanNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 日期
     */
    private LocalDate viewDate;

    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 需求计划数量
     */
    private BigDecimal requirementPlanQuantity;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Integer updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 商品名称
     * fixme liyu,此字段应该要被废弃,但是补货计划分页接口筛选还是从这里查的
     */
    private String pdName;

    /**
     * 子单主键
     */
    private String replenishmentPlanSubNo;

    /**
     * 调拨单订单口径在途
     */
    private BigDecimal transferOrderInQuantity;

    /**
     * 采购订单口径在途
     */
    private BigDecimal poOnWayQuantity;

    /**
     * 小规格需求量
     */
    private BigDecimal smallSkuRequireQuantity;
}