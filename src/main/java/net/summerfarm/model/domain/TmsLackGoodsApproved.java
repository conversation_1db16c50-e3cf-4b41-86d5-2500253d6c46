package net.summerfarm.model.domain;

import cn.hutool.core.date.DateTime;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.summerfarm.common.util.LocalDateTimeConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TmsLackGoodsApproved {
    //任务编号
    @ExcelProperty(value = "任务编号",index = 0)
    private Long id;
    //城配仓
    @ExcelIgnore
    private Integer storeNo;
    ///库存仓
    @ExcelIgnore
    private Integer warehouseNo;

    @ExcelIgnore
    private Integer deliveryPathId;
    //sku
    @ExcelProperty(value = "sku",index = 8)
    private String sku;

    @ExcelIgnore
    private Integer mId;

    @ExcelProperty(value = "缺货数量",index = 10)
    private Integer lackNum;

    @ExcelIgnore
    private BigDecimal money;

    //缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它
    @ExcelProperty(value = "缺货类型",index = 12)
    private String lackType;

    @ExcelProperty(value = "备注",index = 13)
    private String remark;

    @ExcelIgnore
    private Integer responsible;

    //状态 1 待核准 2待判责 3 已完成
    @ExcelIgnore
    private Integer state;

    @ExcelIgnore
    private Integer buyOut;

    @ExcelProperty(value = "买赔金额",index = 16)
    private BigDecimal buyOutMoney;

    @ExcelProperty(value = "判责意见",index = 17)
    private String judgmentOpinion;

    @ExcelIgnore
    private Integer stockTaskId;

    @ExcelIgnore
    private String pic;

    @ExcelIgnore
    private LocalDateTime createTime;

    @ExcelIgnore
    private LocalDateTime updateTime;

    @ExcelIgnore
    private Integer approvedAdminId;

    @ExcelIgnore
    private LocalDateTime approvedTime;

    @ExcelIgnore
    private Integer responsibilityAdminId;

    @ExcelIgnore
    private LocalDateTime responsibilityTime;

    @ExcelProperty(value = "配送完成时间",index = 6,converter = LocalDateTimeConverter.class)
    private LocalDateTime finishTime;

    @ExcelIgnore
    private String orderNo;

    @ExcelIgnore
    private Integer amount;

    @ExcelIgnore
    private Integer stockLackNum;
}