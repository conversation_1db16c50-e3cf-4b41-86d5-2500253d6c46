package net.summerfarm.model.domain.saas;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class CancleAfterSale {
    /**
     * 要取消的售后单号
     */
    private String orderNo;

    /**
     * 关闭售后单时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;
}