package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* 主子单关联关系表
* @TableName order_relation
*/
@Data
public class OrderRelation implements Serializable {

    /**
    * primary key
    */
    private Long id;
    /**
    * 主订单号
    */
    private String masterOrderNo;
    /**
    * 子订单号
    */
    private String orderNo;
    /**
    * create time
    */
    private LocalDateTime createTime;
    /**
    * update time
    */
    private LocalDateTime updateTime;
    /**
    * 精准送费用
    */
    private BigDecimal precisionDeliveryFee;

    private static final long serialVersionUID = 1L;
}
