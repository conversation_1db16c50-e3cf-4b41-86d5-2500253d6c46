package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import io.swagger.models.auth.In;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.CharLength;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "商品SKU实体类")
public class Inventory implements Serializable {


    @ApiModelProperty(value = "id")
    @Null(message = "invertory.invId", groups = {Add.class})
    @NotNull(message = "invertory.aitId",groups = {Update.class})
    private Long invId;

    @ApiModelProperty(value = "sku编号")
    @Null(message = "invertory.invId", groups = {Add.class})
    @NotNull(message = "invertory.aitId.null", groups = {Update.class})
    private String sku;

    @ApiModelProperty(value = "属性ID")
    private Integer aitId;

    @ApiModelProperty(value = "pdId")
    @NotNull(message = "pdId.null", groups = {Add.class})
    private Long pdId;

    @ApiModelProperty(value = "销售模式")
    private Integer salesMode; //销售模式

    @ApiModelProperty(value = "产地")
    private String origin;  //产地

    @ApiModelProperty(value = "包装")
    @NotNull(message = "包装不能为空", groups = {Add.class,Update.class})
    private String unit;    //包装

    @ApiModelProperty(value = "包数")
    private String pack;

    @ApiModelProperty(value = "租户ID：1-鲜沐")
    private Long tenantId;

    @ApiModelProperty(value = "上新类型：0、平台 1、大客户 2、帆台代仓")
    private String createType;

    @ApiModelProperty(value = "规格")
    @NotNull(message = "weight.null", groups = {Add.class,Update.class})
    @CharLength(max = 50,groups = {Add.class,Update.class},message = "weight.length")
    private String weight; //规格

    @ApiModelProperty(value = "规格备注")
    private String weightNotes;

    @ApiModelProperty(value = "是否为国产，0：不是，1是")
    private Integer isDomestic;

    @ApiModelProperty(value = "体积")
    private String volume;

    @ApiModelProperty(value = "重量")
    @Min(value = 0, message = "重量必须大于0", groups = {Add.class, Update.class})
    private BigDecimal weightNum;

    @ApiModelProperty(value = "有效期")
    private String expiryDate;  //有效期

    @ApiModelProperty(value = "是否展示")
    private Boolean show;

    @ApiModelProperty(value = "生熟度")
    private String maturity;    //

    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "贮存区域")
    private String storageMethod;

    @ApiModelProperty(value = "销售价")
    @Min(value = 0, message = "invertory.too.min", groups = {Add.class, Update.class})
    private BigDecimal salePrice;

    @ApiModelProperty(value = "促销价")
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "商品介绍")
    private String introduction;

    @ApiModelProperty(value = "标记位-过时的sku")
    private Integer outdated;

    @ApiModelProperty(value = "售后最大数量")
    @Min(value = 1, message = "最大售后量不能小于1", groups = {Add.class, Update.class})
    private Integer afterSaleQuantity;

    @ApiModelProperty(value = "最小起售量")
    @Min(value = 1, message = "最小起售量不能小于1", groups = {Add.class, Update.class})
    private Integer baseSaleQuantity;

    @ApiModelProperty(value = "售卖规格")
    @Min(value = 1, message = "起售规格不能小于1", groups = {Add.class, Update.class})
    private Integer baseSaleUnit;

    @ApiModelProperty(value = "类型 0 自营 1 代仓")
    private Integer type;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓、5 鲜果pop
     */
    private Integer subType;

    @ApiModelProperty(value = "所属/大客户ID")
    private Integer adminId;

    @ApiModelProperty(value = "是否放入样品池 ")
    @NotNull(message = "是否放入样品池不能为空", groups = {Add.class,Update.class})
    private Integer samplePool;

    @ApiModelProperty(value = "sku头图")
    private String skuPic;

    @ApiModelProperty(value = "售后单位")
    @NotNull(message = "售后单位不能为空", groups = {Add.class,Update.class})
    private String afterSaleUnit;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "供应商是否可见：0不可见，1可见")
    private Integer supplierVisible;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;


    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     * @see net.summerfarm.enums.InventoryExtTypeEnum#NORMAL
     * @see net.summerfarm.enums.InventoryExtTypeEnum#ACTIVITY
     * @see net.summerfarm.enums.InventoryExtTypeEnum#TEMPORARY_INSURANCE
     * @see net.summerfarm.enums.InventoryExtTypeEnum#UNPACKING
     * @see net.summerfarm.enums.InventoryExtTypeEnum#NOT_SALE
     * @see net.summerfarm.enums.InventoryExtTypeEnum#BROKEN_BAG
     */
    private Integer extType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 任务类型：0、SPU 1、SKU
     */
    private Integer taskType;

    /**
     * 工商名称
     */
    private String realName;

    /**
     * 审核人adminId
     */
    private Integer auditor;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 净重
     */
    private String netWeightUnit;

    /**
     * 视频链接
     */
    private String videoUrl;
    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值
     */
    private Integer minAutoAfterSaleThreshold;

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public BigDecimal getNetWeightNum() {
        return netWeightNum;
    }

    public void setNetWeightNum(BigDecimal netWeightNum) {
        this.netWeightNum = netWeightNum;
    }

    public String getNetWeightUnit() {
        return netWeightUnit;
    }

    public void setNetWeightUnit(String netWeightUnit) {
        this.netWeightUnit = netWeightUnit;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getAfterSaleRuleDetail() {
        return afterSaleRuleDetail;
    }

    public void setAfterSaleRuleDetail(String afterSaleRuleDetail) {
        this.afterSaleRuleDetail = afterSaleRuleDetail;
    }

    public Integer getSupplierVisible() {
        return supplierVisible;
    }

    public void setSupplierVisible(Integer supplierVisible) {
        this.supplierVisible = supplierVisible;
    }

    public Integer getAveragePriceFlag() {
        return averagePriceFlag;
    }

    public void setAveragePriceFlag(Integer averagePriceFlag) {
        this.averagePriceFlag = averagePriceFlag;
    }

    public String getWeightNotes() {
        return weightNotes;
    }

    public void setWeightNotes(String weightNotes) {
        this.weightNotes = weightNotes;
    }
    public String getCreateType() {
        return createType;
    }

    public void setCreateType(String createType) {
        this.createType = createType;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getAfterSaleUnit() {
        return this.afterSaleUnit;
    }

    public void setAfterSaleUnit(String afterSaleUnit) {
        this.afterSaleUnit = afterSaleUnit;
    }

    public Integer getBaseSaleUnit() {
        return baseSaleUnit;
    }

    public void setBaseSaleUnit(Integer baseSaleUnit) {
        this.baseSaleUnit = baseSaleUnit;
    }

    public Integer getBaseSaleQuantity() {
        return baseSaleQuantity;
    }

    public void setBaseSaleQuantity(Integer baseSaleQuantity) {
        this.baseSaleQuantity = baseSaleQuantity;
    }

    public Integer getAfterSaleQuantity() {
        return afterSaleQuantity;
    }

    public void setAfterSaleQuantity(Integer afterSaleQuantity) {
        this.afterSaleQuantity = afterSaleQuantity;
    }

    public Integer getOutdated() {
        return outdated;
    }

    public void setOutdated(Integer outdated) {
        this.outdated = outdated;
    }

    public Long getInvId() {
        return invId;
    }

    public void setInvId(Long invId) {
        this.invId = invId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Integer getAitId() {
        return aitId;
    }

    public void setAitId(Integer aitId) {
        this.aitId = aitId;
    }

    public Long getPdId() {
        return pdId;
    }

    public void setPdId(Long pdId) {
        this.pdId = pdId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin == null ? null : origin.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getPack() {
        return pack;
    }

    public void setPack(String pack) {
        this.pack = pack == null ? null : pack.trim();
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight == null ? null : weight.trim();
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getStorageMethod() {
        return storageMethod;
    }

    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod == null ? null : storageMethod.trim();
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }


    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Integer getSalesMode() {
        return salesMode;
    }

    public void setSalesMode(Integer salesMode) {
        this.salesMode = salesMode;
    }

    public String getMaturity() {
        return maturity;
    }

    public void setMaturity(String maturity) {
        this.maturity = maturity;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Boolean getShow() {
        return show;
    }

    public void setShow(Boolean show) {
        this.show = show;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getSamplePool() {
        return samplePool;
    }

    public void setSamplePool(Integer samplePool) {
        this.samplePool = samplePool;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public String getCreateRemark() {
        return createRemark;
    }

    public void setCreateRemark(String createRemark) {
        this.createRemark = createRemark;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getRefuseReason() {
        return this.refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    @Override
    public String toString() {
        return "Inventory{" +
                ", sku='" + sku + '\'' +
                ", aitId=" + aitId +
                ", pdId=" + pdId +
                ", salesMode=" + salesMode +
                ", origin='" + origin + '\'' +
                ", unit='" + unit + '\'' +
                ", pack='" + pack + '\'' +
                ", weight='" + weight + '\'' +
                ", expiryDate='" + expiryDate + '\'' +
                ", show=" + show +
                ", maturity='" + maturity + '\'' +
                ", productionDate=" + productionDate +
                ", storageMethod='" + storageMethod + '\'' +
                ", salePrice=" + salePrice +
                ", promotionPrice=" + promotionPrice +
                ", introduction='" + introduction + '\'' +
                ", outdated=" + outdated +
                ", afterSaleQuantity=" + afterSaleQuantity +
                ", type=" + type +
                ", adminId=" + adminId +
                '}';
    }

    public void setVideoInfo(String videoUrl, String adminName) {
        if(StringUtils.isNotBlank (videoUrl)){
            this.setVideoUploadTime (LocalDateTime.now ());
            this.setVideoUploadUser (adminName);
        }
    }
}
