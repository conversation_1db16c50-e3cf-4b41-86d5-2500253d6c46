package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/6/29  17:05
 */
@Data
public class ContactRecord {

    private Integer id;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改地址申请id")
    private Integer contactAdjustId;

    @ApiModelProperty(value = "省份")
    private String oldProvince;

    @ApiModelProperty(value = "城市")
    private String oldCity;

    @ApiModelProperty(value = "区域")
    private String oldArea;

    @ApiModelProperty(value = "地址")
    private String oldAddress;

    @ApiModelProperty(value = "地址")
    private String oldPoiNote;


    @ApiModelProperty(value = "门牌号")
    private String oldHouseNumber;



}
