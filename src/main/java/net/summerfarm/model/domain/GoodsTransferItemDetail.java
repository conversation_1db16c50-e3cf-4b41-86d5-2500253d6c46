package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2020/4/1  15:46
 */
@Data
public class GoodsTransferItemDetail {

    private Integer id;

    /**
    * 货位转移ID
    */
    private Integer goodsTransferItemId;

    /**
    * 原货位
    */
    private String originGlNo;

    /**
     * 批次
     */
    private String batch;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 剩余数量
     */
    private Integer quantity;

    /**
    * 转移数量
    */
    private Integer transferQuantity;

    /**
     * 新货位
     */
    private String newGlNo;
}
