package net.summerfarm.model.domain.bms;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @describe 区间计费模型
 * @date 2023/06/19 17:04:58
 */
@Data
public class BmsDeliveryQuotaCalculateCostRange {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 计费模型主键
     */
    private Integer quoteCalculateCostId;

    /**
     * 类目id
     */
    private Long itemDetailId;

    /**
     * 下限
     */
    private BigDecimal lowerLimit;

    /**
     * 上限
     */
    private BigDecimal upperLimit;

    /**
     * 公式
     */
    private String formula;

    /**
     * 计费模型名称
     */
    private String quoteCalculateCostName;
}