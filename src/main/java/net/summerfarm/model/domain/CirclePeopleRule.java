package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * circle_people_rule
 * <AUTHOR>
@Data
public class CirclePeopleRule implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 0上传execl
     */
    private Integer type;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 错误报告
     */
    private String errorReport;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}