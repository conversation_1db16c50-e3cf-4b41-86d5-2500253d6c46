package net.summerfarm.model.domain;

import java.time.LocalDateTime;
import lombok.Data;

/**
    * 营销-拼团订单表
    */
@Data
public class PartnershipBuyOrder {
    /**
    * 主键、自增
    */
    private Long id;

    /**
    * 拼团id(market_partnership_buy.id)
    */
    private Long partnershipBuyId;

    /**
    * 拼团配置id(market_partnership_buy_config.id)
    */
    private Long configId;

    /**
    * 拼团成员(m_id)
    */
    private Long mId;

    /**
    * 拼团成员类型:0-团员 1-团长
    */
    private Integer roleType;

    /**
    * 订单编号
    */
    private String orderNo;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;
}