package net.summerfarm.model.domain;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 收款单列表
 *
 * <AUTHOR>
 * @Date 2023/5/25 16:52
 */
@Data
public class SaveReceiptListInput implements Serializable {
    private static final long serialVersionUID = 3461691248532653342L;

    /**
     * 账单id
     */
    @NotNull(message = "来源id不能为空")
    private Long financeOrderId;

    /**
     * 来源单号
     */
    @NotEmpty(message = "来源单号不能为空")
    private String sourceNo;

    /**
     * 收款总额
     */
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.00", message = "金额不能小于0")
    private BigDecimal receiptAmount;

}
