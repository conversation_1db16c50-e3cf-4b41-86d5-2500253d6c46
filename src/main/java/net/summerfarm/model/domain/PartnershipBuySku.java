package net.summerfarm.model.domain;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 营销-多人拼团配置商品表
 */
@Data
public class PartnershipBuySku {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 拼团配置id(market_partnership_buy_config.id)
     */
    private Long configId;

    /**
     * 商品编号
     */
    private String sku;

    /**
     * 起购量
     */
    private Integer minSaleNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updater;
}