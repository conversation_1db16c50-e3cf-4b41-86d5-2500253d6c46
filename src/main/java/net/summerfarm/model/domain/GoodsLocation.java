package net.summerfarm.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2020/3/23  14:56
 * 货位
 */
@Data
public class GoodsLocation {

    private Integer id;

    /**
    * 仓库编码
    */
    private Integer storeNo;

    /**
    * 货位号编码
    */
    private String  glNo;

    /**
    * 添加时间
    */
    private Date addTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 温区 常温，冷藏，冷冻，对应的编码为3,2,1
    */
    private Integer temperature;

    /**
    * 通道
    */
    private String passageway;

    /**
    * 类型 0 储货位 1 暂存位 2 捡货位
    */
    private Integer type;
}
