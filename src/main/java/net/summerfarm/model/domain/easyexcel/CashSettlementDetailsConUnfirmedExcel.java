package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 现结明细截至本期末已收款未退款未送达订单明细sheeteasyexcel实体类
 */
@Data
@EqualsAndHashCode
public class CashSettlementDetailsConUnfirmedExcel {

    @ColumnWidth(50)
    @ExcelProperty(value = "订单编号", index = 0)
    private String orderNo;

    @ColumnWidth(50)
    @ExcelProperty(value = "所属省市", index = 1)
    private String city;

    @ColumnWidth(50)
    @ExcelProperty(value = "资金去向", index = 2)
    private String whereaboutsOfFunds;

    @ColumnWidth(50)
    @ExcelProperty(value = "支付类型", index = 3)
    private String paymentType;

    @ColumnWidth(50)
    @ExcelProperty(value = "门店名称", index = 4)
    private String mname;

    @ColumnWidth(50)
    @ExcelProperty(value = "大客户工商名称", index = 5)
    private String supplierName;

    @ColumnWidth(50)
    @ExcelProperty(value = "大客户品牌名称", index = 6)
    private String nameRemarks;

    @ColumnWidth(50)
    @ExcelProperty(value = "下单时间", index = 7)
    private String orderTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "支付时间", index = 8)
    private String payTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "银行收款时间", index = 9)
    private String bankCollectionTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "计划配送时间", index = 10)
    private String planTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "超时加单费用", index = 11)
    private String outTimesFee;

    @ColumnWidth(50)
    @ExcelProperty(value = "配送费", index = 12)
    private String deliveryFee;

    @ColumnWidth(50)
    @ExcelProperty(value = "司机送达时间", index = 13)
    private String revenueRecognitionTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "skuId", index = 14)
    private String sku;

    @ColumnWidth(50)
    @ExcelProperty(value = "商品名称", index = 15)
    private String pdName;

    @ColumnWidth(50)
    @ExcelProperty(value = "商品经营类型", index = 16)
    private String operationType;

    @ColumnWidth(50)
    @ExcelProperty(value = "税率", index = 17)
    private String taxRate;

    @ExcelProperty(value = "下单数量", index = 18)
    private String quantity;

    @ColumnWidth(50)
    @ExcelProperty(value = "实付总价", index = 19)
    private String priceAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "应付总价", index = 20)
    private String totalPricePayable;

    @ColumnWidth(50)
    @ExcelProperty(value = "营销费用", index = 21)
    private String marketingExpenses;

}
