package net.summerfarm.model.domain;

import lombok.Data;
import net.summerfarm.biz.finance.enums.FinancialInvoiceAsyncTaskEnum;

import java.time.LocalDateTime;

/**
* @description: 发票异步任务接口
* @author: George
* @date: 2024-05-10
**/
@Data
public class FinancialInvoiceAsyncTask {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 类型 0、蓝字 1、红冲 2、作废 3、下载 4、红字发票准备 5、作废红字发票准备 6、查询红字发票
     * @see FinancialInvoiceAsyncTaskEnum.TaskType
    */
    private Integer type;

    /**
    * 发票id
    */
    private Long invoiceId;

    /**
    * 进度:0:初始化 1:已完成 2:失败
     * @see FinancialInvoiceAsyncTaskEnum.Status
    */
    private Integer taskResult;

    /**
    * 执行次数
    */
    private Integer invokeCount;

    /**
    * 执行参数
    */
    private String invokeParams;

    /**
     * 百旺返回的异步任务id
     */
    private String taskId;
}