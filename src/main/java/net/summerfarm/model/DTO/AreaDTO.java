package net.summerfarm.model.DTO;

import lombok.Data;

@Data
public class AreaDTO {

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 规则ID
     */
    private Integer ruleId;

    /**
     * 业务线0=鲜沐;1=pop
     */
    private Integer businessLine;

    /**
     * 范围id （人群包：merchant_pool_info主键ID，运营城市：areaNo，运营大区：largeAreaNo）
     */
    private Long scopeId;

    /**
     * 活动范围类型，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;
}
