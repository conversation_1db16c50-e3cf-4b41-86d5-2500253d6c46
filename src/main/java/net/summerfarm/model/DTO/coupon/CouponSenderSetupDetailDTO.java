package net.summerfarm.model.DTO.coupon;

import lombok.Data;
import net.summerfarm.enums.market.activity.ScopeTypeEnum;
import net.summerfarm.model.domain.Coupon;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡券发放设置详情页面展示数据
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/24
 */
@Data
public class CouponSenderSetupDetailDTO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 设置名称
     */
    private String name;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 配置规则类型 1-圈人 2-城市 3-运营大区
     * @see ScopeTypeEnum
     */
    private Integer type;

    /**
     * 发放方式 1-需用户领取 2-新人注册后立即发放 3-推荐好友下单，确定收货后立即发放
     * @see net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum
     */
    private Integer senderType;

    /**
     * 有效状态 0-未生效 1-有效 2-无效
     */
    private Integer status;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 发放规则集合
     */
    private List<CouponSenderRuleDTO> couponSenderRuleDTOS;

    /**
     * 优惠劵数据 -- 命名不太准确，历史原因导致
     */
    private List<Coupon> couponIds;
}
