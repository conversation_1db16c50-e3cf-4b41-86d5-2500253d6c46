package net.summerfarm.model.DTO.market;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import net.summerfarm.common.valid.group.market.ActivityCreate;
import net.summerfarm.common.valid.group.market.SkuConfig;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
public class ActivityLadderConfigDTO implements Serializable {

    /**
     * 阶梯数
     */
    @NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请选择数量")
    private Integer unit;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整（定价方式）
     */
    @NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请选择定价方式")
    private Integer roundingMode;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    @NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请选择价格调整方式")
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    @NotNull(groups = {ActivityCreate.class, SkuConfig.class}, message = "请填写调价幅度")
    private BigDecimal amount;




    public static String initDefaultLadderConfig(BigDecimal amount, Integer roundingMode, Integer adjustType){
        List<ActivityLadderConfigDTO> result = new ArrayList<>();
        ActivityLadderConfigDTO dto = new ActivityLadderConfigDTO();
        dto.setAmount(amount);
        dto.setRoundingMode(roundingMode);
        dto.setAdjustType(adjustType);
        dto.setUnit(1);
        result.add(dto);
        return JSON.toJSONString(result);
    }
}
