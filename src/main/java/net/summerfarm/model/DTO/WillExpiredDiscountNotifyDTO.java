package net.summerfarm.model.DTO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WillExpiredDiscountNotifyDTO implements Serializable {

    /**
     * 上期临保计算最小折扣
     */
    private BigDecimal minOldDiscount;

    /**
     * 上期临保计算最大折扣
     */
    private BigDecimal maxOldDiscount;

    /**
     * 最新临保计算最小折扣
     */
    private BigDecimal minDiscount;

    /**
     * 最新临保计算最大折扣
     */
    private BigDecimal maxDiscount;

    private BigDecimal minCalPrice;

    private BigDecimal maxCalPrice;

    private Boolean isTrigger;

    public WillExpiredDiscountNotifyDTO(BigDecimal minOldDiscount,BigDecimal maxOldDiscount,BigDecimal minDiscount,
                                        BigDecimal maxDiscount,BigDecimal minCalPrice,BigDecimal maxCalPrice,Boolean isTrigger){
        this.minOldDiscount = minOldDiscount;
        this.maxOldDiscount = maxOldDiscount;
        this.minDiscount = minDiscount;
        this.maxDiscount = maxDiscount;
        this.minCalPrice = minCalPrice;
        this.maxCalPrice = maxCalPrice;
        this.isTrigger = isTrigger;
    }
}
