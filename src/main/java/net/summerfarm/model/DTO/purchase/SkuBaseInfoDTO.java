package net.summerfarm.model.DTO.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SkuBaseInfoDTO implements Serializable {

    private static final long serialVersionUID = 2159429295836357434L;

    private Long invId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 商品id
     */
    private Long pdId;
    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * 规格
     */
    private String weight;


    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    private String packaging;
    private Integer storageArea;
    private Integer categoryId;
    /**
     * 类型 0 自营 1 代仓
     * @see net.summerfarm.enums.InventoryTypeEnum
     */
    private Integer pdAttribute;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     * @see net.summerfarm.enums.InventoryExtTypeEnum
     */
    private Integer extType;

    /**
     * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
     */
    private Integer outdated;

    /**
     * 所属大客户ID
     */
    private Integer adminId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku图片
     */
    private String skuPic;

    private Integer isDomestic;
}
