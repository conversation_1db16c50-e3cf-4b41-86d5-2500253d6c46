package net.summerfarm.model.DTO.inventory;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;

/**
 * @author: <EMAIL>
 * @create: 2023/1/9
 */
@Data
public class DynamicPriceTaskQueryDTO extends BasePageInput implements Serializable {

    /**
     * 类目类型
     */
    private Integer categoryType;

    /**
     * 库存仓编号
     */
    private List<Integer> warehouseNos;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
