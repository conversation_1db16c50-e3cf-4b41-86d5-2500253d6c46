package net.summerfarm.model.vo;

import net.summerfarm.common.util.validation.annotation.InRange;

import java.math.BigDecimal;

/**
 * @Package: net.summerfarm.mall.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
public class ProductVO {

    private String sku;

    //销售模式
    private int salesMode;

    //限购数量
    private int limitedQuantity;

    //库存数量
    private int quantity;

    private Integer pdId;

    //商品名称
    private String pdName;

    //商品类别
    private int categoryId;

    //详情
    private String pddetail;

    //单位
    private String unit;

    private String maturity;

    private String origin;

    private String picture_path;


    private String picturePath;

    private String brand;

    private String pack;

    private String weight;

    private Double sale_price;

    private Double costPrice;

    private int on_sale;

    private String introduction;

    private Integer priority;

    private Integer sellOut;

    private String detailPicture;

    //-------Detail part
    private String slogan;

    private String otherSlogan;

    private String storageMethod;

    private String detailpicture1_path;

    private String detailpicture2_path;

    private String detailpicture3_path;

    @InRange(rangeNums = {0,1,2,3,4})  //0，未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通
    private Integer storageLocation;

    private Boolean share;

    private Integer baseSaleUnit;

    private Integer baseSaleQuantity;

    private String volume;

    private BigDecimal weightNum;


    private Integer type;

    /**
     * 1 全部,2乳制品,3其他,4水果
     */
    private Integer categoryType;

    private String category;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期单位
     */
    private String qualityTimeUnit;

    private String subType;

    public Integer getQualityTime() {
        return qualityTime;
    }

    public void setQualityTime(Integer qualityTime) {
        this.qualityTime = qualityTime;
    }

    public String getQualityTimeUnit() {
        return qualityTimeUnit;
    }

    public void setQualityTimeUnit(String qualityTimeUnit) {
        this.qualityTimeUnit = qualityTimeUnit;
    }

    public Integer getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public Integer getBaseSaleUnit() {
        return baseSaleUnit;
    }

    public void setBaseSaleUnit(Integer baseSaleUnit) {
        this.baseSaleUnit = baseSaleUnit;
    }

    public Integer getBaseSaleQuantity() {
        return baseSaleQuantity;
    }

    public void setBaseSaleQuantity(Integer baseSaleQuantity) {
        this.baseSaleQuantity = baseSaleQuantity;
    }


    public Boolean getShare() {
        return share;
    }

    public void setShare(Boolean share) {
        this.share = share;
    }

    public Integer getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(Integer storageLocation) {
        this.storageLocation = storageLocation;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public String getPddetail() {
        return pddetail;
    }

    public void setPddetail(String pddetail) {
        this.pddetail = pddetail;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getPicture_path() {
        return picture_path;
    }

    public void setPicture_path(String picture_path) {
        this.picture_path = picture_path;
    }

    public String getPack() {
        return pack;
    }

    public void setPack(String pack) {
        this.pack = pack;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public Double getSale_price() {
        return sale_price;
    }

    public void setSale_price(Double sale_price) {
        this.sale_price = sale_price;
    }

    public int getOn_sale() {
        return on_sale;
    }

    public void setOn_sale(int on_sale) {
        this.on_sale = on_sale;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public String getStorageMethod() {
        return storageMethod;
    }

    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod;
    }

    public String getDetailpicture1_path() {
        return detailpicture1_path;
    }

    public void setDetailpicture1_path(String detailpicture1_path) {
        this.detailpicture1_path = detailpicture1_path;
    }

    public String getDetailpicture2_path() {
        return detailpicture2_path;
    }

    public void setDetailpicture2_path(String detailpicture2_path) {
        this.detailpicture2_path = detailpicture2_path;
    }

    public String getDetailpicture3_path() {
        return detailpicture3_path;
    }

    public void setDetailpicture3_path(String detailpicture3_path) {
        this.detailpicture3_path = detailpicture3_path;
    }

    public Double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    }

    public int getSalesMode() {
        return salesMode;
    }

    public void setSalesMode(int salesMode) {
        this.salesMode = salesMode;
    }

    public int getLimitedQuantity() {
        return limitedQuantity;
    }

    public void setLimitedQuantity(int limitedQuantity) {
        this.limitedQuantity = limitedQuantity;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getSellOut() {
        return sellOut;
    }

    public void setSellOut(Integer sellOut) {
        this.sellOut = sellOut;
    }

    public String getMaturity() {
        return maturity;
    }

    public void setMaturity(String maturity) {
        this.maturity = maturity;
    }

    public String getDetailPicture() {
        return detailPicture;
    }

    public void setDetailPicture(String detailPicture) {
        this.detailPicture = detailPicture;
    }

    public String getOtherSlogan() {
        return otherSlogan;
    }

    public void setOtherSlogan(String otherSlogan) {
        this.otherSlogan = otherSlogan;
    }

    public Integer getPdId() {
        return pdId;
    }

    public void setPdId(Integer pdId) {
        this.pdId = pdId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }
}
