package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Create 2020-11-03
 */
@ApiModel(description = "采购预测VO")
public class FruitPurchasesPredictionVO {

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "下架, 上架")
    private String onSale;

    @ApiModelProperty(value = "可用库存数量")
    private Integer quantity;

    @ApiModelProperty(value = "采购负责人")
    private String purchaser;

    /** 今日销量预测 */
    private Integer salesPrediction;

    /**  第二天预测销量*/
    private Integer preSaleSecondDay;

    /**  第三天预测销量*/
    private Integer preSaleThirdDay;

    /**  第四天预测销量*/
    private Integer preSaleFourthDay;

    /**  第五天预测销量*/
    private Integer preSaleFifthDay;

    /**  第六天预测销量*/
    private Integer preSaleSixthDay;

    /**  第七天预测销量*/
    private Integer preSaleSeventhDay;

    /** 大可户3天平均销量 */
    private Integer vipAvgSales;

    /** 单店近三天平均销量 */
    private Integer singleAvgSales;

    /** 销量同比增长率 */
    private  String salesRate;

    public String getSku() {
        return this.sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPdName() {
        return this.pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public String getWeight() {
        return this.weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getOnSale() {
        return this.onSale;
    }

    public void setOnSale(String onSale) {
        this.onSale = onSale;
    }

    public Integer getQuantity() {
        return this.quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getPreSaleSecondDay() {
        return preSaleSecondDay;
    }

    public void setPreSaleSecondDay(Integer preSaleSecondDay) {
        this.preSaleSecondDay = preSaleSecondDay;
    }

    public Integer getPreSaleThirdDay() {
        return preSaleThirdDay;
    }

    public void setPreSaleThirdDay(Integer preSaleThirdDay) {
        this.preSaleThirdDay = preSaleThirdDay;
    }

    public Integer getPreSaleFourthDay() {
        return preSaleFourthDay;
    }

    public void setPreSaleFourthDay(Integer preSaleFourthDay) {
        this.preSaleFourthDay = preSaleFourthDay;
    }

    public Integer getPreSaleFifthDay() {
        return preSaleFifthDay;
    }

    public void setPreSaleFifthDay(Integer preSaleFifthDay) {
        this.preSaleFifthDay = preSaleFifthDay;
    }

    public Integer getPreSaleSixthDay() {
        return preSaleSixthDay;
    }

    public void setPreSaleSixthDay(Integer preSaleSixthDay) {
        this.preSaleSixthDay = preSaleSixthDay;
    }

    public Integer getPreSaleSeventhDay() {
        return preSaleSeventhDay;
    }

    public void setPreSaleSeventhDay(Integer preSaleSeventhDay) {
        this.preSaleSeventhDay = preSaleSeventhDay;
    }

    public Integer getVipAvgSales() {
        return vipAvgSales;
    }

    public void setVipAvgSales(Integer vipAvgSales) {
        this.vipAvgSales = vipAvgSales;
    }

    public Integer getSingleAvgSales() {
        return singleAvgSales;
    }

    public void setSingleAvgSales(Integer singleAvgSales) {
        this.singleAvgSales = singleAvgSales;
    }

    public String getSalesRate() {
        return salesRate;
    }

    public void setSalesRate(String salesRate) {
        this.salesRate = salesRate;
    }

    public String getPurchaser() {
        return purchaser;
    }

    public void setPurchaser(String purchaser) {
        this.purchaser = purchaser;
    }

    public Integer getSalesPrediction() {
        return salesPrediction;
    }

    public void setSalesPrediction(Integer salesPrediction) {
        this.salesPrediction = salesPrediction;
    }
}
