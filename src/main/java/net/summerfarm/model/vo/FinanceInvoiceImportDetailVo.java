package net.summerfarm.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:16
 */
@Data
public class FinanceInvoiceImportDetailVo {
    @ExcelProperty(value = "id")
    @ColumnWidth(10)
    private String id;
    @ExcelProperty(value = "快递单号")
    @ColumnWidth(18)
    private String express;
    @ColumnWidth(18)
    @ExcelProperty(value = "失败原因")
    private String reason;
}
