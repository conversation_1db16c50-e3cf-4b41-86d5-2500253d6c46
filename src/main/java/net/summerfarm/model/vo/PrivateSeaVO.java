package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-08-13
 * @description
 */
@ApiModel(description = "我的私海model")
@Data
public class PrivateSeaVO {
    @ApiModelProperty(name = "mId")
    private Integer mId;
    @ApiModelProperty(name = "客户名称")
    private String mname;
    @ApiModelProperty(name = "城市编号")
    private Integer areaNo;
    @ApiModelProperty(name = "城市名称")
    private String areaName;
    @ApiModelProperty(name = "会员等级")
    private Integer grade;
    @ApiModelProperty(name = "生命周期：0新注册，1首单，2非稳，3稳定")
    private Integer lifecycle;
    @ApiModelProperty(name = "上月GMV")
    private BigDecimal lastMonthGmv;
    @ApiModelProperty(name = "本月GMV")
    private BigDecimal thisMonthGmv;
    @ApiModelProperty(name = "上月品类")
    private Integer lastMonthSkuCount;
    @ApiModelProperty(name = "本月品类")
    private Integer thisMonthSkuCount;
    @ApiModelProperty(name = "倒计时")
    private Integer dangerDay;
    @ApiModelProperty(name = "当月是否下单")
    private Boolean orderCurrentMonth;
    /**
     * 商户等级
     */
    private String size;
    /**
     * BD
     */
    private Integer adminId;
    /**
     * 最近下单时间
     */
    private LocalDateTime lastOrderTime;
    /**
     * 未下单天数
     */
    private Integer notOrder;
    /**
     * 未拜访天数
     */
    private Integer notFollow;
    /**
     * 下单预警标识
     */
    private Boolean orderCycleWarn;
    /**
     * 上月客单价
     */
    private BigDecimal lastMonthDeliveryUnitPrice;
    /**
     * 本月客单价
     */
    private BigDecimal thisMonthDeliveryUnitPrice;
    /**
     * 本月配送gmv
     */
    private BigDecimal distributionGmv;
    /**
     * 上月配送gmv
     */
    private BigDecimal lastDistributionGmv;
    /**
     * 配送次数
     */
    private Integer distributionAmount;
    /**
     * 核心客户标识
     */
    private Integer coreMerchantTag;
    /**
     * 商户注册时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime registerTime;
}
