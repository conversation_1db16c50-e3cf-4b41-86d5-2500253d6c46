package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.Category;
import net.summerfarm.model.domain.FrontCategory;
import net.summerfarm.model.domain.FrontCategoryToArea;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-05-29
 * @description
 */
@ApiModel(description = "前台类目vo")
@Data
public class FrontCategoryVO extends FrontCategory {
    /**
     * 子类目
     */
    @ApiModelProperty(value = "子类目")
    private List<FrontCategoryVO> childList;
    /**
     * 后台类目
     */
    @ApiModelProperty(value = "后台类目")
    private List<Category> categoryList;

    /**
     * 类目映射城市
     */
    @ApiModelProperty(value = "类目映射城市")
    private List<FrontCategoryToArea> frontCategoryToAreaList;
}
