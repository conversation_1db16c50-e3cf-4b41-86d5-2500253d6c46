package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.saas.TmsDeliveryPlanDetail;

/**
 * <AUTHOR>
 */
@Data
public class TmsDeliveryPlanDetailsVO extends TmsDeliveryPlanDetail {

    /**
     * 配送类型 0配送、1回收、2回收配送、 3自提
     */
    private Integer deliveryPlanType;

    /**
     * 单号（订单后、售后单号）
     */
    private String orderNo;

    /**
     * 缺货数量
     */
    private int shortCount;

    /**
     * 0正常 1异常
     */
    private int state;

    /**
     * 回收缺货原因
     */
    private String remark;
}
