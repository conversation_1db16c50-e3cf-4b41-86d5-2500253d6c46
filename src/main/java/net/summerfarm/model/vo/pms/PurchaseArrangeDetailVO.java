package net.summerfarm.model.vo.pms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.model.domain.purchase.SaasSku;

import java.math.BigDecimal;

/**
 * <AUTHOR> chenjie
 * @date : 2023-03-06 11:02
 * @describe :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseArrangeDetailVO implements SaasSku {
    /** 预约单ID **/
    private Integer arrangeId;
    /** sku **/
    private String sku;
    /** 图片 **/
    private String pic;
    /** saasSkuId **/
    private Long saasSkuId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /** 商品名称 **/
    private String pdName;
    /** 规格 **/
    private String weight;
    /** 单位 **/
    private String unit;

    /** 实收数量 **/
    private Integer actCount;
    /** 预约数量 **/
    private Integer arrCount;

}
