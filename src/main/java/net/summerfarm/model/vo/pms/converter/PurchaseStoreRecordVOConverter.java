package net.summerfarm.model.vo.pms.converter;

import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.model.vo.pms.PurchaseStoreRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PurchaseStoreRecordVOConverter {

    PurchaseStoreRecordVOConverter INSTANCE = Mappers.getMapper(PurchaseStoreRecordVOConverter.class);


    PurchaseStoreRecordVO convert(StoreRecordVO storeRecordVO);

    List<PurchaseStoreRecordVO> convertList(List<StoreRecordVO> storeRecordVOList);
}
