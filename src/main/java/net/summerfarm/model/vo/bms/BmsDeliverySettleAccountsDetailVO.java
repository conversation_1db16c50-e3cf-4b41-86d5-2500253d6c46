package net.summerfarm.model.vo.bms;

import lombok.Data;
import net.summerfarm.model.domain.bms.DeliverySettleAccountsDetail;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/8/24
 */
@Data
public class BmsDeliverySettleAccountsDetailVO extends DeliverySettleAccountsDetail {

    /**
     * 结算单id
     */
    private Integer settleAccountId;
    /**
     * 应付费用
     */
    private BigDecimal payableAmount;

    /**
     * 扣减费用
     */
    private BigDecimal deductionAmount;

    /**
     * 是否存在审核中的任务 0否 1是
     */
    private Integer haveExamineTask;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 城配仓
     */
    private String storeName;
    /**
     * 配送日期
     */
    private LocalDate deliveryDate;

    /**
     * 配送司机
     */
    private String driver;

    /**
     * 服务行政区/县
     */
    private List<String> districts;

    /**
     * 服务行政区/县
     */
    private String stringDistricts;

    /**
     * 服务区域
     */
    private String serviceAreaName;

    /**
     * 承运商id
     */
    private String carrierId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 服务区域id
     */
    private Integer serviceAreaId;

    /**
     * 承运商
     */
    private String carrierName;

    /**
     * 结算单状态
     */
    private Integer status;
}
