package net.summerfarm.model.vo;


import net.summerfarm.model.domain.Purview;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description: 角色VO
 * @author: <EMAIL>
 * @Date: 2016/8/19
 */
@ApiModel(value = "角色VO")
public class RoleVO {

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "角色名称")
    private String rolename;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "角色列表")
    private List<Purview> purviews;

    public RoleVO() {
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRolename() {
        return rolename;
    }

    public void setRolename(String rolename) {
        this.rolename = rolename;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<Purview> getPurviews() {
        return purviews;
    }

    public void setPurviews(List<Purview> purviews) {
        this.purviews = purviews;
    }
}
