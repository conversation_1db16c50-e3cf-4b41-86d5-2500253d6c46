package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.model.DTO.merchant.ContactAddressRemark;
import net.summerfarm.model.domain.AccountChange;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.FollowUpRecord;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.MerchantCluePool;
import net.summerfarm.model.input.MerchantLabelCorrelationReq;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/24
 */

@ApiModel(description = "商户VO类")
public class MerchantVO extends Merchant {

    @ApiModelProperty(value = "BD实际名称")
    private String adminRealname;

    @ApiModelProperty(value = "跟进者")
    private String adminName;

    @ApiModelProperty(value = "城市名称")
    private String areaName;

    //1代表多地址 2代表更换账号审核
    @ApiModelProperty(value = "1代表多地址 2代表更换账号审核")
    private Integer chat;

    @ApiModelProperty(value = "邀请者手机号")
    private String inviterPhone;

    @ApiModelProperty(value = "账号变更")
    private AccountChange accountChange;

    @ApiModelProperty(value = "跟进记录")
    private List<FollowUpRecord> followUpRecords;

    @ApiModelProperty(value = "联系人列表")
    private List<Contact> contacts;

    @ApiModelProperty(value = "state")
    private Integer state;

    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    @ApiModelProperty(value = "0无，1又购物，2 无")
    private Integer followType;

    @ApiModelProperty(value = "加入公海池的时间，没有释放 按照审核时间")
    private LocalDateTime reassignTime;

    @ApiModelProperty(value = "释放原因")
    private String reason;

    @ApiModelProperty(value = "跟进关系id")
    private Integer followId;

    @ApiModelProperty(value = "notOrder")
    private Integer notOrder;

    @ApiModelProperty(value = "notFollow")
    private Integer notFollow;

    @ApiModelProperty(value = "dangerDay")
    private Integer dangerDay;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;
    /**
     * 合作方式
     */
    private String contractMethod;

    private String mSize;

    private String tag;

    @ApiModelProperty
    private BigDecimal deliveryFee;

    //所属大客户名称
    private String realName;
    /**
     * 是否在流转白名单,1在、0或者空，不在
     */
    private Integer whiteListType;

    /**
    * 省心送标签 0 无 1 是省心送
    */
    private Integer timingFollowType;

    /**
    * 是否是在公海
    */
    private Integer reassign;

    /**
    *  是否存在标签
    */
    private Integer coreMerchantTag;

    @ApiModelProperty(value = "今天是否有拜访计划")
    private Boolean hasVisitPlanToday;
    /**
     * 历史下单
     */
    private Integer historyOrderCount;
    /**
     * 历史商品数
     */
    private Integer historySkuAmount;
    /**
     * 历史售后
     */
    private Integer historyAfterSaleCount;
    /**
     * 历史流转
     */
    private Integer historyRelation;

    private MerchantCluePool merchantCluePool;

    /**
    * 截单区域
    */
    private String mapSection;

    /**
    * 是否在截单区域内
    */
    private Boolean inMapSection;

    /**
     * es线索池Id
     */
    private String esId;

    /**
     * 线索池地址
     */
    private String clueAddress;

    /**
     * 线索池门店名称
     */
    private String clueMName;

    /**
     * 线索池 手机号
     */
    private String cluePhone;

    /**
     * 当月是否下单
     */
    private Boolean orderCurrentMonth;

    /**
     * poi
     */
    private PoiVO poi;

    /**
     * 店铺名称集合
     */
    private List<String> mnameList;

    /**
     * 手机号码集合
     */
    private List<String> phoneList;


    private LocalDate deliveryTime;

    /**
     * 店铺名称集合
     */
    private List<Long> mIdList;

    /**
    * 拆分城市信息
    */
    private Integer splitAreaNo;

    /**
    * 大客户截单时间
    */
    private String closeOrderTime;

    /**
    * 地址变更id
    */
    private Integer contactAdjustId;

    /**
     * 月度采购额
     */
    private String monthPurmoney;

    /**
     * 最新的下单
     */
    private LocalDateTime latestOrderTime;

    /**
     * 上月gmv
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月gmv
     */
    private BigDecimal thisMonthGmv;
    /**
     * 上月客单价
     */
    private BigDecimal lastMonthDeliveryUnitPrice;
    /**
     * 本月客单价
     */
    private BigDecimal thisMonthDeliveryUnitPrice;
    /**
     * 上月配送gmv
     */
    private BigDecimal lastDistributionGmv;
    /**
     * 本月配送gmv
     */
    private BigDecimal thisDistributionGmv;
    /**
     * 本月配送次数
     */
    private Integer distributionAmount;
    /**
     * 数据更新时间
     */
    private Integer updateTime;
    /**
     * 是否是销售
     */
    private Boolean roleTag;
    /**
     * bd维度-城市等级
     */
    private String areaGrade;
    /**
     * 核心客户定义之本月gmv阈值
     */
    private Integer gmvThreshold;
    /**
     * 核心客户定义之本月配送客单价阈值
     */
    private Integer priceThreshold;

    /**
     * 团长身份信息：0、普通 1、团长
     */
    private Integer groupHeadFlag;

    /**
     * 团长所属运营区域
     */
    private Integer groupBuyAreaNo;

    /**
     * 大区编号
     */
    private Integer largeAreaNo;

    /**
     * 地推人员
     */
    private String adminRealName;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;
    /**
     * tms 打印配送单
     */
    private Boolean printOutTMSConfig;

    /**
     * 自定义地址备注
     */
    private ContactAddressRemark contactAddressRemark;

    public ContactAddressRemark getContactAddressRemark() {
        return contactAddressRemark;
    }

    public void setContactAddressRemark(ContactAddressRemark contactAddressRemark) {
        this.contactAddressRemark = contactAddressRemark;
    }

    public String getAdminRealName() {
        return adminRealName;
    }

    public void setAdminRealName(String adminRealName) {
        this.adminRealName = adminRealName;
    }

    public Integer getGmvThreshold() {
        return gmvThreshold;
    }

    public void setGmvThreshold(Integer gmvThreshold) {
        this.gmvThreshold = gmvThreshold;
    }

    public Integer getPriceThreshold() {
        return priceThreshold;
    }

    public void setPriceThreshold(Integer priceThreshold) {
        this.priceThreshold = priceThreshold;
    }

    public String getAreaGrade() {
        return areaGrade;
    }

    public void setAreaGrade(String areaGrade) {
        this.areaGrade = areaGrade;
    }

    public Integer getDistributionAmount() {
        return distributionAmount;
    }

    public void setDistributionAmount(Integer distributionAmount) {
        this.distributionAmount = distributionAmount;
    }

    public Boolean getRoleTag() {
        return roleTag;
    }

    public void setRoleTag(Boolean roleTag) {
        this.roleTag = roleTag;
    }

    private Long contactId;

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getThisDistributionGmv() {
        return thisDistributionGmv;
    }

    public void setThisDistributionGmv(BigDecimal thisDistributionGmv) {
        this.thisDistributionGmv = thisDistributionGmv;
    }

    public BigDecimal getLastMonthGmv() {
        return lastMonthGmv;
    }

    public void setLastMonthGmv(BigDecimal lastMonthGmv) {
        this.lastMonthGmv = lastMonthGmv;
    }

    public BigDecimal getThisMonthGmv() {
        return thisMonthGmv;
    }

    public void setThisMonthGmv(BigDecimal thisMonthGmv) {
        this.thisMonthGmv = thisMonthGmv;
    }

    public BigDecimal getLastMonthDeliveryUnitPrice() {
        return lastMonthDeliveryUnitPrice;
    }

    public void setLastMonthDeliveryUnitPrice(BigDecimal lastMonthDeliveryUnitPrice) {
        this.lastMonthDeliveryUnitPrice = lastMonthDeliveryUnitPrice;
    }

    public BigDecimal getThisMonthDeliveryUnitPrice() {
        return thisMonthDeliveryUnitPrice;
    }

    public void setThisMonthDeliveryUnitPrice(BigDecimal thisMonthDeliveryUnitPrice) {
        this.thisMonthDeliveryUnitPrice = thisMonthDeliveryUnitPrice;
    }

    public BigDecimal getLastDistributionGmv() {
        return lastDistributionGmv;
    }

    public void setLastDistributionGmv(BigDecimal lastDistributionGmv) {
        this.lastDistributionGmv = lastDistributionGmv;
    }
    /**
     * 是否自动发券：0表示不自动发券，1表示自动发券
     */
    private Integer sendCoupon;

    public Integer getSendCoupon() {
        return sendCoupon;
    }

    public void setSendCoupon(Integer sendCoupon) {
        this.sendCoupon = sendCoupon;
    }

    /**
     * 客户类型
     */
    private Integer adminType;

    /**
     * 客户备注
     * @return
     */
    private String nameRemakes;

    /**
     * 外部对接-门店映射的外部平台
     */
    List<MerchantOuterVO> outerMappings;

    /**
     * 近三个月平均下单周期
     */
    private Integer orderCycle;

    /**
     * 下单预警标识
      */
    private Boolean orderCycleWarn;

    /**
     * 已超过平均下单周期天数
     */
    private Integer moreThanOrderCycle;
    /**
     * 搜索关键字：CRM：客户名称及手机号
     */
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }


    @ApiModelProperty(value = "单店工商名称")
    private String invoiceTitle;

    @ApiModelProperty(value = "是否需要显示FreeDay 0不展示")
    private Integer showFreeDay;

    @ApiModelProperty(value = "配送规则")
    private String deliveryRule;

    /**
     * 门店所属BD id
     */
    private Long salerId;

    /**
     * 门店所属BD名称
     */
    private String salerName;
    /**
     * 客户标签集合
     */
    private List<String> merchantLabelList;

    private Long followUpAdminId;

    /**
     * 客户标签
     */
    private String merchantLabel;

    public String getMerchantLabel() {
        return merchantLabel;
    }

    public void setMerchantLabel(String merchantLabel) {
        this.merchantLabel = merchantLabel;
    }

    public List<String> getMerchantLabelList() {
        return merchantLabelList;
    }

    public void setMerchantLabelList(List<String> merchantLabelList) {
        this.merchantLabelList = merchantLabelList;
    }

    public Integer getCoreMerchantTag() {
        return coreMerchantTag;
    }

    public void setCoreMerchantTag(Integer coreMerchantTag) {
        this.coreMerchantTag = coreMerchantTag;
    }

    public Integer getShowFreeDay() {
        return showFreeDay;
    }

    public void setShowFreeDay(Integer showFreeDay) {
        this.showFreeDay = showFreeDay;
    }
    public String getDeliveryRule() {
        return deliveryRule;
    }

    public void setDeliveryRule(String deliveryRule) {
        this.deliveryRule = deliveryRule;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public Integer getOrderCycle() {
        return orderCycle;
    }

    public void setOrderCycle(Integer orderCycle) {
        this.orderCycle = orderCycle;
    }

    public Boolean isOrderCycleWarn() {
        return orderCycleWarn;
    }

    public void setOrderCycleWarn(Boolean orderCycleWarn) {
        this.orderCycleWarn = orderCycleWarn;
    }

    public Integer getMoreThanOrderCycle() {
        return moreThanOrderCycle;
    }

    public void setMoreThanOrderCycle(Integer moreThanOrderCycle) {
        this.moreThanOrderCycle = moreThanOrderCycle;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }

    public LocalDateTime getLatestOrderTime() {
        return latestOrderTime;
    }

    public void setLatestOrderTime(LocalDateTime latestOrderTime) {
        this.latestOrderTime = latestOrderTime;
    }

    public String getMonthPurmoney() {
        return monthPurmoney;
    }

    public void setMonthPurmoney(String monthPurmoney) {
        this.monthPurmoney = monthPurmoney;
    }

    public List<String> getMnameList() {
        return mnameList;
    }

    public void setMnameList(List<String> mnameList) {
        this.mnameList = mnameList;
    }

    public List<String> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<String> phoneList) {
        this.phoneList = phoneList;
    }

    public String getInviterPhone() {
        return inviterPhone;
    }

    public void setInviterPhone(String inviterPhone) {
        this.inviterPhone = inviterPhone;
    }

    public String getAdminRealname() {
        return adminRealname;
    }

    public void setAdminRealname(String adminRealname) {
        this.adminRealname = adminRealname;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public List<FollowUpRecord> getFollowUpRecords() {
        return followUpRecords;
    }

    public void setFollowUpRecords(List<FollowUpRecord> followUpRecords) {
        this.followUpRecords = followUpRecords;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public List<Contact> getContacts() {
        return contacts;
    }

    public void setContacts(List<Contact> contacts) {
        this.contacts = contacts;
    }

    public Integer getChat() {
        return chat;
    }

    public void setChat(Integer chat) {
        this.chat = chat;
    }

    public AccountChange getAccountChange() {
        return accountChange;
    }

    public void setAccountChange(AccountChange accountChange) {
        this.accountChange = accountChange;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public Integer getFollowType() {
        return followType;
    }

    public void setFollowType(Integer followType) {
        this.followType = followType;
    }

    public LocalDateTime getReassignTime() {
        return reassignTime;
    }

    public void setReassignTime(LocalDateTime reassignTime) {
        this.reassignTime = reassignTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getFollowId() {
        return followId;
    }

    public void setFollowId(Integer followId) {
        this.followId = followId;
    }


    public Integer getNotOrder() {
        return notOrder;
    }

    public void setNotOrder(Integer notOrder) {
        this.notOrder = notOrder;
    }

    public Integer getNotFollow() {
        return notFollow;
    }

    public void setNotFollow(Integer notFollow) {
        this.notFollow = notFollow;
    }

    public Integer getDangerDay() {
        return dangerDay;
    }

    public void setDangerDay(Integer dangerDay) {
        this.dangerDay = dangerDay;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getContractMethod() {
        return contractMethod;
    }

    public void setContractMethod(String contractMethod) {
        this.contractMethod = contractMethod;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }
    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }


    public Integer getTimingFollowType() {
        return timingFollowType;
    }

    public void setTimingFollowType(Integer timingFollowType) {
        this.timingFollowType = timingFollowType;
    }

    public Integer getWhiteListType() {
        return whiteListType;
    }

    public void setWhiteListType(Integer whiteListType) {
        this.whiteListType = whiteListType;
    }

    public Integer getReassign() {
        return reassign;
    }

    public void setReassign(Integer reassign) {
        this.reassign = reassign;
    }

    public Boolean getHasVisitPlanToday() {
        return hasVisitPlanToday;
    }

    public void setHasVisitPlanToday(Boolean hasVisitPlanToday) {
        this.hasVisitPlanToday = hasVisitPlanToday;
    }

    public Integer getHistoryOrderCount() {
        return historyOrderCount;
    }

    public void setHistoryOrderCount(Integer historyOrderCount) {
        this.historyOrderCount = historyOrderCount;
    }

    public Integer getHistorySkuAmount() {
        return historySkuAmount;
    }

    public void setHistorySkuAmount(Integer historySkuAmount) {
        this.historySkuAmount = historySkuAmount;
    }

    public Integer getHistoryAfterSaleCount() {
        return historyAfterSaleCount;
    }

    public void setHistoryAfterSaleCount(Integer historyAfterSaleCount) {
        this.historyAfterSaleCount = historyAfterSaleCount;
    }

    public Integer getHistoryRelation() {
        return historyRelation;
    }

    public void setHistoryRelation(Integer historyRelation) {
        this.historyRelation = historyRelation;
    }

    public MerchantCluePool getMerchantCluePool() {
        return merchantCluePool;
    }

    public void setMerchantCluePool(MerchantCluePool merchantCluePool) {
        this.merchantCluePool = merchantCluePool;
    }


    public String getMapSection() {
        return mapSection;
    }

    public void setMapSection(String mapSection) {
        this.mapSection = mapSection;
    }

    public Boolean getInMapSection() {
        return inMapSection;
    }

    public void setInMapSection(Boolean inMapSection) {
        this.inMapSection = inMapSection;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getClueAddress() {
        return clueAddress;
    }

    public void setClueAddress(String clueAddress) {
        this.clueAddress = clueAddress;
    }

    public String getClueMName() {
        return clueMName;
    }

    public void setClueMName(String clueMName) {
        this.clueMName = clueMName;
    }

    public String getCluePhone() {
        return cluePhone;
    }

    public void setCluePhone(String cluePhone) {
        this.cluePhone = cluePhone;
    }

    public Boolean getOrderCurrentMonth() {
        return orderCurrentMonth;
    }

    public void setOrderCurrentMonth(Boolean orderCurrentMonth) {
        this.orderCurrentMonth = orderCurrentMonth;
    }

    public PoiVO getPoi() {
        return poi;
    }

    public void setPoi(PoiVO poi) {
        this.poi = poi;
    }


    public LocalDate getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(LocalDate deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public List<Long> getmIdList() {
        return mIdList;
    }

    public void setmIdList(List<Long> mIdList) {
        this.mIdList = mIdList;
    }

    public Integer getSplitAreaNo() {
        return splitAreaNo;
    }

    public void setSplitAreaNo(Integer splitAreaNo) {
        this.splitAreaNo = splitAreaNo;
    }

    public String getCloseOrderTime() {
        return closeOrderTime;
    }

    public void setCloseOrderTime(String closeOrderTime) {
        this.closeOrderTime = closeOrderTime;
    }

    public Integer getContactAdjustId() {
        return contactAdjustId;
    }

    public void setContactAdjustId(Integer contactAdjustId) {
        this.contactAdjustId = contactAdjustId;
    }

    public List<MerchantOuterVO> getOuterMappings() {
        return outerMappings;
    }

    public void setOuterMappings(List<MerchantOuterVO> outerMappings) {
        this.outerMappings = outerMappings;
    }

    public Long getSalerId() {
        return salerId;
    }

    public void setSalerId(Long salerId) {
        this.salerId = salerId;
    }

    public String getSalerName() {
        return salerName;
    }

    public void setSalerName(String salerName) {
        this.salerName = salerName;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Integer getGroupHeadFlag() {
        return groupHeadFlag;
    }

    public void setGroupHeadFlag(Integer groupHeadFlag) {
        this.groupHeadFlag = groupHeadFlag;
    }

    public Integer getGroupBuyAreaNo() {
        return groupBuyAreaNo;
    }

    public void setGroupBuyAreaNo(Integer groupBuyAreaNo) {
        this.groupBuyAreaNo = groupBuyAreaNo;
    }

    public Long getFollowUpAdminId() {
        return followUpAdminId;
    }

    public void setFollowUpAdminId(Long followUpAdminId) {
        this.followUpAdminId = followUpAdminId;
    }

    public Integer getLargeAreaNo() {
        return largeAreaNo;
    }

    public void setLargeAreaNo(Integer largeAreaNo) {
        this.largeAreaNo = largeAreaNo;
    }

    public Boolean getPrintOutTMSConfig() {
        return printOutTMSConfig;
    }

    public void setPrintOutTMSConfig(Boolean printOutTMSConfig) {
        this.printOutTMSConfig = printOutTMSConfig;
    }
}
