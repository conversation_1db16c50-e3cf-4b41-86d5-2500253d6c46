package net.summerfarm.model.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.Purchases;
import net.summerfarm.model.domain.PurchasesPlan;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description: 采购单复杂对象
 * @author: <EMAIL>
 * @Date: 2016/11/10
 */
@ApiModel(description = "采购单VO")
@Data
public class PurchasesVO extends Purchases {

    /**
     * 列表是否展示详情
     **/
    private Boolean isShowDetail;

    /**
     * SKU ID
     */
    private String saasSkuId;

    @ApiModelProperty(value = "sku编号")
    private String sku;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "purchaseNoOne")
    private String purchaseNoOne;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    private Integer inQuantity;

    @ApiModelProperty(value = "采购入库任务状态")
    private Integer stockState;

    @ApiModelProperty(value = "采购计划列表")
    private List<PurchasesPlan> purchasesPlans;

    @ApiModelProperty(value = "结算状态：1、待发起 2、部分发起 3、已全部发起 4、打款失败待发起")
    private Integer settleStatus;

    @ApiModelProperty(value = "采购时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate purchaseTimeStart;

    @ApiModelProperty(value = "采购时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate purchaseTimeEnd;

    @ApiModelProperty(value = "采购金额总计")
    private BigDecimal totalPurchaseAmount;

    @ApiModelProperty(value = "已匹配发票金额总计")
    private BigDecimal totalMatchedInvoice;

    @ApiModelProperty(value = "发票总进展")
    private String overallProgress;

    @ApiModelProperty(value = "预约单id")
    private Integer stockArrangeId;

    @ApiModelProperty(value = "预约单状态")
    private Integer stockArrangeState;

    /**
     * 预付金额
     */
    private BigDecimal advanceAmount;

    /**
     * 采购单总金额（不含退订）
     */
    private BigDecimal totalPrice;

    /**
     * 采购单总金额（不含退订）
     */
    private BigDecimal actualPrice;

    /**
     * 付款金额（含预付）
     */
    private BigDecimal totalAmount;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmount;

    /**
     * 抵扣金额
     **/
    private BigDecimal deductionAmount;

    /**
     * 采购单仓库名
     */
    private String warehouseName;

    /**
     * json参数(用于下载中心展示导出条件)
     */
    private String paramJson;

    private List<PurchasesPlanVO> purchasePlanList;
    /**
     * 仓库列表
     **/
    private List<Long> warehouseNoList;
    /**
     * 供应商ID列表
     */
    private List<Integer> supplierIdList;

    private Integer pageIndex = 1;
    private Integer pageSize = 10;

}
