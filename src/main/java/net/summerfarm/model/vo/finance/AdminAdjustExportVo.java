package net.summerfarm.model.vo.finance;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/6/20 11:52
 */
@Data
@HeadStyle(fillPatternType = FillPatternTypeEnum.NO_FILL)
@HeadFontStyle(fontHeightInPoints = 12)
@ContentFontStyle(fontHeightInPoints = 10)
@ColumnWidth(15)
@HeadRowHeight(30)
public class AdminAdjustExportVo {
    @ExcelIgnore
    private Long mId;

    @ExcelProperty(value = "门店名称")
    private String mname;

    @ExcelProperty(value = "结算方式")
    private String settlementMethod;

    /**
     * 调整单号
     */
    @ExcelProperty(value = "调整单号")
    private String adjustNo;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单时间
     */
    @ExcelProperty(value = "下单时间")
    private String orderTime;

    /**
     * 调整时间
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "调整通过时间")
    private String adjustmentTime;

    /**
     * sku
     */
    @ExcelProperty(value = "鲜沐sku")
    private String sku;

    /**
     * 商品名
     */
    @ExcelProperty(value = "商品名称")
    private String pdName;

    /**
     * 规格
     */
    @ExcelProperty(value = "规格")
    private String weight;

    /**
     * 性质
     */
    @ExcelProperty(value = "性质")
    private String property;

    /**
     * 数量
     */
    @ExcelProperty(value = "商品数量")
    private Integer quantity;

    /**
     * 调整实际金额
     */
    @ExcelProperty(value = "调整实付总价")
    private BigDecimal adjustmentActualAmount;

    /**
     * 调整运费金额
     */
    @ExcelProperty(value = "调整运费")
    private BigDecimal adjustmentDeliveryAmount;

    /**
     * 调整总额
     */
    @ExcelProperty(value = "调整总价")
    private BigDecimal adjustmentTotalAmount;
}
