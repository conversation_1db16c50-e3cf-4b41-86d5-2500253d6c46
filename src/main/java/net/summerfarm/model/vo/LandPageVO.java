package net.summerfarm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.LandPage;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description: 落地页类型类
 * @Date: 2021/3/9 21:54
 * @Author: <EMAIL>
 */
@Data
public class LandPageVO extends LandPage {

    private static final long serialVersionUID = -7665982425247160042L;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 第一层的包裹
     */
    private List<LandPageItemVO> levelOneList;

    /**
     * 查询时间(入参）
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate queryTime;

    /**
     * 查询开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;
}


