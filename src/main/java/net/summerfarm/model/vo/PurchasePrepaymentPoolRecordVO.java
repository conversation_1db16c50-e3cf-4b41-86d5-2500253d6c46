package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.PurchasePrepaymentPoolRecord;

import java.time.LocalDateTime;

/**
 * @description 预付池往来记录VO
 * <AUTHOR>
 * @date 2022/1/21 10:24
 */
@Data
public class PurchasePrepaymentPoolRecordVO extends PurchasePrepaymentPoolRecord {

    /**
     * 预付单id
     */
    private Long purchaseAdvancedOrderId;

    /**
     * 退款单id
     */
    private Long financeAdvancedRefundId;

    /**
     * 对账单id
     */
    private Long statementsId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

}
