package net.summerfarm.model.vo.price;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/16 11:20
 */

@Data
public class CommodityVO implements Serializable {


    /**
     * sku编号
     */
    private String sku;

    /**
     *   商品图
     */
    private String pdImage;

    /**
     *   商品名称
     */
    private String pdName;

    /**
     *  规格
     */
    private String weight;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 大区编号
     */
    private Integer areaNo;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     *  规格
     */
    private String itemLabel;
}
