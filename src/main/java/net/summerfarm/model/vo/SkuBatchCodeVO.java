package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.SkuBatchCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2021/12/14  14:31
 */
@Data
public class SkuBatchCodeVO extends SkuBatchCode {

    /**
    * 本次需要打印次数
    */
    private Integer canPrintNumber;

    /**
    * 温区
    */
    private String storageLocation;


    /**
    * 匹配的订单号
    */
    private String mateOrderNo;

    /**
    * 供应商
    */
    private String supplier;

    /**
    *  商品名称
    */
    private String productName;

    /**
    * 规格
    */
    private String weight;

    /**
    * 物品唯一码
    */
    private String goodsOnlyCode;

    /**
    * 库存仓编号
    */
    private Integer warehouseNo;

    /**
    * 入库单id
    */
    private Integer stockTaskProcessDetailId;

    /**
    * 保质期
    */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate qualityDate;

    /**
     * 类型
     * 0-转换任务
     */
    private Integer type;

    /**
     * 任务操作id
     */
    private String bizId;



}
