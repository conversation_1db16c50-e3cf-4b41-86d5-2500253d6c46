package net.summerfarm.model.vo;

import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.RechargeRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@ApiModel(description = "余额变动记录VO")
public class RechargeRecordVO extends RechargeRecord {

    @ApiModelProperty(value = "店铺名称")
    private String mname;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "记录人")
    private String recorder;

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }
}
