package net.summerfarm.model.input;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/5/20
 */
@Data
public class OrderAbnormalInput {

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 客户类型
     */
    private String mType;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 客户名称
     */
    private String mName;

    /**
     * 异常订单ids
     */
    private String orderAbnormalIds;
}
