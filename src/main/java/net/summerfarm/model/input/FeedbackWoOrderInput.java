package net.summerfarm.model.input;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 查询订单是否存在
 * @Date: 2020/12/7 10:37
 * @Author: <EMAIL>
 */
@Data
public class FeedbackWoOrderInput implements Serializable {

    private static final long serialVersionUID = 3615134152873707445L;

    /**
     * 新建问题反馈时填入的订单号，包括大客户或门店客户下的订单和售后单 */
    private String orderNo;

    /**
     * 若是大客户入参的大客户的amdim
     */
    private Long adminId;

    /**
     * 若是普通客户则入参的phone;
     */
    private String phone;

    /**
     * 若是普通客户则入参的为mname
     */
    private  String mname;

    /**
     * 若是普通客户入参的mId
     */
    private Long mId;
}


