package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 黑白名单上传
 * @date 2023/6/20 18:02:44
 */
@Data
public class TimingOrderRefundWhiteListInitReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号集合
     */
    private List<String> orderNos;
}
