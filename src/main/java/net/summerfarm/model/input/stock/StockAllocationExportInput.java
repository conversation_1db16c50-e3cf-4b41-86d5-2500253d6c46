package net.summerfarm.model.input.stock;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.aspectj.lang.annotation.Before;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class StockAllocationExportInput implements Serializable {

    private static final long serialVersionUID = 7290404350305951075L;

    /**
     * 调拨单单号
     */
    @NotEmpty(message = "listNo不能为空")
    private String listNo;

    /**
     * 调入仓开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime inHistoryStartTime;

    /**
     * 调入仓结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime inHistoryEndTime;

    /**
     * 调出仓开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime outHistoryStartTime;

    /**
     * 调出仓开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime outHistoryEndTime;

}
