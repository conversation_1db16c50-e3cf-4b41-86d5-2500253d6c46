package net.summerfarm.model.input;

import lombok.Data;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.model.domain.StockStorageItemDetail;
import net.summerfarm.model.vo.StockStorageItemDetailVO;

import java.util.List;

@Data
public class StockStorageItemReq {
    private Integer stockAllocationItemId;

    private Integer id;

    private Integer stockTaskId;

    private String listNo;

    private String sku;

    private Integer areaNo;

    private List<StockStorageItemDetailVO> stockAllocationItemDetails;
}
