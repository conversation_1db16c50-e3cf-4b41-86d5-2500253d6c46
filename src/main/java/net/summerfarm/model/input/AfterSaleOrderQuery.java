package net.summerfarm.model.input;

import lombok.Data;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/9/14 20:50
 */
@Data
public class AfterSaleOrderQuery {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 售后状态:0审核中 1处理中 2成功 3失败 4补充凭证 11取消 12退款中',
     */
    private Integer status;

    /**
     * 退款标识:0 退款(包含任意退款方式)
     */
    private Integer refundTag;

}
