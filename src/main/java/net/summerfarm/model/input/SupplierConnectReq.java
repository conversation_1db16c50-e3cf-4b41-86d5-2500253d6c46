package net.summerfarm.model.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.SupplierConnect;

import java.util.List;


@ApiModel(description = "供应商联系人Req")
@Data
public class SupplierConnectReq {
    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商联系方式列表")
    private List<SupplierConnect> connectList;
}
