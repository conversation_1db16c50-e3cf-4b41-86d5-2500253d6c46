package net.summerfarm.model.input.purchase;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.ProductWarehouseConfigEnums;

import java.util.List;

@Data
public class ProductWarehouseQueryInput {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * spu_id
     */
    private Long pdId;

    /**
     * spu_id
     */
    private List<Long> pdIds;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 库存仓编号
     */
    private List<Integer> warehouseNos;

    /**
     * 采购类型
     * @see ProductWarehouseConfigEnums.PurchaseType
     */
    private Integer purchaseType;

    /**
     * 采购类型
     * @see ProductWarehouseConfigEnums.PurchaseType
     */
    private List<Integer> purchaseTypes;

    /**
     * 采购负责人ID
     */
    private Integer adminId;

    /**
     * 采购负责人ID
     */
    private List<Integer> adminIds;

    /**
     * 采购负责人姓名
     */
    private String adminName;

    /**
     * 采购负责人姓名
     */
    private String adminNameStartWith;

    /**
     * 采购负责人姓名
     */
    private String adminNameLike;

}
