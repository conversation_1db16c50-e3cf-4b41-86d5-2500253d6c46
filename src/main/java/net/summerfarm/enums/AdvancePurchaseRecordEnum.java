package net.summerfarm.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public enum AdvancePurchaseRecordEnum {

    NEW_ADVANCE_PURCHASE(0,"新增预购单"),
    AUDIT(1,"审核"),
    PAYMENT(2,"打款"),
    FAIL(3,"审核失败"),
    AGAIN_START(4,"重新发起"),
    NEW_PURCHASES(5,"新增采购单"),
    PURCHASE_PRODUCT_UPDATE(6,"商品调整"),
    PURCHASE_FAIL(7,"采购单作废"),
    PURCHASE_ARRIVAL_BACK(8,"采购单已入库退货"),
    PURCHASE_NOT_ARRIVAL_BACK(9,"采购单未入库退货")
    ;

    private Integer id;

    private String status;


    AdvancePurchaseRecordEnum(Integer id,String status){
        this.id = id;
        this.status = status;
    }

    public static String getStatusName(Integer id){
        String status = NEW_ADVANCE_PURCHASE.getStatus();
        List<AdvancePurchaseRecordEnum> advancePurchaseRecordEnums = Arrays.asList(AdvancePurchaseRecordEnum.values());
        for (AdvancePurchaseRecordEnum recordEnum : advancePurchaseRecordEnums) {
            if(Objects.equals(recordEnum.getId(),id)){
                status = recordEnum.getStatus();
            }
        }
        return status;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
