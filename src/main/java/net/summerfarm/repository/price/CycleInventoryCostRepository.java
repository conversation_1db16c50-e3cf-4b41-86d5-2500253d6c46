package net.summerfarm.repository.price;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.mapper.manage.CycleInventoryCostMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.domain.CycleInventoryCost;
import net.summerfarm.model.vo.CostChangeVo;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/16 14:10
 */

@Slf4j
@Component
public class CycleInventoryCostRepository {

    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CycleInventoryCostMapper cycleInventoryCostMapper;
    @Resource
    private ProductCostQueryFacade productCostQueryFacade;
    @Resource
    private AreaStoreMapper areaStoreMapper;


    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public BigDecimal selectCycleCost(String sku, Integer warehouseNo){
        BigDecimal costPrice = null;
        if(dynamicConfig.getMallNewCostPriceSwitch()){
            costPrice = productCostQueryFacade.selectCycleCost(sku, warehouseNo);
        } else {
            costPrice = inventoryMapper.selectCycleCost(sku, warehouseNo);
        }
        return costPrice;
    }


    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public CycleInventoryCost selectBySku(String sku, Integer warehouseNo){
        CycleInventoryCost cost = null;
        if(dynamicConfig.getMallNewCostPriceSwitch()){
            ProductCostQueryResp costQueryResp = productCostQueryFacade.selectBySku(sku, warehouseNo);
            cost = this.toCycleInventoryCost(costQueryResp);
        } else {
            cost = cycleInventoryCostMapper.selectBySku(sku, warehouseNo);
        }
        return cost;
    }


    /**
     * 获取当前成本价，没有则取最新批次成本
     * @param sku
     * @param warehouseNo
     * @return
     */
    public BigDecimal selectCycleCostWithLastBatchCost(String sku, Integer warehouseNo){
        BigDecimal costPrice = null;
        if(dynamicConfig.getMallNewCostPriceSwitch()){
            costPrice = productCostQueryFacade.selectCycleCost(sku, warehouseNo);
        } else {
            costPrice = inventoryMapper.selectCycleCost(sku, warehouseNo);
        }

        if(costPrice == null) {
            CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(warehouseNo, sku);
            if (Objects.nonNull(costChangeVo) && costChangeVo.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
                costPrice = costChangeVo.getCostPrice();
                log.info("查询日周期成本时，在仓库 {} 下的sku {} 成本价为空，取最新批次成本：{}", warehouseNo, sku, costPrice);
            }
        }
        return costPrice;
    }


    private CycleInventoryCost toCycleInventoryCost(ProductCostQueryResp resp){
        if (resp == null) {
            return null;
        }
        CycleInventoryCost cycleInventoryCost = new CycleInventoryCost();
        cycleInventoryCost.setSku(resp.getSku());
        cycleInventoryCost.setWarehouseNo(resp.getWarehouseNo());
        cycleInventoryCost.setFirstCycleCost(resp.getCurrentCost());
        cycleInventoryCost.setFirstCycleCostTime(resp.getCurrentCostDay() == null ? null : resp.getCurrentCostDay().atStartOfDay());
        cycleInventoryCost.setEndCycleCost(resp.getPreviousCost());
        cycleInventoryCost.setEndCycleCostTime(resp.getPreviousCostDay() == null ? null : resp.getPreviousCostDay().atStartOfDay());
        cycleInventoryCost.setCurrentCostUpdateTime(resp.getCurrentCostUpdateTime());
        return cycleInventoryCost;
    }
}
