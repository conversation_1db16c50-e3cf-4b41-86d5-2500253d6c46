package net.summerfarm.contexts;

/**
 * @author: <EMAIL>
 * @create: 2022/8/19
 */
public interface BaseConstant {

    /**
     * 七牛云下载域名
     */
    String DOWNLOAD_DOMAIN = "https://azure.summerfarm.net/";

    /**
     * 圈人平台2.0 标签索引名称
     */
    String MERCHANT_TAGS_INDEX = "xianmu_merchant_tags";

    /**
     * merchant_tags_pool表名
     */
    String TABLE_NAME_MERCHANT_TAGS_POOL = "merchant_tags_pool";

    /**
     * 圈人平台2.0 上传模板文件配置名 config表中配置
     */
    String CIRCLE_TEMPLATE_FILE = "circle_template_file";

    /**
     * 圈人平台es同步进度信息
     */
    String CIRCLE_ES_SYNC_KEY = "circle_people:sync";

    String TAG_LONG_NO_BUY_FOURTH_CATEGORY = "tag_long_no_buy_fourth_category";

    String CIRCLE_ONLINE_KEY_PREFIX = "circle_people:es:search:";

    String CIRCLE_DATA_UPDATE_KEY = "circle_people:data:update:";

    /**
     * 离线库：动态定价-sku指标表 表名
     */
    String DYNAMIC_PRICE_STATISTICS = "dynamic_price_statistics";


    long SCROLL_TIMEOUT = 180000;

    /**
     * 鲜沐tenantId
     */
    public static final Long XIANMU_TENANT_ID = 1L;

    /**
     * 是否鲜沐租户
     * @param tenantId
     * @return
     */
    public static Boolean isXm(Long tenantId) {
        if (tenantId != null && tenantId > XIANMU_TENANT_ID) {
            return false;
        }
        return true;
    }

    String XIANMU_TEACH = "杭州鲜沐科技有限公司";


    String WAREHOUSE_ARRANGE_TIME_END="WAREHOUSE_ARRANGE_DEADLINE";


    public static final String XIANMU="xianmu";


}
