package net.summerfarm.facade.wms.converter;

import net.summerfarm.model.DTO.req.cabinetInventory.CabinetInventoryOccupyReq;
import net.summerfarm.model.DTO.res.cabinetInventory.CabinetInventoryOccupyResp;
import net.summerfarm.module.wms.model.domain.CabinetInventoryOccupyDetail;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryOccupyReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CabinetInventoryOccupyReqConverter {

    CabinetInventoryOccupyReqConverter INSTANCE = Mappers.getMapper(CabinetInventoryOccupyReqConverter.class);


    CabinetInventoryOccupyReq convert(CabinetInventoryOccupyReqDTO req);

    CabinetInventoryOccupyDetail convertResp(CabinetInventoryOccupyResp req);

    List<CabinetInventoryOccupyDetail> convertRespList(List<CabinetInventoryOccupyResp> req);

}
