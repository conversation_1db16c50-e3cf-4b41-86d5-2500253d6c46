package net.summerfarm.facade.goods.converter;

import net.summerfarm.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GoodsInfoDTOConvert {

    GoodsInfoDTOConvert INSTANCE = Mappers.getMapper(GoodsInfoDTOConvert.class);

    GoodsInfoDTO convert(ProductSkuDetailResp resp);
}
