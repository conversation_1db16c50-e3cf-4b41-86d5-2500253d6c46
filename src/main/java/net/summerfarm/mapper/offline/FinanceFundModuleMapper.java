package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.FinanceFundModule;
import net.summerfarm.model.vo.FinanceFundModuleVO;

import java.time.LocalDateTime;
import java.util.List;

public interface FinanceFundModuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceFundModule record);

    int insertSelective(FinanceFundModule record);

    /**
     * 查看资金模块信息
     * @param startTime
     * @param endTime
     * @return
     */
    FinanceFundModuleVO selectAll(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 资金确认额趋势
     * @param startTime
     * @param endTime
     * @return
     */
    List<FinanceFundModuleVO> selectList(LocalDateTime startTime, LocalDateTime endTime);

    int updateByPrimaryKeySelective(FinanceFundModule record);

    int updateByPrimaryKey(FinanceFundModule record);
}