package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.WarehousePathTime;
import net.summerfarm.model.input.offline.WarehousePathTimeInput;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehousePathTimeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarehousePathTime record);

    int insertSelective(WarehousePathTime record);

    WarehousePathTime selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarehousePathTime record);

    int updateByPrimaryKey(WarehousePathTime record);

    List<WarehousePathTime> selectBySelective(WarehousePathTimeInput selectiveKey);
}