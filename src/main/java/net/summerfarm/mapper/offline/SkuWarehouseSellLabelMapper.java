package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.SkuWarehouseSellLabel;
import org.apache.ibatis.annotations.Param;

public interface SkuWarehouseSellLabelMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SkuWarehouseSellLabel record);

    int insertSelective(SkuWarehouseSellLabel record);

    SkuWarehouseSellLabel selectByPrimaryKey(Long id);

    SkuWarehouseSellLabel selectBySkuWarehouseNo(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);

    int updateByPrimaryKeySelective(SkuWarehouseSellLabel record);

    int updateByPrimaryKey(SkuWarehouseSellLabel record);
}