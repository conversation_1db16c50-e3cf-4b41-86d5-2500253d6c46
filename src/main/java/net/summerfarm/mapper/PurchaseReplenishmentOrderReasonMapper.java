package net.summerfarm.mapper;

import net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason;

public interface PurchaseReplenishmentOrderReasonMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PurchaseReplenishmentOrderReason record);

    int insertSelective(PurchaseReplenishmentOrderReason record);

    PurchaseReplenishmentOrderReason selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseReplenishmentOrderReason record);

    int updateByPrimaryKey(PurchaseReplenishmentOrderReason record);
}