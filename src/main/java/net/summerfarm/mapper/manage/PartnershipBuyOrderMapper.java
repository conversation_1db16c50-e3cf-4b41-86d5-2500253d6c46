package net.summerfarm.mapper.manage;
import net.summerfarm.model.DTO.market.AssociationOrderNoDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Collection;

import net.summerfarm.model.domain.PartnershipBuyOrder;

public interface PartnershipBuyOrderMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(PartnershipBuyOrder record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(PartnershipBuyOrder record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    PartnershipBuyOrder selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PartnershipBuyOrder record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PartnershipBuyOrder record);

    AssociationOrderNoDTO selectAssociationOrderNoByOrderNo(String orderNo);
}