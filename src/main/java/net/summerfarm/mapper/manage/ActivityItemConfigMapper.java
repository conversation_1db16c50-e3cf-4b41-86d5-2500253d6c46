package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.market.ActivityItemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityItemConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityItemConfig record);

    
    int insertSelective(ActivityItemConfig record);

    
    ActivityItemConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityItemConfig record);

    
    int updateByPrimaryKey(ActivityItemConfig record);

    int updateDelFlag(ActivityItemConfig itemConfig);

    ActivityItemConfig getByInfoId(@Param("basicInfoId") Long basicInfoId);



}