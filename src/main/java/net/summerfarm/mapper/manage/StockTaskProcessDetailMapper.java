package net.summerfarm.mapper.manage;

import net.summerfarm.data.dto.StockTaskProcessDetailDTO;
import net.summerfarm.model.domain.StockTaskProcessDetail;
import net.summerfarm.model.input.StockTaskProcessDetailInput;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import net.summerfarm.module.pms.model.vo.AccountStockProcessVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface StockTaskProcessDetailMapper {
    int insert(StockTaskProcessDetail processDetail);

    int insertBatch(List<StockTaskProcessDetail> processDetails);

    /**
     * 将出入库单详情绑定发票关系改变
     * @param id
     * @param state)
     * @return
     */
    int updateState(@Param("id") Integer id, @Param("state") Integer state);

    List<StockTaskProcessDetailVO> selectByProcessId(@Param("stockTaskProcessId")Integer stockTaskProcessId);

    List<StockTaskProcessDetailVO> selectByTaskId(@Param("stockTaskId")Integer stockTaskId);


    StockTaskProcessDetail selectByPrimaryKey(Integer id);

    int deleteByPrimaryKey(Integer id);

    /**
     * 根据任务id查询任务单详情
     * @param taskId
     * @return
     */
    List<StockTaskProcessDetailVO> selectTransferByProcessId(Integer taskId);

    List<StockTaskProcessDetailDTO> selectTransferByProcessIdForSync(Integer taskId);

    List<StockTaskProcessDetailVO> selectByProcess(StockTaskProcessDetail processDetail);

    /**
     * 查询出入库单详情
     * @param stockTaskProcessDetailInput
     * @return
     */
    List<StockTaskProcessDetailVO> selectByProcessList(StockTaskProcessDetailInput stockTaskProcessDetailInput);

    /**
     * 查询出入库单详情
     * @param stockTaskProcessDetailInput
     * @return
     */
    List<AccountStockProcessVO> selectByList(StockTaskProcessDetailInput stockTaskProcessDetailInput);


    List<StockTaskProcessDetail> selectByPurchaseNoAndSku(@Param("type") Integer type, @Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    List<StockTaskProcessDetail> selectTransferUnValidDetail(@Param("stockTaskId") Integer stockTaskId, @Param("sku") String sku);

    Boolean unFinishTemporaryTransferTask(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("listNo") String listNo, @Param("qualityDate") LocalDate qualityDate, @Param("glNo") String glNo);

    /**
     * 根据任务编号查询出入库任务单详情
     * @param stockTaskId
     * @return
     */
    List<StockTaskProcessDetailVO> selectByStockTaskId(Integer stockTaskId,String sku);

    /**
     * 查询采购单入库单
     * @param purchaseNo
     * @return
     */
    List<StockTaskProcessDetail> selectWarehouseByPurchaseNo(String purchaseNo);

    /**
     * 查询入库明细数量
     * @param processDetail
     * @return
     */
    StockTaskProcessDetailVO selectByProcessDetail(StockTaskProcessDetailVO processDetail);

    /**
     * 根据采购单号和sku查询出入库任务单详情
     * @param sku
     * @param listNo
     * @return
     */
    List<StockTaskProcessDetail> selectBySkuAndPurchaseNo(@Param("sku") String sku, @Param("listNo") String listNo);

    /**
     * 初始化出入库单状态
     * @param purchaseNo
     * @param sku
     * @return
     */
    int updateByState(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    StockTaskProcessDetail selectBySkuAndStockStorageItemIdAndListNoAndQDate(@Param("sku") String sku,@Param("id") Integer id,@Param("listNo") String listNo
            ,@Param("qualityDate") LocalDate qualityDate);
}
