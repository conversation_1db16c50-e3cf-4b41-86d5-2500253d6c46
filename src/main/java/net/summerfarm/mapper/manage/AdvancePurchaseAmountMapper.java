package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdvancePurchaseAmount;
import net.summerfarm.model.vo.AdvancePurchaseAmountVO;
import org.springframework.stereotype.Repository;

@Repository
public interface AdvancePurchaseAmountMapper {

    /**
    * 新增记录
    */
    Integer insertAdvancePurchaseAmount(AdvancePurchaseAmount advancePurchaseAmount);

    /**
    * 添加金额记录
    */
    Integer addAdvancePurchaseAmount(AdvancePurchaseAmount advancePurchaseAmount);

    /**
     * 扣减记录
     */
    Integer subAdvancePurchaseAmount(AdvancePurchaseAmount advancePurchaseAmount);

    AdvancePurchaseAmount selectAmount(Integer supplierId);

    AdvancePurchaseAmountVO selectUseAmount(Integer supplierId);

}
