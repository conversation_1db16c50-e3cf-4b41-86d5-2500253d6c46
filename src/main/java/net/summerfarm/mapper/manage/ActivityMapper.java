package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Activity;
import net.summerfarm.model.domain.ActivitySku;
import net.summerfarm.model.input.MallActivityQuery;
import net.summerfarm.model.vo.ActivitySkuVO;
import net.summerfarm.model.vo.ActivityVO;
import net.summerfarm.model.vo.ArrivalNoticeVO;
import net.summerfarm.model.vo.activity.ActivitySkuStatusVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 已废弃，不再使用
 * Created by wjd on 2017/9/25.
 */
@Deprecated
@Repository
public interface ActivityMapper {

//    void insert(Activity activity);
//
//    void update(Activity activity);
//
//    void updateJob(@Param("startJob") String startJob, @Param("endJob") String endJob, @Param("id") int id);
//
//    @RequiresDataPermission(originalField = "a.area_no")
//    List<Activity> select(ActivityVO activityVO);
//
//    List<Activity> selectWithoutPermission(ActivityVO activityVO);

    @RequiresDataPermission(originalField = "a.area_no")
    Activity selectById(int id);

    /**
     * 根据id查询活动
     * @param id 活动id
     * @return 活动数据
     */
    Activity selectActivityById(int id);

//    void delete(Integer id);

//    List<ArrivalNoticeVO> selectActivitySku(@Param("type") Integer type, @Param("storeNo") Integer storeNo);

    /**
     * 判断sku是否在某个城市参加活动
     * @param areaNo
     * @param sku
     * @param now
     * @return
     */
    @Deprecated
    boolean isOnActivity(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("now") LocalDateTime now);

    /**
     * 判断sku是否在某个城市参加活动
     * @param areaNo
     * @param sku
     * @param startTime
     * @param endTime
     * @return
     */
    @Deprecated
    boolean isOnActivityOnTime(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Deprecated
    List<ActivitySkuStatusVO> isOnActivityByAreaNos(@Param("areaNos") Collection<Integer> areaNos, @Param("sku") String sku,
                                                    @Param("now")LocalDateTime now);

//    /**
//     * 是否在其他有效活动中
//     * @param areaNo 城市
//     * @param sku sku
//     * @param activityId 当前活动id
//     * @return
//     */
//    List<Activity> inOtherActiveActivity(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("activityId") Integer activityId);

//    /**
//     * 是否在其他有效活动中判断时间
//     * @param areaNo 城市
//     * @param sku sku
//     * @param activityId 当前活动id
//     * @return
//     */
//    List<Activity> inOtherActiveActivityOnTime(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("activityId") Integer activityId,
//                                               @Param("startTime") Date startTime,@Param("endTime") Date endTime);

    /**
     * 查询特价活动
     * 该sql已收敛活动与活动sku, 修改sql需要考虑活动与活动sku的对应关系是否还正确
     * @param mallActivityQuery 查询条件
     * @return 区域内特价活动详情
     */
    List<Activity> selectOnSale(MallActivityQuery mallActivityQuery);

//    /**
//     * 查询特价活动关联的sku
//     * @param activityId 活动id
//     * @return 特价活动关联的sku详情
//     */
//    List<ActivitySkuVO> selectSkuByActivityId(@Param("activityId") Integer activityId, @Param("areaNo") Long areaNo);
//
//    void insertBatch(@Param("activity") Activity activity, @Param("areas") List<Integer> areas);

//    /**
//     * 取出所有已设活动,但城市关闭的活动
//     * @return  已设活动,但城市关闭的活动
//     */
//    List<Integer> selectNoNearExpiredSkuArea();
//
//    /**
//     * 取最新一条临保活动
//     * @param areaNo 区域
//     * @return 临保活动
//     */
//    ActivityVO selectExpiredSku(@Param("areaNo") Integer areaNo);

//    /**
//     * 判断是否是临保活动
//     * @param sku SKU
//     * @param areaNo 区域
//     * @return
//     */
//    Activity judgeActivity(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 根据extType查询
     * @param extType
     * @return
     */
//    List<ActivityVO> selectByExtType(Integer extType);

    /**
     * 根据活动类型，批量获取生效中的活动
     * @param activityType
     * @param areaNos
     * @return
     */
//    List<Activity> listActIdsByExtType(@Param("activityType") Integer activityType, @Param("areaNos") Set<Integer> areaNos);

    List<Activity> listByExtType();
}
