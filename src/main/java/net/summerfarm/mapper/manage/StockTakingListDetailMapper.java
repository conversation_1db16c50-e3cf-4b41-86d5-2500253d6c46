package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockTakingListDetail;
import net.summerfarm.model.vo.StockTakingListDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface StockTakingListDetailMapper {

    int insertBatch(List<StockTakingListDetail> stockTakingListDetails);

    int delete(Integer id);

    Integer countPurchasesNo(String purchasesNo);

    List<StockTakingListDetailVO> selectByPrimaryKey(Integer id);

    List<StockTakingListDetailVO> selectList(@Param("id") Integer id, @Param("pdName") String pdName, @Param("sku") String sku, @Param("diff") Boolean diff);

    List<StockTakingListDetail> select(StockTakingListDetail stockTakingDetail);

    int updateByPrimaryKey(StockTakingListDetail stockTakingListDetail);

    List<StockTakingListDetail> selectFinishDetails(@Param("areaNo") Integer areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据盘点单id查询盘点详情
     * @param takingId
     * @return
     */
    List<StockTakingListDetail> selectByTakingId(@Param("takingId") Integer takingId);
}
