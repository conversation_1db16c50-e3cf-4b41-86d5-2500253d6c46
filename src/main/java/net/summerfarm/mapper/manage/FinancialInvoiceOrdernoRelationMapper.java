package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FinancialInvoiceOrdernoRelationMapper {

    int insertSelective(FinancialInvoiceOrdernoRelation record);

    /**
     * 批量插入订单与发票信息至关联表
     * @param list 订单与发票信息
     * @return ok
     */
    int insertBatch(List<FinancialInvoiceOrdernoRelation> list);

    FinancialInvoiceOrdernoRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinancialInvoiceOrdernoRelation record);

    /**
     * 获取开票记录与订单的关系信息
     * @param id  发票记录id
     * @return 开票记录与订单的关系信息
     */
    List<FinancialInvoiceOrdernoRelation> selectByFinancialInvoiceId(Long id);

    /**
     * 统计发票绑定订单个数
     * @param id 发票记录id
     * @return 订单个数
     * */
    int countOrdersByInvoiceId(Long id);

    /**
     * 根据订单明细查询票据信息
     *
     * @param orderItems
     * @return {@link List}<{@link FinancialInvoiceOrdernoRelation}>
     */
    List<FinancialInvoiceOrdernoRelation> selectByOrderItems(@Param("oderItems")List<Long> orderItems);
}