package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ProductSyncRecord;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductSyncRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProductSyncRecord record);

    int insertSelective(ProductSyncRecord record);

    ProductSyncRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ProductSyncRecord record);

    int updateByPrimaryKey(ProductSyncRecord record);
}