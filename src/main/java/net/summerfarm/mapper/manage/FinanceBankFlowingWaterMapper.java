package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceBankFlowingWater;
import net.summerfarm.model.input.FinanceBankFlowingWaterInput;
import net.summerfarm.model.vo.FinanceBankFlowingWaterVO;

import java.util.List;

public interface FinanceBankFlowingWaterMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceBankFlowingWater record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(FinanceBankFlowingWater record);

    /**
     * 根据id查询信息
     * @param id
     * @return
     */
    FinanceBankFlowingWater selectByPrimaryKey(Long id);

    /**
     * 根据流水号查询数据
     * @param serialNumber
     * @return
     */
    FinanceBankFlowingWater selectBySerialNumber(String serialNumber);

    /**
     * 流水列表
     * @param financeBankFlowingWaterInput
     * @return
     */
    List<FinanceBankFlowingWaterVO> selectList(FinanceBankFlowingWaterInput financeBankFlowingWaterInput);

    /**
     * 修改
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceBankFlowingWater record);

    /**
     * 修改回待认领
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinanceBankFlowingWater record);
}