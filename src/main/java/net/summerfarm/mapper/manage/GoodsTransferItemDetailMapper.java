package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GoodsTransferItemDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/7  17:45
 */
@Repository
public interface GoodsTransferItemDetailMapper {
    int insertBathDetail(List<GoodsTransferItemDetail> lists);

    List<GoodsTransferItemDetail> selectDetail(Integer id);
}
