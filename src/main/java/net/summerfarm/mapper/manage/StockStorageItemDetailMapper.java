package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockAllocationItemDetail;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.model.domain.StockStorageItemDetail;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2022-02-24
 */
@Repository
public interface StockStorageItemDetailMapper {

    /**
     * 查询入库条目
     * @param stockStorageItemId
     * @return
     */
    List<StockAllocationItemDetail> select(Integer stockStorageItemId);

    /**
     * 查询入库条目
     * @param stockShipmentItemId
     * @return
     */
    List<StockShipmentItemDetail> selectByItemId(Integer stockShipmentItemId);


    StockStorageItemDetail selectOne(StockStorageItemDetail detail);

    void updateByPrimaryKey(StockStorageItemDetail update);

    /**
     * 批次新增入库明细
     * @param processDetails
     */
    void insertBatch(@Param("stockStorageItemId") Integer stockStorageItemId,@Param("list") List<StockAllocationItemDetail> processDetails);

    /**
     * 查询入库条目
     * @param stockStorageItemId
     * @return
     */
    List<StockTaskProcessDetailVO> selectProcess(Integer stockStorageItemId);

    /**
     * 根据任务id查询明细
     * @param id
     * @return
     */
    List<StockTaskProcessDetailVO> selectByTaskId(Integer id);

    /**
     *
     * @param queryItemDetail
     * @return
     */
    StockStorageItemDetail selectSumQuantity(StockStorageItemDetail queryItemDetail);

    /**
     * 新增入库明细
     * @param stockStorageItemDetail
     */
    void insert(StockStorageItemDetail stockStorageItemDetail);

    /**
     * 查询汇总入库明细
     * @param stockStorageItemId
     * @return
     */
    List<StockAllocationItemDetail> selectMergeDetail(Integer stockStorageItemId);

    /**
     * 根据调拨单编号查询sku采购批次
     * @param sku
     * @param batch
     * @return
     */
    List<StockAllocationItemDetail> selectByListNo(String sku, String batch);
}
