package net.summerfarm.mapper.manage;

import org.apache.ibatis.annotations.Param;
import net.summerfarm.model.domain.CompleteDeliveryCustomer;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021/07/09
 */
@Repository
public interface CompleteDeliveryCustomerMapper {

    /**
     * 插入特殊客户信息
     */
    void insertBatchCompleteDelivery(List<CompleteDeliveryCustomer> list);

    int insert(@Param("id") Integer id, @Param("idAdmin") Integer idAdmin, @Param("nameRemakes") String nameRemakes);

    /**
     * 查询当前特殊客户情况
     * @return
     */
    List<CompleteDeliveryCustomer> selectAdmin(@Param("id") Integer id);

    List<CompleteDeliveryCustomer> selectAdminNameRemakes(@Param("id") Integer id);

    /**
     * 删除特殊客户
     * @param id
     * @param idAdmin
     * @return
     */
    int deleteOldAdminId(@Param("id") Integer id, @Param("idAdmin") Integer idAdmin);

    /**
     * 删除特殊客户
     * @param id
     * @return
     */
    int deleteEmpty(@Param("id") Integer id);

    int selectAdminId(@Param("adminId") Integer adminId);

    List<CompleteDeliveryCustomer> accountSum(@Param("storeNo") Integer storeNo);

    List<CompleteDeliveryCustomer> selectByCompleteDeliveryId(@Param("completeDeliveryId") Integer completeDeliveryId);
}
