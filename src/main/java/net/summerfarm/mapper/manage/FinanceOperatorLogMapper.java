package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceOperatorLog;

import java.util.List;

/**
 * <AUTHOR>
 * 财务审核记录表
 */
public interface FinanceOperatorLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceOperatorLog record);

    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(FinanceOperatorLog record);

    FinanceOperatorLog selectByPrimaryKey(Long id);

    /**
     * 查询审核信息
     * @param financeOperatorLog
     * @return
     */
    FinanceOperatorLog selectById(FinanceOperatorLog financeOperatorLog);

    /**
     * 查询操作记录
     * @param financeOperatorLog
     * @return
     */
    List<FinanceOperatorLog> selectByAdvance(FinanceOperatorLog financeOperatorLog);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceOperatorLog record);

    int updateByPrimaryKey(FinanceOperatorLog record);
}