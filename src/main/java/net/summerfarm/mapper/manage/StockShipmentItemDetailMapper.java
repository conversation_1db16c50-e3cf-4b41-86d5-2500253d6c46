package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockAllocationItemDetail;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2022-02-24
 */
@Repository
public interface StockShipmentItemDetailMapper {

    /**
     * 查询出库条目
     * @param stockShipmentItemId
     * @return
     */
    List<StockAllocationItemDetail> select(Integer stockShipmentItemId);

    /**
     * 查询出库条目
     * @param stockShipmentItemId
     * @return
     */
    List<StockShipmentItemDetail> selectByItemId(Integer stockShipmentItemId);

    StockShipmentItemDetail selectOne(@Param("purchaseNo") String purchaseNo,
                                      @Param("stockShipmentItemId") Integer stockShipmentItemId,
                                      @Param("glNo") String glNo,
                                      @Param("qualityDate") LocalDate qualityDate,
                                      @Param("cabinetCode") String cabinetCode);

    void updateOutQuantityAdd(@Param("id") Integer id,
                              @Param("actualOutQuantityAdd") Integer actualOutQuantityAdd);

    /**
     * 批次新增出库明细
     * @param processDetails
     */
    void insertBatch(List<StockShipmentItemDetail> processDetails);

    /**
     * 根据任务id查询明细
     * @param id
     * @return
     */
    List<StockTaskProcessDetailVO> selectByTaskId(Integer id);

}
