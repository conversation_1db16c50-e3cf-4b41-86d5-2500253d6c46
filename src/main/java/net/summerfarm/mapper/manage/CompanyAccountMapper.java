package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CompanyAccount;
import net.summerfarm.model.vo.CompanyAccountVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyAccountMapper {

    int insert(CompanyAccount companyAccount);

    List<CompanyAccountVO> selectList(CompanyAccountVO companyAccountVO);

    CompanyAccountVO selectByPrimaryKey(Integer id);

    int updateByPrimaryKey(CompanyAccount companyAccount);

    CompanyAccount selectOne(CompanyAccount companyAccount);

    List<CompanyAccount> selectFixAccount();
}
