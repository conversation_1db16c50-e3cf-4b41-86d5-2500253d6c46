package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PriceStrategyAuditRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PriceStrategyAuditRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PriceStrategyAuditRecord record);

    int insertSelective(PriceStrategyAuditRecord record);

    PriceStrategyAuditRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PriceStrategyAuditRecord record);

    int updateByPrimaryKey(PriceStrategyAuditRecord record);

    /**
     * 查询审批明细
     * @param auditId 审批id
     * @return PriceStrategyAuditRecord
     */
    List<PriceStrategyAuditRecord> selectByAuditId(@Param("auditId") Long auditId);
}