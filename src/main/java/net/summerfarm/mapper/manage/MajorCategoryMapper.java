package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MajorCategory;
import net.summerfarm.model.vo.MajorCategoryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/1/29  14:25
 */
@Repository
public interface MajorCategoryMapper {

    /** 插入 */
    int insertMajorCategory(MajorCategory majorCategory);

    /** 批量插入 */
    int insertMajorCategoryList(List<MajorCategory> list);

    /** 更新 */
    int updateMajorCategory(MajorCategory majorCategory);

    /** 查询 */
    List<MajorCategoryVO> selectMajorCategory(MajorCategoryVO majorCategory);

    /**
    * 查询详情
    */
    MajorCategoryVO selectMajorCategoryById(Integer id);

    List<MajorCategoryVO> selectNewSku(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<MajorCategoryVO> majorPriceSpu(String pdName);

    List<MajorCategoryVO> majorPriceBySku(@Param("sku") String sku, @Param("adminId") Integer adminId, @Param("direct") Integer direct);

    MajorCategory queryMajorCategory(MajorCategory majorCategory);

    /**
     * 查询是否存在生效的类目报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param categoryId categoryId
     * @return 报价单信息
     */
    Boolean getMajorCategory(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("direct") Integer direct, @Param("categoryId") Integer categoryId);
}
