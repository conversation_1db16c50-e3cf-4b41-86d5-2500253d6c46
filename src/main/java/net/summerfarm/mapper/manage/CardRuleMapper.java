package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CardRule;
import net.summerfarm.model.vo.CardRuleVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CardRuleMapper {

    int insert(CardRule cardRule);

    List<CardRuleVO> selectVO(CardRuleVO cardRuleVO);

    int update(CardRule cardRule);

    CardRule selectByPrimaryKey(Integer id);

    List<CardRule> selectList(CardRule cardRule);
}
