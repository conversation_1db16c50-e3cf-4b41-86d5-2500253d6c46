package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.DynamicPriceRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceRecordMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(DynamicPriceRecord record);

    
    int insertSelective(DynamicPriceRecord record);

    
    DynamicPriceRecord selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(DynamicPriceRecord record);

    
    int updateByPrimaryKey(DynamicPriceRecord record);

    List<DynamicPriceRecord> listBySkuTaskId(@Param("skuTaskId") Long skuTaskId);
}