package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Cash;
import net.summerfarm.model.domain.CashDetail;
import net.summerfarm.model.vo.CashVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface CashMapper {
    int deleteByPrimaryKey(Integer cashId);

    int insert(Cash record);

    int insertSelective(Cash record);

    Cash selectByPrimaryKey(Integer cashId);

    int updateByPrimaryKeySelective(Cash record);

    int updateByPrimaryKey(Cash record);

    /**
     * 统计未审核金额
     * @param adminId 管理员ID
     * @return 金额
     */
    BigDecimal countUnAuditAmount(@Param("adminId") Integer adminId);

    /**
     * 统计当天已发放金额
     * @param adminId 管理员ID
     * @return 金额
     */
    BigDecimal countSameDayAmount(@Param("adminId") Integer adminId);

    /**
     * 统计待申请金额
     * @param adminId 管理员ID
     * @return 金额
     */
    BigDecimal countUnApplyAmount(@Param("adminId") Integer adminId);

    /**
     * 统计已发放红包总额（已审核通过）
     * @param adminId 管理员ID
     * @return 金额
     */
    BigDecimal countTotalRpAmount(@Param("adminId") Integer adminId);

    /**
     * 查询
     * @param cashVO 查询条件
     * @return list
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<CashDetail> selectByCashVO(CashVO cashVO);
    /**
     * 查询提现记录
     * @param cashVO 查询条件
     * @return list
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<CashDetail> selectRecordByCashVO(CashVO cashVO);
}