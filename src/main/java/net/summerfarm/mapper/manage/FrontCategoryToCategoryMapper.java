package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FrontCategoryToCategory;
import net.summerfarm.model.vo.EsFrontCategoryVO;

import java.util.List;

public interface FrontCategoryToCategoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FrontCategoryToCategory record);

    int insertSelective(FrontCategoryToCategory record);

    FrontCategoryToCategory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FrontCategoryToCategory record);

    int updateByPrimaryKey(FrontCategoryToCategory record);

    /**
     * 更具前台类目查询映射关系
     * @param frontCategoryId
     * @return
     */
    List<FrontCategoryToCategory> selectByFrontCategoryId(Integer frontCategoryId);

    List<EsFrontCategoryVO> selectFrontCategory(Long pdId);
}