package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DatabaseHandleRecordDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatabaseHandleRecordDetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DatabaseHandleRecordDetail record);

    int insertSelective(DatabaseHandleRecordDetail record);

    DatabaseHandleRecordDetail selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DatabaseHandleRecordDetail record);

    int updateByPrimaryKey(DatabaseHandleRecordDetail record);

    void insertBatch(List<DatabaseHandleRecordDetail> detailList);

    List<DatabaseHandleRecordDetail> selectPageList(@Param("restoreId") Integer restoreId, @Param("pageStart") int pageIndex, @Param("pageSize") int pageSize);
}