package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MarketCouponSendDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MarketCouponSendDetailMapper {

    /**
     * 批量新增发放记录详情
     * @param detailList 发放记录详情实体集合
     * @return 受影响的行数
     */
    int insertBatch(List<MarketCouponSendDetail> detailList);

    /**
     * 新增发放记录详情
     * @param couponSendDetail 发放记录详情实体
     * @return 受影响的行数
     */
    int insert(MarketCouponSendDetail couponSendDetail);

    /**
     * 更新发放记录详情状态
     * @param sendId 发放记录详情id
     * @param status 状态
     */
    void updateStatus(Long sendId, Integer status);

    /**
     * 更新未使用优惠券的发放详情记录为已撤回
     * @param sendId 发放记录id
     * @param status 状态
     * @param mIdList 商户id集合
     */
    void updateStatusByMids(@Param("sendId") Long sendId, @Param("status") Integer status, @Param("mIds") List<Long> mIdList);

    /**
     * 通过发放记录id查询详情集合
     * @param sendId 发放记录id
     * @return 发放记录详情集合
     */
    List<MarketCouponSendDetail> selectList(Long sendId);

    /**
     * 批量更新发放详情状态
     * @param ids 详情id集合
     * @param status 状态
     */
    void updateBatch(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    MarketCouponSendDetail selectByPrimaryKey(Long id);

    /**
     * 根据发放设置id删除
     * @param sendId
     * @return
     */
    int deleteBySendId(Long sendId);

    /**
     * 更新未使用优惠券的发放详情记录为已撤回
     * @param status 状态
     * @param sendDetailIds id集合
     */
    int updateStatusByIds(@Param("status") Integer status, @Param("sendDetailIds") List<Long> sendDetailIds);

    /**
     * 更新未使用优惠券的发放详情记录为已撤回
     * @param sendId 发放记录id
     * @param status 更新后状态
     * @param originStatus 更新前状态
     * @param mIdList id集合
     */
    void updateStatusByMidsAndOrigin(@Param("sendId") Long sendId, @Param("status") Integer status,
                                     @Param("originStatus") Integer originStatus, @Param("mIds") List<Long> mIdList);
}
