package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.OuterPlatformPushRecord;
import net.summerfarm.model.vo.OuterPlatformPushRecordVO;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接-外部平台推送记录
 * @createTime 2021年10月19日 15:22:00
 */
public interface OuterPlatformPushRecordMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insert(OuterPlatformPushRecord record);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insertSelective(OuterPlatformPushRecord record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    OuterPlatformPushRecord selectByPrimaryKey(Long id);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OuterPlatformPushRecord record);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKey(OuterPlatformPushRecord record);

    /**
     *  根据上下架类型、sku、城市、adminId查询最新推送记录
     * @param outerPlatformPushRecordVO
     * @return
     */
    OuterPlatformPushRecord selectRecordByLatestOne(OuterPlatformPushRecordVO outerPlatformPushRecordVO);

    /**
     * 根据鲜沐订单号查询推送记录
     * @param xmOrderNo
     * @return
     */
    OuterPlatformPushRecord selectRecordByXmOrderNo(String xmOrderNo, Integer type);

}