package net.summerfarm.mapper.manage;

import net.summerfarm.enums.PriceStrategyAuditEnum;
import net.summerfarm.model.domain.PriceStrategyAudit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PriceStrategyAuditMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PriceStrategyAudit record);

    int insertSelective(PriceStrategyAudit record);

    PriceStrategyAudit selectByPrimaryKey(Long id);

    PriceStrategyAudit selectWaitingAudit(Long id);

    int updateByPrimaryKeySelective(PriceStrategyAudit record);

    int updateByPrimaryKey(PriceStrategyAudit record);

    /**
     * 查询审核中的数据
     * @param areaNo 城市编号
     * @param sku    sku
     * @return PriceStrategyAudit
     */
    List<PriceStrategyAudit> selectInAudit(@Param("areaNo") Integer areaNo, @Param("sku") String sku);
}