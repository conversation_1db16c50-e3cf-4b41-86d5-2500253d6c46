package net.summerfarm.mapper;

import net.summerfarm.model.domain.OrderHeyTea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderHeyTeaMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_hey_tea
     *
     * @mbg.generated
     */
    int insert(OrderHeyTea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_hey_tea
     *
     * @mbg.generated
     */
    int insertSelective(OrderHeyTea record);

    /**
     * 根据喜茶订单号查询是否有记录
     *
     * @param htOrderCode
     * @return
     */
    List<OrderHeyTea> selectByHtOrderCode(@Param("htOrderCode") String htOrderCode);

    /**
     * 根据订单号查询喜茶信息
     *
     * @param orderNos
     * @return
     */
    List<OrderHeyTea> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 统计喜茶订单
     *
     * @param orderNos 订单号
     * @return int
     */
    int countByOrderNos(@Param("orderNos") List<String> orderNos);
    /**
     * 删除喜茶记录
     *
     * @param orderNo
     * @return
     */
    int deleteByOrderNo(@Param("orderNo") String orderNo);
}