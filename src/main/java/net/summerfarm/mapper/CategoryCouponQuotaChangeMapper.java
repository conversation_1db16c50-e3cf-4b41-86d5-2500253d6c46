package net.summerfarm.mapper;

import net.summerfarm.model.domain.CategoryCouponQuotaChange;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 品类券额度变化
 *
 * <AUTHOR>
 * @Date 2023/3/2 18:30
 */
@Repository
public interface CategoryCouponQuotaChangeMapper {
    /**
     * 根据客情id查询
     *
     * @param bizId 客情id
     * @return {@link CategoryCouponQuotaChange}
     */
    CategoryCouponQuotaChange selectByBizId(@Param("bizId") Integer bizId);

    /**
     * 根据客情id删除记录
     *
     * @param bizId 客情id
     */
    void delByBizId(@Param("bizId") Integer bizId);

    /**
     * 根据优惠券id查询记录
     *
     * @param merchantCouponId 优惠券id
     * @return {@link CategoryCouponQuotaChange}
     */
    CategoryCouponQuotaChange selectByMerchantCouponId(@Param("merchantCouponId") Integer merchantCouponId);

    int updateSelective(@Param("updated")CategoryCouponQuotaChange updated);

}
