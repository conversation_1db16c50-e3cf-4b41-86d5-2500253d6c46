package net.summerfarm.service;

import net.summerfarm.model.domain.ProcessOrderSkuSaveCmd;
import net.summerfarm.model.domain.StockTaskOrderSku;
import net.summerfarm.model.domain.StockTaskProcessDetail;
import net.summerfarm.model.domain.StockTaskProcessOrderSku;

/**
 * @Description
 * @Date 2023/2/8 16:59
 * @<AUTHOR>
 */
public interface StockTaskProcessOrderSkuService {

    /**
     * 保存出库单订单明细
     *
     * <AUTHOR>
     * @date 2023/2/8 17:08
     * @param saveCmd 出库单详情
     */
    void saveStockTaskProcessOrderSku(ProcessOrderSkuSaveCmd saveCmd);

}
