package net.summerfarm.service;

import net.summerfarm.model.domain.ProductsPropertyValue;
import net.summerfarm.model.domain.ProductsPropertyValueInfo;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @date 2022/10/9 14:23
 */
public interface ProductsPropertyValueService {

    /**
     * 根据PdId和属性id查询
     *
     * @param propertyId
     * @param pdId
     * @return
     */
    ProductsPropertyValue selectByPdIdAndPropertyId(Long pdId, Long propertyId);

    /**
     * 根据pdId值查询属性值
     * @param pdIds
     * @return
     */
    List<ProductsPropertyValue> selectByPdIds(List<Long> pdIds);

    /**
     * 查指定商品的指定属性值列表
     * @param pdId
     * @param propertyId
     * @return
     */
    List<ProductsPropertyValueInfo>  selectByPdIdAndProductsPropertyIds(Long pdId, List<Long> propertyId);

    /***
     * @author: lzh
     * @description: 查询sku属性
     * @date: 2024/4/19 18:06
     * @param: [pdId, sku, newArrayList]
     * @return: java.util.List<net.summerfarm.model.domain.ProductsPropertyValueInfo>
     **/
    List<ProductsPropertyValueInfo> selectByPdIdAndSkuAndProductsPropertyIds(Long pdId, String sku, List<Long> propertyId);
}
