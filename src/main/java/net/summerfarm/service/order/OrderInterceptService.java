package net.summerfarm.service.order;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.enums.AfterSaleTypeEnums;
import net.summerfarm.enums.OrdersTypeEnum;
import net.summerfarm.facade.ofc.OfcOrderCommandFacade;
import net.summerfarm.facade.ofc.dto.InterceptOrderCheckDTO;
import net.summerfarm.facade.ofc.input.InterceptOrderInput;
import net.summerfarm.mapper.manage.DeliveryPlanMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.model.domain.DeliveryPlan;
import net.summerfarm.model.domain.Orders;
import net.summerfarm.model.vo.order.BatchOperateResultVO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderInterceptService extends BaseService {

    @Autowired
    private OfcOrderCommandFacade ofcOrderCommandFacade;

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private OrdersMapper ordersMapper;

    /**
     * 批处理拦截订单
     *
     * @param orderNos         订单号
     * @param remark           备注
     * @param afterSalesReason 售后原因
     * @param deliveryPlanId   交货计划id
     * @param storeNo          城配仓号
     * @return {@link CommonResult}<{@link BatchOperateResultVO}>
     */
    public CommonResult<BatchOperateResultVO> batchInterceptOrder(List<String> orderNos,
                                                                  String remark,
                                                                  Integer afterSalesReason,
                                                                  Integer deliveryPlanId,
                                                                  Integer storeNo){
        String afterSaleType = null;
        if (afterSalesReason == null){
            afterSaleType = AfterSaleTypeEnums.DISCONTINUED.getTypeDesc();
        }else if (AfterSaleTypeEnums.DISCONTINUED.getType().equals(afterSalesReason) ){
            afterSaleType = AfterSaleTypeEnums.DISCONTINUED.getTypeDesc();
        }else if (AfterSaleTypeEnums.CUSTOM_REASON.getType().equals(afterSalesReason)){
            afterSaleType = AfterSaleTypeEnums.CUSTOM_REASON.getTypeDesc();
        }else if(AfterSaleTypeEnums.OTHER_REASON.getType().equals(afterSalesReason)){
            afterSaleType = AfterSaleTypeEnums.OTHER_REASON.getTypeDesc();
        }
        // 先处理拦截省心送订单的单个配送计划
        if (deliveryPlanId != null){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryPlanId);
            InterceptOrderCheckDTO interceptOrderCheckDTO = ofcOrderCommandFacade.batchInterceptOrder(Collections.singletonList(new InterceptOrderInput(
                            deliveryPlan.getOrderNo(), deliveryPlan.getDeliveryTime(), deliveryPlan.getContactId())),
                    afterSaleType, remark, getAdminName());
            if (!CollectionUtils.isEmpty(interceptOrderCheckDTO.getFailedOrderList())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, interceptOrderCheckDTO.getFailedOrderList().get(0).getFailedReason());
            }else {
                return CommonResult.ok(new BatchOperateResultVO(1, Collections.emptyList()));
            }
        }
        // 直接根据传的订单号 查出 所有的配送计划，然后交给OFC处理
        List<DeliveryPlan> deliveryPlans = deliveryPlanMapper.listDeliveryPlanByOrderNoList(orderNos);
        if (CollectionUtils.isEmpty(deliveryPlans)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "拦截失败，没有配送计划");
        }
        if (CollectionUtils.isEmpty(orderNos)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单号不能为空");
        }
        List<Orders> ordersFromDb = ordersMapper.queryByOrderNoList(orderNos);
        Map<String, Orders> ordersMap = ordersFromDb.stream().collect(Collectors.toMap(Orders::getOrderNo, o -> o));
        List<InterceptOrderInput> orderList = new ArrayList<>(deliveryPlans.size());
        for (DeliveryPlan deliveryPlan : deliveryPlans) {
            Orders order = ordersMap.get(deliveryPlan.getOrderNo());
            if (order == null){
                log.info("订单不存在 order >>> {}", deliveryPlan.getOrderNo());
                continue;
            }
            if (OrdersTypeEnum.SAVE_WORRY.getId().equals(order.getType())
                    && !LocalDate.now().equals(deliveryPlan.getDeliveryTime())){
                log.info("省心送订单的批量拦截只拦截配送日期是今天的！ order >>> {}, deliveryTime {}", deliveryPlan.getOrderNo(), deliveryPlan.getDeliveryTime());
                continue;
            }

            orderList.add(new InterceptOrderInput(
                    deliveryPlan.getOrderNo(), deliveryPlan.getDeliveryTime(), deliveryPlan.getContactId()));
        }
        if (CollectionUtils.isEmpty(orderList)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "当前无可拦截的订单");
        }
        // 交给OFC执行（校验->发TMS->处理TMS返回结果等）
        InterceptOrderCheckDTO interceptOrderCheckDTO = ofcOrderCommandFacade.batchInterceptOrder(orderList, afterSaleType, remark, getAdminName());

        // 如果只有一条则返回错误信息
        if (deliveryPlans.size() == 1 && !CollectionUtils.isEmpty(interceptOrderCheckDTO.getFailedOrderList())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, interceptOrderCheckDTO.getFailedOrderList().get(0).getFailedReason());
        }
        return CommonResult.ok(new BatchOperateResultVO(interceptOrderCheckDTO.getSuccessOrderList().size(),
                interceptOrderCheckDTO.getFailedOrderList().stream().map(o -> o.getFailedOrder().getOrderNo()).collect(Collectors.toList())));
    }

}
