package net.summerfarm.service;

import net.summerfarm.model.DTO.FinanceAuditRecordDTO;
import net.summerfarm.model.domain.SettlementConfig;

import java.math.BigDecimal;

/**
 * @description 财务审核审批流Service
 * <AUTHOR>
 * @date 2022/1/18 18:25
 */
public interface FinanceAuditRecordService {

    /**生成审核流
     * 生鲜通过钉钉组织架构获取对应的审核审批流
     * 标品则按照配置去确定对应的审核审批流
     *
     * @param type 1 预付单 2 对账单
     * @param pdType 1 生鲜 2 品牌
     * @param totalAmount 预付单/对账单总金额
     * @return
     */
    FinanceAuditRecordDTO generateAuditInfo(Integer type, Integer pdType, BigDecimal totalAmount);


    /**查看是否满足免审批
     *
     * @param config 审核配置
     * @param totalAmount 总金额
     * @return
     */
    Boolean checkSkipApprove(SettlementConfig config, BigDecimal totalAmount);

}
