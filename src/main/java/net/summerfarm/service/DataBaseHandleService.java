package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;

public interface DataBaseHandleService {
    /**
     * 创建还原点
     *
     * @param desc 还原点备注
     * @return 返回还原点id
     */
    Integer createRestore(String desc);

    /**
     * 添加还原项
     *
     * @param restoreId 还原点id
     * @param oldData   原数据
     * @param newData   修改数据
     * @param clazz     数据类型
     * @param idField   主键字段名
     * @param fields    对比字段、null对比所有字段
     */
    <T> void addRestoreData(Integer restoreId, Object oldData, Object newData, Class<T> clazz, String idField, String... fields);

    /**
     * 一键还原
     *
     * @param restoreId 还原点
     * @return AjaxResult
     */
    AjaxResult snap(Integer restoreId);
}
