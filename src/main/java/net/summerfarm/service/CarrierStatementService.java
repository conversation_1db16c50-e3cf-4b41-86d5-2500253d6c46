package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.CarrierStatement;
import net.summerfarm.model.domain.CarrierStatementExtras;
import net.summerfarm.model.vo.CarrierStatementVo;

/**
 * @Classname CarrierStatementService
 * @Description 配送结算单相关.
 * @Date 2022/1/4 10:41
 * @Created by hx
 */
public interface CarrierStatementService {

    /**
     * 分页查看承运商结算单
     * @param carrierStatement
     * @param pageIndex
     * @param pageSize
     * @return 承运商结算单
     */
    AjaxResult selectCarrierStatement(CarrierStatementVo carrierStatement, int pageIndex, int pageSize);

    /**
     * 查看杂费信息
     * @param carrierStatement
     * @return 杂费信息
     */
    AjaxResult selectCarrierStatementExtras(CarrierStatement carrierStatement);

    /**
     * 新增杂费
     * @param carrierStatementExtras
     * @return
     */
    AjaxResult addExtras(CarrierStatementExtras carrierStatementExtras);

    /**
     * 更新杂费
     * @param carrierStatementExtras
     * @return
     */
    AjaxResult updateExtras(CarrierStatementExtras carrierStatementExtras);

    /**
     * 导出结算单
     * @param carrierStatement
     * @return
     */
    AjaxResult exportCarrierStatement(CarrierStatementVo carrierStatement);

    /**
     * 生成承运商结算单定时任务
     * @return
     */
    AjaxResult carrierStatementTask();

    /**
     * 生成前置仓费
     * @return
     */
    AjaxResult deliveryCarFeeTask();
}
