package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.HelpOrderVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/3/14
 */
public interface HelpOrderService {
    AjaxResult helpOrder(HelpOrderVO helpOrderVO);

    AjaxResult batchHelpOrder(MultipartFile file);

    void download(String exportId);

    void sendGiftOrderMsg(String orderNo);

    AjaxResult getGiftAmountLimit();

    void batchHelpOrderTemplate(HttpServletResponse response) throws IOException;

    /**
     * 喜茶批量代下单
     *
     * @param file
     * @return
     */
    AjaxResult heyTeaBatchHelperOrder(MultipartFile file);

    /**
     * 喜茶模板下载
     *
     * @param response
     * @throws IOException
     */
    void heyTeaBatchHelpOrderTemplate(HttpServletResponse response) throws IOException;
}
