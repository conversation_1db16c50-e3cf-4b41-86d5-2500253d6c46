package net.summerfarm.service.cargoInsp.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.StorageLocation;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.StockInspectDetailMapper;
import net.summerfarm.model.domain.StockInspectDetail;
import net.summerfarm.model.domain.Supplier;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.StockInspectDetailVO;
import net.summerfarm.service.cargoInsp.CargoInspService;
import net.summerfarm.service.cargoInsp.dto.StockInspectDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CargoInspServiceImpl implements CargoInspService {

    @Resource
    private StockInspectDetailMapper stockInspectDetailMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public void createCargo(StockInspectDTO stockInspectDTO) {

        StockInspectDetail query = new StockInspectDetail();
        query.setAreaNo(stockInspectDTO.getWarehouseNo().intValue());
        query.setSku(stockInspectDTO.getSku());
        query.setBatch(stockInspectDTO.getBatch());
        query.setStatus(0);
        query.setStockTaskProcessId(stockInspectDTO.getStockStorageTaskId().intValue());
        List<StockInspectDetailVO> select = stockInspectDetailMapper.select(query);
        if (CollectionUtils.isNotEmpty(select)) {
            log.info("存在未完成货检的任务,未完成货检任务id为:{}", select.stream().map(StockInspectDetailVO::getId).collect(Collectors.toList()));
            return;
        }

        StockInspectDetail stockInspectDetail = new StockInspectDetail(stockInspectDTO.getSku(),
                stockInspectDTO.getStockStorageTaskId().intValue(), null, LocalDateTime.now());
        InventoryVO inventoryVO = inventoryMapper.selectBySkuAndAreaNo(stockInspectDTO.getSku(), stockInspectDTO.getWarehouseNo().intValue());
        stockInspectDetail.setSupplier(stockInspectDTO.getSupplier());
        stockInspectDetail.setSupplierId(stockInspectDTO.getSupplierId().intValue());
        stockInspectDetail.setQuantity(stockInspectDTO.getQuantity());
        stockInspectDetail.setInQuantity(stockInspectDTO.getInQuantity());
        stockInspectDetail.setStatus(NumberUtils.INTEGER_ZERO);
        stockInspectDetail.setPdName(stockInspectDTO.getPdName());
        stockInspectDetail.setWeight(stockInspectDTO.getSpecification());
        stockInspectDetail.setType(stockInspectDTO.getType());
        stockInspectDetail.setStorageLocation(stockInspectDTO.getStorageLocation());
        stockInspectDetail.setStorageMethod(stockInspectDTO.getStorageLocation() != null ? StorageLocation.getTypeById(stockInspectDTO.getStorageLocation()) : null);
        stockInspectDetail.setQualityTime(inventoryVO.getQualityTime());
        stockInspectDetail.setQualityTimeUnit(inventoryVO.getQualityTimeUnit());
        stockInspectDetail.setBatch(stockInspectDTO.getBatch());
        stockInspectDetail.setUnit(inventoryVO.getUnit());
        stockInspectDetail.setAreaNo(stockInspectDTO.getWarehouseNo().intValue());
        stockInspectDetail.setTenantId(stockInspectDTO.getTenantId());
        stockInspectDetailMapper.insert(stockInspectDetail);
    }
}
