package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.model.DTO.DynamicPriceSkuRecordDTO;
import net.summerfarm.model.DTO.inventory.DynamicPriceSkuTaskDTO;
import net.summerfarm.model.DTO.inventory.DynamicPriceTaskDTO;
import net.summerfarm.model.DTO.inventory.DynamicPriceTaskQueryDTO;
import net.summerfarm.model.DTO.inventory.ModelConfigDTO;
import net.summerfarm.model.DTO.inventory.PriceModelDTO;
import net.summerfarm.model.DTO.inventory.WhiteListSkuDTO;
import net.summerfarm.model.DTO.inventory.WhiteListSkuDetailDTO;
import net.summerfarm.model.domain.DynamicPriceField;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.common.result.CommonResult;

/**
 * @author: <EMAIL>
 * @create: 2023/1/28
 */
public interface DynamicPriceService {

    /**
     * 新增白名单商品
     * @param whiteListSkuDTO
     * @return
     */
    CommonResult<Boolean> addWhiteListSku(WhiteListSkuDTO whiteListSkuDTO);

    /**
     * 编辑白名单商品(编辑仓库)
     * @param whiteListSkuDTO
     * @return
     */
    CommonResult<Boolean> updateWhiteListSku(WhiteListSkuDTO whiteListSkuDTO);

    /**
     * 删除白名单商品
     * @param sku
     * @return
     */
    CommonResult<Boolean> deleteWhiteListSku(String sku);

    /**
     * 白名单商品分页查询
     * @param whiteListSkuDTO
     * @return
     */
    CommonResult<PageInfo<WhiteListSkuDetailDTO>> pageWhiteListSku(WhiteListSkuDTO whiteListSkuDTO);

    /**
     * 新增定价模型
     * @param priceModelDTO
     * @return
     */
    CommonResult<Boolean> savePriceModel(PriceModelDTO priceModelDTO);

    /**
     * 修改定价模型
     * @param priceModelDTO
     * @return
     */
    CommonResult<Boolean> updatePriceModel(PriceModelDTO priceModelDTO);

    /**
     * 删除定价模型
     * @param modelConfigId
     * @return
     */
    CommonResult<Boolean> deletePriceModel(Long modelConfigId);

    /**
     * 可配置字段列表
     * @return
     */
    CommonResult<List<DynamicPriceField>> listFields();

    /**
     * 展示定价模型(编辑回显用)
     * @param categoryType
     * @return
     */
    CommonResult<PriceModelDTO> showPriceModel(Integer categoryType);

    /**
     * 动态定价任务分页查询(仓+类目类型维度)
     * @param queryDTO
     * @return
     */
    CommonResult<PageInfo<DynamicPriceTaskDTO>> pageTask(DynamicPriceTaskQueryDTO queryDTO);

    /**
     * sku动态定价-定价因子信息分页查询
     * @param taskId
     * @param pageInput
     * @return
     */
    CommonResult<PageInfo<DynamicPriceSkuTaskDTO>> pageSkuTask(Long taskId, BasePageInput pageInput);

    /**
     * sku动态定价-定价记录明细分页查询
     * @param skuTaskId
     * @param pageInput
     * @return
     */
    CommonResult<PageInfo<DynamicPriceSkuRecordDTO>> pageSkuRecord(Long skuTaskId, BasePageInput pageInput);

    /**
     * 任务时刻对应的定价模型详情
     * @param taskId
     * @return
     */
    CommonResult<ModelConfigDTO> showPriceModelDetail(Long taskId);

}
