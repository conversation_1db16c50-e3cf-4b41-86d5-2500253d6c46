package net.summerfarm.service.finance.impl;

import net.summerfarm.biz.finance.dto.FinancialInvoiceOrderDTO;
import net.summerfarm.biz.finance.input.FinancialInvoiceInput;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Orders;
import net.summerfarm.service.FinancialInvoiceService;
import net.summerfarm.service.finance.FinancialInvoiceSaveService;
import net.summerfarm.service.order.OrderQueryService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 发票保存服务实现类
 * @author: George
 * @date: 2025-03-17
 **/
@Service
public class FinancialInvoiceSaveServiceImpl implements FinancialInvoiceSaveService {

    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private FinancialInvoiceService financialInvoiceService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult invoiceSaveBySellingEntity(FinancialInvoiceInput financialInvoiceInput) {
        if (financialInvoiceInput ==null) {
            throw new ParamsException("参数不能为空");
        }
        List<FinancialInvoiceInput> financialInvoiceInputs = preGroupBySellingEntity(financialInvoiceInput);
        for (FinancialInvoiceInput invoiceInput : financialInvoiceInputs) {
            AjaxResult ajaxResult = financialInvoiceService.invoiceSave(invoiceInput);
            if (!ajaxResult.isSuccess()) {
                throw new BizException(ajaxResult.getMsg());
            }
        }
        return AjaxResult.getOK();
    }

    /**
     * 根据销售主体分组
     * @param financialInvoiceInput
     * @return
     */
    public List<FinancialInvoiceInput> preGroupBySellingEntity(FinancialInvoiceInput financialInvoiceInput) {
        if (financialInvoiceInput == null) {
            return Collections.emptyList();
        }
        List<FinancialInvoiceOrderDTO> orderList = financialInvoiceInput.getFinancialInvoiceOrderDTOList();
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        List<String> orderNos = orderList.stream().map(FinancialInvoiceOrderDTO::getOrderNo).collect(Collectors.toList());
        List<Orders> orders = orderQueryService.queryByOrderNos(orderNos);
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        Map<String, String> orderSellingEntityNameMap = orders.stream().collect(Collectors.toMap(Orders::getOrderNo, Orders::getSellingEntityName));
        orderList.forEach(order -> order.setSellingEntityName(orderSellingEntityNameMap.get(order.getOrderNo())));
        // 账期不根据销售主体分组
        if (!StringUtils.isEmpty(financialInvoiceInput.getBillNumber())) {
            financialInvoiceInput.setSellingEntityName(orderList.get(0).getSellingEntityName());
            return Collections.singletonList(financialInvoiceInput);
        }
        // 将financialInvoiceInput里的订单按销售主体分组，组装多个FinancialInvoiceInput
        return orderList.stream().collect(Collectors.groupingBy(FinancialInvoiceOrderDTO::getSellingEntityName)).values().stream().map(list -> {
            FinancialInvoiceInput input = new FinancialInvoiceInput();
            input.setId(financialInvoiceInput.getId());
            input.setInvoiceId(financialInvoiceInput.getInvoiceId());
            input.setInvoiceType(financialInvoiceInput.getInvoiceType());
            input.setBelongType(financialInvoiceInput.getBelongType());
            input.setPersonName(financialInvoiceInput.getPersonName());
            input.setCreatorRemark(financialInvoiceInput.getCreatorRemark());
            input.setPersonMail(financialInvoiceInput.getPersonMail());
            input.setCreatorName(financialInvoiceInput.getCreatorName());
            input.setMId(financialInvoiceInput.getMId());
            input.setBillNumber(financialInvoiceInput.getBillNumber());
            input.setSellingEntityName(list.get(0).getSellingEntityName());
            input.setFinancialInvoiceOrderDTOList(list);
            return input;
        }).collect(Collectors.toList());
    }
}
