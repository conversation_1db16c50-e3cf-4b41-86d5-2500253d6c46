package net.summerfarm.service.finance.impl;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.biz.finance.constant.BwInvoiceConstant;
import net.summerfarm.biz.finance.dto.FinancialInvoiceSellerInfoDTO;
import net.summerfarm.facade.bms.fms.SellingEntityFacade;
import net.summerfarm.service.ConfigService;
import net.summerfarm.service.finance.FinancialSellingEntityQueryService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-03-25
 **/
@Service
public class FinancialSellingEntityQueryServiceImpl implements FinancialSellingEntityQueryService {

    @Resource
    private SellingEntityFacade sellingEntityFacade;

    @Resource
    private ConfigService configService;

    @Override
    public Map<String, FinancialInvoiceSellerInfoDTO> getSellingEntityInfo(Set<String> sellingEntitySets) {
        // 获取销售主体信息
        Map<String, FinancialInvoiceSellerInfoDTO> collect = sellingEntityFacade.querySellingEntityList(sellingEntitySets)
                .stream()
                .collect(Collectors.toMap(FinancialInvoiceSellerInfoDTO::getSellingEntityName, dto -> dto));

        // 一般都有销售主体  没有的话走默认销售主体兜底下
        FinancialInvoiceSellerInfoDTO sellerInfoDTO = sellingEntityFacade.queryDefaultSellingEntity();
        sellingEntitySets.forEach(sellingEntityName -> collect.computeIfAbsent(sellingEntityName, key -> sellerInfoDTO));

        // 获取开票账号配置信息
        String invoiceIssuerInfo = configService.getValue(BwInvoiceConstant.INVOICE_ISSUER);
        if (StringUtils.isEmpty(invoiceIssuerInfo)) {
            throw new BizException("请联系管理员配置开票账号信息");
        }

        // 解析 JSON 配置
        Map<String, JSONObject> invoiceIssuerMap = Optional.ofNullable(JSONObject.parseObject(invoiceIssuerInfo))
                .map(obj -> obj.getInnerMap().entrySet().stream()
                        .filter(entry -> entry.getValue() instanceof JSONObject)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> (JSONObject) entry.getValue())))
                .orElse(Collections.emptyMap());

        // 遍历销售主体，设置账密信息
        collect.forEach((sellingEntityName, sellerInfo) -> {
            JSONObject info = invoiceIssuerMap.get(sellingEntityName);
            if (info == null) {
                throw new BizException("销售主体 [" + sellingEntityName + "] 未配置开票账号信息，请联系管理员");
            }
            sellerInfo.setLoginName(info.getString("loginName"));
            sellerInfo.setPassword(info.getString("password"));
            sellerInfo.setAreaCode(info.getString("areaCode"));

            if (StringUtils.isEmpty(sellerInfo.getLoginName()) || StringUtils.isEmpty(sellerInfo.getPassword()) || StringUtils.isEmpty(sellerInfo.getAreaCode())) {
                throw new BizException("销售主体 [" + sellingEntityName + "] 的开票账号信息不完整，请联系管理员");
            }
        });

        return collect;
    }
}
