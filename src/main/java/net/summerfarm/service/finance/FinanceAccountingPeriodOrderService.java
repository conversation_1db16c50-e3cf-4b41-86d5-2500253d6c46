package net.summerfarm.service.finance;

import com.aliyun.teautil.Common;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FinanceAccountingAuditRecord;
import net.summerfarm.model.domain.FinanceSettlement;
import net.summerfarm.model.input.AccountingPeriodOrderQuery;
import net.summerfarm.model.input.BillQueryInput;
import net.summerfarm.model.input.finance.AccountingPeriodOrderInput;
import net.summerfarm.model.input.finance.ReissueBillInput;
import net.summerfarm.model.vo.BillInfoVo;
import net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO;
import net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderSummary;
import net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderResult;
import net.summerfarm.model.vo.finance.FinancePeriodDetailVO;
import net.summerfarm.model.vo.finance.FinanceStoreDetailVo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/3 15:11
 */
public interface FinanceAccountingPeriodOrderService {
    /**
     * 定时任务生成账单
     */
    void generateAccountingPeriodBill();

    /**
     * 品牌维度生成账单
     *
     * @param financeSettlements 品牌出账信息
     * @param mIds               用于补发，被补发的品牌下的门店
     * @param billStartDateTime  用于补发 账单开始时间
     * @param billEndDateTime    用于补发 账单结束时间
     */
    void brandBill(List<FinanceSettlement> financeSettlements, List<Integer> mIds, LocalDateTime billStartDateTime, LocalDateTime billEndDateTime);

    /**
     * 单店账单
     *
     * @param mIds              用于补发/未绑定品牌抬头的品牌门店 门店id
     * @param billStartDateTime 用于补发/未绑定品牌抬头的品牌门店 账单开始日期时间
     * @param billEndDateTime   用于补发/未绑定品牌抬头的品牌门店 账单结束日期时间
     */
    void singleStoreBill(List<Integer> mIds, LocalDateTime billStartDateTime, LocalDateTime billEndDateTime);

    /**
     * 补发品牌账单
     *
     * @param input 补发条件
     */
    void reissueBrandBill(ReissueBillInput input);

    /**
     * 补发单店账单
     *
     * @param input 补发条件
     */
    void reissueSingleStoreBill(ReissueBillInput input);

    /**
     * 账期信息统计列表/统计信息
     *
     * @param input 账单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    CommonResult<PageInfo<FinanceAccountingPeriodOrderSummary>> allList(AccountingPeriodOrderInput input);

    /**
     * 账期订单列表
     *
     * @param input 账单查询条件
     * @return {@link CommonResult}<{@link PageInfo}<{@link FinanceAccountingPeriodOrderResult}>>
     */
    CommonResult<PageInfo<FinanceAccountingPeriodOrderResult>> listPeriod(AccountingPeriodOrderInput input);

    /**
     * 生成账单明细excel
     *
     * @param billNo 账单编号
     * @param file   填充文件
     * @return 文件
     */
    void getBillItemFileByBillNo(String billNo, File file);

    /**
     * 生成账单概览excel
     *
     * @param billNo 账单编号
     * @param file   填充文件
     * @return 文件
     */
    void getOverviewFileByCondition(List<String> billNo, File file);

    /**
     * 单个帐单明细excel
     *
     * @param billNo   账单编号
     * @param filename 文件名
     * @return 文件
     */
    void billDownload(String billNo, String filename,Long resId);

    /**
     * 所有帐单excel下载
     *
     * @param billNo   账单编号
     * @param filename 压缩文件名称
     * @param type     导出类型 0:概览;1:明细;
     * @param resId    资源id
     */
    void allBillDownload(List<String> billNo, String filename, int type,Long resId);

    /**
     * 单个账单下载
     *
     * @param billNo 账单编号
     * @return
     */
    CommonResult<Long> storeDetailsDownLoad(String billNo);

    /**
     * 根据查询条件下载账单
     *
     * @param input 查询条件
     * @return
     */
    CommonResult<Void> downloadByCondition(AccountingPeriodOrderInput input);

    /**
     * 确认账单
     *
     * @param id id
     */
    CommonResult<Void> confirmBill(Long id);

    /**
     * 财务审核账单
     *
     * @param input 审核条件
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> financialAudit(AccountingPeriodOrderInput input);

    /**
     * 逾期详情
     *
     * @param input 输入
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    CommonResult<PageInfo<FinanceAccountingPeriodOrderSummary>> overdueDetails(AccountingPeriodOrderInput input);

    /**
     * 应收概况
     *
     * @param input 输入
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    CommonResult<FinanceAccountingPeriodOrderSummary> receivableOverview(AccountingPeriodOrderInput input);

    /**
     * 账单更新
     *
     * @param input 更新条件
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> billUpdate(AccountingPeriodOrderInput input);

    /**
     * 查询账单未核销金额
     * <p>
     * 由于可能会出现超额核销的情况，未核销金额可能为负数
     *
     * @param billNo            账单编号
     * @return
     */
    BigDecimal selectUnWrittenOffAmount(String billNo);

    /**
     * 账单应收金额
     *
     * @param billNo 比尔没有
     * @return {@link BigDecimal}
     */
    BigDecimal getReceivablesAmount(String billNo);

    /**
     * 查询账单信息
     *
     * @param billNos 输入
     * @return {@link List}<{@link BillInfoVo}>
     */
    List<BillInfoVo> selectByBillNos(List<String> billNos);

    /**
     * 账单更新
     *
     * @param input 更新条件
     * @return {@link CommonResult}<{@link Void}>
     */
    void updateBill(AccountingPeriodOrderInput input);

    /**
     * 确认历史
     *
     * @param billNo 账单编号
     * @return {@link List}<{@link FinanceAccountingAuditRecord}>
     */
    List<FinanceAccountingAuditRecord> listAuditRecord(String billNo);

    /**
     * 查询大客户账单概览
     *
     * @param input
     * @return {@link FinancePeriodDetailVO}
     */
    FinanceAccountingPeriodOrderSummary selectDetailByNameRemakes(AccountingPeriodOrderInput input);

    /**
     * 查询账单的门店详情
     *
     * @param input 查询条件
     * @return {@link FinanceStoreDetailVo}
     */
    List<FinanceStoreDetailVo> selectStoreDetailByBillNo(AccountingPeriodOrderInput input);

    /**
     * 上传文件
     *
     * @param file     文件
     * @param filename 文件名
     * @param resId    res id
     */
    void uploadFile(File file,String filename,Long resId);
}
