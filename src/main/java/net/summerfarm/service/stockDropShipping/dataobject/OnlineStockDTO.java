package net.summerfarm.service.stockDropShipping.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class OnlineStockDTO implements Serializable {
    private static final long serialVersionUID = -2026153696812952880L;

    /**
     * 业务id，如采购：采购单
     */
    String bizId;

    /**
     * 数量
     */
    Integer quantity;

    /**
     * sku
     */
    String sku;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 库存变更业务类型
     * @see net.summerfarm.service.stockDropShipping.enums.StockChangeBizTypeEnum
     */
    Integer type;
}
