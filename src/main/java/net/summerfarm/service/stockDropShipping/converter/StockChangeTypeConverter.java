package net.summerfarm.service.stockDropShipping.converter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.service.stockDropShipping.enums.StockChangeBizTypeEnum;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum StockChangeTypeConverter {
    PURCHASE_IN(StockChangeBizTypeEnum.PURCHASE_IN, OtherStockChangeTypeEnum.PURCHASE_DROP_SHIPPING_IN),
    PURCHASE_OUT(StockChangeBizTypeEnum.PURCHASE_OUT, OtherStockChangeTypeEnum.PURCHASE_DROP_SHIPPING_OUT),
    ;

    public static OtherStockChangeTypeEnum convert(StockChangeBizTypeEnum type) {
        return Arrays.stream(StockChangeTypeConverter.values())
                .filter(o -> o.getBizTypeEnum().equals(type))
                .findFirst().orElse(StockChangeTypeConverter.PURCHASE_IN).getStockChangeType();
    }

    StockChangeBizTypeEnum bizTypeEnum;
    OtherStockChangeTypeEnum stockChangeType;
}
