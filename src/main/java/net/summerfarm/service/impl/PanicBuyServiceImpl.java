package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.CirclePeopleRelationTypeEnum;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.enums.PriceStrategyTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.AreaVO;
import net.summerfarm.model.vo.PanicBuySkuVO;
import net.summerfarm.model.vo.PanicBuyVO;
import net.summerfarm.model.vo.PriceStrategyVO;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-05-10
 * @description
 */
@Service
public class PanicBuyServiceImpl extends BaseService implements PanicBuyService {
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private PanicBuyMapper panicBuyMapper;
    @Resource
    private PanicBuySkuMapper panicBuySkuMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private CirclePeopleRelationMapper circlePeopleRelationMapper;
    @Resource
    private CirclePeopleService circlePeopleService;
    @Resource
    private MerchantOrderRecordMapper merchantOrderRecordMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private BaseService baseService;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CycleInventoryCostMapper cycleInventoryCostMapper;
    @Resource
    PriceService priceService;
    @Resource
    private PriceStrategyService priceStrategyService;
    @Resource
    private FenceService fenceService;

    @Override
    public AjaxResult selectPanicBuy(int pageIndex, int pageSize, PanicBuyVO instance) {
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(pageIndex, pageSize, () -> {
            List<PanicBuyVO> voList = panicBuyMapper.select(instance);

            //状态、活动城市处理
            LocalDateTime now = LocalDateTime.now();
            voList.forEach(el -> {
                //补全售罄状态信息
                PanicBuyVO panicBuyVO = panicBuyMapper.selectSellProgress(el.getId());
                el.setSellOutCount(panicBuyVO.getSellOutCount());
                el.setTotalCount(panicBuyVO.getTotalCount());
                String nameOrId = circlePeopleRelationMapper.selectNameOrIdByActId(el.getId(), CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());
                el.setNameOrId(nameOrId);
                if (now.isBefore(el.getStartTime())) {
                    el.setStatus(0);
                } else if (el.getEndTime().isBefore(now)) {
                    el.setStatus(2);
                } else {
                    el.setStatus(1);
                }
            });
            return voList;
        }));
    }

    @Override
    public AjaxResult selectDetail(Integer id) {
        PanicBuyVO query = new PanicBuyVO();
        query.setId(id);
        List<PanicBuyVO> voList = panicBuyMapper.select(query);

        if (CollectionUtils.isEmpty(voList) || voList.size() > 1) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        PanicBuyVO result = voList.get(0);
        PanicBuyVO panicBuyVO = panicBuyMapper.selectSellProgress(result.getId());
        result.setSellOutCount(panicBuyVO.getSellOutCount());
        result.setTotalCount(panicBuyVO.getTotalCount());
        Integer storeNo = fenceService.selectStoreNoByAreaNo(result.getAreaNo());
        //sku信息
        List<PanicBuySkuVO> skuVOS = panicBuySkuMapper.selectByPanicId(id);
        for (PanicBuySkuVO skuVO : skuVOS) {
            skuVO.setWeight(skuVO.getWeight());
            WarehouseInventoryMapping wim = warehouseInventoryService.selectByUniqueIndex(storeNo, skuVO.getSku());
            if (wim != null){
                AreaStore ao = areaStoreMapper.selectWithOutDataPermission(new AreaStore(wim.getWarehouseNo(), skuVO.getSku()));
                skuVO.setOnlineQuantity(ao.getOnlineQuantity());
            }

            // 查询价格策略
            PriceStrategyVO strategyVO = priceStrategyService.selectPriceStrategyVO(PriceStrategyTypeEnum.PANIC, skuVO.getId().longValue());
            skuVO.setPriceStrategy(strategyVO);

            // 查询城市售卖价
            AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(result.getAreaNo(), skuVO.getSku());
            if (areaSku != null){
                PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku);
                skuVO.setOriginalPrice(priceInfo.getPrice());
            }
            Integer warehouseNo = inventoryMapper.selectWarehouseNo(skuVO.getSku(), result.getAreaNo());
            if (warehouseNo != null){
                CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(skuVO.getSku(), warehouseNo);
                if (cycleInventoryCost != null){
                    skuVO.setCostPrice(cycleInventoryCost.getFirstCycleCost());
                }
            }
        }
        result.setSkuList(skuVOS);
        String nameOrId = circlePeopleRelationMapper.selectNameOrIdByActId(id, CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());
        result.setNameOrId(nameOrId);

        return AjaxResult.getOK(result);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult addPanicBuy(PanicBuyVO instance) {
        //参数校验
        if (instance.getStartTime() == null || instance.getEndTime() == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if (instance.getStartTime().isBefore(LocalDateTime.now())) {
            return AjaxResult.getErrorWithMsg("开始时间需要在当前时间之后");
        }
        if (!instance.getStartTime().plusMinutes(5).isBefore(instance.getEndTime())) {
            return AjaxResult.getErrorWithMsg("结束时间需要大于当前时间5分钟");
        }
        if (CollectionUtils.isEmpty(instance.getAreaList()) || CollectionUtils.isEmpty(instance.getSkuList())) {
            return AjaxResult.getErrorWithMsg("存在必填项未填写，请检查");
        }

        Set<String> skuSet = new HashSet<>();
        for (PanicBuySku buySku : instance.getSkuList()) {
            if (buySku.getPlanQuantity() == null) {
                return AjaxResult.getErrorWithMsg("请填写计划额度");
            }
            if (skuSet.contains(buySku.getSku())) {
                return AjaxResult.getErrorWithMsg("sku重复添加");
            }
            skuSet.add(buySku.getSku());
        }

        //同个城配仓的时间段内与已有场次重合，则不通过
        DateUtils.TimeSlot slot = DateUtils.buildSlot(instance.getStartTime(), instance.getEndTime());
        for (AreaVO areaVO : instance.getAreaList()) {
            //同个城配仓的时间段内与已有场次重合，则不通过
            //排除有交叉的活动
            Integer areaNo = areaVO.getAreaNo();
            List<PanicBuy> list = panicBuyMapper.selectByAreaNo(areaNo, instance.getStartTime());
            for (PanicBuy buy : list) {
                if (DateUtils.overlapped(slot, DateUtils.buildSlot(buy.getStartTime(), buy.getEndTime()))) {
                    return AjaxResult.getErrorWithMsg("只可创建完全相同时间、完全不同时间的活动");
                }
            }
            //排除时间相等、圈人规则相等的用户和时间相等没有圈人规则的
            List<PanicBuy> lis = null;
            if (instance.getRuleId() != null) {
                lis = panicBuyMapper.
                        selectByAreaNos(instance.getId(), areaNo, instance.getStartTime(), instance.getRuleId(), instance.getEndTime());
                if (!CollectionUtils.isEmpty(lis)) {
                    return AjaxResult.getErrorWithMsg("当前时间段,已存在该圈人规则秒杀");
                }
            } else {
                //取当前时间生效的秒杀
                List<Integer> currentPanic = panicBuyMapper.selectCurrentPanic(instance.getId(), areaNo, instance.getStartTime(), instance.getEndTime());
                if (!CollectionUtils.isEmpty(currentPanic)) {
                    int i = circlePeopleRelationMapper.selectByTypeIds(currentPanic, CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());
                    if (currentPanic.size() > i) {
                        return AjaxResult.getErrorWithMsg("当前时间段,已存在无圈人规则秒杀");
                    }
                }

            }

        }

        //开启已购限制时，校验
        if(Objects.equals(instance.getStartFlag(),1)){
            List<String> skus = instance.getSkuList().stream().map(PanicBuySku::getSku).collect(Collectors.toList());
            //将不存在于已购表中sku数据取出
            List<String> ExistSkus =  merchantOrderRecordMapper.selectNotExistSku(skus);
            skus.removeAll(ExistSkus);
            if(!CollectionUtils.isEmpty(skus)) {
                Config config = configMapper.selectOne(ConfigValueEnum.PANIC_BUY_ORDER_TIME.getKey());
                //根据sku查询45天的数据插入其中
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime start = DateUtils.getDayStart(now.minusDays(Long.parseLong(config.getValue())+1));
                LocalDateTime end = DateUtils.getDayEnd(now.minusDays(1L));
                merchantOrderRecordMapper.insertBySkus(skus,start,end);
            }
        }

        List<AreaVO> areaNos = instance.getAreaList();
        for (AreaVO areaVO : areaNos) {
            instance.setStoreNo(areaVO.getParentNo());
            instance.setAreaNo(areaVO.getAreaNo());
            instance.setCreator(getAdminId());
            instance.setCreateTime(LocalDateTime.now());
            panicBuyMapper.insertSelective(instance);
            //插入圈人平台
            if (!Objects.equals(instance.getRuleId(), null)) {
                CirclePeopleRelation circlePeopleRelation = new CirclePeopleRelation();
                circlePeopleRelation.setType(CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());
                circlePeopleRelation.setTypeId(instance.getId());
                circlePeopleRelation.setRuleId(instance.getRuleId());
                circlePeopleRelation.setCreator(getAdminId());
                circlePeopleRelation.setCreateTime(LocalDateTime.now());
                circlePeopleRelationMapper.insertSelective(circlePeopleRelation);
            }

            //处理sku
            for (PanicBuySkuVO panicBuySku : instance.getSkuList()) {
                panicBuySku.setPanicId(instance.getId());
                panicBuySku.setCreator(getAdminId());
                panicBuySku.setCreateTime(LocalDateTime.now());
                panicBuySku.setLockQuantity(0);
                panicBuySku.setActualQuantity(panicBuySku.getPlanQuantity());
                AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaVO.getAreaNo(), panicBuySku.getSku());
                if (areaSku == null) {
                    throw new DefaultServiceException("此sku:" + panicBuySku.getSku() + "在" + areaVO.getAreaName() + "暂无售卖信息");
                }
                panicBuySku.setPanicPrice(areaSku.getPrice());
                panicBuySkuMapper.insertSelective(panicBuySku);

                PriceStrategy priceStrategy = initPriceStrategy(panicBuySku);
                priceStrategyService.insertOrUpdate(PriceStrategyTypeEnum.PANIC, priceStrategy, areaVO.getAreaNo(), panicBuySku.getSku(), panicBuySku.getId());
            }
        }
        return AjaxResult.getOK();
    }

    /**
     * 封装价格调整数据
     * @param panicBuySku 秒杀sku对象
     * @return
     */
    private PriceStrategy initPriceStrategy(PanicBuySkuVO panicBuySku) {
        PriceStrategyVO priceStrategy = panicBuySku.getPriceStrategy();
        priceStrategy.setBusinessId(panicBuySku.getId().longValue());
        priceStrategy.setType(PriceStrategyTypeEnum.PANIC.getType());
        priceStrategy.setCreator(baseService.getAdminId());
        priceStrategy.setUpdater(baseService.getAdminId());
        return priceStrategy;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult updatePanicBuy(PanicBuyVO instance) {
        //新增-修改
        List<PanicBuySkuVO> addList = new ArrayList<>();
        List<PanicBuySkuVO> updateList = new ArrayList<>();
        Set<Integer> set = new HashSet<>();
        Set<String> skuSet = new HashSet<>();
        for (PanicBuySkuVO buySku : instance.getSkuList()) {
            if (skuSet.contains(buySku.getSku())) {
                return AjaxResult.getError("sku：" + buySku.getSku() + "重复添加");
            }
            skuSet.add(buySku.getSku());

            if (buySku.getId() == null) {
                addList.add(buySku);
                continue;
            }

            set.add(buySku.getId());
            updateList.add(buySku);
        }

        //删除
        List<PanicBuySku> deleteList = new ArrayList<>();
        List<PanicBuySkuVO> buySkuVOS = panicBuySkuMapper.selectByPanicId(instance.getId());
        buySkuVOS.forEach(el -> {
            if (!set.contains(el.getId())) {
                deleteList.add(el);
            }
        });

        PanicBuy panicBuy = panicBuyMapper.selectByPrimaryKey(instance.getId());
        LocalDateTime now = LocalDateTime.now().plusSeconds(3);
        boolean isSale = now.isAfter(panicBuy.getStartTime()) && now.isBefore(panicBuy.getEndTime());

        //校验是否允许删除
        if (!CollectionUtils.isEmpty(updateList) && panicBuy.getEndTime().isBefore(now)) {
            return AjaxResult.getErrorWithMsg("当前场次不可修改sku");
        }

        //删除
        if (!CollectionUtils.isEmpty(deleteList)) {
            //校验是否允许删除
            if (isSale) {
                return AjaxResult.getErrorWithMsg("当前场次不可删除sku");
            }
            deleteList.forEach(el -> panicBuySkuMapper.deleteByPrimaryKey(el.getId()));
        }
        //判断
        if(Objects.equals(panicBuy.getStartFlag(),1)){
            List<String> skus = instance.getSkuList().stream().map(PanicBuySku::getSku).collect(Collectors.toList());
            //将不存在于已购表中sku数据取出
            List<String> ExistSkus =  merchantOrderRecordMapper.selectNotExistSku(skus);
            skus.removeAll(ExistSkus);
            if(!CollectionUtils.isEmpty(skus)) {
                Config config = configMapper.selectOne(ConfigValueEnum.PANIC_BUY_ORDER_TIME.getKey());
                //根据sku查询45天的数据插入其中
                LocalDateTime nowS = LocalDateTime.now();
                LocalDateTime start = DateUtils.getDayStart(nowS.minusDays(Long.parseLong(config.getValue())+1));
                LocalDateTime end = DateUtils.getDayEnd(now.minusDays(1L));
                merchantOrderRecordMapper.insertBySkus(skus,start,end);
            }
        }

        //新增
        if (!CollectionUtils.isEmpty(addList)) {
            addList.forEach(el -> {
                //新增时秒杀额度和计划额度相等
                el.setActualQuantity(el.getPlanQuantity());
                if(isSale){
                    Integer storeNo = fenceService.selectStoreNoByAreaNo(panicBuy.getAreaNo());
                    checkOnlineQuantity(storeNo, el);
                }

                //查询原价
                AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(panicBuy.getAreaNo(), el.getSku());
                if (areaSku == null || !areaSku.getOnSale()) {
                    throw new DefaultServiceException(el.getSku() + "未上架，不可参与秒杀活动");
                }

                el.setPanicPrice(areaSku.getPrice());
                el.setPanicId(instance.getId());
                el.setCreator(getAdminId());
                el.setCreator(getAdminId());
                el.setCreateTime(LocalDateTime.now());
                panicBuySkuMapper.insertSelective(el);

                PriceStrategy priceStrategy = initPriceStrategy(el);
                priceStrategyService.insertOrUpdate(PriceStrategyTypeEnum.PANIC, priceStrategy, panicBuy.getAreaNo(), el.getSku(), el.getId());
            });
        }

        //修改
        if (!CollectionUtils.isEmpty(updateList)) {
            updateList.forEach(el -> {
                if(isSale){
                    //校验价格
                    PanicBuySku panicBuySku = panicBuySkuMapper.selectByPrimaryKey(el.getId());
                    if (panicBuySku.getPanicPrice().compareTo(el.getPanicPrice()) != 0) {
                        throw new DefaultServiceException("当前场次不可修改秒杀价，sku：" + panicBuySku.getSku());
                    }

                    //校验计划量
                    if(el.getPlanQuantity() < panicBuySku.getLockQuantity()){
                        throw new DefaultServiceException("计划额度不能小于当前销量，sku：" + panicBuySku.getSku());
                    }

                    //疯抢中不可修改计划额度
                    el.setPlanQuantity(null);

                    //校验库存
                    Integer storeNo = fenceService.selectStoreNoByAreaNo(panicBuy.getAreaNo());
                    checkOnlineQuantity(storeNo, el);
                }
                el.setActualQuantity(el.getPlanQuantity());
                el.setLockQuantity(null);
                el.setCreator(null);
                el.setCreateTime(null);
                el.setPanicPrice(null);
                el.setUpdater(getAdminId());
                el.setUpdateTime(LocalDateTime.now());
                panicBuySkuMapper.updateByPrimaryKeySelective(el);

                PriceStrategy updateStrategy = initPriceStrategy(el);
                priceStrategyService.insertOrUpdate(PriceStrategyTypeEnum.PANIC, updateStrategy, panicBuy.getAreaNo(), el.getSku(), el.getId());
            });
        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult sellOut(PanicBuyVO instance) {
        PanicBuy panicBuy = panicBuyMapper.selectByPrimaryKey(instance.getId());

        LocalDateTime now = LocalDateTime.now();
        if (!(panicBuy.getStartTime().isBefore(now) && now.isBefore(panicBuy.getEndTime()))) {
            return AjaxResult.getErrorWithMsg("该场次不可操作售罄");
        }

        for (PanicBuySku buySku : instance.getSkuList()) {
            panicBuySkuMapper.sellOut(buySku.getId());
        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult delete(Integer id) {
        PanicBuy panicBuy = panicBuyMapper.selectByPrimaryKey(id);
        if (panicBuy.getStartTime().isBefore(LocalDateTime.now().plusSeconds(3))) {
            return AjaxResult.getErrorWithMsg("当前场次不可删除");
        }

        //sku信息处理
        panicBuySkuMapper.deleteByPanicId(id);

        //秒杀处理
        panicBuyMapper.deleteByPrimaryKey(id);

        //删除圈人关联
        circlePeopleRelationMapper.deletByTypeId(id,CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public AjaxResult batchAdd(PanicBuyVO instance) {
        //参数校验
        if(CollectionUtils.isEmpty(instance.getPanicBuyIdList())){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        Set<String> skuSet = new HashSet<>();
        for (PanicBuySku buySku : instance.getSkuList()) {
            if (buySku.getPlanQuantity() == null) {
                return AjaxResult.getErrorWithMsg("请填写计划额度");
            }
            if(buySku.getPanicPrice() == null || buySku.getPanicPrice().compareTo(BigDecimal.ZERO) < 0){
                return AjaxResult.getErrorWithMsg("秒杀价填写错误");
            }
            if(skuSet.contains(buySku.getSku())){
                return AjaxResult.getErrorWithMsg("sku重复添加");
            }
            skuSet.add(buySku.getSku());
        }

        //添加数据
        for (Integer panicBuyId : instance.getPanicBuyIdList()) {
            List<PanicBuySkuVO> voList = panicBuySkuMapper.selectByPanicId(panicBuyId);
            Set<String> oldSkuSet = voList.stream().map(PanicBuySku::getSku).collect(Collectors.toSet());
            for (PanicBuySkuVO buySku : instance.getSkuList()) {
                if(oldSkuSet.contains(buySku.getSku())){
                    continue;
                }

                buySku.setActualQuantity(buySku.getPlanQuantity());
                buySku.setPanicId(panicBuyId);
                buySku.setCreator(getAdminId());
                buySku.setCreator(getAdminId());
                buySku.setCreateTime(LocalDateTime.now());
                panicBuySkuMapper.insertSelective(buySku);

                PanicBuy panicBuy = panicBuyMapper.selectByPrimaryKey(panicBuyId);
                PriceStrategy priceStrategy = initPriceStrategy(buySku);
                priceStrategyService.insertOrUpdate(PriceStrategyTypeEnum.PANIC, priceStrategy, panicBuy.getAreaNo(), buySku.getSku(), instance.getId());
            }
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult checkPanicBuy(PanicBuyVO instance) {
        //同个城配仓的时间段内与已有场次重合，则不通过
        StringBuffer panics = new StringBuffer();
        List<String> mechants = new ArrayList<>();
        List<Integer> ids = new LinkedList<>();
        for (AreaVO areaVO : instance.getAreaList()) {
            if( instance.getRuleId()!=null){
                Integer areaNo = areaVO.getAreaNo();
                List<Integer> currentPanic = panicBuyMapper.selectCurrentPanic(instance.getId(),areaNo, instance.getStartTime(),instance.getEndTime());
                for (Integer li : currentPanic) {
                    List<String> sameMids = circlePeopleService.checkParmas(li, instance.getRuleId(), CirclePeopleRelationTypeEnum.PANIC_TYPE.getType());
                    if(sameMids.size() ==0){
                        continue;
                    }else{
                        mechants.addAll(sameMids);
                        panics.append("秒杀"+li+" ");
                        ids.add(li);
                    }
                }
            }
        }
        if(mechants.size() !=0){
            List<String> collect = mechants.stream().distinct().collect(Collectors.toList());
            ids.add(instance.getId());
            PanicBuy info = panicBuyMapper.selectFirstTime(ids);
            panics.append("秒杀"+instance.getId()+" ");
            return AjaxResult.getOK(collect.size());
        }else{
            return AjaxResult.getOK();
        }
    }

    @Override
    @Transactional
    public void timedTaskOrderSku() {
        //删除表中所有数据
        merchantOrderRecordMapper.deleteByAll();
        //查询待开抢的秒杀的skuList
        List<String> sku = panicBuySkuMapper.selectByDate();
        if(!CollectionUtils.isEmpty(sku)) {
            Config config = configMapper.selectOne(ConfigValueEnum.PANIC_BUY_ORDER_TIME.getKey());
            //根据sku查询45天的数据插入其中
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = DateUtils.getDayStart(now.minusDays(Long.parseLong(config.getValue())));
            merchantOrderRecordMapper.insertBySkus(sku,start,now);
        }

    }

    private void checkOnlineQuantity(Integer storeNo, PanicBuySku panicBuySku) {
        WarehouseInventoryMapping wim = warehouseInventoryService.selectByUniqueIndex(storeNo, panicBuySku.getSku());
        AreaStore areaStore = areaStoreMapper.selectWithOutDataPermission(new AreaStore(wim.getWarehouseNo(), panicBuySku.getSku()));
        if (panicBuySku.getActualQuantity() - Optional.ofNullable(panicBuySku.getLockQuantity()).orElse(0) > areaStore.getOnlineQuantity()) {
            throw new DefaultServiceException("sku：" + panicBuySku.getSku() + "秒杀额度-秒杀销量不能超过虚拟库存");
        }
    }
}
