package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.biz.stockTransfer.StockTransferBizService;
import net.summerfarm.biz.stockTransfer.bo.StockTransferCreateBO;
import net.summerfarm.biz.stockTransfer.bo.StockTransferFinishBO;
import net.summerfarm.biz.stockTransfer.bo.StockTransferItemOpBO;
import net.summerfarm.biz.stockTransfer.bo.TransferInInfoBO;
import net.summerfarm.biz.stockTransfer.constants.TransferExportConstant;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dao.stockTransfer.StockTransferDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemDAO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.enums.*;
import net.summerfarm.facade.goods.GoodsReadFacade;
import net.summerfarm.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.facade.wms.CabinetBatchInventoryFacade;
import net.summerfarm.facade.wms.converter.CabinetInventoryRecommendConverter;
import net.summerfarm.facade.wms.dto.CabinetInventoryRecommendDTO;
import net.summerfarm.facade.wms.dto.CabinetInventoryRecommendOutFacadeRespDTO;
import net.summerfarm.facade.wms.dto.CabinetInventorySingleRecommendOutReqFacadeDTO;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.MailWorkBookDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.StockTaskProcessReq;
import net.summerfarm.model.input.StockTaskReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.*;
import net.summerfarm.service.stockTransfer.dto.GoodsLocationInfo;
import net.summerfarm.service.stockTransfer.dto.TransferOutInfo;
import net.summerfarm.service.stockTransfer.enums.TransferDimensionEnum;
import net.summerfarm.service.stockTransfer.enums.TransferOpEnum;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.constants.OssConstants;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.redisson.api.RLock;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_UP;

@Service
@Slf4j
public class TransferServiceImpl extends BaseService implements TransferService, StockTaskStrategy {
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Lazy
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private PriceAdjustmentTriggerMapper priceAdjustmentTriggerMapper;
    @Lazy
    @Resource
    private InterestRateRecordService interestRateRecordService;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private GoodsLocationDetailService goodsLocationDetailService;
    @Resource
    private GoodsLocationDetailMapper goodsLocationDetailMapper;
    @Lazy
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private WMSBuilderService wmsBuilderService;
    @Resource
    private StockTakingService stockTakingService;
    @Resource
    private ConversionSkuConfigMapper conversionSkuConfigMapper;
    @Resource
    private ConversionSkuQuantityMapper conversionSkuQuantityMapper;
    @Resource
    private SkuBatchCodeService skuBatchCodeService;
    @Resource
    private WarehouseStockExtService warehouseStockExtService;
    @Resource
    private BatchProveMapper batchProveMapper;
    @Resource
    private WmsPesticideResidueReportMapper residueReportMapper;

    @Resource
    private StockTransferBizService stockTransferBizService;

    @Resource
    private StockTransferItemDAO stockTransferItemDAO;

    @Resource
    private StockTransferDAO stockTransferDAO;
    @Resource
    private FenceService fenceService;
    @Resource
    private CabinetBatchInventoryFacade cabinetBatchInventoryFacade;
    @Resource
    private WarehouseConfigService warehouseConfigService;

    @Override
    public AjaxResult stockList(int pageIndex, int pageSize, StockTaskReq stockTaskReq) {
        stockTaskReq.setType(StockTaskType.TRANSFER_TASK.getId());
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTaskVO> stockTaskVOS = stockTaskMapper.selectVO(stockTaskReq);
        return AjaxResult.getOK(new PageInfo<>(stockTaskVOS));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockTransfer(TransformVO transformVO) {
        if (CollectionUtils.isEmpty(transformVO.getTransformItemVOList())) {
            return AjaxResult.getError();
        }
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(transformVO.getStockTaskId());
        if (Objects.equals(StockTaskState.FINISH, stockTask.getState())) {
            return AjaxResult.getErrorWithMsg("当前任务已关闭，请勿继续操作");
        }

        StockTaskItem stockTaskItem = stockTaskItemMapper.selectByPrimaryKey(transformVO.getStockTaskItemId());
        if (!Objects.equals(transformVO.getStockTaskId(), stockTaskItem.getStockTaskId()) ||
                !Objects.equals(transformVO.getInSku(), stockTaskItem.getSku())) {
            return AjaxResult.getErrorWithMsg("转入sku与任务sku不匹配");
        }

        //实际已入库数量
        Integer actualQuantity = stockTaskItem.getActualQuantity();

        //转换单
        StockTaskProcess process = new StockTaskProcess();
        process.setStockTaskId(stockTask.getId());
        process.setAddtime(LocalDateTime.now());
        process.setRecorder(getAdminName());
        stockTaskProcessMapper.insert(process);
        List<StockTaskProcessDetail> processDetails = new ArrayList<>();
        //校验货位可用性
        boolean check = goodsLocationDetailService.checkoutGoodsLocation(transformVO.getStoreNo());
        if (check) {
            List<TransformItemVO> transformItemVOList = transformVO.getTransformItemVOList();
            List<GoodsLocationDetail> goodsLocationDetails = new ArrayList<>();
            transformItemVOList.forEach(x -> {
                StoreRecord storeRecord = storeRecordMapper.selectById(x.getId());
                StoreRecord selectKey = ReflectUtils.copyData(new StoreRecord(), storeRecord, "areaNo", "sku", "batch", "qualityDate");
                StoreRecord record = storeRecordMapper.selectOne(selectKey);
                GoodsLocationDetail detail = new GoodsLocationDetail();
                detail.setBatch(record.getBatch());
                detail.setGlNo(x.getInGlNo());
                detail.setQualityDate(record.getQualityDate());
                detail.setSku(transformVO.getInSku());
                goodsLocationDetails.add(detail);
            });

            goodsLocationDetailService.checkoutGlNoList(goodsLocationDetails);
        }

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (TransformItemVO itemVO : transformVO.getTransformItemVOList()) {
            StoreRecord storeRecord = storeRecordMapper.selectById(itemVO.getId());
            if (!Objects.equals(transformVO.getOutSku(), storeRecord.getSku()) ||
                    !Objects.equals(transformVO.getStoreNo(), storeRecord.getAreaNo())) {
                throw new DefaultServiceException(storeRecord.getBatch() + "批次" + storeRecord.getQualityDate() + "与转出sku的信息不一致");
            }

            StoreRecord selectKey = ReflectUtils.copyData(new StoreRecord(), storeRecord, "areaNo", "sku", "batch", "qualityDate");
            StoreRecord record = storeRecordMapper.selectOne(selectKey);
            if (!Objects.equals(storeRecord.getId(), record.getId())) {
                throw new DefaultServiceException(storeRecord.getBatch() + "批次" + storeRecord.getQualityDate() + "数据并非最新批次信息,请刷新页面重新操作");
            }


            if (storeRecord.getStoreQuantity() < itemVO.getOutQuantity()) {
                throw new DefaultServiceException(storeRecord.getBatch() + "批次" + storeRecord.getQualityDate() + "剩余仓库库存不足");
            }
            AreaStore areaStoreOut = areaStoreMapper.selectOne(new AreaStore(transformVO.getStoreNo(), transformVO.getOutSku()));
            if (areaStoreOut.getSync() == 1) {
                //1.扣除虚拟库存、库存仓库库存、增加转换出库记录
                areaStoreService.updateOnlineStockByStoreNo(true, -itemVO.getOutQuantity(), transformVO.getOutSku(), transformVO.getStoreNo(), OtherStockChangeTypeEnum.TRANSFORM_OUT, null, recordMap, NumberUtils.INTEGER_ONE);
            }

            areaStoreService.updateStoreStockByWarehouseNo(-itemVO.getOutQuantity(), transformVO.getOutSku(), transformVO.getStoreNo(), OtherStockChangeTypeEnum.TRANSFORM_OUT, StoreRecordType.TRANSFER_OUT.getId(), null, recordMap);
            StoreRecord outRecord = new StoreRecord(storeRecord.getBatch(), transformVO.getOutSku(), StoreRecordType.TRANSFER_OUT.getId(), itemVO.getOutQuantity(), null,
                    getAdminName(), null, new Date(), transformVO.getStoreNo(), storeRecord.getQualityDate(), storeRecord.getProductionDate(),
                    storeRecord.getStoreQuantity() - itemVO.getOutQuantity(), storeRecord.getCost()
                    , storeRecord.getTenantId());

            storeRecordMapper.insert(outRecord);

            AreaStore areaStoreIn = areaStoreMapper.selectOne(new AreaStore(transformVO.getStoreNo(), transformVO.getInSku()));
            if (areaStoreIn.getSync() == 1) {
                //2.增加虚拟库存、增加仓库库存、增加入库记录
                areaStoreService.updateOnlineStockByStoreNo(true, itemVO.getInQuantity(), transformVO.getInSku(), transformVO.getStoreNo(), OtherStockChangeTypeEnum.TRANSFORM_IN, null, recordMap, NumberUtils.INTEGER_ONE);
            }
            areaStoreService.updateStoreStockByWarehouseNo(itemVO.getInQuantity(), transformVO.getInSku(), transformVO.getStoreNo(), OtherStockChangeTypeEnum.TRANSFORM_IN, StoreRecordType.TRANSFER_IN.getId(), null, recordMap);

            StoreRecord inRecord = new StoreRecord();

            if (StringUtils.isBlank(storeRecord.getCost())) {
                throw new DefaultServiceException(storeRecord.getBatch() + "批次" + storeRecord.getQualityDate() + "转出sku单价成本异常");
            }
            //转入单价 = 转出单价*转出数量 / 转入数量
            BigDecimal cost = storeRecord.getCost().multiply(BigDecimal.valueOf(itemVO.getOutQuantity())).divide(BigDecimal.valueOf(itemVO.getInQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
            inRecord.setCost(cost);
//            }
            //判断是否在一个spu库存转换
            if (!Objects.equals(inventoryMapper.selectInventoryVOBySku(transformVO.getOutSku()).getPdId(), inventoryMapper.selectInventoryVOBySku(transformVO.getInSku()).getPdId())) {
                inRecord.setLotType(NumberUtils.INTEGER_ONE);
            }
            //获取新批次号 可能存在相同批次，为不影响成本新增一个批次
            String newPurchasesNo = stockTakingService.createNewPurchasesNo();
            inRecord.setBatch(newPurchasesNo);
            inRecord.setSku(transformVO.getInSku());
            inRecord.setType(StoreRecordType.TRANSFER_IN.getId());
            inRecord.setQualityDate(storeRecord.getQualityDate());
            inRecord.setStoreQuantity(itemVO.getInQuantity());
            inRecord.setAreaNo(transformVO.getStoreNo());
            inRecord.setUpdateTime(new Date());
            inRecord.setRecorder(getAdminName());
            inRecord.setQuantity(itemVO.getInQuantity());
            inRecord.setProductionDate(storeRecord.getProductionDate());
            inRecord.setRemark(storeRecord.getRemark());
            inRecord.setTenantId(storeRecord.getTenantId());
            storeRecordMapper.insert(inRecord);

            //库存转换证明处理
            transferProveHandle(transformVO, storeRecord, newPurchasesNo);

            SkuBatchCode skuBatchCode = new SkuBatchCode(transformVO.getInSku(), newPurchasesNo, storeRecord.getProductionDate());
            skuBatchCode.setQualityDate(storeRecord.getQualityDate());
            skuBatchCodeService.createBatchCode(skuBatchCode, transformVO.getStoreNo());
            //转换入库任务详情
            StockTaskItemDetail itemDetail = new StockTaskItemDetail(stockTaskItem.getId(), transformVO.getInSku(), storeRecord.getBatch(),
                    storeRecord.getQualityDate(), itemVO.getInQuantity(), null, storeRecord.getProductionDate());
            itemDetail.setName(getAdminName());
            //嘉兴仓转换单处理
            if (check) {
                //被转移的sku不存在该货位，校验货位信息
                GoodsLocationDetail detail = new GoodsLocationDetail();
                detail.setBatch(storeRecord.getBatch());
                detail.setGlNo(itemVO.getOutGlNo());
                detail.setQualityDate(record.getQualityDate());
                detail.setSku(record.getSku());
                goodsLocationDetailService.updateDetail(detail, -itemVO.getOutQuantity(), StoreRecordType.TRANSFER_OUT.getId());
                GoodsLocationDetail inDetail = new GoodsLocationDetail();
                inDetail.setBatch(newPurchasesNo);
                inDetail.setGlNo(itemVO.getInGlNo());
                inDetail.setQualityDate(record.getQualityDate());
                inDetail.setSku(transformVO.getInSku());
                inDetail.setStatus(0);
                inDetail.setSaleLockQuantity(0);
                inDetail.setAddTime(new Date());
                inDetail.setUpdateTime(new Date());
                inDetail.setProductionDate(record.getProductionDate());
                inDetail.setQuantity(itemVO.getInQuantity());
                goodsLocationDetailMapper.insertDetail(inDetail);

            }
            if (storeRecord.getQualityDate() != null) {   // mysql唯一约束只对非空数据生效
                stockTaskItemDetailMapper.merge(itemDetail);
            } else {
                StockTaskItemDetail itemDetailRecord = stockTaskItemDetailMapper.selectOne(itemDetail);
                if (itemDetailRecord != null) {
                    StockTaskItemDetail update = new StockTaskItemDetail();
                    update.setId(itemDetailRecord.getId());
                    update.setQuantity(itemDetailRecord.getQuantity() + itemDetail.getQuantity());
                    stockTaskItemDetailMapper.update(update);
                } else {
                    stockTaskItemDetailMapper.insert(itemDetail);
                }
            }
            actualQuantity = actualQuantity + itemVO.getInQuantity();

            //转换单详情
            StockTaskProcessDetail processDetail = ReflectUtils.copyData(new StockTaskProcessDetail(), itemDetail, "sku", "listNo", "qualityDate", "quantity", "productionDate");
            processDetail.setStockTaskProcessId(process.getId());
            processDetail.setTransferScale(transformVO.getTransferScale());
            processDetail.setTransferSku(transformVO.getOutSku());
            processDetail.setTransferQuantity(itemVO.getOutQuantity());
            processDetail.setCreateTime(LocalDateTime.now());
            processDetail.setGlNo(itemVO.getOutGlNo());
            processDetail.setInGlNo(itemVO.getInGlNo());
            processDetail.setCreator(getAdminName());
            processDetail.setStatus(1);
            processDetail.setTransferListNo(newPurchasesNo);
            processDetails.add(processDetail);

            //预警通知
            purchasesConfigService.msgArrival(transformVO.getStoreNo(), inRecord.getSku());
            purchasesConfigService.msgArrival(transformVO.getStoreNo(), outRecord.getSku());

            List<AreaStore> areaStores = new ArrayList<>();
            AreaStore areaStore = new AreaStore();
            areaStore.setSku(inRecord.getSku());
            areaStore.setAreaNo(transformVO.getStoreNo());
            areaStores.add(areaStore);
            msgAdminService.sendOnSaleMsg(areaStores);

            //计算市场价
            PriceAdjustmentTrigger trigger = new PriceAdjustmentTrigger();
            trigger.setCostPrice(inRecord.getCost());
            trigger.setQuantity(inRecord.getQuantity());
            trigger.setValid(1);
            trigger.setBusinessId(SnowflakeUtil.nextId());
            PurchasesPlan pp = purchasesPlanMapper.selectByNoAndSku(storeRecord.getBatch(), outRecord.getSku(), storeRecord.getQualityDate());
            if (pp != null && pp.getMarketPrice() != null) {
                //转入市场价 = 转出市场价*转出数量 / 转入数量
                BigDecimal marketPrice = pp.getMarketPrice().multiply(BigDecimal.valueOf(itemVO.getOutQuantity())).divide(BigDecimal.valueOf(itemVO.getInQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
                trigger.setMarketPrice(marketPrice);
            }
            priceAdjustmentTriggerMapper.insertSelective(trigger);

            //触发自动调价：采购价自动更新，售价进入审核
            interestRateRecordService.autoChangePrice(transformVO.getStoreNo(), transformVO.getInSku(), trigger.getCostPrice(), trigger.getMarketPrice(), trigger.getBusinessId());
        }
        quantityChangeRecordService.insertRecord(recordMap);


        Integer status = Objects.nonNull(transformVO.getCloseTask()) && transformVO.getCloseTask() ? StockTaskState.FINISH.ordinal() : StockTaskState.PART_IN_OUT.ordinal();
        stockTaskProcessDetailMapper.insertBatch(processDetails);
        StockTask taskUpdate = new StockTask();
        taskUpdate.setId(stockTask.getId());
        taskUpdate.setState(status);
        taskUpdate.setUpdatetime(LocalDateTime.now());
        stockTaskMapper.update(taskUpdate);

        StockTaskItem itemUpdate = new StockTaskItem();
        itemUpdate.setId(stockTaskItem.getId());
        itemUpdate.setActualQuantity(actualQuantity);
        stockTaskItemMapper.update(itemUpdate);
        //关闭任务
        //todo 新需求里是不是不需要了
        if (Objects.equals(status, StockTaskState.FINISH.ordinal())) {
            closeTask(stockTask);
        }
        return AjaxResult.getOK();
    }

    /**
     * 库存转换证明处理
     *
     * @param transformVO    库存转换VO
     * @param storeRecord    库存记录
     * @param newPurchasesNo 批次号
     */
    private void transferProveHandle(TransformVO transformVO, StoreRecord storeRecord, String newPurchasesNo) {
        //库存转换同时代入四项证明
        BatchProve batchProveQuery = new BatchProve();
        batchProveQuery.setSku(transformVO.getOutSku());
        batchProveQuery.setBatch(storeRecord.getBatch());
        batchProveQuery.setQualityDate(storeRecord.getProductionDate());
        logger.info("四项证明查询条件：{}", JSON.toJSONString(batchProveQuery));
        List<BatchProve> batchProves = batchProveMapper.selectBatchProve(batchProveQuery);
        logger.info("四项证明查询结果：{}", JSON.toJSONString(batchProves));
        //校验四项证明是否存在
        if (Objects.nonNull(batchProves) && !batchProves.isEmpty()) {
            BatchProve batchProve = batchProves.get(NumberUtils.INTEGER_ZERO);
            batchProve.setBatch(newPurchasesNo);
            batchProve.setSku(transformVO.getInSku());
            batchProve.setQualityDate(storeRecord.getProductionDate());
            batchProveMapper.saveBatchProve(batchProve);
        }
        //库存转换同时代入农残检测报告
        WmsPesticideResidueReport reportQuery = new WmsPesticideResidueReport(transformVO.getOutSku(), storeRecord.getBatch(), storeRecord.getProductionDate());
        logger.info("农残检测报告查询条件：{}", JSON.toJSONString(reportQuery));
        WmsPesticideResidueReport residueReport = residueReportMapper.queryResidueReport(reportQuery);
        logger.info("农残检测报告查询结果：{}", JSON.toJSONString(residueReport));
        //校验农残检测报告是否存在
        if (Objects.nonNull(residueReport)) {
            residueReport.setBatch(newPurchasesNo);
            residueReport.setSku(transformVO.getInSku());
            residueReport.setProductionDate(storeRecord.getProductionDate());
            residueReport.setStatus(WmsPesticideResidueReport.REPORT_STATUS_HAVE);
            residueReportMapper.savePesticideResidueReport(residueReport);
        }
    }

    @Override
    public AjaxResult processList(int pageIndex, int pageSize, StockTaskProcessReq req) {
        req.setType(StockTaskType.TRANSFER_TASK.getId());
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTaskProcessVO> processVOS = stockTaskProcessMapper.selectStockTaskProcessVO(null, StockTaskType.TRANSFER_TASK.getId(), req.getAreaNo(), req.getStockTaskId(), req.getPdName(), req.getDimension(), req.getSupplierId(), null, null, null, null);
        return AjaxResult.getOK(new PageInfo<>(processVOS));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoTransferTask(Integer storeNo, String sku, LocalDateTime outTime, Integer configId) throws Exception {
        String key = RedisKeyEnum.AUTO_TRANSFER_TASK.getKey(storeNo, sku, configId);
        RLock lock = RedissonLockUtil.lock(key, 10);
        try {
            this.handleTransferTask(storeNo, sku, outTime, configId);
        } catch (Exception e) {
            log.warn("handleTransferTask fail request: store:{}, sku:{}, outTime:{}, configId:{}"
                    , storeNo, sku, outTime, configId, e);
            throw e;
        } finally {
            lock.unlock();
        }

    }

    public void handleTransferTask(Integer storeNo, String sku, LocalDateTime outTime, Integer configId) {
        logger.info("自动触发转换任务,warehouseNo:{},sku:{},outTime:{},configId:{}", storeNo, sku, outTime, configId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(configId), "configId为空");
        ExceptionUtil.checkAndThrow(this.checkValidTime(), "当前时间不在自动转换生效范围内");
        ExceptionUtil.checkAndThrow(this.checkInSkuStatus(storeNo, sku), "sku非(可)上架状态");
        ConversionSkuConfig skuConfig = conversionSkuConfigMapper.selectConfigDetail(configId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuConfig), "未查询到相关sku转换配置信息");
        // sku为非鲜果类型
        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(storeNo);
        List<GoodsInfoDTO> goodsInfoDTOS = goodsReadFacade.listGoodsInfoBySkus(center.getWarehouseNo().longValue(), Lists.newArrayList(sku), null);
        if (CollectionUtils.isEmpty(goodsInfoDTOS)) {
            throw new BizException("sku:" + sku + "无货品信息");
        }
        GoodsInfoDTO goodsInfoDTO = goodsInfoDTOS.get(NumberUtils.INTEGER_ZERO);
        if (Objects.isNull(goodsInfoDTO) ||
                Objects.equals(CategoryTypeEnum.FRUIT.getType(), goodsInfoDTO.getCategoryType())) {
            log.info("sku为鲜果类型，不执行自动转换任务 sku:{}", sku);
            return;
        }
        //默认转换维度为库存转换 ，获取转入sku的销量信息
        Integer transferQuantity = this.calcInSkuTransferQuantity(skuConfig);
        if (transferQuantity <= 0 && Objects.nonNull(skuConfig.getRates())) {
            ExceptionUtil.wrapAndThrow("已配置转换比例，可转换数量为0，不进行拆包操作");
        }
        Long stockTransferId = this.initStockTransferTask(storeNo, sku, transferQuantity);
        //发送钉钉消息

        if (AreaTypeEnum.INTERNAL_AREA.getType().equals(center.getType())) {
            //触发自动完成转换逻辑 存在比例
            if (StringUtils.isNotBlank(skuConfig.getRates())) {
                StockTransferItemOpBO opBO = buildOpBO(skuConfig, transferQuantity, stockTransferId);
                if (CollectionUtils.isEmpty(opBO.getTransferOutInfos())) {
                    log.warn("无可转换批次,storeNo：{}, sku：{}", storeNo, sku);
                    stockTransferBizService.finishStockTransferTask(StockTransferFinishBO.builder()
                            .stockTransferId(stockTransferId).build());
                    return;
                }
                stockTransferBizService.operateStockTransferItem(opBO);
                stockTransferBizService.finishStockTransferTask(StockTransferFinishBO.builder()
                        .stockTransferId(stockTransferId).build());

                Workbook workbook = stockTransferBizService.excelTransfer(stockTransferId);
                try {
                    //生成导入报告/
                    String warehouseName = Global.warehouseMap.get(storeNo);
                    String time = DateUtils.localDateToString(LocalDate.now(), null);
                    String fileNameFormat = "%s,%s,%s需要线下拆包数据.xls";
                    String fileName = String.format(fileNameFormat, time, warehouseName, stockTransferId);
                    //上传模版
                    CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
                    //发送钉钉消息提醒
                    Config config = configMapper.selectOne("un_parking_url");
                    String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT));
                    if (config != null) {
                        long outNum = opBO.getTransferOutInfos().stream().mapToLong(TransferOutInfo::getTransferOutNum).sum();
                        //钉钉推送链接
                        String url = "https://admin." + Global.TOP_DOMAIN_NAME + "/stock-task/transfer/download?fileName=" + fileName;
                        DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> {
                            Map<String, String> md = new HashMap<>();
                            String title = center.getWarehouseName() + "生成转换任务";
                            md.put("title", title);
                            md.put("text", "#### " + title + ":" + stockTransferId + "\n> ###### " +
                                    "时间：" + date + "\n> ###### " + "内容：" + goodsInfoDTO.getTitle() + "自动完成拆包转换" + "-" + goodsInfoDTO.getSpecification() + "转换数量为" + outNum
                                    + "\n> ###### " + "  线下请及时操作" +
                                    "\n> ###### " + "[" + fileName + "]" + "(" + url + ")");
                            return md;
                        }, null);
                    } else {
                        logger.info("钉钉推送失败，配置为空");
                    }
                } catch (Exception e) {
                    logger.error("生成文件推送失败 e={}", e.getMessage());
                }
            }
        }
    }

    /**
     * 计算未配置换算比例时的库存转换数量
     *
     * @param skuConfig
     * <AUTHOR>
     * @Date 2022/12/14 16:07
     **/
    private Integer calcTransNumWithoutRate(ConversionSkuConfig skuConfig) {
        // 查询sku销量信息
        ConversionSkuQuantity param = new ConversionSkuQuantity();
        param.setSku(skuConfig.getInSku());
        param.setDate(LocalDate.now().minusDays(1));
        param.setWarehouseNo(skuConfig.getWarehouseNo());
        ConversionSkuQuantity skuQuantity = conversionSkuQuantityMapper.selectDetail(param);
        if (Objects.isNull(skuQuantity) || Objects.isNull(skuQuantity.getMaxSaleSeven())) {
            return 0;
        }
        return skuQuantity.getMaxSaleSeven().setScale(0, ROUND_UP).intValue();

    }

    private boolean checkValidTime() {
        return LocalDateTime.now().getHour() * 60 + LocalDateTime.now().getMinute() < 17 * 60 + 30;
    }

    public Long initStockTransferTask(Integer storeNo, String sku, Integer transferQuantity) {
        return stockTransferBizService.createStockTransferTask(StockTransferCreateBO.builder()
                .warehouseNo(storeNo.longValue())
                .transferDimension(TransferDimensionEnum.PEER_MOVE.getCode())
                .transferInInfos(Lists.newArrayList(TransferInInfoBO.builder()
                        .transferInNum(transferQuantity.longValue())
                        .transferInSku(sku)
                        .build()))
                .remark(TransferExportConstant.UNPACKING)
                .build());
    }

    /**
     * 校验转入sku状态
     *
     * @param storeNo 库存仓
     * @param sku     sku编码
     * <AUTHOR>
     * @Date 2022/12/2 14:43
     **/
    private boolean checkInSkuStatus(Integer storeNo, String sku) {
        // 获取城市编号areaNo,一个库存仓会对应多个运营区域？
        Set<Integer> areaNoSet = fenceService.selectAreaNosByWareNos(storeNo, sku);
        if (CollectionUtils.isEmpty(areaNoSet)) {
            logger.warn("未查询到库存仓对应的运营区域 warehouseNo:{}, sku:{}", storeNo, sku);
            return false;
        }
        List<AreaSku> areaSkuList = areaSkuMapper.selectBySkuAndAreaNos(sku, areaNoSet);
        if (CollectionUtils.isEmpty(areaSkuList)) {
            return false;
        }
        List<Boolean> onSaleList = areaSkuList.stream().map(AreaSku::getOnSale).collect(Collectors.toList());
        List<Integer> openSaleList = areaSkuList.stream().map(AreaSku::getOpenSale).collect(Collectors.toList());
        if (onSaleList.contains(Boolean.TRUE)) {
            return true;
        }
        if (openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON.ordinal()) ||
                openSaleList.contains(OpenSaleEnum.STOCK_TURN_ON_FOREVER.ordinal())) {
            return true;
        }
        return false;
    }

    /**
     * 计算转入sku数量
     *
     * @param skuConfig sku转换配置
     * <AUTHOR>
     * @Date 2022/12/5 11:52
     **/
    private Integer calcInSkuTransferQuantity(ConversionSkuConfig skuConfig) {
        String inSku = skuConfig.getInSku();
        String outSku = skuConfig.getOutSku();
        Integer warehouseNo = skuConfig.getWarehouseNo();
        // 校验转出商品库存信息
        AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSku(warehouseNo, outSku);
        ExceptionUtil.checkAndThrow(Objects.nonNull(areaStore), String.format("未查询到该sku库存信息 sku:%s, warehouseNo:%s", outSku, warehouseNo));
        ExceptionUtil.checkAndThrow(areaStore.getQuantity() > 0, String.format("该sku无库存信息 sku:%s, warehouseNo:%s", outSku, warehouseNo));
        Integer useQuantity = areaStore.getQuantity() - areaStore.getLockQuantity() - areaStore.getSafeQuantity();
        // 查询sku销量信息
        ConversionSkuQuantity param = new ConversionSkuQuantity();
        param.setSku(inSku);
        param.setDate(LocalDate.now().minusDays(1));
        param.setWarehouseNo(warehouseNo);
        ConversionSkuQuantity skuQuantity = conversionSkuQuantityMapper.selectDetail(param);
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuQuantity), "未查询到昨日库存销量信息");
        BigDecimal maxSaleSeven = skuQuantity.getMaxSaleSeven();
        // 校验大规格库存是否满足拆包
        if (Objects.isNull(skuConfig.getRates())) {
            return Objects.isNull(maxSaleSeven) ? 0 : maxSaleSeven.setScale(0, ROUND_UP).intValue();
        }
        String[] split = skuConfig.getRates().split(":");
        Integer rate = new BigDecimal(split[1]).divide(new BigDecimal(split[0])).intValue();
        if (Objects.isNull(maxSaleSeven) || maxSaleSeven.equals(BigDecimal.ZERO)) {
            log.info("七天每日总销量峰值为0，转入sku数量为换算比例值，{}", rate);
            return rate;
        }
        Integer needOutSkuNum = maxSaleSeven.divide(BigDecimal.valueOf(rate), ROUND_UP).setScale(0, ROUND_UP).intValue();
        return useQuantity > needOutSkuNum ? needOutSkuNum : useQuantity * rate;
    }

    private StockTransferItemOpBO buildOpBO(ConversionSkuConfig skuConfig, Integer transferQuantity, Long stockTransferId) {
        Boolean open = warehouseConfigService.openCabinetManagement(skuConfig.getWarehouseNo());
        List<TransferOutInfo> outInfos = Lists.newArrayList();
        List<GoodsLocationInfo> glInfos = Lists.newArrayList();
        Integer warehouseNo = skuConfig.getWarehouseNo();
        String rates = skuConfig.getRates();
        String outSku = skuConfig.getOutSku();
        Integer rate = Integer.valueOf(rates.split(":")[1]);
        boolean checkoutGoodsLocation = goodsLocationDetailService.checkoutGoodsLocation(warehouseNo);
        //货位批次信息
        StoreRecordVO storeRecordVO = new StoreRecordVO();
        storeRecordVO.setSku(outSku);
        storeRecordVO.setAreaNo(warehouseNo);
        storeRecordVO.setStoreQuantityMin(1);
        List<StoreRecordVO> recordList;
        //获取可以转换的批次信息
        if (checkoutGoodsLocation) {
            recordList = storeRecordMapper.selectLastJX(storeRecordVO);
        } else {
            recordList = storeRecordMapper.selectLastedVO(storeRecordVO);
        }
        //转出sku转出数量
        Integer outQuantity = BigDecimal.valueOf(transferQuantity)
                .divide(BigDecimal.valueOf(rate), ROUND_UP).setScale(0, RoundingMode.UP).intValue();
        Boolean rollback = true;

        // 库位推荐明细
        Map<String, List<CabinetInventoryRecommendDTO>> cabinetInventoryRecommendMap = new HashMap<>();
        if (Boolean.TRUE.equals(open)) {
            List<CabinetInventoryRecommendDTO> allCabinetInventoryRecommendList = new ArrayList<>();
            for (StoreRecordVO recordVO : recordList) {
                PageInfo<CabinetInventoryRecommendOutFacadeRespDTO> page = cabinetBatchInventoryFacade.queryRecommendOut(CabinetInventorySingleRecommendOutReqFacadeDTO.builder()
                        .pageIndex(1)
                        .pageSize(50)
                        .warehouseNo(warehouseNo)
                        .skuCode(recordVO.getSku())
                        .skuBatchCode(recordVO.getBatch())
                        .qualityDate(DateUtil.toDate(recordVO.getQualityDate()))
                        .produceDate(DateUtil.toDate(recordVO.getProductionDate()))
                        .build());
                List<CabinetInventoryRecommendOutFacadeRespDTO> list = page.getList();
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
                    log.error("自动转换仓库:{},sku:{}批次:{},生产日期:{},保质期:{}查询不到库位库存", warehouseNo, recordVO.getSku(), recordVO.getBatch(), recordVO.getProductionDate(), recordVO.getQualityDate());
                    continue;
                }
                for (CabinetInventoryRecommendOutFacadeRespDTO dto : list) {
                    CabinetInventoryRecommendDTO cabinetInventoryRecommendDTO = CabinetInventoryRecommendConverter.INSTANCE.convert(dto);
                    cabinetInventoryRecommendDTO.setBatchNo(recordVO.getBatch());
                    allCabinetInventoryRecommendList.add(cabinetInventoryRecommendDTO);
                }
            }
            cabinetInventoryRecommendMap = allCabinetInventoryRecommendList.stream().distinct().collect(Collectors.groupingBy(item -> getGroupKey(item.getSkuCode(), item.getProduceDate(), item.getQualityDate())));
        }

        for (StoreRecordVO recordVO : recordList) {
            Integer storeQuantity = recordVO.getStoreQuantity();
            if (transferQuantity <= 0) {
                break;
            }
            Integer newTransferQuantity = outQuantity - storeQuantity >= 0 ? storeQuantity : outQuantity;
            if (newTransferQuantity <= 0) {
                continue;
            }
            outQuantity = outQuantity - newTransferQuantity;
            // 开启库位管理
            if (Boolean.TRUE.equals(open)) {
                List<CabinetInventoryRecommendDTO> cabinetInventoryRecommendDTOList = cabinetInventoryRecommendMap.get(getGroupKey(recordVO.getSku(), DateUtil.toDate(recordVO.getProductionDate()), DateUtil.toDate(recordVO.getQualityDate())));
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(cabinetInventoryRecommendDTOList)) {
                    log.error("自动转换仓库:{},sku:{}批次:{},生产日期:{},保质期:{}查询不到库位库存", warehouseNo, recordVO.getSku(), recordVO.getBatch(), recordVO.getProductionDate(), recordVO.getQualityDate());
                    continue;
                }
                // 排序
                List<CabinetInventoryRecommendDTO> sortedCabinetInventoryRecommendList = cabinetInventoryRecommendDTOList.stream().sorted(Comparator.comparing(CabinetInventoryRecommendDTO::getQualityDate).thenComparing(CabinetInventoryRecommendDTO::getCabinetPurpose).thenComparing(CabinetInventoryRecommendDTO::getCabinetCode)).collect(Collectors.toList());
                Long exeQuantity = newTransferQuantity.longValue();
                for (CabinetInventoryRecommendDTO cabinetInventoryRecommendDTO : sortedCabinetInventoryRecommendList) {
                    if (exeQuantity == 0) {
                        break;
                    }
                    long diffQuantity = exeQuantity - cabinetInventoryRecommendDTO.getAvailableQuantity().longValue() >= 0 ? cabinetInventoryRecommendDTO.getAvailableQuantity().longValue() : exeQuantity;
                    TransferOutInfo outInfo = TransferOutInfo.builder()
                            .transferOutNum(diffQuantity)
                            .transferOutBatch(cabinetInventoryRecommendDTO.getBatchNo())
                            .produceTime(DateUtil.toMill(cabinetInventoryRecommendDTO.getProduceDate()))
                            .shelfLife(DateUtil.toMill(cabinetInventoryRecommendDTO.getQualityDate()))
                            .transferOutSku(recordVO.getSku())
                            .transferOutCabinetNo(cabinetInventoryRecommendDTO.getCabinetCode())
                            .build();
                    outInfos.add(outInfo);
                    exeQuantity -= diffQuantity;
                }
                rollback = false;
            } else {
                TransferOutInfo outInfo = TransferOutInfo.builder()
                        .transferOutNum(newTransferQuantity.longValue())
                        .transferOutBatch(recordVO.getBatch())
                        .produceTime(DateUtil.toMill(recordVO.getProductionDate()))
                        .shelfLife(DateUtil.toMill(recordVO.getQualityDate()))
                        .transferOutSku(recordVO.getSku())
                        .build();
                outInfos.add(outInfo);
                rollback = false;
            }
            GoodsLocationInfo glInfo = GoodsLocationInfo.builder()
                    .batch(recordVO.getBatch())
                    .sku(recordVO.getSku())
                    .produceTime(DateUtil.toMill(recordVO.getProductionDate()))
                    .shelfLife(DateUtil.toMill(recordVO.getQualityDate()))
                    .inGlNo(recordVO.getGlNo())
                    .outGlNo(recordVO.getGlNo())
                    .build();
            glInfos.add(glInfo);
        }
        if (Boolean.TRUE.equals(rollback)) {
            throw new BizException("无可转换库存数据");
        }
        List<StockTransferItemDO> stockTransferItemDOS = stockTransferItemDAO.listByStockTransferId(stockTransferId);
        return StockTransferItemOpBO.builder()
                .stockTransferItemId(stockTransferItemDOS.get(NumberUtils.INTEGER_ZERO).getId())
                .transferInSku(skuConfig.getInSku())
                .stockTransferId(stockTransferId)
                .transferOutInfos(outInfos)
                .warehouseNo(warehouseNo.longValue())
                .transferOutSku(skuConfig.getOutSku())
                .transferRatio(skuConfig.getRates())
                .glInfos(glInfos)
                .type(TransferOpEnum.ONE_WAY.getCode())
                .build();
    }


    @Override
    public AjaxResult selectTransferProcessDetail(Integer id) {
        List<StockTaskProcessDetailVO> processDetailVOS = stockTaskProcessDetailMapper.selectTransferByProcessId(id);
        processDetailVOS.forEach(el -> {
            //获取打印次数及 是否可以打印
            String sku = el.getSku();
            String listNo = el.getTransferListNo();
            LocalDate productionDate = el.getProductionDate();
            SkuBatchCode skuBatchCode = new SkuBatchCode(sku, listNo, productionDate);
            SkuBatchCode queryCode = skuBatchCodeService.selectSkuBatchCod(skuBatchCode);
            if (!Objects.isNull(queryCode)) {
                Integer printNumber = queryCode.getPrintNumber();
                el.setPrintNumber(printNumber);
            }
            InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
            Integer canPrint = !Objects.isNull(inventoryVO) && Objects.equals(inventoryVO.getCategoryType(), CategoryTypeEnum.FRUIT.getType())
                    ? CodePrintEnum.CAN_PRINT.ordinal() : CodePrintEnum.NOT_CAN_PRINT.ordinal();
            el.setCanPrint(canPrint);

        });
        wmsBuilderService.batchBuildWMSInfo(processDetailVOS);
        return AjaxResult.getOK(processDetailVOS);
    }

    @Override
    public void autoSaleTransferTask() {
        //查询需要转换的数据
        //获取转换信息
        ConversionSkuConfig skuConfig = new ConversionSkuConfig();
        skuConfig.setStatus(CurrencyStatusEnum.EFFECTIVE.ordinal());
        List<ConversionSkuConfig> resultSkuConfig = conversionSkuConfigMapper.selectConfig(skuConfig);
        //获取当前库存数量,以及销量峰值
        for (ConversionSkuConfig conversionSkuConfig : resultSkuConfig) {
            String inSku = conversionSkuConfig.getInSku();
            Integer warehouseNo = conversionSkuConfig.getWarehouseNo();
            AreaStore areaStoreKey = new AreaStore(warehouseNo, inSku);
            //获取库存仓数量
            AreaStore areaStore = areaStoreMapper.selectWithOutDataPermission(areaStoreKey);
            //获取昨天的销量信息
            ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity();
            skuQuantity.setSku(inSku);
            skuQuantity.setWarehouseNo(warehouseNo);
            skuQuantity.setDate(LocalDate.now().minusDays(1));
            ConversionSkuQuantity resultSkuQuantity = conversionSkuQuantityMapper.selectDetail(skuQuantity);
            if (areaStore.getQuantity() > 0 || Objects.isNull(resultSkuQuantity)) {
                continue;
            }
            //开始发生转换
            String rates = skuConfig.getRates();
            Integer rate = StringUtils.isEmpty(rates) ? NumberUtils.INTEGER_ONE : Integer.valueOf(rates.split(":")[NumberUtils.INTEGER_ONE]);
            //订单最小值
            Integer realRate = resultSkuQuantity.getMinSaleCnt().intValue() == 0 ? rate : resultSkuQuantity.getMinSaleCnt().intValue();
            if (areaStore.getQuantity() < realRate) {
                try {
                    // autoTransferTask(warehouseNo, inSku, null, conversionSkuConfig.getId());
                } catch (Exception e) {
                    logger.error("转换失败 e={}", e);
                }
            }

        }
    }

    /**
     * 转换出入库的入口在此 {@link TransferService#stockTransfer(TransformVO)}
     */
    @Override
    public AjaxResult inOutStore(Integer type, String data) {
        return AjaxResult.getError();
    }

    @Override
    public void closeTask(StockTask stockTask) {
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTask.getId());
        List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(select);
        if (!CollectionUtils.isEmpty(stockTaskItems)) {
            for (StockTaskItem item : stockTaskItems) {
                areaStoreService.updateAreaStoreStatus(stockTask.getAreaNo(), item.getSku());
            }
        }

        //更新保质期
        Executors.newSingleThreadExecutor().execute(() -> {
            List<StockTaskProcessDetailVO> detailList = stockTaskProcessDetailMapper.selectTransferByProcessId(stockTask.getId());
            Set<String> skuSet = new HashSet<>();
            detailList.forEach(el -> {
                skuSet.add(el.getSku());
                skuSet.add(el.getTransferSku());
            });
            if (!CollectionUtils.isEmpty(skuSet)) {
                skuSet.forEach(el -> {
                    areaSkuService.syncSkuQualityDateByStoreNo(stockTask.getAreaNo(), el);
                });
            }
        });
    }

    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Integer rowIndex = excelTitle(stockTask, sheet);
        excelContact(sheet, rowIndex, stockTask.getId());
        String warehouseName = Global.warehouseMap.get(stockTask.getAreaNo());
        try {
            ExcelUtils.outputExcel(workbook, stockTask.getAddtime().toLocalDate() + warehouseName + "转换任务.xls",
                    RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 生成excel表
     *
     * @param areaNo   库存仓
     * @param type     出库任务类型
     * @param taskList 出库任务列表
     * @return 生成的excel(文件名 + 文件实体)
     */
    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        return null;
    }

    @Override
    public void stockTaskBatchDetailDownload(List<StockTask> stockTaskList) {

    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        List<StockTaskItemVO> stockTaskItemVOS = stockTaskItemMapper.selectByStockTaskId(result.getId());
        wmsBuilderService.batchBuildWMSInfo(stockTaskItemVOS);
        result.setList(stockTaskItemVOS);
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public AjaxResult manualCloseStockTask(Integer stockTaskId, String closeReason, Integer type) {
        return null;
    }

    /**
     * 创建转换信息
     *
     * @param transformItemVOS
     * @param skuConfig
     */
    private void createTransformItemVO(List<TransformItemVO> transformItemVOS, ConversionSkuConfig skuConfig, Integer quantity) {
        Integer warehouseNo = skuConfig.getWarehouseNo();
        String rates = skuConfig.getRates();
        String outSku = skuConfig.getOutSku();
        Integer rate = Integer.valueOf(rates.split(":")[1]);
        boolean checkoutGoodsLocation = goodsLocationDetailService.checkoutGoodsLocation(warehouseNo);
        //货位批次信息
        StoreRecordVO storeRecordVO = new StoreRecordVO();
        storeRecordVO.setSku(outSku);
        storeRecordVO.setAreaNo(warehouseNo);
        storeRecordVO.setStoreQuantityMin(1);
        List<StoreRecordVO> recordList;
        //获取可以转换的批次信息
        if (checkoutGoodsLocation) {
            recordList = storeRecordMapper.selectLastJX(storeRecordVO);
        } else {
            recordList = storeRecordMapper.selectLastedVO(storeRecordVO);
        }
        //转出sku转出数量
        Integer outQuantity = quantity / rate;
        for (StoreRecordVO recordVO : recordList) {
            TransformItemVO transformItemVO = new TransformItemVO();
            Integer storeQuantity = recordVO.getStoreQuantity();
            if (outQuantity <= 0) {
                break;
            }
            Integer transferQuantity = outQuantity >= storeQuantity ? storeQuantity : outQuantity;
            outQuantity = outQuantity - transferQuantity;
            transformItemVO.setId(recordVO.getId());
            transformItemVO.setInQuantity(transferQuantity * rate);
            transformItemVO.setOutQuantity(transferQuantity);
            transformItemVO.setInGlNo(recordVO.getGlNo());
            transformItemVO.setOutGlNo(recordVO.getGlNo());
            transformItemVOS.add(transformItemVO);
        }
    }

    /**
     * 获取转换数量
     *
     * @param skuConfig
     * @return
     */
    private AreaStore getTransferQuantity(ConversionSkuConfig skuConfig) {
        ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity();
        skuQuantity.setSku(skuConfig.getInSku());
        skuQuantity.setWarehouseNo(skuConfig.getWarehouseNo());
        skuQuantity.setDate(LocalDate.now().minusDays(NumberUtils.INTEGER_ONE));
        ConversionSkuQuantity inSkuQuantity = conversionSkuQuantityMapper.selectDetail(skuQuantity);
        skuQuantity.setSku(skuConfig.getOutSku());
        ConversionSkuQuantity outSkuQuantity = conversionSkuQuantityMapper.selectDetail(skuQuantity);
        if (Objects.isNull(outSkuQuantity) || Objects.isNull(inSkuQuantity)) {
            throw new DefaultServiceException("无销量信息不发生转换");
        }
        //获取转入sku库存信息
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(skuConfig.getInSku());
        areaStore.setAreaNo(skuConfig.getWarehouseNo());
        AreaStore queryInAreaStore = areaStoreMapper.selectWithOutDataPermission(areaStore);

        //获取转出sku数量信息
        areaStore.setSku(skuConfig.getOutSku());
        AreaStore queryOutAreaStore = areaStoreMapper.selectWithOutDataPermission(areaStore);
        Integer useQuantity = queryOutAreaStore.getQuantity() - queryOutAreaStore.getLockQuantity() - queryOutAreaStore.getSafeQuantity();
        //校验转出sku的可用库存是否可以大于7天平均销量
        if (outSkuQuantity.getSaleCntFifteen().intValue() >= useQuantity) {
            logger.info("storeNo：{}, sku：{} 转出sku可用库存不足", skuConfig.getWarehouseNo(), skuConfig.getInSku());
            throw new DefaultServiceException("转出sku可用库存不足,不发生转换");
        }
        //计算转入sku数量
        BigDecimal saleCntFifteen = inSkuQuantity.getSaleCntFifteen();
        BigDecimal multiply = saleCntFifteen.multiply(BigDecimal.valueOf(1.05));
        Integer saleCntQuantity = multiply.setScale(0, ROUND_UP).intValue();
        Integer inQuantity = saleCntQuantity - queryInAreaStore.getQuantity();
        Integer quantity = inQuantity < 0 ? 0 : inQuantity;
        //存在比例 根据比例计算
        if (!StringUtils.isEmpty(skuConfig.getRates())) {
            // Integer rate = Integer.valueOf(skuConfig.getRates().split(":")[1]);
            String[] split = skuConfig.getRates().split(":");
            Integer rate = new BigDecimal(split[1]).divide(new BigDecimal(split[0])).intValue();
            quantity = quantity < rate ? rate : quantity - (quantity % rate);
            //可操作转换数量
            Integer allQuantity = useQuantity - outSkuQuantity.getSaleCntSeven().intValue();
            //计算数量 转出sku可转出数量 * 比例 < 转入数量
            quantity = allQuantity * rate < quantity ? allQuantity * rate : quantity;
            if (quantity <= 0) {
                throw new DefaultServiceException("转出sku可转换数量不足");
            }
        }

        AreaStore returnAreaStore = new AreaStore();
        returnAreaStore.setQuantity(quantity);
        logger.info("转换计算 sku:{},quantity:{}", skuConfig.getInSku(), quantity);
        return returnAreaStore;
    }

    /**
     * 自动生成转换任务表格信息
     */
    @Override
    public void autoCreateTransferDate() {
        log.info("自动推送转换任务信息任务开始执行...");
        //获取所有的库存仓
        //遍历库存仓获取今天转换的信息
        List<StockTransferDO> stockTransferDos = stockTransferDAO.selectByCreatedAt(LocalDate.now(), TransferExportConstant.UNPACKING);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(stockTransferDos)) {
            log.info("无可发送的转换信息,date:{},自动推送转换任务信息任务执行结束...", LocalDate.now());
            return;
        }
        Set<Long> warehouseNoList = stockTransferDos.stream().map(StockTransferDO::getWarehouseNo).collect(Collectors.toSet());
        Map<Long, List<StockTransferDO>> transferMap = stockTransferDos.stream().collect(Collectors.groupingBy(StockTransferDO::getWarehouseNo));

        //文件名信息
        List<String> filenameList = new ArrayList<>();
        Map<String, String> fileNameMap = Maps.newHashMap();
        for (Long warehouseNo : warehouseNoList) {
            List<StockTransferDO> transferList = transferMap.get(warehouseNo);
            if (CollectionUtils.isEmpty(transferList)) {
                continue;
            }
            Workbook workbook = stockTransferBizService.excelTransferPool(warehouseNo,
                    transferList.stream().map(StockTransferDO::getId).collect(Collectors.toList()));
            //生成导入报告
            String warehouseName = Global.warehouseMap.get(warehouseNo.intValue());
            String time = DateUtils.localDateToString(LocalDate.now(), null);
            String fileNameFormat = "%s,%s线下拆包数据汇总.xls";
            String fileName = String.format(fileNameFormat, time, warehouseName);
            //上传模版
            // CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
            File file = null;
            FileOutputStream out = null;
            try {
                file = new File(fileName);
                out = new FileOutputStream(file);
                // excel输出到文件
                workbook.write(out);
                OssUploadResult uploadResult = OssUploadUtil.uploadExpireThreeDay(fileName, file);
                fileNameMap.put(fileName, uploadResult.getUrl());
            } catch (Throwable e) {
                logger.error("startUpload.异步上传外部存储异常,自动推送转换任务信息任务执行结束...", e);
            }
            filenameList.add(fileName);
        }

        try {
            Config config = configMapper.selectOne("un_parking_url");
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT));
            StringBuffer buffer = new StringBuffer();
            for (String fileName : filenameList) {
                // String url = "https://admin." + Global.TOP_DOMAIN_NAME + "/stock-task/transfer/download?fileName=" + fileName;
                String url = fileNameMap.get(fileName);
                buffer.append("\n> ###### ").append("[")
                        .append(fileName).append("]").append("(")
                        .append(url).append(")");
            }
            if (config != null && !CollectionUtils.isEmpty(filenameList)) {
                DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> {
                    Map<String, String> md = new HashMap<>();
                    String title = "当日自动生成转换任务汇总";
                    md.put("title", title);
                    md.put("text", "#### " + title + "\n> ###### " + "时间：" + date + "\n> ###### " + "内容："
                            + buffer.toString() + "\n> ######       线下请及时操作");
                    return md;
                }, null);
                log.info("自动推送转换任务信息任务执行结束...");
            } else {
                logger.info("钉钉推送失败,配置为空,自动推送转换任务信息任务执行结束...");
            }
        } catch (Exception e) {
            logger.error("钉钉消息推送失败 e={},自动推送转换任务信息任务执行结束...", e);
        }
    }

    @Override
    public void inSkuAutoTransferTask() {
        ConversionSkuConfig skuConfig = new ConversionSkuConfig();
        skuConfig.setStatus(0);
        List<ConversionSkuConfig> skuConfigList = conversionSkuConfigMapper.selectConfig(skuConfig);
        if (CollectionUtils.isEmpty(skuConfigList)) {
            logger.info("当前无生效状态的sku转换配置");
            return;
        }
        skuConfigList.forEach(config -> {
            String inSku = config.getInSku();
            Integer warehouseNo = config.getWarehouseNo();
            ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity();
            skuQuantity.setSku(inSku);
            skuQuantity.setWarehouseNo(warehouseNo);
            skuQuantity.setDate(LocalDate.now().minusDays(1));
            ConversionSkuQuantity result = conversionSkuQuantityMapper.selectDetail(skuQuantity);
            if (Objects.isNull(result)) {
                logger.warn("未查询到sku销量统计数据:{}", JSONUtil.toJsonStr(skuQuantity));
                return;
            }
            // 无销量峰值或者为0
            BigDecimal maxSaleSeven = result.getMaxSaleSeven();
            if (Objects.isNull(maxSaleSeven)) {
                logger.info("sku近七日无销量数据，{}", JSONUtil.toJsonStr(skuQuantity));
                return;
            }
            // 仓库库存信息
            AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSku(warehouseNo, inSku);
            if (Objects.isNull(areaStore)) {
                logger.warn("未查询到仓库库存信息 warehouseNo:{}, inSku:{}", warehouseNo, inSku);
                return;
            }
            // 虚拟库存校验
            if (areaStore.getOnlineQuantity() > maxSaleSeven.divide(BigDecimal.valueOf(2), ROUND_UP)
                    .setScale(0, BigDecimal.ROUND_UP).intValue()) {
                logger.info("虚拟库存量大于等于近七日峰值一半 不触发拆包 areaStore：{}, maxSaleSeven:{}",
                        JSONUtil.toJsonStr(areaStore), maxSaleSeven);
                return;
            }
            try {
                this.autoTransferTask(warehouseNo, inSku, LocalDateTime.now(), config.getId());
            } catch (Exception e) {
                logger.warn("自动拆包流程结束 原因:{}, areaNo:{}, sku:{}, configId:{}", e.getMessage(), warehouseNo, inSku, config.getId());
            }
        });


    }

    /**
     * 拼装表头信息
     */
    private Integer excelTitle(StockTask stockTask, Sheet sheet) {

        int rowIndex = 0;
        Row firstRow = sheet.createRow(rowIndex++);
        firstRow.createCell(0).setCellValue("库存转换:");
        String id = Objects.isNull(stockTask.getId()) ? "" : stockTask.getId().toString();
        firstRow.createCell(1).setCellValue(id);

        Row secondRow = sheet.createRow(rowIndex++);
        secondRow.createCell(0).setCellValue("仓库");

        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(stockTask.getAreaNo());
        String warehouseName = center.getWarehouseName();
        secondRow.createCell(1).setCellValue(warehouseName);

        Row thirdRow = sheet.createRow(rowIndex++);
        thirdRow.createCell(0).setCellValue("发起时间");
        String time = DateUtils.localDateTimeToString(stockTask.getAddtime());
        thirdRow.createCell(1).setCellValue(time);
        rowIndex++; //空置一行

        Row title = sheet.createRow(rowIndex++);
        String[] titleNames = {"类目类型", "类目名称", "sku编码", "商品名称", "规格", "商品归属", "储存区域", "包装", "应转入数量", "批次", "生产日期", "保质期", "实际转入数量"};
        for (int i = 0; i < titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }
        return rowIndex;
    }

    private Integer excelContact(Sheet sheet, Integer rowIndex, Integer stockTaskId) {
        List<StockTaskItemVO> itemVOS = stockTaskItemMapper.selectByStockTaskId(stockTaskId);
        wmsBuilderService.batchBuildWMSInfo(itemVOS);
        itemVOS = wmsBuilderService.sortedFruitPriority(itemVOS);

        for (StockTaskItemVO itemVO : itemVOS) {
            List<StockTaskItemDetailVO> itemDetailVOS = itemVO.getStockTaskItemDetailVOS();
            if (!CollectionUtils.isEmpty(itemDetailVOS)) {
                if (itemDetailVOS.size() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 4, 4));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 5, 5));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 6, 6));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + itemDetailVOS.size() - 1, 7, 7));
                }


                for (StockTaskItemDetailVO itemDetailVO : itemDetailVOS) {
                    Row row = sheet.createRow(rowIndex++);
                    row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
                    row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
                    row.createCell(2).setCellValue(itemVO.getSku());
                    row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
                    row.createCell(4).setCellValue(itemVO.getWeight());
                    String productsType = StringUtils.productType(itemVO.getSkuType(), itemVO.getNameRemakes());
                    row.createCell(5).setCellValue(productsType);
                    row.createCell(6).setCellValue(itemVO.getStorageArea());
                    row.createCell(7).setCellValue(itemVO.getPacking());
                    row.createCell(8).setCellValue(itemVO.getQuantity());
                    row.createCell(9).setCellValue(itemDetailVO.getListNo());
                    row.createCell(10).setCellValue(itemDetailVO.getProductionDate() != null ? itemDetailVO.getProductionDate().toString() : "无");
                    row.createCell(11).setCellValue(itemDetailVO.getQualityDate() != null ? itemDetailVO.getQualityDate().toString() : "无");
                    row.createCell(12).setCellValue(itemDetailVO.getQuantity());
                }

            } else {
                Row row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
                row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
                row.createCell(2).setCellValue(itemVO.getSku());
                row.createCell(3).setCellValue(itemVO.getPdName());
                row.createCell(4).setCellValue(itemVO.getWeight());
                String productsType = StringUtils.productType(itemVO.getSkuType(), itemVO.getNameRemakes());
                row.createCell(5).setCellValue(productsType);
                row.createCell(6).setCellValue(itemVO.getStorageArea());
                row.createCell(7).setCellValue(itemVO.getPacking());
                row.createCell(8).setCellValue(itemVO.getQuantity());
            }
        }
        return rowIndex;
    }

    private String getGroupKey(String sku, Date produceTime, Date shelfLife) {
        return sku + "_" + produceTime + "_" + shelfLife;
    }

}
