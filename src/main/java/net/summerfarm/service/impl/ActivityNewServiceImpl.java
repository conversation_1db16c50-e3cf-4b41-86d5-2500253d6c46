package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.FeiShuSingUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.BlackAndWhiteTypeEnum;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.enums.InventoryExtTypeEnum;
import net.summerfarm.enums.market.activity.AccountLimitEnum;
import net.summerfarm.enums.market.activity.ActivityStatusEnum;
import net.summerfarm.enums.market.activity.ActivityTagEnum;
import net.summerfarm.enums.market.activity.ActivityTypeEnum;
import net.summerfarm.enums.market.activity.AdjustTypeEnum;
import net.summerfarm.enums.market.activity.GoodSelectWayEnum;
import net.summerfarm.enums.market.activity.PlatformEnum;
import net.summerfarm.enums.market.activity.ScopeTypeEnum;
import net.summerfarm.facade.SaleInventoryFacade;
import net.summerfarm.facade.WarehouseInventoryFacade;
import net.summerfarm.facade.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.facade.WarehouseStorageQueryFacade;
import net.summerfarm.mapper.manage.ActivityBasicInfoMapper;
import net.summerfarm.mapper.manage.ActivityBlackAndWhiteListMapper;
import net.summerfarm.mapper.manage.ActivityItemConfigMapper;
import net.summerfarm.mapper.manage.ActivitySceneConfigMapper;
import net.summerfarm.mapper.manage.ActivityScopeConfigMapper;
import net.summerfarm.mapper.manage.ActivitySkuDetailMapper;
import net.summerfarm.mapper.manage.ActivitySkuPriceMapper;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.LargeAreaMapper;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.mapper.offline.SkuSalesVolumeMapper;
import net.summerfarm.mapper.offline.SkuWarehouseSellLabelMapper;
import net.summerfarm.mapper.offline.TemporaryInsuranceRiskMapper;
import net.summerfarm.model.DTO.AreaInfoDTO;
import net.summerfarm.model.DTO.market.ActivityBasicInfoDTO;
import net.summerfarm.model.DTO.market.ActivityBasicQueryDTO;
import net.summerfarm.model.DTO.market.ActivityBlackAndWhiteListDTO;
import net.summerfarm.model.DTO.market.ActivityBlackImportDTO;
import net.summerfarm.model.DTO.market.ActivityItemConfigDTO;
import net.summerfarm.model.DTO.market.ActivityItemScopeDTO;
import net.summerfarm.model.DTO.market.ActivityLadderConfigDTO;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.DTO.market.ActivityNewDTO;
import net.summerfarm.model.DTO.market.ActivityPageQueryDTO;
import net.summerfarm.model.DTO.market.ActivityPageRespDTO;
import net.summerfarm.model.DTO.market.ActivityScopeConfigDTO;
import net.summerfarm.model.DTO.market.ActivityScopeQueryDTO;
import net.summerfarm.model.DTO.market.ActivitySkuBatchDTO;
import net.summerfarm.model.DTO.market.ActivitySkuDetailDTO;
import net.summerfarm.model.DTO.market.ActivitySkuPriceDTO;
import net.summerfarm.model.DTO.market.ActivityWhiteImportDTO;
import net.summerfarm.model.DTO.market.LargeAreaSkuPriceDTO;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import net.summerfarm.model.converter.ActivityNewConverter;
import net.summerfarm.model.domain.ActivityBlackAndWhiteList;
import net.summerfarm.model.domain.ActivitySku;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.DataSynchronizationInformation;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.PriceStrategy;
import net.summerfarm.model.domain.market.ActivityBasicInfo;
import net.summerfarm.model.domain.market.ActivityItemConfig;
import net.summerfarm.model.domain.market.ActivitySceneConfig;
import net.summerfarm.model.domain.market.ActivityScopeConfig;
import net.summerfarm.model.domain.market.ActivitySkuDetail;
import net.summerfarm.model.domain.market.ActivitySkuPrice;
import net.summerfarm.model.domain.offline.SkuSalesVolume;
import net.summerfarm.model.domain.offline.SkuWarehouseSellLabel;
import net.summerfarm.model.domain.offline.TemporaryInsuranceRisk;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.ActivitySkuDetailValueObject;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.LargeAreaVO;
import net.summerfarm.model.vo.PriceStrategyAuditRecordVO;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.CycleInventoryCostService;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.PriceStrategyService;
import net.summerfarm.service.helper.market.ActivityNewServiceHelper;
import net.summerfarm.wms.inventory.req.QueryBatchInventoryBySkuAndWarehouseReq;
import net.summerfarm.wms.inventory.resp.QueryBatchInventoryBySkuAndWarehouseResp;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryResp;
import net.summerfarm.wnc.client.req.WarehouseBaseInfoByNoReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2022/12/2
 */
@Slf4j
@Service
public class ActivityNewServiceImpl extends BaseService implements ActivityNewService {

    @Resource
    private ActivityNewServiceHelper activityNewServiceHelper;

    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;

    @Resource
    private ActivityItemConfigMapper activityItemConfigMapper;

    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Resource
    private ActivityScopeConfigMapper activityScopeConfigMapper;

    @Resource
    private ActivitySceneConfigMapper activitySceneConfigMapper;

    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private PriceStrategyService priceStrategyService;

    @Resource
    private CycleInventoryCostService cycleInventoryCostService;

    @Resource
    private SkuSalesVolumeMapper skuSalesVolumeMapper;

    @Resource
    private TemporaryInsuranceRiskMapper temporaryInsuranceRiskMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private WarehouseInventoryFacade warehouseInventoryFacade;

    @Resource
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Resource
    private SaleInventoryFacade saleInventoryFacade;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private ActivityBlackAndWhiteListMapper activityBlackAndWhiteListMapper;

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    @Resource
    private SkuWarehouseSellLabelMapper skuWarehouseSellLabelMapper;

    @Resource
    private LargeAreaMapper largeAreaMapper;

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(4, 8,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
            new NamedThreadFactory("营销活动-", false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 剩余库存默认值
     */
    public static final int DEFAULT_ACTUAL_QUANTITY = 100000;

    /**
     * 默认临保风险日期
     */
    public static final int DEFAULT_CLINICAL_INSURANCE = 30;

    public static final String PRICING_PARAMS = "pricingParams";

    public static final String LARGE_AREAS = "largeAreas";

    public static final String RISK_ROBOT = "temporaryInsuranceRiskRobot";

    public static final String LINE_FEED = "\n";

    public static final Pattern PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addBasicInfo(ActivityNewDTO activityNewDTO) {
        activityNewServiceHelper.check(activityNewDTO);
        //校验信息判空
        activityNewDTO.getItemConfigDTO().setGoodSelectWay(GoodSelectWayEnum.SKU.getCode());
        Integer adminId = getAdminId();
        ActivityBasicInfoDTO basicInfoDTO = activityNewDTO.getBasicInfoDTO();
        activityNewDTO.setAdminId(adminId);
        //构建并保存活动基础信息
        ActivityBasicInfo basicInfo = ActivityNewConverter.INSTANCE.dtoToInfo(basicInfoDTO);
        basicInfo.setCreatorId(adminId);
        basicInfo.setStatus(1);
        activityBasicInfoMapper.insertSelective(basicInfo);
        Long basicInfoId = basicInfo.getId();
        basicInfoDTO.setBasicInfoId(basicInfoId);
        ActivitySceneConfig sceneConfig = new ActivitySceneConfig();
        sceneConfig.setBasicInfoId(basicInfoId);
        sceneConfig.setPlatform(basicInfoDTO.getPlatform());
        activitySceneConfigMapper.insertSelective(sceneConfig);

        //构建并保存活动范围信息
        Integer scopeType = activityNewDTO.getScopeConfigDTO().getScopeType();
        List<ActivityScopeConfig> scopeConfigs = activityNewDTO.getScopeConfigDTO().getScopeIds()
                .stream().map(x -> {
                    ActivityScopeConfig scopeConfig = new ActivityScopeConfig();
                    scopeConfig.setBasicInfoId(basicInfoId);
                    scopeConfig.setScopeId(x);
                    scopeConfig.setScopeType(scopeType);
                    scopeConfig.setUpdaterId(adminId);
                    return scopeConfig;
                }).collect(Collectors.toList());

        activityScopeConfigMapper.insertBatch(scopeConfigs);

        //构建并保存sku相关信息
        ActivityItemConfig itemConfig = ActivityNewConverter.INSTANCE.dtoToItemConfig(
                activityNewDTO.getItemConfigDTO());
        itemConfig.setBasicInfoId(basicInfoId);
        //本期默认
        activityItemConfigMapper.insertSelective(itemConfig);
        activityNewDTO.getItemConfigDTO().setId(itemConfig.getId());

        //判断活动商品配置类型
        List<ActivitySkuDetailDTO> skuDetailDTOList = activityNewDTO.getItemConfigDTO()
                .getSkuDetailList();
        Map<String, Long> skuCount = skuDetailDTOList.stream().map(x -> x.getSku())
                .collect(Collectors.groupingBy(x -> x, Collectors.counting()));
        List<String> repeatSkus = skuCount.entrySet().stream().filter(x -> x.getValue() > 1)
                .map(x -> x.getKey()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(repeatSkus)) {
            String skuStr = repeatSkus.stream().collect(Collectors.joining(","));
            throw new BizException("活动创建失败，存在重复的活动商品:" + skuStr);
        }
        List<ActivitySkuDetail> skuDetailList = Lists.newArrayList();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(),
                activityNewDTO.getItemConfigDTO().getGoodSelectWay())) {
            skuDetailList = ActivityNewConverter.INSTANCE.dtoListToSkuDetailList(
                    skuDetailDTOList);
            for (ActivitySkuDetail skuDetail : skuDetailList) {
                if (skuDetail.getLimitQuantity() == null) {
                    skuDetail.setLimitQuantity(0);
                }
                if (skuDetail.getMinSaleNum() == null) {
                    skuDetail.setMinSaleNum(0);
                }
                if (skuDetail.getActualQuantity() == null) {
                    //设置剩余库存默认值
                    skuDetail.setActualQuantity(DEFAULT_ACTUAL_QUANTITY);
                }
                if (skuDetail.getDiscountLabel() == null) {
                    //默认展示特价标签
                    skuDetail.setDiscountLabel(CommonStatus.YES.getCode());
                }
            }
            activitySkuDetailMapper.insertBatch(skuDetailList, itemConfig.getId());

        } else {
            //其他类型
        }
        final List<ActivitySkuDetail> skuDetailListFinal = skuDetailList;
        final Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        //异步计算活动价并保存，后面可以改成MQ消息的方式处理
        EXECUTOR_SERVICE.execute(() ->
        {
            try {
                MDC.setContextMap(copyOfContextMap);
                activityNewServiceHelper.dealActivitySkuPrice(activityNewDTO, skuDetailListFinal);
            } catch (Exception e) {
                log.warn("创建营销活动,处理活动价异常,活动id:{},cause:{}", basicInfoId,
                        Throwables.getStackTraceAsString(e));
            }
        });
        return CommonResult.ok(Boolean.TRUE);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateBasicInfo(ActivityBasicInfoDTO basicInfoDTO) {
        Integer adminId = getAdminId();
        //校验活动id
        ActivityBasicInfo oldBasicInfo = activityBasicInfoMapper.selectByPrimaryKey(
                basicInfoDTO.getBasicInfoId());
        if (oldBasicInfo == null) {
            throw new BizException("活动不存在");
        }
        ActivityNewDTO activityNewDTO = new ActivityNewDTO();
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);
        //改基础信息，需要校验时间重复问题
        //构建活动范围
        activityNewServiceHelper.buildScopeConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);

        //构建商品信息
        activityNewServiceHelper.buildItemConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        activityNewServiceHelper.check(activityNewDTO);
        if (basicInfoDTO.getType() != null && !Objects.equals(basicInfoDTO.getType(),
                oldBasicInfo.getType())) {
            throw new BizException("活动类型不支持修改");
        }

        ActivityBasicInfo basicInfo = ActivityNewConverter.INSTANCE.dtoToInfo(basicInfoDTO);
        basicInfo.setUpdaterId(adminId);
        activityBasicInfoMapper.updateByPrimaryKeySelective(basicInfo);

        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> delete(Long basicInfoId) {
        Integer adminId = getAdminId();
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //生效中不能删除
        boolean flag =
                basicInfo.getIsPermanent() == 1 || (new Date().after(basicInfo.getStartTime())
                        && new Date().before(basicInfo.getEndTime()));
        //在活动时间范围内，且开启状态
        if (flag && basicInfo.getStatus() == 1) {
            throw new BizException("活动生效中，不能关闭");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        //软删除
        ActivityBasicInfo updateBasicInfo = new ActivityBasicInfo();
        updateBasicInfo.setId(basicInfoId);
        updateBasicInfo.setUpdaterId(adminId);
        updateBasicInfo.setDelFlag(1);
        activityBasicInfoMapper.updateByPrimaryKeySelective(updateBasicInfo);
        //要将活动商品、活动范围也软删除;需要，因为会单个删除商品或者范围
        activityScopeConfigMapper.updateDelFlag(basicInfoId, adminId);
        ActivityItemConfig itemConfig = new ActivityItemConfig();
        itemConfig.setBasicInfoId(basicInfoId);
        itemConfig.setDelFlag(1);
        activityItemConfigMapper.updateDelFlag(itemConfig);
        activitySkuDetailMapper.updateDelFlag(itemConfig.getId(), null);
        //活动价格直接物理删除
        activitySkuPriceMapper.deleteByBasicInfoId(basicInfoId);
        log.info("营销活动:{}删除成功,操作人id:{}", basicInfoId, adminId);
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addScopeConfig(ActivityScopeConfigDTO scopeConfigDTO) {
        Integer adminId = getAdminId();
        Long basicInfoId = scopeConfigDTO.getBasicInfoId();
        List<Long> scopeIds = scopeConfigDTO.getScopeIds();
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper.selectByInfoId(
                basicInfoId, null);
        Integer scopeType = scopeConfigs.get(0).getScopeType();
        if (!Objects.equals(scopeType, scopeConfigDTO.getScopeType())) {
            throw new BizException("活动范围类型必须保持一致");
        }
        ActivityScopeConfig activityScopeConfig = activityScopeConfigMapper.selectByScopeId(
                basicInfoId, scopeIds.get(0));
        if (activityScopeConfig != null) {
            throw new BizException("活动范围已存在，请勿重复添加");
        }
        ActivityBasicInfoDTO basicInfoDTO = ActivityNewConverter.INSTANCE.infoToDto(basicInfo);

        ActivityNewDTO activityNewDTO = new ActivityNewDTO();
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);
        //改基础信息，需要校验时间重复问题
        //构建活动范围
        activityNewServiceHelper.buildScopeConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        activityNewDTO.getScopeConfigDTO().getScopeIds().addAll(scopeIds);

        //构建商品信息
        activityNewServiceHelper.buildItemConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        //校验因为添加导致的活动范围重复
        activityNewServiceHelper.check(activityNewDTO);

        ActivityScopeConfig scopeConfig = new ActivityScopeConfig();
        scopeConfig.setScopeId(scopeIds.get(0));
        scopeConfig.setScopeType(scopeType);
        scopeConfig.setBasicInfoId(basicInfoId);
        scopeConfig.setUpdaterId(getAdminId());
        activityScopeConfigMapper.insertSelective(scopeConfig);
        log.info("营销活动:{}新增活动范围:{}成功,操作人id:{}", basicInfoId, scopeIds, adminId);

        //还需要将新增的活动范围的价格处理
        //异步计算活动价并保存，后面可以改成MQ消息的方式处理
        //重新设置成待添加的
        activityNewDTO.setScopeConfigDTO(scopeConfigDTO);
        activityNewDTO.setAdminId(adminId);

        final Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        EXECUTOR_SERVICE.execute(() -> {
            MDC.setContextMap(copyOfContextMap);
            activityNewServiceHelper.dealActivitySkuPrice(activityNewDTO, null);
        });
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult deleteScopeConfig(ActivityScopeConfigDTO scopeConfigDTO) {
        Long basicInfoId = scopeConfigDTO.getBasicInfoId();
        Long scopeId = scopeConfigDTO.getScopeIds().get(0);
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动id:" + basicInfoId + "活动不存在");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        int total = activityScopeConfigMapper.countByBasicInfoId(basicInfoId);
        if (total <= 1) {
            throw new BizException("不支持删除，活动范围至少保留一条");
        }
        ActivityScopeConfig scopeConfig = activityScopeConfigMapper.selectByScopeId(basicInfoId,
                scopeId);
        if (scopeConfig == null) {
            throw new BizException("活动id:" + basicInfoId + "活动范围不存在");
        }
        //校验因为删除导致的活动范围重复，先将删除后剩余的活动范围查出来，然后做对比

        ActivityBasicInfoDTO basicInfoDTO = ActivityNewConverter.INSTANCE.infoToDto(basicInfo);

        ActivityNewDTO activityNewDTO = new ActivityNewDTO();
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);
        //改基础信息，需要校验时间重复问题
        //构建活动范围
        activityNewServiceHelper.buildScopeConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        activityNewDTO.getScopeConfigDTO().getScopeIds().remove(scopeId);

        //构建商品信息
        activityNewServiceHelper.buildItemConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        activityNewServiceHelper.check(activityNewDTO);

        scopeConfig.setUpdaterId(getAdminId());
        scopeConfig.setDelFlag(1);
        activityScopeConfigMapper.updateByPrimaryKeySelective(scopeConfig);

        if (Objects.equals(scopeConfig.getScopeType(), ScopeTypeEnum.AREA.getCode())) {
            //需要将对应的sku价格都删除
            activitySkuPriceMapper.deleteSkuByInfoId(basicInfoId, null,
                    Lists.newArrayList(scopeId.intValue()));
        }
        if (Objects.equals(scopeConfig.getScopeType(), ScopeTypeEnum.LARGE_AREA.getCode())) {
            //需要将对应的sku价格都删除
            List<Integer> areaNos = areaMapper.selectByLargeAreaNos(
                    Lists.newArrayList(scopeId.intValue()));
            if (CollectionUtil.isNotEmpty(areaNos)) {
                activitySkuPriceMapper.deleteSkuByInfoId(basicInfoId, null, areaNos);
            }
        }
        log.info("营销活动:{}删除活动范围scopeId:{}成功,操作人id:{}", basicInfoId, scopeId, getAdminId());

        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(key = "ActivityNewService.updateItem:{itemConfigDTO.basicInfoId}")
    public CommonResult updateItem(ActivityItemConfigDTO itemConfigDTO) {
        Integer adminId = getAdminId();
        Long basicInfoId = itemConfigDTO.getBasicInfoId();
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //临保活动不支持创建、修改
        /*if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }*/

        //需要更新对应商品的活动价
        ActivityBasicInfoDTO basicInfoDTO = ActivityNewConverter.INSTANCE.infoToDto(basicInfo);
        ActivityNewDTO activityNewDTO = new ActivityNewDTO();
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);
        //改基础信息，需要校验时间重复问题
        //构建活动范围
        activityNewServiceHelper.buildScopeConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        List<Long> scopeIds = activityNewDTO.getScopeConfigDTO().getScopeIds();
        if (CollectionUtil.isEmpty(scopeIds)) {
            throw new BizException("当前活动缺少活动范围，请添加活动范围");
        }
        //构建商品信息
        activityNewServiceHelper.buildItemConfig(basicInfoDTO.getBasicInfoId(), activityNewDTO,
                false);
        ActivityItemConfigDTO oldItemConfigDTO = activityNewDTO.getItemConfigDTO();
        //校验因为添加导致的活动范围重复
        activityNewServiceHelper.check(activityNewDTO);

        Long itemConfigId = oldItemConfigDTO.getId();
        ActivitySkuDetail activitySkuDetail = new ActivitySkuDetail();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), oldItemConfigDTO.getGoodSelectWay())) {
            ActivitySkuDetailDTO skuDetailDTO = itemConfigDTO.getSkuDetailList().get(0);
            itemConfigDTO.setGoodSelectWay(oldItemConfigDTO.getGoodSelectWay());
            activitySkuDetail = ActivityNewConverter.INSTANCE.dtoToSkuDetail(skuDetailDTO);
            activitySkuDetail.setItemConfigId(itemConfigId);
            if (activitySkuDetail.getActualQuantity() == null) {
                activitySkuDetail.setActualQuantity(DEFAULT_ACTUAL_QUANTITY);
            }
            if (activitySkuDetail.getDiscountLabel() == null) {
                activitySkuDetail.setDiscountLabel(CommonStatus.YES.getCode());
            }
            //不管有没有先删除数据，再添加
            //activitySkuDetailMapper.deleteSkuByConfigId(itemConfigId, skuDetailDTO.getSku());
            //删除价格信息
            activitySkuPriceMapper.deleteSkuByInfoId(basicInfoId, skuDetailDTO.getSku(), null);

            //重新查询数据
            ActivitySkuDetail skuDetail = activitySkuDetailMapper.selectBySku(itemConfigId, skuDetailDTO.getSku());
            if (skuDetail == null) {
                activitySkuDetailMapper.insertSelective(activitySkuDetail);
            } else {
                activitySkuDetail.setId(skuDetail.getId());
                activitySkuDetailMapper.updateByPrimaryKeySelective(activitySkuDetail);
            }
            log.info("营销活动id:{},活动sku:{}信息配置变更", basicInfoId, skuDetailDTO.getSku());
        } else {
            //类目或者标签处理

        }

        if (Objects.equals(
                ScopeTypeEnum.MERCHANT_POOL.getCode(),
                activityNewDTO.getScopeConfigDTO().getScopeType())) {
            log.info("人群包活动basicInfoId:{}不需要计算活动价格", basicInfoId);
            return CommonResult.ok(activitySkuDetail.getId());
        }
        //需要增加对应商品的活动价
        basicInfoDTO.setBasicInfoId(basicInfoId);
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);
        //重新设置为变更的商品
        activityNewDTO.setItemConfigDTO(itemConfigDTO);
        activityNewDTO.setAdminId(adminId);
        final List<ActivitySkuDetail> skuDetailListFinal = Lists.newArrayList(activitySkuDetail);
        final Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        EXECUTOR_SERVICE.execute(() ->{
            MDC.setContextMap(copyOfContextMap);
            activityNewServiceHelper.dealActivitySkuPrice(activityNewDTO, skuDetailListFinal);});
        return CommonResult.ok(activitySkuDetail.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult deleteItem(Long basicInfoId, String sku) {
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        ActivityItemConfig itemConfig = activityItemConfigMapper.getByInfoId(basicInfoId);
        Long itemConfigId = itemConfig.getId();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfig.getGoodSelectWay())) {
            int total = activitySkuDetailMapper.countByItemConfig(itemConfigId);
            if (total <= 1) {
                throw new BizException("不支持删除，活动sku至少保留一条");
            }
            int count = activitySkuDetailMapper.updateDelFlag(itemConfigId, sku);
            if (count < 1) {
                throw new BizException("活动sku:" + sku + "不存在");
            }
            //需要联动删除价格信息
            activitySkuPriceMapper.deleteSkuByInfoId(basicInfoId, sku, null);
        } else {
            //类目或者标签处理

        }
        log.info("营销活动:{}删除活动sku:{}成功,操作人id:{}", basicInfoId, sku, getAdminId());
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    public CommonResult<ActivityNewDTO> getDetail(Long basicInfoId) {
        ActivityNewDTO activityNewDTO = new ActivityNewDTO();
        //校验活动id
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //构建基础信息
        ActivityBasicInfoDTO basicInfoDTO = ActivityNewConverter.INSTANCE.infoToDto(basicInfo);
        ActivitySceneConfig sceneConfig = activitySceneConfigMapper.selectByInfoId(basicInfoId);
        basicInfoDTO.setPlace(sceneConfig.getPlace());
        basicInfoDTO.setPlatform(sceneConfig.getPlatform());
        ActivityStatusEnum activityStatusEnum = activityNewServiceHelper.checkActivityStatus(
                basicInfo.getStatus(),
                basicInfo.getStartTime(), basicInfo.getEndTime(), basicInfo.getIsPermanent());
        basicInfoDTO.setActivityStatus(activityStatusEnum.getCode());
        activityNewDTO.setBasicInfoDTO(basicInfoDTO);

        //构建活动范围
        activityNewServiceHelper.buildScopeConfig(basicInfoId, activityNewDTO, true);

        //构建商品信息
        activityNewServiceHelper.buildItemConfig(basicInfoId, activityNewDTO, true);

        return CommonResult.ok(activityNewDTO);
    }

    @Override
    public CommonResult<Boolean> openDown(Long basicInfoId, Integer status) {
        Integer adminId = getAdminId();
        ActivityBasicInfo basicInfo = activityBasicInfoMapper.selectByPrimaryKey(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        ActivityBasicInfo updateBasicInfo = new ActivityBasicInfo();
        updateBasicInfo.setId(basicInfoId);
        updateBasicInfo.setUpdaterId(adminId);
        updateBasicInfo.setStatus(status);
        activityBasicInfoMapper.updateByPrimaryKeySelective(updateBasicInfo);
        log.info("营销活动:{}变更开关状态:{}成功,操作人id:{}", basicInfoId, status, adminId);
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    public CommonResult<PageInfo<ActivityPageRespDTO>> page(ActivityPageQueryDTO pageQueryDTO) {
        // if (pageQueryDTO.getSku() != null && (pageQueryDTO.getLargeAreaNo() != null
        //         || pageQueryDTO.getMerchantPoolId() != null)) {
        //     throw new BizException("sku和活动范围只能2选1筛选");
        // }
        //目前sku 和活动范围只能2选1查询，否则会慢sql，如需优化，需要引入es
        ActivityBasicQueryDTO basicQueryDTO = new ActivityBasicQueryDTO();
        basicQueryDTO.setType(pageQueryDTO.getType());
        basicQueryDTO.setId(pageQueryDTO.getId());
        basicQueryDTO.setActivityStatus(pageQueryDTO.getActivityStatus());
        basicQueryDTO.setCreatorId(pageQueryDTO.getCreatorId());
        basicQueryDTO.setName(pageQueryDTO.getName());
        basicQueryDTO.setSku(pageQueryDTO.getSku());
        List<ActivityScopeQueryDTO> scopeList = Lists.newArrayList();
        if (pageQueryDTO.getMerchantPoolId() != null) {
            scopeList.add(new ActivityScopeQueryDTO(pageQueryDTO.getMerchantPoolId(),
                    ScopeTypeEnum.MERCHANT_POOL.getCode()));
        }
        if (pageQueryDTO.getLargeAreaNo() != null) {
            //需要查询大区和大区下运营城市的活动
            scopeList.add(new ActivityScopeQueryDTO(Long.valueOf(pageQueryDTO.getLargeAreaNo()),
                    ScopeTypeEnum.LARGE_AREA.getCode()));
            //查询大区下生效的运营城市
            List<Integer> areaNos = areaMapper.selectByLargeAreaNos(
                    Lists.newArrayList(pageQueryDTO.getLargeAreaNo()));
            areaNos.forEach(x -> {
                ActivityScopeQueryDTO queryDTO = new ActivityScopeQueryDTO(x.longValue(),
                        ScopeTypeEnum.AREA.getCode());
                scopeList.add(queryDTO);
            });
        }
        basicQueryDTO.setScopeList(scopeList);
        PageInfo<ActivityPageRespDTO> pageInfo = PageInfoHelper.createPageInfo(
                pageQueryDTO.getPageIndex(), pageQueryDTO.getPageSize(), () -> {
                    List<ActivityPageRespDTO> activityBasicInfos = activityBasicInfoMapper.listByQuery(
                            basicQueryDTO);
                    return activityBasicInfos;
                });
        if (CollectionUtil.isEmpty(pageInfo.getList())) {
            return CommonResult.ok(new PageInfo<>());
        }
        pageInfo.getList().forEach(x -> {
            activityNewServiceHelper.buildPageRespDTO(x);
        });

        return CommonResult.ok(pageInfo);
    }

    @Override
    public CommonResult<List<LargeAreaSkuPriceDTO>> listSkuPrice(Long basicInfoId, String sku) {
        List<ActivityScopeConfig> scopeConfigs = activityNewServiceHelper.checkAndGetScopes(
                basicInfoId, sku);
        Integer scopeType = scopeConfigs.get(0).getScopeType();
        if (Objects.equals(scopeType, ScopeTypeEnum.MERCHANT_POOL.getCode())) {
            log.warn("活动范围为人群包，无法获取价格信息");
            return CommonResult.ok();
        }
        List<LargeAreaSkuPriceDTO> list = Lists.newArrayList();
        List<Integer> scopeIds = scopeConfigs.stream().map(x -> x.getScopeId().intValue())
                .collect(Collectors.toList());
        List<AreaInfoDTO> areaInfoDTOS = Lists.newArrayList();
        if (Objects.equals(scopeType, ScopeTypeEnum.AREA.getCode())) {
            areaInfoDTOS = areaMapper.listByAreaNos(scopeIds);
        }
        //大区也先转换成运营城市去处理，最终再匹配城市：大区对应关系
        if (Objects.equals(scopeType, ScopeTypeEnum.LARGE_AREA.getCode())) {
            areaInfoDTOS = areaMapper.listAreaByLargeAreaNos(scopeIds);
        }
        if (CollectionUtil.isEmpty(areaInfoDTOS)) {
            return CommonResult.ok(list);
        }
        Map<Integer, AreaInfoDTO> areaNoMap = areaInfoDTOS.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), Function.identity(), (p1, p2) -> p2));
        Set<Integer> areaNos = areaNoMap.keySet();
        Map<Integer, Integer> areaWarehouseMap = activityNewServiceHelper.buildAreaWarehouseMap(
                sku, areaNos);
        //根据运营城市去获取成本价、原价信息
        List<ActivitySkuPrice> activitySkuPrices = activitySkuPriceMapper.selectBySkuAndBasicInfoId(
                basicInfoId, sku);
        List<AreaSku> areaSkus = areaSkuMapper.selectSkuAreaPrice(sku, areaNos);
        Map<Integer, BigDecimal> areaSkuPriceMap = areaSkus.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), x -> x.getPrice(), (p1, p2) -> p2));
        Map<Integer, List<ActivityLadderPriceDTO>> activityPriceMap = activitySkuPrices.stream()
                .collect(Collectors.toMap(ActivitySkuPrice::getAreaNo, x -> JSON.parseArray(x.getLadderPrice(), ActivityLadderPriceDTO.class)));
        List<ActivitySkuPriceDTO> skuPriceList = Lists.newArrayList();
        Map<Integer, BigDecimal> costPriceMap = new HashMap<>(16);
        for (Integer areaNo : areaNos) {
            Integer warehouseNo = areaWarehouseMap.get(areaNo);
            BigDecimal salePrice = areaSkuPriceMap.get(areaNo);
            if (warehouseNo == null || salePrice == null) {
                continue;
            }
            ActivitySkuPriceDTO skuPriceDTO = new ActivitySkuPriceDTO();
            AreaInfoDTO areaInfoDTO = areaNoMap.get(areaNo);
            skuPriceDTO.setLargeAreaName(areaInfoDTO.getLargeAreaName());
            skuPriceDTO.setAreaName(areaInfoDTO.getAreaName());
            List<ActivityLadderPriceDTO> ladderPriceDTOS = activityPriceMap.get(areaNo);
            skuPriceDTO.setActivityLadderPriceList(ladderPriceDTOS);
            skuPriceDTO.setSalePrice(salePrice);
            BigDecimal costPrice = costPriceMap.get(warehouseNo);
            if (costPrice != null) {
                skuPriceDTO.setCostPrice(costPrice);
            } else {
                costPrice = cycleInventoryCostService.selectCostByWarehouseNo(sku,
                        warehouseNo);
                costPriceMap.put(warehouseNo, costPrice);
                skuPriceDTO.setCostPrice(costPrice);
            }
            skuPriceList.add(skuPriceDTO);
        }
        Map<String, List<ActivitySkuPriceDTO>> listMap = skuPriceList.stream()
                .collect(Collectors.groupingBy(ActivitySkuPriceDTO::getLargeAreaName));

        for (Entry<String, List<ActivitySkuPriceDTO>> entry : listMap.entrySet()) {
            LargeAreaSkuPriceDTO largeAreaSkuPriceDTO = new LargeAreaSkuPriceDTO();
            largeAreaSkuPriceDTO.setLargeAreaName(entry.getKey());
            largeAreaSkuPriceDTO.setAreaPriceList(entry.getValue());
            list.add(largeAreaSkuPriceDTO);
        }

        return CommonResult.ok(list);
    }


    @Override
    public CommonResult<Boolean> isActivitySku(String sku, Integer areaNo) {
        try {
            List<ActivityItemScopeDTO> configs = activityNewServiceHelper.listActivityItemConfigs(
                    Lists.newArrayList(sku),
                    areaNo);
            if (CollectionUtil.isEmpty(configs)) {
                return CommonResult.ok(Boolean.FALSE);
            }
            List<Long> itemConfigIds = configs.stream().filter(x -> x.getItemConfigId() != null)
                    .map(x -> x.getItemConfigId()).collect(Collectors.toList());
            //查询是否有活动商品
            List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.listByItemConfigs(
                    itemConfigIds, sku);
            return CommonResult.ok(CollectionUtil.isNotEmpty(skuDetailList));
        } catch (Exception e) {
            log.error("查询是否活动sku异常,sku:{},areaNo:{},cause:{}", sku, areaNo, Throwables.getStackTraceAsString(e));
            return CommonResult.ok(Boolean.FALSE);
        }

    }

    @Override
    public Set<String> batchIsActivitySku(List<String> skus, Integer areaNo) {
        Set<String> res = new HashSet<>();

        List<ActivityItemScopeDTO> configs = activityNewServiceHelper.listActivityItemConfigs(
                Lists.newArrayList(skus), areaNo);
        if (CollectionUtil.isEmpty(configs)) {
            return res;
        }
        List<Long> itemConfigIds = configs.stream()
                .map(ActivityItemScopeDTO::getItemConfigId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //查询是否有活动商品
        List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.listByItemConfigsSkus(itemConfigIds, skus);

        res = skuDetailList.stream().map(ActivitySkuDetail::getSku).collect(Collectors.toSet());

        return res;
    }

    @Override
    public CommonResult<List<ActivityItemScopeDTO>> listActivityItemConfigs(String sku,
            List<Integer> areaNos) {
        List<ActivityScopeQueryDTO> scopeList = Lists.newArrayList();
        areaNos.forEach(x -> {
                    ActivityScopeQueryDTO queryDTO = new ActivityScopeQueryDTO(Long.valueOf(x),
                            ScopeTypeEnum.AREA.getCode());
                    scopeList.add(queryDTO);
                }
        );
        //查询运营城市生效中的临保或者特价活动
        List<Area> areas = areaMapper.selectAreaNos(areaNos);
        areas.stream().map(x -> Long.valueOf(x.getLargeAreaNo())).distinct().forEach(
                t -> {
                    ActivityScopeQueryDTO queryDTO = new ActivityScopeQueryDTO(t,
                            ScopeTypeEnum.LARGE_AREA.getCode());
                    scopeList.add(queryDTO);
                }
        );
        List<ActivityItemScopeDTO> configs = Lists.newArrayList();
        //特价活动查询
        List<ActivityItemScopeDTO> specialConfigs = activityBasicInfoMapper.listByScope(scopeList,
                ActivityTypeEnum.SPECIAL_PRICE.getCode(), ActivityStatusEnum.EFFECTING.getCode());
        configs.addAll(specialConfigs);

        Inventory inventory = new Inventory();
        if (sku != null) {
            inventory = inventoryMapper.queryBySku(sku);
        }
        //临保活动查询（非临保品可跳过）
        if (sku == null || (inventory != null && Objects.equals(inventory.getExtType(),
                InventoryExtTypeEnum.TEMPORARY_INSURANCE.type()))) {
            List<ActivityItemScopeDTO> extConfigs = activityBasicInfoMapper.listByScope(scopeList,
                    ActivityTypeEnum.NEAR_EXPIRED.getCode(),
                    ActivityStatusEnum.EFFECTING.getCode());
            configs.addAll(extConfigs);
        }

        return CommonResult.ok(configs);
    }

    @Override
    public CommonResult<ActivitySku> getActivitySku(String sku, Integer areaNo) {
        ActivitySku activitySku = buildActivitySku(sku, areaNo);
        return CommonResult.ok(activitySku);
    }

    private ActivitySku buildActivitySku(String sku, Integer areaNo) {
        List<ActivityItemScopeDTO> configs = activityNewServiceHelper.listActivityItemConfigs(
                Lists.newArrayList(sku),
                areaNo);
        if (CollectionUtil.isEmpty(configs)) {
            return null;
        }
        Map<Long, Long> itemConfigBasicMap = configs.stream()
                .collect(Collectors.toMap(x -> x.getItemConfigId(), y -> y.getBasicInfoId(), (p1, p2) -> p2));
        List<Long> itemConfigIds = Lists.newArrayList(itemConfigBasicMap.keySet());
        // 查询是否有活动商品
        List<ActivitySkuDetailValueObject> skuDetailList = activitySkuDetailMapper.listDetailByItemConfigs(
                itemConfigIds, sku);
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            ActivitySku activitySku = new ActivitySku();
            //获取到sku信息，包含价格
            //如果当前sku在运营城市有多个特价活动，取最晚创建活动的活动价, 先按时间、再按id（这里需要优化成按优先级排序）
            List<ActivitySkuDetailValueObject> sortDetailList = skuDetailList.stream().sorted(Comparator.comparing(ActivitySkuDetailValueObject::getActivityCreateTime,Comparator.reverseOrder()).thenComparing(ActivitySkuDetailValueObject::getBasicInfoId, Comparator.reverseOrder())).collect(Collectors.toList());

            ActivitySkuDetailValueObject skuDetail = sortDetailList.get(0);
            ActivitySkuPrice skuPrice = activitySkuPriceMapper.selectByDetailId(
                    skuDetail.getId(), sku, areaNo);
            //在创建活动后新增了运营区域，如果特价活动配置的是大区可能导致新增的活动区域没有活动价，需要当做不是活动商品处理
            if (skuPrice == null) {
                return null;
            }
            // 默认的活动价根据最小起售量计算
            int unit = inventoryService.selectMinSaleUnit(sku);
            BigDecimal activityPrice = skuPrice.getLadderPrice() == null ? skuPrice.getActivityPrice() : priceStrategyService.selectLadderPriceByUnit(JSON.parseArray(skuPrice.getLadderPrice(), ActivityLadderPriceDTO.class), unit, skuPrice.getSalePrice());
            activitySku.setActivityId(itemConfigBasicMap.get(skuDetail.getItemConfigId()));
            activitySku.setSku(skuDetail.getSku());
            activitySku.setSalePrice(skuPrice.getSalePrice());
            activitySku.setLadderPrice(skuPrice.getLadderPrice());
            activitySku.setActivityPrice(activityPrice);
            return activitySku;
        }
        return null;
    }

    @Override
    public CommonResult<List<ActivitySku>> listActivitySku(List<String> skus, Integer areaNo) {
        List<ActivitySku> list = Lists.newArrayList();
        for (String sku : skus) {
            ActivitySku activitySku = buildActivitySku(sku, areaNo);
            if (activitySku != null) {
                list.add(activitySku);
            }
        }
        return CommonResult.ok(list);
    }

    @Override
    public CommonResult<List<String>> listActivitySkuByAreaNos(List<Integer> areaNos) {
        CommonResult<List<ActivityItemScopeDTO>> commonResult = listActivityItemConfigs(null,
                areaNos);
        if (CollectionUtil.isEmpty(commonResult.getData())) {
            return CommonResult.ok(Lists.newArrayList());
        }
        List<Long> itemConfigIds = commonResult.getData().stream().map(x -> x.getItemConfigId())
                .distinct().collect(Collectors.toList());
        //获取所有活动中的sku
        List<String> skus = activitySkuDetailMapper.listSkuByItemConfigIdStrings(itemConfigIds);
        return CommonResult.ok(skus);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> updateActivitySkuPrice(String sku, Integer areaNo, BigDecimal price) {
        List<ActivitySkuDetail> skuDetails = activityNewServiceHelper.listValidActivitySkuDetails(
                sku, areaNo);
        if (CollectionUtil.isNotEmpty(skuDetails)) {
            //单条更新
            for (ActivitySkuDetail skuDetail : skuDetails) {
                AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
                if (areaSku == null) {
                    log.warn("营销活动商品sku:{} 在运营区域:{} 下不存在", sku, areaNo);
                    continue;
                }
                ActivitySkuPrice activitySkuPrice = activitySkuPriceMapper.selectByDetailId(
                        skuDetail.getId(), sku, areaNo);
                if (activitySkuPrice == null) {
                    log.warn("营销活动价不存在,skuDetail:{}", skuDetail);
                    continue;
                }
                //成本价查询,可以优化成库存仓去查成本
                BigDecimal costPrice = cycleInventoryCostService.selectCostByAreaNo(sku,
                        areaNo);

                String ladderConfig = skuDetail.getLadderConfig();
                if (ladderConfig != null) {
                    log.info("阶梯活动已配置，开始处理营销活动,sku={},areaNo:{}", sku, areaNo);
                    // 这里是售价联动。如果阶梯价的数据还没有初始化、那么就处理老的活动价
                    List<ActivityLadderConfigDTO> ladderConfigDTOS = JSON.parseArray(ladderConfig, ActivityLadderConfigDTO.class);
                    if (CollectionUtil.isEmpty(ladderConfigDTOS)) {
                        log.warn("活动id:{},sku活动详情还未配置!商品sku:{} ,运营区域:{} ", activitySkuPrice.getBasicInfoId(), sku, JSON.toJSONString(areaNo));
                        continue;
                    }
                    PriceStrategyAuditRecordVO vo = null;
                    List<ActivityLadderPriceDTO> activityLadderPriceDTOList = new ArrayList<>();
                    for (ActivityLadderConfigDTO dto : ladderConfigDTOS) {
                        PriceStrategy strategy = new PriceStrategy();
                        strategy.setAdjustType(dto.getAdjustType());
                        strategy.setAmount(dto.getAmount());
                        strategy.setRoundingMode(dto.getRoundingMode());
                        vo = priceStrategyService.calcStrategyPrice(
                                strategy,
                                costPrice, price);
                        if (vo == null || vo.getNewPrice() == null) {
                            log.error("活动价计算异常,sku={},areaNo:{}, price:{}", sku, areaNo, JSON.toJSONString(vo));
                            continue;
                        }

                        ActivityLadderPriceDTO priceDTO = new ActivityLadderPriceDTO();
                        priceDTO.setUnit(dto.getUnit());
                        priceDTO.setPrice(vo.getNewPrice());
                        activityLadderPriceDTOList.add(priceDTO);
                    }
                    // 兼容处理mall还未发布的特价联动
                    if(isOldActivity(ladderConfigDTOS) && skuDetail.getAdjustType() != null && skuDetail.getAmount() != null && skuDetail.getRoundingMode() != null) {
                        log.info("开始联动处理老的营销活动,sku={},areaNo:{}", sku, areaNo);
                        PriceStrategy strategy = new PriceStrategy();
                        strategy.setAdjustType(skuDetail.getAdjustType());
                        strategy.setAmount(skuDetail.getAmount());
                        strategy.setRoundingMode(skuDetail.getRoundingMode());
                        PriceStrategyAuditRecordVO oldVo = priceStrategyService.calcStrategyPrice(strategy,
                                costPrice, price);
                        if (oldVo == null) {
                            log.error("老营销活动价计算异常,sku={},areaNo:{}", sku, areaNo);
                            continue;
                        }
                        activitySkuPrice.setActivityPrice(oldVo.getNewPrice());
                    }
                    activitySkuPrice.setSalePrice(price);
                    activitySkuPrice.setLadderPrice(JSON.toJSONString(activityLadderPriceDTOList));
                    activitySkuPrice.setUpdaterId(0);
                    activitySkuPriceMapper.updatePriceSelective(activitySkuPrice);
                } else {
                    log.info("阶梯活动还未配置，处理老的营销活动,sku={},areaNo:{}", sku, areaNo);
                    PriceStrategy strategy = new PriceStrategy();
                    strategy.setAdjustType(skuDetail.getAdjustType());
                    strategy.setAmount(skuDetail.getAmount());
                    strategy.setRoundingMode(skuDetail.getRoundingMode());
                    PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy,
                            costPrice, price);
                    if (vo == null) {
                        log.error("营销活动价计算异常,sku={},areaNo:{}", sku, areaNo);
                        continue;
                    }
                    activitySkuPrice.setSalePrice(price);
                    activitySkuPrice.setActivityPrice(vo.getNewPrice());
                    activitySkuPrice.setUpdaterId(0);
                    activitySkuPriceMapper.updatePriceSelective(activitySkuPrice);
                }
            }

            log.info("营销活动价更新成功, sku:{}, areaNo:{}", sku, areaNo);
            return CommonResult.ok(Boolean.TRUE);
        }
        return CommonResult.ok(Boolean.FALSE);
    }

    private boolean isOldActivity(List<ActivityLadderConfigDTO> ladderConfigDTOS){
        if(CollUtil.isEmpty(ladderConfigDTOS)){
            return false;
        }
        return ladderConfigDTOS.size() == 1 && Objects.equals(ladderConfigDTOS.get(0).getUnit(), 1);
    }

    @Override
    public CommonResult<ActivitySkuBatchDTO> batchAddSku(MultipartFile multipartFile) {
        ActivitySkuBatchDTO skuBatchDTO = activityNewServiceHelper.dealWithExcel(multipartFile);
        return CommonResult.ok(skuBatchDTO);
    }

    @Override
    public Boolean automaticPrice(ActivityItemConfigReq activityItemConfigReq) {
        ActivitySkuDetail activitySkuDetail = activitySkuDetailMapper.selectByPrimaryKey(activityItemConfigReq.getId());
        if (Objects.isNull(activitySkuDetail)) {
            throw new BizException("当前数据不存在！");
        }
        activitySkuDetail.setAutoPrice(activityItemConfigReq.getAutoPrice());
        int update = activitySkuDetailMapper.updateByPrimaryKeySelective(activitySkuDetail);
        return update > 0;
    }

    @Override
    public void downloadBlackList(HttpServletResponse response) {
        Map<String, List<List<String>>> data = new HashMap<>();
        List<String> rowData = new ArrayList<>();
        rowData.add("库存仓");
        rowData.add("skuid");
        List<List<String>> sheetData = new ArrayList<>();
        sheetData.add(rowData);
        data.put("临保风险品黑名单", sheetData);
        try {
            ExcelUtils.outputExcel(data, "临保风险品黑名单.xls", response);
        } catch (IOException e) {
            throw new BizException("下载模版失败！");
        }
    }

    @Override
    public void downloadWhiteList(HttpServletResponse response) {
        Map<String, List<List<String>>> data = new HashMap<>();
        List<String> rowData = new ArrayList<>();
        rowData.add("城市");
        rowData.add("skuid");
        rowData.add("排序值");
        List<List<String>> sheetData = new ArrayList<>();
        sheetData.add(rowData);
        data.put("特价频道商品排序", sheetData);
        try {
            ExcelUtils.outputExcel(data, "特价频道商品排序.xls", response);
        } catch (IOException e) {
            throw new BizException("下载模版失败！");
        }
    }

    @Override
    public PageInfo<ActivityBlackAndWhiteListDTO> blackAndWhiteList(ActivityBlackAndWhiteListPageQuery activityBlackListPageQuery) {
        PageInfo<ActivityBlackAndWhiteListDTO> pageInfo = PageInfoHelper.createPageInfo(
                activityBlackListPageQuery.getPageIndex(), activityBlackListPageQuery.getPageSize(), () -> {
                    List<ActivityBlackAndWhiteListDTO> activityBlackAndWhiteListDTOS = activityBlackAndWhiteListMapper.page(activityBlackListPageQuery);
                    if (CollectionUtil.isEmpty(activityBlackAndWhiteListDTOS)) {
                        return Collections.emptyList();
                    }

                    //组装商品信息数据,批量查询商品信息
                    List<String> skus = activityBlackAndWhiteListDTOS.stream().map(x -> x.getSku())
                            .collect(Collectors.toList());
                    List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(
                            skus);
                    if (CollectionUtil.isEmpty(skuBaseInfoDTOS)) {
                        return activityBlackAndWhiteListDTOS;
                    }
                    Map<String, SkuBaseInfoDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                            .collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (p1, p2) -> p2));
                    activityBlackAndWhiteListDTOS.stream().forEach(e -> {
                        SkuBaseInfoDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(e.getSku());
                        if (Objects.nonNull(skuBaseInfoDTO)) {
                            e.setSkuName(skuBaseInfoDTO.getPdName());
                        }
                    });
                    return activityBlackAndWhiteListDTOS;
                });
        return pageInfo;
    }

    @Override
    public Boolean batchDeleteWhiteAndBlackList(ActivityBlackAndWhiteListReq activityBlackListReq) {
        if (Objects.isNull(activityBlackListReq) || CollectionUtils.isEmpty(activityBlackListReq.getIds())) {
            throw new BizException("ID集合不能为空！");
        }
        int delete = activityBlackAndWhiteListMapper.batchDelete(activityBlackListReq.getIds());
        return delete > 0;
    }

    @Override
    public Map<String, Object> uploadWhiteAndBlackList(ActivityBlackAndWhiteListUploadReq uploadReq) {
        //记录错误数据类型
        Map<String, Object> result = new HashMap<>();

        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(uploadReq.getKey());
        if (Objects.equals(uploadReq.getType(), BlackAndWhiteTypeEnum.BLACK.getCode())) {
            List<String> failList = new ArrayList<>();
            List<ActivityBlackImportDTO> activityBlackImportDTOS;
            try {
                activityBlackImportDTOS = EasyExcel.read(inputStream,
                        ActivityBlackImportDTO.class, null).doReadAllSync();
            } catch (Exception e) {
                throw new BizException("上传临保风险品黑名单解析异常！");
            }

            //校验集合
            if (CollectionUtil.isEmpty(activityBlackImportDTOS)) {
                throw new BizException("上传临保风险品黑名单不能为空！");
            }

            ActivityBlackAndWhiteList activityBlackAndWhiteList;
            WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq;
            for (int i = 0; i < activityBlackImportDTOS.size(); i++) {
                ActivityBlackImportDTO activityBlackImportDTO = activityBlackImportDTOS.get(i);
                if (i >= 2500) {
                    failList.add("单次新增最大仅支持2500条,剩余部分请重新上传(第"+ i + "条skuId:" + activityBlackImportDTO.getSku() + ",仓库编号:" + activityBlackImportDTO.getWarehouseNo() + ")");
                    continue;
                }

                if (Objects.isNull(activityBlackImportDTO.getWarehouseNo())) {
                    failList.add("skuId:" + activityBlackImportDTO.getSku() + ",不填库存仓");
                    continue;
                }
                if (Objects.isNull(activityBlackImportDTO.getSku())) {
                    failList.add("不填skuId,库存仓:" + activityBlackImportDTO.getWarehouseNo());
                    continue;
                }

                //校验库存仓是否存在
                warehouseBaseInfoByNoReq = new WarehouseBaseInfoByNoReq();
                warehouseBaseInfoByNoReq.setWarehouseNos(Collections.singletonList(activityBlackImportDTO.getWarehouseNo()));
                List<WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoResps = warehouseStorageQueryFacade.queryBaseInfoByWarehouseNo(warehouseBaseInfoByNoReq);
                if (CollectionUtils.isEmpty(warehouseBaseInfoByNoResps)) {
                    failList.add("skuId:" + activityBlackImportDTO.getSku() + ",不存在库存仓:" + activityBlackImportDTO.getWarehouseNo());
                    continue;
                }

                //校验sku是否存在
                Inventory inventory = inventoryMapper.selectOneBySku(activityBlackImportDTO.getSku());
                if (Objects.isNull(inventory)) {
                    failList.add("不存在的skuId:" + activityBlackImportDTO.getSku() + ",库存仓:" + activityBlackImportDTO.getWarehouseNo());
                    continue;
                }

                //校验是否已经存在重复数据
                activityBlackAndWhiteList = new ActivityBlackAndWhiteList();
                activityBlackAndWhiteList.setSku(activityBlackImportDTO.getSku());
                activityBlackAndWhiteList.setWarehouseNo(activityBlackImportDTO.getWarehouseNo());
                activityBlackAndWhiteList.setWarehouseName(warehouseBaseInfoByNoResps.get(0).getWarehouseName());
                int repeat = activityBlackAndWhiteListMapper.checkRepeat(activityBlackAndWhiteList);
                if (repeat > 0) {
                    failList.add("skuId:" + activityBlackAndWhiteList.getSku() + "、仓库名称:" + activityBlackAndWhiteList.getWarehouseName()
                            + "(" + activityBlackAndWhiteList.getWarehouseNo() + ") 已存在");
                    continue;
                }
                activityBlackAndWhiteList.setType(uploadReq.getType());
                activityBlackAndWhiteList.setCreator(getAdminName());
                activityBlackAndWhiteListMapper.insertSelective(activityBlackAndWhiteList);
            }
            result.put("failList", failList);
        } else if (Objects.equals(uploadReq.getType(), BlackAndWhiteTypeEnum.WHITE.getCode())) {
            List<String> failList = new ArrayList<>();
            List<ActivityWhiteImportDTO> activityBlackImportDTOS;
            try {
                activityBlackImportDTOS = EasyExcel.read(inputStream,
                        ActivityWhiteImportDTO.class, null).doReadAllSync();
            } catch (Exception e) {
                throw new BizException("特价频道商品排序解析异常！");
            }
            if (CollectionUtil.isEmpty(activityBlackImportDTOS)) {
                throw new BizException("上传特价频道商品排序数据不能为空！");
            }

            ActivityBlackAndWhiteList activityBlackAndWhiteList;
            for (int i = 0; i < activityBlackImportDTOS.size(); i++) {
                ActivityWhiteImportDTO activityWhiteImportDTO = activityBlackImportDTOS.get(i);
                if (i >= 2500) {
                    failList.add("单次新增最大仅支持2500条，剩余部分请重新上传(第"+ i + "条skuId:" + activityWhiteImportDTO.getSku() + ",城市:"
                            + activityWhiteImportDTO.getAreaNo() + ",排序值:" + activityWhiteImportDTO.getSort() + ")");
                    continue;
                }

                if (Objects.isNull(activityWhiteImportDTO.getSku())) {
                    if (Objects.isNull(activityWhiteImportDTO.getAreaNo())) {
                        failList.add("不填skuId,不填城市,排序值:" + activityWhiteImportDTO.getSort());
                        continue;
                    } else if (Objects.isNull(activityWhiteImportDTO.getSort())) {
                        failList.add("不填skuId,城市:" + activityWhiteImportDTO.getAreaNo() + ",不填排序值");
                        continue;
                    }
                    failList.add("不填skuId,城市:" + activityWhiteImportDTO.getAreaNo() + ",排序值:" + activityWhiteImportDTO.getSort());
                    continue;
                }
                if (Objects.isNull(activityWhiteImportDTO.getAreaNo())) {
                    if (Objects.isNull(activityWhiteImportDTO.getSort())) {
                        failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",不填城市,不填排序值");
                        continue;
                    }
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",不填城市,排序值:" + activityWhiteImportDTO.getSort());
                    continue;
                }
                if (Objects.isNull(activityWhiteImportDTO.getSort())) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",城市:" + activityWhiteImportDTO.getAreaNo() + ",不填排序值");
                    continue;
                }

                //校验排序值是否1-100之间正整数
                String sort = activityWhiteImportDTO.getSort();
                if (!PATTERN.matcher(sort).matches()) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",城市:" + activityWhiteImportDTO.getAreaNo() + ",排序值:" + activityWhiteImportDTO.getSort() + " 格式不正确(仅支持1-100正整数)");
                    continue;
                }
                if(Integer.parseInt(sort) < 1 || Integer.parseInt(sort) > 100) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",城市:" + activityWhiteImportDTO.getAreaNo() + ",排序值:" + activityWhiteImportDTO.getSort()  + " 格式不正确(仅支持1-100正整数)");
                    continue;
                }

                //校验城市是否存在
                Area area = areaMapper.selectByAreaNo(activityWhiteImportDTO.getAreaNo());
                if (Objects.isNull(area)) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",城市:" + activityWhiteImportDTO.getAreaNo() + " 不存在,排序值:" + activityWhiteImportDTO.getSort());
                    continue;
                }

                //校验sku是否存在
                Inventory inventory = inventoryMapper.selectOneBySku(activityWhiteImportDTO.getSku());
                if (Objects.isNull(inventory)) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + " 不存在,城市:" + activityWhiteImportDTO.getAreaNo() + ",排序值:" + activityWhiteImportDTO.getSort());
                    continue;
                }

                //数据上限、每个城市最多支持上传10条数据
                ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
                andWhiteList.setAreaNo(activityWhiteImportDTO.getAreaNo());
                int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count >= 10) {
                    failList.add("城市:" + area.getAreaName() + "商品已超出10个,超出的部分不支持上传,请删除部分商品后重试.");
                    continue;
                }

                //校验是否已经存在重复数据
                activityBlackAndWhiteList = new ActivityBlackAndWhiteList();
                activityBlackAndWhiteList.setSku(activityWhiteImportDTO.getSku());
                activityBlackAndWhiteList.setAreaNo(activityWhiteImportDTO.getAreaNo());
                int repeat = activityBlackAndWhiteListMapper.checkRepeat(activityBlackAndWhiteList);
                if (repeat > 0) {
                    failList.add("skuId:" + activityWhiteImportDTO.getSku() + ",城市:" + area.getAreaName() + " 已存在");
                    continue;
                }

                //同一个城市排序值不能重复
                andWhiteList.setSort(Integer.valueOf(sort));
                count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count > 0) {
                    failList.add("城市:" + area.getAreaName() + ",排序值:" + andWhiteList.getSort() + " 已存在");
                    continue;
                }
                activityBlackAndWhiteList.setSort(Integer.valueOf(sort));
                activityBlackAndWhiteList.setAreaName(area.getAreaName());
                activityBlackAndWhiteList.setType(uploadReq.getType());
                activityBlackAndWhiteList.setCreator(getAdminName());
                activityBlackAndWhiteListMapper.insertSelective(activityBlackAndWhiteList);
            }
            result.put("failList", failList);
        } else {
            throw new BizException("名单类型有误！");
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityBlackAndWhiteListDTO insertWhiteAndBlackList(ActivityBlackAndWhiteListInsertReq activityWhiteListReq) {
        //校验sku是否存在
        Inventory inventory = inventoryMapper.selectOneBySku(activityWhiteListReq.getSku());
        if (Objects.isNull(inventory)) {
            throw new BizException("sku:" + activityWhiteListReq.getSku() + " 不存在");
        }

        ActivityBlackAndWhiteList activityBlackAndWhiteList = new ActivityBlackAndWhiteList();
        activityBlackAndWhiteList.setSku(activityWhiteListReq.getSku());
        activityBlackAndWhiteList.setType(activityWhiteListReq.getType());
        activityBlackAndWhiteList.setCreator(getAdminName());
        if (Objects.equals(activityWhiteListReq.getType(), BlackAndWhiteTypeEnum.BLACK.getCode())) {
            //校验参数
            if (Objects.isNull(activityWhiteListReq.getWarehouseNo())) {
                throw new BizException("新增临保风险品黑名单的库存仓编号不能为空！");
            }

            //校验库存仓是否存在
            WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq = new WarehouseBaseInfoByNoReq();
            warehouseBaseInfoByNoReq.setWarehouseNos(Collections.singletonList(activityWhiteListReq.getWarehouseNo()));
            List<WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoResps = warehouseStorageQueryFacade.queryBaseInfoByWarehouseNo(warehouseBaseInfoByNoReq);
            if (CollectionUtils.isEmpty(warehouseBaseInfoByNoResps)) {
                throw new BizException("库存仓:" + activityWhiteListReq.getWarehouseNo() + " 不存在");
            }

            //校验是否已经存在重复数据
            activityBlackAndWhiteList.setWarehouseNo(activityWhiteListReq.getWarehouseNo());
            activityBlackAndWhiteList.setWarehouseName(warehouseBaseInfoByNoResps.get(0).getWarehouseName());
            int count = activityBlackAndWhiteListMapper.checkRepeat(activityBlackAndWhiteList);
            if (count > 0) {
                throw new BizException("skuId:" + activityBlackAndWhiteList.getSku() + "、仓库名称:" + activityBlackAndWhiteList.getWarehouseName() + " 已存在");
            }
        } else if(Objects.equals(activityWhiteListReq.getType(), BlackAndWhiteTypeEnum.WHITE.getCode())) {
            //校验参数
            if (Objects.isNull(activityWhiteListReq.getAreaNo())) {
                throw new BizException("新增特价频道商品排序的城市编号不能为空！");
            }
            if (Objects.isNull(activityWhiteListReq.getSort())) {
                throw new BizException("新增特价频道商品排序的排序值不能为空！");
            }

            //校验城市是否存在
            Area area = areaMapper.selectByAreaNo(activityWhiteListReq.getAreaNo());
            if (Objects.isNull(area)) {
                throw new BizException("城市:" + activityWhiteListReq.getAreaNo() + " 不存在");
            }

            //数据上限、每个城市最多支持上传10条数据
            ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
            andWhiteList.setAreaNo(activityWhiteListReq.getAreaNo());
            int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
            if (count >= 10) {
                throw new BizException("城市:" + area.getAreaName() + " 商品已超出10个，超出的部分不支持上传，请删除部分商品后重试。");
            }

            //校验是否已经存在重复数据
            activityBlackAndWhiteList.setAreaName(area.getAreaName());
            activityBlackAndWhiteList.setAreaNo(activityWhiteListReq.getAreaNo());
            count = activityBlackAndWhiteListMapper.checkRepeat(activityBlackAndWhiteList);
            if (count > 0) {
                throw new BizException("skuId:" + activityBlackAndWhiteList.getSku() + "、城市:" + activityBlackAndWhiteList.getAreaName() + " 已存在");
            }

            //同一个城市排序值不能重复
            andWhiteList.setSort(activityWhiteListReq.getSort());
            count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
            if (count > 0) {
                throw new BizException("城市:" + activityBlackAndWhiteList.getAreaName() + "，排序值:" + andWhiteList.getSort() + " 已存在");
            }
            activityBlackAndWhiteList.setSort(activityWhiteListReq.getSort());
        } else {
            throw new BizException("新增类型有误！");
        }
        activityBlackAndWhiteListMapper.insertSelective(activityBlackAndWhiteList);
        ActivityBlackAndWhiteListDTO activityBlackAndWhiteListDTO = new ActivityBlackAndWhiteListDTO();
        activityBlackAndWhiteListDTO.setId(activityBlackAndWhiteList.getId());
        return activityBlackAndWhiteListDTO;
    }

    @Override
    public Boolean updateWhiteAndBlackList(ActivityBlackAndWhiteListUpdateReq andWhiteListUpdateReq) {
        ActivityBlackAndWhiteList activityBlackAndWhiteList = activityBlackAndWhiteListMapper.selectByPrimaryKey(andWhiteListUpdateReq.getId());
        String oldSku = activityBlackAndWhiteList.getSku();
        if (Objects.isNull(activityBlackAndWhiteList)) {
            throw new BizException("当前数据不存在！");
        }

        if (!Objects.equals(oldSku, andWhiteListUpdateReq.getSku())) {
            //校验sku是否存在
            Inventory inventory = inventoryMapper.selectOneBySku(andWhiteListUpdateReq.getSku());
            if (Objects.isNull(inventory)) {
                throw new BizException("sku:" + andWhiteListUpdateReq.getSku() + " 不存在");
            }
            activityBlackAndWhiteList.setSku(andWhiteListUpdateReq.getSku());
        }

        if (Objects.equals(activityBlackAndWhiteList.getType(), BlackAndWhiteTypeEnum.BLACK.getCode())) {
            //校验参数
            if (Objects.isNull(andWhiteListUpdateReq.getWarehouseNo())) {
                throw new BizException("黑名单的库存仓编号不能为空！");
            }

            //校验库存仓是否存在
            if (!Objects.equals(activityBlackAndWhiteList.getWarehouseNo(), andWhiteListUpdateReq.getWarehouseNo())) {
                WarehouseBaseInfoByNoReq warehouseBaseInfoByNoReq = new WarehouseBaseInfoByNoReq();
                warehouseBaseInfoByNoReq.setWarehouseNos(Collections.singletonList(andWhiteListUpdateReq.getWarehouseNo()));
                List<WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoResps = warehouseStorageQueryFacade.queryBaseInfoByWarehouseNo(warehouseBaseInfoByNoReq);
                if (CollectionUtils.isEmpty(warehouseBaseInfoByNoResps)) {
                    throw new BizException("库存仓:" + andWhiteListUpdateReq.getWarehouseNo() + " 不存在");
                }
                activityBlackAndWhiteList.setWarehouseName(warehouseBaseInfoByNoResps.get(0).getWarehouseName());
            }

            //校验是否已经存在重复数据
            if (!Objects.equals(oldSku, andWhiteListUpdateReq.getSku()) ||
                    !Objects.equals(activityBlackAndWhiteList.getWarehouseNo(), andWhiteListUpdateReq.getWarehouseNo())) {
                ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
                andWhiteList.setWarehouseNo(andWhiteListUpdateReq.getWarehouseNo());
                andWhiteList.setSku(andWhiteListUpdateReq.getSku());
                int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count > 0) {
                    throw new BizException("skuId:" + andWhiteList.getSku() + "、仓库名称:" + activityBlackAndWhiteList.getWarehouseName() + " 已存在");
                }
                activityBlackAndWhiteList.setWarehouseNo(andWhiteListUpdateReq.getWarehouseNo());
            }
        } else {
            if (Objects.isNull(andWhiteListUpdateReq.getAreaNo())) {
                throw new BizException("白名单的城市编号不能为空！");
            }
            if (Objects.isNull(andWhiteListUpdateReq.getSort())) {
                throw new BizException("白名单的排序值不能为空！");
            }

            if (!Objects.equals(andWhiteListUpdateReq.getAreaNo(), activityBlackAndWhiteList.getAreaNo())) {
                //校验城市是否存在
                Area area = areaMapper.selectByAreaNo(andWhiteListUpdateReq.getAreaNo());
                if (Objects.isNull(area)) {
                    throw new BizException("城市:" + andWhiteListUpdateReq.getAreaNo() + " 不存在");
                }

                //数据上限、每个城市最多支持上传10条数据
                ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
                andWhiteList.setAreaNo(andWhiteListUpdateReq.getAreaNo());
                int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count >= 10) {
                    throw new BizException("城市:" + area.getAreaName() + " 商品已超出10个，超出的部分不支持上传，请删除部分商品后重试。");
                }
                activityBlackAndWhiteList.setAreaName(area.getAreaName());
            }

            if (!Objects.equals(andWhiteListUpdateReq.getAreaNo(), activityBlackAndWhiteList.getAreaNo()) ||
                    !Objects.equals(andWhiteListUpdateReq.getSku(), oldSku)) {
                //校验是否已经存在重复数据
                ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
                andWhiteList.setAreaNo(andWhiteListUpdateReq.getAreaNo());
                andWhiteList.setSku(andWhiteListUpdateReq.getSku());
                int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count > 0) {
                    throw new BizException("skuId:" + andWhiteList.getSku() + "、城市:" + activityBlackAndWhiteList.getAreaName() + " 已存在");
                }
                activityBlackAndWhiteList.setAreaNo(andWhiteListUpdateReq.getAreaNo());
            }

            if (!Objects.equals(andWhiteListUpdateReq.getAreaNo(), activityBlackAndWhiteList.getAreaNo()) ||
                    !Objects.equals(andWhiteListUpdateReq.getSort(), activityBlackAndWhiteList.getSort())) {
                //同一个城市排序值不能重复
                ActivityBlackAndWhiteList andWhiteList = new ActivityBlackAndWhiteList();
                andWhiteList.setAreaNo(andWhiteListUpdateReq.getAreaNo());
                andWhiteList.setSort(andWhiteListUpdateReq.getSort());
                int count = activityBlackAndWhiteListMapper.checkRepeat(andWhiteList);
                if (count > 0) {
                    throw new BizException("城市:" + activityBlackAndWhiteList.getAreaName() + ",排序值:" + andWhiteList.getSort() + " 已存在");
                }
                activityBlackAndWhiteList.setSort(andWhiteListUpdateReq.getSort());
            }
        }
        activityBlackAndWhiteList.setCreator(getAdminName());
        int update = activityBlackAndWhiteListMapper.updateByPrimaryKeySelective(activityBlackAndWhiteList);
        return update > 0;
    }

    @Override
    public ActivityBlackAndWhiteListDTO getDetailWhiteAndBlackList(ActivityBlackAndWhiteListReq andWhiteListReq) {
        if (Objects.isNull(andWhiteListReq) || Objects.isNull(andWhiteListReq.getId())) {
            throw new BizException("ID不能为空！");
        }
        ActivityBlackAndWhiteList activityBlackAndWhiteList = activityBlackAndWhiteListMapper.selectByPrimaryKey(andWhiteListReq.getId());
        if (Objects.isNull(activityBlackAndWhiteList)) {
            throw new BizException("数据不存在，请传入正确ID！");
        }
        ActivityBlackAndWhiteListDTO activityBlackAndWhiteListDTO = new ActivityBlackAndWhiteListDTO();
        activityBlackAndWhiteListDTO.setId(activityBlackAndWhiteList.getId());
        activityBlackAndWhiteListDTO.setSku(activityBlackAndWhiteList.getSku());
        activityBlackAndWhiteListDTO.setAreaName(activityBlackAndWhiteList.getAreaName());
        activityBlackAndWhiteListDTO.setAreaNo(activityBlackAndWhiteList.getAreaNo());
        activityBlackAndWhiteListDTO.setWarehouseName(activityBlackAndWhiteList.getWarehouseName());
        activityBlackAndWhiteListDTO.setWarehouseNo(activityBlackAndWhiteList.getWarehouseNo());
        activityBlackAndWhiteListDTO.setType(activityBlackAndWhiteList.getType());
        activityBlackAndWhiteListDTO.setSort(activityBlackAndWhiteList.getSort());
        activityBlackAndWhiteListDTO.setCreator(activityBlackAndWhiteList.getCreator());
        return activityBlackAndWhiteListDTO;
    }

    @Override
    public Boolean insertPricingParams(ActivityPricingParamsInsertReq paramsInsertReq) {
        log.info("ActivityNewServiceImpl[]insertPricingParams[]start[]paramsInsertReq:{}", JSON.toJSONString(paramsInsertReq));
        Config config = configMapper.selectOne(PRICING_PARAMS);
        if (Objects.isNull(config)) {
            config = new Config();
            config.setKey(PRICING_PARAMS);
            config.setValue(JSON.toJSONString(paramsInsertReq.getPricingParams()));
            config.setRemark("临保风险品定价参数");
            configMapper.insert(config);
            return Boolean.TRUE;
        }
        config.setValue(JSON.toJSONString(paramsInsertReq.getPricingParams()));
        configMapper.update(config);
        return Boolean.TRUE;
    }

    @Override
    public Map<String, Map<String, String>> getPricingParams() {
        Config config = configMapper.selectOne(PRICING_PARAMS);
        if (Objects.isNull(config)) {
            return null;
        }
        return JSON.parseObject(config.getValue(), new TypeReference<HashMap<String, Map<String, String>>>() {});
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temporaryInsuranceRiskJob(String key) {
        log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]start[]key:{}", key);
        //1、判断是否设置定价参数
        Config config = configMapper.selectOne(PRICING_PARAMS);
        if (Objects.isNull(config) || Objects.isNull(config.getValue())) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]pricingParams is empty!");
            return;
        }
        HashMap<String, Map<String, String>> pricingParams = JSON.parseObject(config.getValue(), new TypeReference<HashMap<String, Map<String, String>>>() {});
        if (CollectionUtils.isEmpty(pricingParams)) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]pricingParams is empty! config:{}", JSON.toJSONString(config));
            return;
        }

        //2、按大区维度生成每个特价活动 活动周期每周二10点至下周一22点
        Map<Integer, Long> activityMap = this.initActivity();
        if (CollectionUtil.isEmpty(activityMap)) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]initActivity fail!");
            return;
        }

        //3、获取离线数据--数仓抓取的临保风险品
        TemporaryInsuranceRisk temporaryInsuranceRisk = new TemporaryInsuranceRisk();
        DataSynchronizationInformation dataSynchronizationInformation = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.TEMPORARY_INSTANCE_RISK.getTableName());
        if (Objects.nonNull(dataSynchronizationInformation)) {
            temporaryInsuranceRisk.setDateFlag(dataSynchronizationInformation.getDateFlag());
        }
        List<TemporaryInsuranceRisk> temporaryInsuranceRisks = temporaryInsuranceRiskMapper.selectByEntity(temporaryInsuranceRisk);
        if (CollectionUtils.isEmpty(temporaryInsuranceRisks)) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]temporaryInsuranceRisks is empty!");
            return;
        }

        //4、获取临保风险品的批次信息
        //获取sku对应的预警天数
        Set<String> skus = temporaryInsuranceRisks.stream().map(TemporaryInsuranceRisk::getSku).collect(Collectors.toSet());
        List<InventoryVO> inventoryVOS = inventoryMapper.batchSelectSkuInfo(skus);
        Map<String, InventoryVO> inventoryVOMap = inventoryVOS.stream()
                .collect(Collectors.toMap(InventoryVO::getSku, inventoryVO -> inventoryVO, (p1, p2) -> p2));

        //获取机器人配置
        Config riskRobotConfig = configMapper.selectOne(RISK_ROBOT);
        Map<String, String> riskRobot = null;
        if (Objects.nonNull(riskRobotConfig) && StringUtils.isNotBlank(riskRobotConfig.getValue())) {
            riskRobot = JSON.parseObject(riskRobotConfig.getValue(), new TypeReference<Map<String, String>>() {
            });
        }

        //需要的剔除sku集合--按照仓维度剔除
        Set<String> deleteSkus = new HashSet<>();

        //保存M、N的值 key:sku value:M-值,N-值
        Map<String, Map<String, Double>> mAndN = new HashMap<>();

        //保存sku对应的临保风险库存--仓维度
        Map<String, Integer> stockMap = new HashMap<>();

        //保存sku对应的批次成本最高价格--仓维度
        Map<String, BigDecimal> maxCostPriceMap = new HashMap<>();

        //按照库存仓维度分组
        Map<Integer, List<TemporaryInsuranceRisk>> listMap = temporaryInsuranceRisks.stream()
                .collect(Collectors.groupingBy(TemporaryInsuranceRisk::getWarehouseNo));
        List<QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO> skuWarehouseDTOS;
        QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO skuWarehouseDTO;
        for (Integer warehouseNo : listMap.keySet()) {
            skuWarehouseDTOS = new ArrayList<>();
            List<TemporaryInsuranceRisk> insuranceRisks = listMap.get(warehouseNo);
            Set<String> skuList = new HashSet<>();
            for (int i = 0; i < insuranceRisks.size(); i++) {
                TemporaryInsuranceRisk insuranceRisk = insuranceRisks.get(i);
                if (skuList.contains(insuranceRisk.getSku())) {
                    if (i == insuranceRisks.size() - 1 && !CollectionUtils.isEmpty(skuWarehouseDTOS)){
                        getStock(deleteSkus, skuWarehouseDTOS, mAndN, warehouseNo, inventoryVOMap, stockMap, maxCostPriceMap, riskRobot);
                    }
                    continue;
                }
                skuList.add(insuranceRisk.getSku());
                skuWarehouseDTO = new QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO();
                skuWarehouseDTO.setSku(insuranceRisk.getSku());
                skuWarehouseDTO.setWarehouseNo(warehouseNo);
                InventoryVO inventoryVO = inventoryVOMap.get(insuranceRisk.getSku());
                if (Objects.isNull(inventoryVO)) {
                    skuWarehouseDTO.setAlertDays(DEFAULT_CLINICAL_INSURANCE);
                } else {
                    skuWarehouseDTO.setAlertDays(DEFAULT_CLINICAL_INSURANCE + inventoryVO.getWarnTime());
                }
                skuWarehouseDTOS.add(skuWarehouseDTO);

                //限制50条查一次
                if (skuWarehouseDTOS.size() == 50) {
                    getStock(deleteSkus, skuWarehouseDTOS, mAndN, warehouseNo, inventoryVOMap, stockMap, maxCostPriceMap, riskRobot);
                } else if (i == insuranceRisks.size() - 1){
                    getStock(deleteSkus, skuWarehouseDTOS, mAndN, warehouseNo, inventoryVOMap, stockMap, maxCostPriceMap, riskRobot);
                }
            }
        }

        log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]deleteSkus:{}", JSON.toJSONString(deleteSkus));
        //9、自动定价
        //根据sku获取对应的大区和城市
        Map<String, List<TemporaryInsuranceRisk>> skuCollect = temporaryInsuranceRisks.stream()
                .collect(Collectors.groupingBy(TemporaryInsuranceRisk::getSku));
        for (String sku : skuCollect.keySet()) {
            //按照大区维度分组
            List<TemporaryInsuranceRisk> insuranceRiskList = skuCollect.get(sku);
            Map<Integer, List<TemporaryInsuranceRisk>> largeAreaNoCollect = insuranceRiskList.stream()
                    .collect(Collectors.groupingBy(TemporaryInsuranceRisk::getLargeAreaNo));

            WarehouseBySkuAreaNoQueryReq queryReq;
            WarehouseBySkuAreaNoDataReq skuAreaNoDataReq;
            QueryWarehouseSkuInventoryReq skuInventoryReq;
            for (Integer largeAreaNo : largeAreaNoCollect.keySet()) {
                Long activityId = activityMap.get(largeAreaNo);
                if (Objects.isNull(activityId)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]activityMap[]activityId empty sku:{},largeAreaNo:{}", JSON.toJSONString(sku), largeAreaNo);
                    continue;
                }

                //获取该大区下面所有的运营城市（排除不开放和测试城市）
                List<AreaSku> areaSkus = areaSkuMapper.selectSkuAreaByLareaAreaNo(sku, largeAreaNo);
                if (CollectionUtils.isEmpty(areaSkus)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]selectSkuAreaPrice[]empty sku:{},largeAreaNo:{}", JSON.toJSONString(sku), largeAreaNo);
                    continue;
                }
                List<Integer> areaNoCollect = areaSkus.stream().map(AreaSku::getAreaNo).collect(Collectors.toList());

                //根据大区下面的城市编号获取库存仓
                queryReq = new WarehouseBySkuAreaNoQueryReq();
                skuAreaNoDataReq = new WarehouseBySkuAreaNoDataReq();
                skuAreaNoDataReq.setSku(sku);
                skuAreaNoDataReq.setAreaNoList(areaNoCollect);
                queryReq.setAreaSkuList(Collections.singletonList(skuAreaNoDataReq));
                List<WarehouseBySkuAreaNoResp> queryBySkuAreNo = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo(queryReq);
                if (CollectionUtils.isEmpty(queryBySkuAreNo)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryBySkuAreNo[]empty queryReq:{}", JSON.toJSONString(queryReq));
                    continue;
                }

                //判断大区库存仓是否同一个 同一个运营城市下面对应多个库存仓取第一个 假如是同一个判断是否仅有一个库存仓有库存 是-继续往下走 否-钉钉消息提醒并剔除sku（大区维度剔除）
                Map<Integer, List<WarehouseBySkuAreaNoResp>> areaGroup = queryBySkuAreNo.stream()
                        .collect(Collectors.groupingBy(WarehouseBySkuAreaNoResp::getAreaNo));
                Set<Integer> warehouseNoSet = new HashSet<>();
                for (Integer areaNo : areaGroup.keySet()) {
                    List<WarehouseBySkuAreaNoResp> warehouseBySkuAreaNoResps = areaGroup.get(areaNo);
                    if (CollectionUtils.isEmpty(warehouseBySkuAreaNoResps)) {
                        continue;
                    }
                    warehouseNoSet.add(warehouseBySkuAreaNoResps.get(0).getWarehouseNo());
                }
                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]warehouseNoSet:{},sku:{},largeAreaNo:{}", JSON.toJSONString(warehouseNoSet), sku, largeAreaNo);
                Integer warehouseNo =  queryBySkuAreNo.get(0).getWarehouseNo();
                if (warehouseNoSet.size() > 1) {
                    //判断是否仅有一个仓有库存
                    skuInventoryReq = new QueryWarehouseSkuInventoryReq();
                    skuInventoryReq.setTenantId(1L);
                    skuInventoryReq.setWarehouseNoList(new ArrayList<>(warehouseNoSet));
                    skuInventoryReq.setSkuCode(sku);
                    WarehouseSkuInventoryResp skuInventory = saleInventoryFacade.queryWarehouseSkuInventory(skuInventoryReq);
                    if (Objects.isNull(skuInventory) || CollectionUtils.isEmpty(skuInventory.getWarehouseSkuInventoryDetailResDTOS())) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]empty skuInventoryReq:{}", JSON.toJSONString(skuInventoryReq));
                        continue;
                    }

                    //判断是否仅有一个库存仓有库存
                    List<WarehouseSkuInventoryDetailResDTO> detailResDTOList = skuInventory.getWarehouseSkuInventoryDetailResDTOS()
                            .stream().filter(e -> e.getAvailableQuantity() > 0).collect(Collectors.toList());
                    if (detailResDTOList.size() != 1) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]warehouseNo empty or more than one sku:{},largeAreaNo:{},detailResDTOList:{}", JSON.toJSONString(sku), largeAreaNo, JSON.toJSONString(detailResDTOList));

                        //发送飞书群消息
                        if (!CollectionUtils.isEmpty(riskRobot)) {
                            LargeAreaQuery largeArea = new LargeAreaQuery ();
                            largeArea.setLargeAreaNo(largeAreaNo);
                            List<LargeAreaVO> largeAreaVOS = largeAreaMapper.selectLargeArea(largeArea);
                            String largeAreaName = largeAreaVOS.get(0).getLargeAreaName();
                            InventoryVO inventoryVO = inventoryVOMap.get(sku);
                            Map<String,Object> text = new HashMap();
                            StringBuilder stringBuilder = new StringBuilder();
                            if (detailResDTOList.size() > 0) {
                                stringBuilder.append("<at user_id=\"all\">所有人</at>以下sku在同一个大区，同时在多个库存仓都有库存，已从本周临保风险品池子中剔除：")
                                        .append(LINE_FEED);
                                stringBuilder.append("skuId：" + sku).append(LINE_FEED);
                                stringBuilder.append("sku名称：" + inventoryVO.getPdName()).append(LINE_FEED);
                                stringBuilder.append("大区名称：" + largeAreaName);
                                Map<Integer, WarehouseBySkuAreaNoResp> skuAreaNoRespMap = queryBySkuAreNo.stream()
                                        .collect(Collectors.toMap(WarehouseBySkuAreaNoResp::getWarehouseNo, skuAreaNoResp -> skuAreaNoResp, (p1, p2) -> p2));
                                Set<Long> set = detailResDTOList.stream().map(WarehouseSkuInventoryDetailResDTO::getWarehouseNo).collect(Collectors.toSet());
                                for (Long aLong : set) {
                                    WarehouseBySkuAreaNoResp warehouseBySkuAreaNoResp = skuAreaNoRespMap.get(aLong.intValue());
                                    if (Objects.nonNull(warehouseBySkuAreaNoResp)) {
                                        stringBuilder.append(LINE_FEED).append("库存仓名称：" + warehouseBySkuAreaNoResp.getWarehouseName());
                                    }
                                }
                            } else {
                                stringBuilder.append("<at user_id=\"all\">所有人</at>以下sku在同一个大区，同时在多个库存仓都无库存，已从本周临保风险品池子中剔除：")
                                        .append(LINE_FEED);
                                stringBuilder.append("skuId：" + sku).append(LINE_FEED);
                                stringBuilder.append("sku名称：" + inventoryVO.getPdName()).append(LINE_FEED);
                                stringBuilder.append("大区名称：" + largeAreaName);
                            }
                            long timeMillis = System.currentTimeMillis() / 1000;
                            Map<String,Object> json=new HashMap();
                            text.put("text", stringBuilder);
                            json.put("content", text);
                            json.put("msg_type", "text");
                            try {
                                json.put("sign", FeiShuSingUtil.GenSign(riskRobot.get("sign"), timeMillis));
                                json.put("timestamp", timeMillis);
                                String body = HttpUtil.createPost(riskRobot.get("webhook")).body(JSON.toJSONString(json), "application/json;charset=UTF-8").execute().body();
                                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]body:{}", JSON.toJSONString(body));
                            } catch (NoSuchAlgorithmException ex) {
                                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]GenSign[]error:{}", JSON.toJSONString(ex));
                            } catch (InvalidKeyException ex) {
                                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]GenSign[]error:{}", JSON.toJSONString(ex));
                            }
                        }
                        continue;
                    }
                    warehouseNo = detailResDTOList.get(0).getWarehouseNo().intValue();
                }

                //判断sku是否剔除
                if (deleteSkus.contains(warehouseNo + ":" + sku)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]deleteSkus[]warehouseNo:{},sku:{}", warehouseNo, sku);
                    continue;
                }

                //获取当前库存仓下面的库存
                Integer stock = stockMap.get(warehouseNo + ":" + sku);
                if (Objects.isNull(stock) || stock <= 0) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]stock[]empty warehouseNo:{},sku:{}", warehouseNo, sku);
                    continue;
                }

                //获取当前库存仓下面的最高成本价
                BigDecimal maxCostPrice = maxCostPriceMap.get(warehouseNo + ":" + sku);
                if (Objects.isNull(maxCostPrice) || maxCostPrice.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]queryWarehouseSkuInventory[]maxCostPrice[]empty warehouseNo:{},sku:{}", warehouseNo, sku);
                    continue;
                }


                //根据大区维度查询售价是否倒挂--获取sku最高成本价对比大区下面所有城市售价（排除测试且不开放城市）
                int count = areaSkuMapper.checkPrice(sku, maxCostPrice, largeAreaNo);
                if (count > 0) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]maxCostPrice less than price sku:{},maxCostPrice:{},largeAreaNo:{}", sku, maxCostPrice, largeAreaNo);
                    continue;
                }

                //获取大区下面所有的城市计算平均价Q（排除测试且不开放城市）
                double Q = areaSkus.stream().map(f -> f.getPrice() == null ? BigDecimal.ZERO : f.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(areaSkus.size()), 2, BigDecimal.ROUND_HALF_UP).doubleValue();

                //获取M、N的值
                Map<String, Double> doubleMap = mAndN.get(warehouseNo + ":" + sku);
                if (CollectionUtils.isEmpty(doubleMap) || doubleMap.values().isEmpty()) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]mAndN[]doubleMap[]empty sku:{}", JSON.toJSONString(sku));
                    continue;
                }
                Double M = doubleMap.get("M");
                Double N = doubleMap.get("N");

                //获取货品分层 若商品没有对应档位，则默认为C档 并获取x、y、z 其中z = 1 - x - y
                SkuWarehouseSellLabel skuWarehouseSellLabel = skuWarehouseSellLabelMapper.selectBySkuWarehouseNo(sku, warehouseNo);
                String levelLabel;
                if (Objects.isNull(skuWarehouseSellLabel) || Objects.isNull(skuWarehouseSellLabel.getLevelLabel())) {
                    levelLabel = "C";
                } else {
                    levelLabel = skuWarehouseSellLabel.getLevelLabel();
                }
                Map<String, String> stringMap = pricingParams.get(levelLabel);
                String x = stringMap.get("X");
                String y = stringMap.get("Y");
                if (StringUtils.isBlank(x, y)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]pricingParams[]X or Y is empty sku:{}", JSON.toJSONString(sku));
                    continue;
                }
                double X = new BigDecimal(x).doubleValue();
                double Y = new BigDecimal(y).doubleValue();
                double Z = 1 - X - Y;

                //根据公式计算活动初始价P = Q*x  + Q*y * [1-(M-1)^2] + Q*z * (1-N^2)
                double v = 1 - Math.pow((M - 1), 2);
                double doubleP = (Q * X) + (Q * Y * v) + (Q * Z * (1 - Math.pow(N, 2)));
                BigDecimal P = new BigDecimal(doubleP).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]price[]result[]sku:{},warehouseNo:{},largeAreaNo:{},X:{},Y:{},Z:{},M:{},N:{},Q:{},maxCostPrice:{},levelLabel:{},P:{}", sku, warehouseNo, largeAreaNo, X, Y, Z, M, N, Q, maxCostPrice, levelLabel, P);
                if (P.compareTo(BigDecimal.ZERO) <= 0) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]P less than zero sku:{}, activityId", sku, activityId);
                    continue;
                }

                //最终价格对比sku批次最高的成本价 假如P > maxCostPrice 则 (最终活动价 = P) else (根据sku对应档次 假如sku属于A、B则最终活动价 = maxCostPrice + 1 假如属于C档 则最终活动价 = P)
                if (P.compareTo(maxCostPrice) <= 0 && !Objects.equals(levelLabel, "C")) {
                    P = maxCostPrice.add(BigDecimal.ONE);
                    log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]price[]finally[]result[]sku:{},warehouseNo:{},largeAreaNo:{},maxCostPrice:{},levelLabel:{},P:{}", sku, warehouseNo, largeAreaNo, maxCostPrice, levelLabel, P);
                }

                //根据活动Id查询活动商品配置信息 配置活动商品 并生成活动sku配置
                ActivityItemConfig activityItemConfig = activityItemConfigMapper.getByInfoId(activityId);
                if (Objects.isNull(activityItemConfig)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]activityItemConfig[] is empty sku:{}, activityId", sku, activityId);
                    continue;
                }
                ActivitySkuDetail activitySkuDetail = new ActivitySkuDetail();
                activitySkuDetail.setItemConfigId(activityItemConfig.getId());
                activitySkuDetail.setActualQuantity(stock);
                activitySkuDetail.setAmount(P);
                activitySkuDetail.setSku(sku);
                activitySkuDetail.setAccountLimit(AccountLimitEnum.NOT_LIMIT.getCode());
                activitySkuDetail.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
                activitySkuDetail.setRoundingMode(0);
                activitySkuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(P, 0, AdjustTypeEnum.FIXED_PRICE.getCode()));
                activitySkuDetailMapper.insertSelective(activitySkuDetail);

                //生成城市活动sku价格信息
                List<ActivitySkuPrice> activitySkuPrices = new ArrayList<>();
                BigDecimal finalP = P;
                areaSkus.stream().forEach(e -> {
                    ActivitySkuPrice activitySkuPrice = new ActivitySkuPrice();
                    activitySkuPrice.setSku(sku);
                    activitySkuPrice.setAreaNo(e.getAreaNo());
                    activitySkuPrice.setActivityPrice(finalP);
                    activitySkuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(finalP));
                    activitySkuPrice.setSalePrice(e.getPrice());
                    activitySkuPrice.setBasicInfoId(activityId);
                    activitySkuPrice.setSkuDetailId(activitySkuDetail.getId());
                    activitySkuPrices.add(activitySkuPrice);
                });
                activitySkuPriceMapper.insertBatch(activitySkuPrices);
            }
        }
        log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]end[]key:{}", key);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temporaryInsuranceRiskStockJob(String key) {
        log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]start[]key:{}", key);
        //查询正在进行中的临保风险品活动
        ActivityBasicInfo basicInfo = new ActivityBasicInfo();
        basicInfo.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
        basicInfo.setTag(ActivityTagEnum.SLOW_SALE_PROMOTION.getCode());
        basicInfo.setRemark("系统创建。每周自动创建临保风险品特价活动");
        basicInfo.setStatus(CommonStatus.YES.getCode());
        basicInfo.setDelFlag(CommonStatus.NO.getCode());
        List<ActivityBasicInfo> basicInfoList = activityBasicInfoMapper.selectUnderwayByEntity(basicInfo);
        if (CollectionUtil.isEmpty(basicInfoList)) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]basicInfoList[]empty!");
            return;
        }
        for (ActivityBasicInfo activityBasicInfo : basicInfoList) {
            Long basicInfoId = activityBasicInfo.getId();

            //活动商品配置
            ActivityItemConfig activityItemConfig = activityItemConfigMapper.getByInfoId(basicInfoId);
            if (Objects.isNull(activityItemConfig)) {
                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]activityItemConfig[]empty!");
                continue;
            }

            //获取活动sku信息
            List<ActivitySkuDetail> activitySkuDetails = activitySkuDetailMapper.selectByItemConfig(activityItemConfig.getId());
            WarehouseBySkuAreaNoQueryReq queryReq;
            WarehouseBySkuAreaNoDataReq skuAreaNoDataReq;
            List<Integer> areaNoCollect;
            QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO skuWarehouseDTO;
            DataSynchronizationInformation dataSynchronizationInformation;
            for (ActivitySkuDetail activitySkuDetail : activitySkuDetails) {
                String sku = activitySkuDetail.getSku();
                List<ActivitySkuPrice> activitySkuPrices = activitySkuPriceMapper.selectBySkuAndBasicInfoId(basicInfoId, sku);
                if (CollectionUtils.isEmpty(activitySkuPrices)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]activitySkuPrices[]empty!basicInfoId:{},sku:{}", basicInfoId, sku);
                    continue;
                }

                //获取预警天数
                List<InventoryVO> inventoryVOS = inventoryMapper.batchSelectSkuInfo(Collections.singleton(sku));
                if (CollectionUtils.isEmpty(inventoryVOS)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]inventoryVOS[]empty!basicInfoId:{},sku:{}", basicInfoId, sku);
                    continue;
                }
                Integer warnTime = inventoryVOS.get(0).getWarnTime();

                //根据大区下面的城市编号获取库存仓
                areaNoCollect = activitySkuPrices.stream().map(ActivitySkuPrice::getAreaNo).collect(Collectors.toList());
                queryReq = new WarehouseBySkuAreaNoQueryReq();
                skuAreaNoDataReq = new WarehouseBySkuAreaNoDataReq();
                skuAreaNoDataReq.setSku(sku);
                skuAreaNoDataReq.setAreaNoList(areaNoCollect);
                queryReq.setAreaSkuList(Collections.singletonList(skuAreaNoDataReq));
                List<WarehouseBySkuAreaNoResp> queryBySkuAreNo = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo(queryReq);
                if (CollectionUtils.isEmpty(queryBySkuAreNo)) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]queryBySkuAreNo[]empty queryReq:{}", JSON.toJSONString(queryReq));
                    continue;
                }

                //判断大区库存仓是否同一个 同一个运营城市下面对应多个库存仓取第一个 假如是同一个判断是否仅有一个库存仓有库存 是-继续往下走 否-钉钉消息提醒并剔除sku（大区维度剔除）
                Map<Integer, List<WarehouseBySkuAreaNoResp>> areaGroup = queryBySkuAreNo.stream()
                        .collect(Collectors.groupingBy(WarehouseBySkuAreaNoResp::getAreaNo));
                Set<Integer> warehouseNoSet = new HashSet<>();
                for (Integer areaNo : areaGroup.keySet()) {
                    List<WarehouseBySkuAreaNoResp> warehouseBySkuAreaNoResps = areaGroup.get(areaNo);
                    if (CollectionUtils.isEmpty(warehouseBySkuAreaNoResps)) {
                        continue;
                    }
                    warehouseNoSet.add(warehouseBySkuAreaNoResps.get(0).getWarehouseNo());
                }
                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]queryWarehouseSkuInventory[]warehouseNoSet:{},sku:{}", JSON.toJSONString(warehouseNoSet), sku);
                Integer warehouseNo = queryBySkuAreNo.get(0).getWarehouseNo();
                if (warehouseNoSet.size() > 1) {
                    //判断是否仅有一个仓有库存
                    QueryWarehouseSkuInventoryReq skuInventoryReq = new QueryWarehouseSkuInventoryReq();
                    skuInventoryReq.setTenantId(1L);
                    skuInventoryReq.setWarehouseNoList(new ArrayList<>(warehouseNoSet));
                    skuInventoryReq.setSkuCode(sku);
                    WarehouseSkuInventoryResp skuInventory = saleInventoryFacade.queryWarehouseSkuInventory(skuInventoryReq);
                    if (Objects.isNull(skuInventory) || CollectionUtils.isEmpty(skuInventory.getWarehouseSkuInventoryDetailResDTOS())) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]queryWarehouseSkuInventory[]empty skuInventoryReq:{}", JSON.toJSONString(skuInventoryReq));
                        continue;
                    }

                    //判断是否仅有一个库存仓有库存
                    List<WarehouseSkuInventoryDetailResDTO> detailResDTOList = skuInventory.getWarehouseSkuInventoryDetailResDTOS()
                            .stream().filter(e -> e.getAvailableQuantity() > 0).collect(Collectors.toList());
                    if (detailResDTOList.size() != 1) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]queryWarehouseSkuInventory[]warehouseNo empty or more than one sku:{},detailResDTOList:{}", JSON.toJSONString(sku), JSON.toJSONString(detailResDTOList));
                        continue;
                    }
                    warehouseNo = detailResDTOList.get(0).getWarehouseNo().intValue();
                }

                //获取批次信息
                skuWarehouseDTO = new QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO();
                skuWarehouseDTO.setSku(sku);
                skuWarehouseDTO.setWarehouseNo(warehouseNo);
                skuWarehouseDTO.setAlertDays(warnTime + DEFAULT_CLINICAL_INSURANCE);
                QueryBatchInventoryBySkuAndWarehouseReq queryBatchInventoryBySkuAndWarehouseReq = new QueryBatchInventoryBySkuAndWarehouseReq();
                queryBatchInventoryBySkuAndWarehouseReq.setSkuWarehouseDTOList(Collections.singletonList(skuWarehouseDTO));
                QueryBatchInventoryBySkuAndWarehouseResp warehouse = warehouseInventoryFacade.queryBatchInventoryBySkuAndWarehouse(queryBatchInventoryBySkuAndWarehouseReq);
                if (Objects.isNull(warehouse) || CollectionUtils.isEmpty(warehouse.getBatchInventoryDTOList())) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]queryBySkuAreNo[]empty queryBatchInventoryBySkuAndWarehouseReq:{}", JSON.toJSONString(queryBatchInventoryBySkuAndWarehouseReq));
                    continue;
                }

                //获取sku的日均销量 为空默认为1
                SkuSalesVolume skuSalesVolume = new SkuSalesVolume();
                dataSynchronizationInformation = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.SKU_SALES_VOLUME.getTableName());
                if (Objects.nonNull(dataSynchronizationInformation)) {
                    skuSalesVolume.setDateFlag(dataSynchronizationInformation.getDateFlag());
                }
                skuSalesVolume.setSku(sku);
                skuSalesVolume.setWarehouseNo(warehouseNo);
                SkuSalesVolume newSkuSalesVolume = skuSalesVolumeMapper.selectByEntity(skuSalesVolume);
                Integer salesVolume;
                if (Objects.isNull(newSkuSalesVolume) || Objects.isNull(newSkuSalesVolume.getSalesVolume())) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]get sku sales volume empty skuSalesVolume:{}", JSON.toJSONString(skuSalesVolume));
                    salesVolume = 0;
                } else {
                    salesVolume = newSkuSalesVolume.getSalesVolume();
                }

                List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO> inventoryDTOS = warehouse.getBatchInventoryDTOList();

                //过滤批次库存为0的
                inventoryDTOS = inventoryDTOS.stream().filter(e -> e.getQuantity() > 0).collect(Collectors.toList());

                //批次成本是否大于0--假如小于0的批次剔除
                inventoryDTOS = inventoryDTOS.stream().filter(e -> e.getCostPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

                //计算临保风险品件数 先按照保质期排序然后计算每个满足条件批次的临保件数
                int quantity = 0;
                List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO> shelfLifeSort = inventoryDTOS.stream()
                        .sorted(Comparator.comparing(QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO::getShelfLife)).collect(Collectors.toList());

                //记录批次累计天数
                double lastK = 0;
                Integer finalWarnTime = warnTime;

                //记录满足条件的最老批次
                int old = 0;
                for (int i = 0; i < shelfLifeSort.size(); i++) {
                    QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO batchInventoryDTO = shelfLifeSort.get(i);

                    //假如保质期在当前时间之前则忽略
                    if (batchInventoryDTO.getShelfLife().isBefore(LocalDate.now())) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]shelfLife is before batchInventoryDTO:{}", JSON.toJSONString(batchInventoryDTO));
                        continue;
                    }

                    //计算距离临保日期L (保质日期 - 到期预警天数 - Today) 其中小等于0和大于30的需要过滤
                    long L = batchInventoryDTO.getShelfLife().toEpochDay() - finalWarnTime - LocalDate.now().toEpochDay();
                    if (L <= 0 || L > 30) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]L error batchInventoryDTO:{},L:{}", JSON.toJSONString(batchInventoryDTO), L);
                        continue;
                    }

                    //计算批次DOC（批次预计可售天数）J 假如销量为0 则J = L
                    double j;
                    try {
                        if (salesVolume == 0) {
                            j = L;
                        } else {
                            j = new BigDecimal((float) batchInventoryDTO.getQuantity() / salesVolume)
                                    .setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                        }
                    } catch (Exception e) {
                        log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]extracted[]j error batchInventoryDTO:{}", JSON.toJSONString(batchInventoryDTO));
                        continue;
                    }

                    //计算常规售卖天数
                    double k;
                    if (old == 0) {
                        if (j >= L) {
                            k = L;
                        } else {
                            k = j;
                        }
                    } else {
                        if ((j + lastK) > L) {
                            k = (L - lastK);
                        } else {
                            k = j;
                        }
                    }

                    //计算当前批次临保件数 得到结果向上取整
                    double ceil = Math.ceil(batchInventoryDTO.getQuantity() - (k * salesVolume));
                    lastK += k;
                    old++;
                    log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]activitySkuDetail:{}, L:{}, j:{}, k:{}, ceil:{}", JSON.toJSONString(activitySkuDetail), L, j, k, ceil);
                    if (ceil <= 0) {
                        continue;
                    }
                    quantity += ceil;
                }

                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]activitySkuDetail:{},basicInfoId:{},quantity:{}", JSON.toJSONString(activitySkuDetail), basicInfoId, quantity);
                //更新库存
                if (quantity < 0) {
                    continue;
                }
                activitySkuDetail.setActualQuantity(quantity);
                activitySkuDetailMapper.updateByPrimaryKeySelective(activitySkuDetail);
            }
        }
        log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskStockJob[]end[]key:{}", key);
    }

    /**
     * @description:
     * @author: lzh
     * @date: 2023/7/4 10:57
     * @param: [inventoryVOMap, deleteSkus, skuWarehouseDTOS, mAndN, warehouseNo, inventoryVO]
     * @return: void
     **/
    private void getStock(Set<String> deleteSkus, List<QueryBatchInventoryBySkuAndWarehouseReq.SkuWarehouseDTO> skuWarehouseDTOS, Map<String, Map<String, Double>> mAndN,
                          Integer warehouseNo, Map<String, InventoryVO> inventoryVOMap, Map<String, Integer> stockMap, Map<String, BigDecimal> maxCostPriceMap, Map<String, String> riskRobot) {
        DataSynchronizationInformation dataSynchronizationInformation;
        if (CollectionUtils.isEmpty(skuWarehouseDTOS)) {
            return;
        }

        //查询批次信息
        QueryBatchInventoryBySkuAndWarehouseReq queryBatchInventoryBySkuAndWarehouseReq = new QueryBatchInventoryBySkuAndWarehouseReq();
        queryBatchInventoryBySkuAndWarehouseReq.setSkuWarehouseDTOList(skuWarehouseDTOS);
        QueryBatchInventoryBySkuAndWarehouseResp warehouse = warehouseInventoryFacade.queryBatchInventoryBySkuAndWarehouse(queryBatchInventoryBySkuAndWarehouseReq);

        //清空集合
        skuWarehouseDTOS.clear();
        if (Objects.isNull(warehouse) || CollectionUtils.isEmpty(warehouse.getBatchInventoryDTOList())) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]queryBatchInventoryBySkuAndWarehouseReq:{}", JSON.toJSONString(queryBatchInventoryBySkuAndWarehouseReq));
            return;
        }
        List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO> inventoryDTOList = warehouse.getBatchInventoryDTOList();

        //按照sku维度分组--库存仓是一样的
        Map<String, List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO>> map = inventoryDTOList.stream()
                .collect(Collectors.groupingBy(QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO::getSku));
        ActivityBlackAndWhiteList activityBlackAndWhiteList;
        for (String sku : map.keySet()) {
            List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO> inventoryDTOS = map.get(sku);
            if (CollectionUtils.isEmpty(inventoryDTOS)) {
                continue;
            }

            //获取sku预警天数
            Integer warnTime = 0;
            InventoryVO inventoryVO = inventoryVOMap.get(sku);
            if (Objects.nonNull(inventoryVO)) {
                warnTime = inventoryVO.getWarnTime();
            }

            //过滤批次库存为0的
            inventoryDTOS = inventoryDTOS.stream().filter(e -> e.getQuantity() > 0).collect(Collectors.toList());

            //5、批次成本是否大于0--假如小于0的批次剔除并且发送钉钉群消息
            inventoryDTOS.stream().forEach(e -> {
                if (e.getCostPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]inventoryVO:{}", JSON.toJSONString(e));

                    //发送飞书群消息
                    if (!CollectionUtils.isEmpty(riskRobot)) {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("<at user_id=\"all\">所有人</at>以下批次成本为0，已从本周临保风险品池子中剔除：").append(LINE_FEED);
                        stringBuilder.append("skuId：" + sku).append(LINE_FEED);
                        stringBuilder.append("sku名称：" + inventoryVO.getPdName()).append(LINE_FEED);
                        stringBuilder.append("批次id：" + e.getPurchaseNo()).append(LINE_FEED);
                        stringBuilder.append("库存仓：" + warehouseNo);
                        long timeMillis = System.currentTimeMillis() / 1000;
                        Map<String,Object> text = new HashMap();
                        Map<String,Object> json=new HashMap();
                        text.put("text", stringBuilder);
                        json.put("content", text);
                        json.put("msg_type", "text");
                        try {
                            json.put("sign", FeiShuSingUtil.GenSign(riskRobot.get("sign"), timeMillis));
                            json.put("timestamp", timeMillis);
                            String body = HttpUtil.createPost(riskRobot.get("webhook")).body(JSON.toJSONString(json), "application/json;charset=UTF-8").execute().body();
                            log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]body:{}", JSON.toJSONString(body));
                        } catch (NoSuchAlgorithmException ex) {
                            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]GenSign[]error:{}", JSON.toJSONString(ex));
                        } catch (InvalidKeyException ex) {
                            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]sendRobot[]GenSign[]error:{}", JSON.toJSONString(ex));
                        }
                    }
                }
            });
            inventoryDTOS = inventoryDTOS.stream().filter(e -> e.getCostPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(inventoryDTOS)) {
                continue;
            }

            //6、取批次最高的成本价-后面校验是否售价倒挂
            BigDecimal maxCostPrice = inventoryDTOS.stream()
                    .map(QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO::getCostPrice).reduce(BigDecimal.ZERO, BigDecimal::max);
            maxCostPriceMap.put(warehouseNo + ":" + sku, maxCostPrice);

            //7、是否在黑名单中 是-剔除
            activityBlackAndWhiteList = new ActivityBlackAndWhiteList();
            activityBlackAndWhiteList.setSku(sku);
            activityBlackAndWhiteList.setWarehouseNo(warehouseNo);
            activityBlackAndWhiteList.setType(BlackAndWhiteTypeEnum.BLACK.getCode());
            int checkRepeat = activityBlackAndWhiteListMapper.checkRepeat(activityBlackAndWhiteList);
            if (checkRepeat > 0) {
                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]activityBlackAndWhiteList:{}", JSON.toJSONString(activityBlackAndWhiteList));
                deleteSkus.add(warehouseNo + ":" + sku);
                continue;
            }

            //8、计算临保件数，并比较是否大于零
            //获取sku的日均销量 为空默认为0
            SkuSalesVolume skuSalesVolume = new SkuSalesVolume();
            dataSynchronizationInformation = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.SKU_SALES_VOLUME.getTableName());
            if (Objects.nonNull(dataSynchronizationInformation)) {
                skuSalesVolume.setDateFlag(dataSynchronizationInformation.getDateFlag());
            }
            skuSalesVolume.setSku(sku);
            skuSalesVolume.setWarehouseNo(warehouseNo);
            SkuSalesVolume newSkuSalesVolume = skuSalesVolumeMapper.selectByEntity(skuSalesVolume);
            Integer salesVolume;
            if (Objects.isNull(newSkuSalesVolume) || Objects.isNull(newSkuSalesVolume.getSalesVolume())) {
                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]get sku sales volume empty skuSalesVolume:{}", JSON.toJSONString(skuSalesVolume));
                salesVolume = 0;
            } else {
                salesVolume = newSkuSalesVolume.getSalesVolume();
            }

            //计算临保风险品件数 先按照保质期排序然后计算每个满足条件批次的临保件数
            int quantity = 0;
            List<QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO> shelfLifeSort = inventoryDTOS.stream()
                    .sorted(Comparator.comparing(QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO::getShelfLife)).collect(Collectors.toList());

            //记录批次累计天数
            double lastK = 0;
            Integer finalWarnTime = warnTime;

            //保存最老批次--计算方式不一样 假如第一个批次临保风险品为0则过滤掉
            int old = 0;
            int o = 0;
            for (int i = 0; i < shelfLifeSort.size(); i++) {
                QueryBatchInventoryBySkuAndWarehouseResp.BatchInventoryDTO batchInventoryDTO = shelfLifeSort.get(i);

                //假如保质期在当前时间之前则忽略
                if (batchInventoryDTO.getShelfLife().isBefore(LocalDate.now())) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]shelfLife is before batchInventoryDTO:{}", JSON.toJSONString(batchInventoryDTO));
                    continue;
                }

                //计算距离临保日期L (保质日期 - 到期预警天数 - Today) 其中小等于0和大于30的需要过滤
                long L = batchInventoryDTO.getShelfLife().toEpochDay() - finalWarnTime - LocalDate.now().toEpochDay();
                if (L <= 0 || L > 30) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]L error batchInventoryDTO:{},L:{}", JSON.toJSONString(batchInventoryDTO), L);
                    continue;
                }

                //计算批次DOC（批次预计可售天数）J 假如销量为0 则J = L
                double j;
                try {
                    if (salesVolume == 0) {
                        j = L;
                    } else {
                        j = new BigDecimal((float) batchInventoryDTO.getQuantity() / salesVolume)
                                .setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                    }
                } catch (Exception e) {
                    log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]j error batchInventoryDTO:{}", JSON.toJSONString(batchInventoryDTO));
                    continue;
                }


                //计算常规售卖天数
                double k;
                if (o == 0) {
                    if (j >= L) {
                        k = L;
                    } else {
                        k = j;
                    }
                } else {
                    if ((j + lastK) > L) {
                        k = (L - lastK);
                    } else {
                        k = j;
                    }
                }

                //计算当前批次临保件数 得到结果向上取整
                double ceil = Math.ceil(batchInventoryDTO.getQuantity() - (k * salesVolume));
                lastK += k;
                o++;
                log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]batchInventoryDTO:{}L:{},j:{},k:{},ceil:{}", JSON.toJSONString(batchInventoryDTO), L, j, k, ceil);
                if (ceil <= 0) {
                    continue;
                }
                quantity += ceil;

                //计算 M 距离临保天数占比 = 批次距离临保日期天数(=保质日期 - 到期预警天数 - Today)/常规售卖时长(=保质日期 -生产日期 - 到期预警天数) 、N 预计临保数量占比 = 预计临保数量/现库存数量 M、N读取第一个批次临保风险不为0的值
                if (old == 0) {
                    Map<String, Double> doubleMap = new HashMap<>();

                    //常规售卖时长(=保质日期 -生产日期 - 到期预警天数)
                    long n = batchInventoryDTO.getShelfLife().toEpochDay() - finalWarnTime - batchInventoryDTO.getProduceAt().toEpochDay();

                    double M = new BigDecimal((double) L / n).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                    double N = new BigDecimal(ceil / batchInventoryDTO.getQuantity()).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                    doubleMap.put("M", M);
                    doubleMap.put("N", N);
                    log.info("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]batchInventoryDTO:{}L:{},j:{},k:{},n:{},ceil:{},M:{},N:{}", JSON.toJSONString(batchInventoryDTO), L, j, k, n, ceil, M, N);
                    mAndN.put(warehouseNo + ":" + sku, doubleMap);
                }
                old++;
            }

            //临保数量小于等于0 则剔除sku
            if(quantity <= 0) {
                log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]extracted[]quantity less than zero sku:{}", sku);
                deleteSkus.add(warehouseNo + ":" + sku);
                continue;
            }
            stockMap.put(warehouseNo + ":" + sku, quantity);
        }
    }

    /**
     * @description: 初始化创建特价活动-按照大区维度
     * @author: lzh
     * @date: 2023/7/5 10:53
     * @param: []
     * @return: Map<Integer, Long> 大区：活动Id
     **/
    private Map<Integer, Long> initActivity() {
        //查询需要参与活动的大区信息
        Config config = configMapper.selectOne(LARGE_AREAS);
        if (Objects.isNull(config) || StringUtils.isBlank(config.getValue())) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]initActivity[]largeAreaList is empty!");
            return null;
        }
        List<String> largeAreaList = JSON.parseObject(config.getValue(), new TypeReference<List<String>>() {
        });

        //查找所有大区
        List<LargeAreaVO> largeAreas = largeAreaMapper.selectAll(CommonStatus.YES.getCode());
        if (CollectionUtils.isEmpty(largeAreas)) {
            log.error("ActivityNewServiceImpl[]temporaryInsuranceRiskJob[]initActivity[]largeAreas is empty!");
            return null;
        }

        //结束开始时间 下周周一 并设置时间为22:00:00
        LocalDate now = LocalDate.now();
        LocalDateTime endTime = DateUtils.getNextWeekDate(now, 1).atTime(22, 00, 00);

        //获取开始时间 本周周二 并设置时间为10:00:00
        LocalDateTime startTime = LocalDateTime.of(now, LocalTime.MIN).with(DayOfWeek.TUESDAY).toLocalDate().atTime(10, 00, 00);

        int monthValue = now.getMonthValue();
        int dayOfMonth = now.getDayOfMonth();
        Map<Integer, Long> map = new HashMap<>(largeAreas.size());
        for (LargeAreaVO largeArea : largeAreas) {
            //过滤不需要的大区
            if (!largeAreaList.contains(largeArea.getLargeAreaName())) {
                continue;
            }

            //生成基本信息
            ActivityBasicInfo basicInfo = new ActivityBasicInfo();
            basicInfo.setName(monthValue + "月" + dayOfMonth + "日临保风险品活动-" + largeArea.getLargeAreaName());
            basicInfo.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
            basicInfo.setIsPermanent(CommonStatus.NO.getCode());
            basicInfo.setNeedPre(CommonStatus.NO.getCode());
            basicInfo.setStatus(CommonStatus.YES.getCode());
            basicInfo.setTag(ActivityTagEnum.SLOW_SALE_PROMOTION.getCode());
            basicInfo.setRemark("系统创建。每周自动创建临保风险品特价活动");
            basicInfo.setStartTime(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
            basicInfo.setEndTime(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
            basicInfo.setCreatorId(0);
            activityBasicInfoMapper.insertSelective(basicInfo);
            Long basicInfoId = basicInfo.getId();

            //生成活动场景配置
            ActivitySceneConfig sceneConfig = new ActivitySceneConfig();
            sceneConfig.setBasicInfoId(basicInfoId);
            sceneConfig.setPlatform(PlatformEnum.MALL.getCode());
            activitySceneConfigMapper.insertSelective(sceneConfig);

            //生成活动商品配置
            ActivityItemConfig itemConfig = new ActivityItemConfig();
            itemConfig.setBasicInfoId(basicInfoId);
            itemConfig.setGoodSelectWay(0);
            itemConfig.setPricingType(0);
            activityItemConfigMapper.insertSelective(itemConfig);

            //生成活动生效范围配置
            ActivityScopeConfig activityScopeConfig = new ActivityScopeConfig();
            activityScopeConfig.setScopeId(largeArea.getLargeAreaNo().longValue());
            activityScopeConfig.setScopeType(ScopeTypeEnum.LARGE_AREA.getCode());
            activityScopeConfig.setBasicInfoId(basicInfoId);
            activityScopeConfigMapper.insertSelective(activityScopeConfig);
            map.put(largeArea.getLargeAreaNo(), basicInfoId);
        }
        return map;
    }
}
