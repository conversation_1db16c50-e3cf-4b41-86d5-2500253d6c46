package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.MQDelayConstant;
import net.summerfarm.enums.*;
import net.summerfarm.enums.market.PartnershipBuyConfigStatus;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.AreaDTO;
import net.summerfarm.model.DTO.area.SeriesOfAreaDTO;
import net.summerfarm.model.DTO.market.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.service.PartnershipBuyConfigService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.model.DTO.market.PartnershipBuySkuDTO.APPOINT;

/**
 * 营销-多人拼团配置业务逻辑类
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-05-25
 */
@Service
public class PartnershipBuyConfigServiceImpl implements PartnershipBuyConfigService {

    @Resource
    PartnershipBuyConfigMapper partnershipBuyConfigMapper;

    @Resource
    PartnershipBuySkuMapper partnershipBuySkuMapper;

    @Resource
    BaseService baseService;

    @Resource
    PriceStrategyMapper priceStrategyMapper;

    @Resource
    SeriesOfAreaMapper seriesOfAreaMapper;

    @Autowired
    MqProducer mqProducer;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult save(PartnershipBuyConfigDTO configDto) {
        // 1.校验开始时间、结束时间是否合法
        LocalDateTime startTime = configDto.getStartTime();
        LocalDateTime endTime = configDto.getEndTime();
        if(startTime.isEqual(endTime) || startTime.isAfter(endTime)) {
            return AjaxResult.getErrorWithMsg("开始时间不能等于或晚于结束时间");
        }
        LocalDateTime now = LocalDateTime.now();
        if(startTime.isBefore(now) || endTime.isBefore(now)){
            return AjaxResult.getErrorWithMsg("开始时间或结束时间不能早于当前时间");
        }
        // 2.校验当前配置的城市是否有在生效中的活动，只要区域不重复，那么同一SKU可以配置在不同区域
        // 生效中的区域
        Set<Integer> effectiveAreaNos = partnershipBuyConfigMapper.selectEffectiveAreaNos(startTime, endTime);
        Set<Integer> areaNos = configDto.getAreaNos();
        // 取当前有效区域与活动配置区域交集
        Sets.SetView<Integer> intersectionAreaNo = Sets.intersection(effectiveAreaNos, areaNos);
        if(CollectionUtils.isNotEmpty(intersectionAreaNo)){
            return AjaxResult.getErrorWithMsg("当前配置区域存在有效活动区域中");
        }

        Integer adminId = baseService.getAdminId();
        // 4.构建活动数据

        // 活动创建人
        PartnershipBuyConfig partnershipBuyConfig = PartnershipBuyConfig.build(configDto, adminId);
        partnershipBuyConfigMapper.insertSelective(partnershipBuyConfig);
        // 5.构建活动商品数据
        List<PartnershipBuySkuDTO> partnershipBuySkuDTOList = configDto.getPartnershipBuySkus();
        for (PartnershipBuySkuDTO partnershipBuySkuDTO : partnershipBuySkuDTOList) {
            // 写入活动商品数据
            PartnershipBuySku partnershipBuySku = savePartnership(adminId, partnershipBuyConfig.getId(), partnershipBuySkuDTO);

            // 写入活动商品定价数据
            savePartnershipBuySku(adminId, partnershipBuySkuDTO, partnershipBuySku);
        }
        List<SeriesOfArea> seriesOfAreas = new ArrayList<>();
        // 6.构建聚合区域数据
        savePartnershipBuyArea(areaNos, adminId, partnershipBuyConfig, seriesOfAreas);

        // 7.发送活动截止延迟消息
        MQData mqData = new MQData();
        mqData.setType(MType.PARTNERSHIP_CONFIG_END.name());
        mqData.setData(partnershipBuyConfig.getId());
        long startDeliverTime = DateUtils.localDateTimeToDate(endTime).getTime();
        mqProducer.sendStartDeliver(RocketMqMessageConstant.MALL_DELAY_LIST,null,JSON.toJSONString(mqData),endTime);
//        producer.sendDelayDataToQueue(RocketMqMessageConstant.MALL_DELAY_LIST,
//                JSON.toJSONString(mqData), MQDelayConstant.FOURTEEN_DELAY_LEVEL, startDeliverTime);
        return AjaxResult.getOK();
    }

    private void savePartnershipBuyArea(Set<Integer> areaNos, Integer adminId, PartnershipBuyConfig partnershipBuyConfig, List<SeriesOfArea> seriesOfAreas) {
        for (Integer areaNo : areaNos) {
            SeriesOfArea seriesOfArea = new SeriesOfArea();
            seriesOfArea.setSeriesType(SeriesTypeEnum.PARTNERSHIP_BUY.getType());
            seriesOfArea.setSeriesId(partnershipBuyConfig.getId().intValue());
            seriesOfArea.setAreaNo(areaNo);
            seriesOfArea.setCreator(adminId);
            seriesOfAreas.add(seriesOfArea);
        }
        seriesOfAreaMapper.insertBatch(seriesOfAreas);
    }

    private void savePartnershipBuySku(Integer adminId, PartnershipBuySkuDTO partnershipBuySkuDTO, PartnershipBuySku partnershipBuySku) {
        PriceStrategy priceStrategy = new PriceStrategy();
        priceStrategy.setBusinessId(partnershipBuySku.getId());
        priceStrategy.setType(PriceStrategyTypeEnum.PARTNERSHIP_BUY.getType());
        if(APPOINT == partnershipBuySkuDTO.getType()){
            priceStrategy.setAdjustType(InventoryAdjustPriceTypeEnum.APPOINT.getType());
        }else{
            priceStrategy.setAdjustType(InventoryAdjustPriceTypeEnum.QUOTA_REDUCTION.getType());
        }
        priceStrategy.setAmount(partnershipBuySkuDTO.getAmount());
        priceStrategy.setCreator(adminId);
        priceStrategy.setUpdater(adminId);
        priceStrategyMapper.insertSelective(priceStrategy);
    }

    private PartnershipBuySku savePartnership(Integer adminId, Long configId, PartnershipBuySkuDTO partnershipBuySkuDTO) {
        PartnershipBuySku partnershipBuySku = new PartnershipBuySku();
        partnershipBuySku.setConfigId(configId);
        partnershipBuySku.setSku(partnershipBuySkuDTO.getSku());
        partnershipBuySku.setMinSaleNum(partnershipBuySkuDTO.getMinSaleNum());
        partnershipBuySku.setCreator(adminId);
        partnershipBuySku.setUpdater(adminId);
        partnershipBuySku.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        partnershipBuySkuMapper.insertSelective(partnershipBuySku);
        return partnershipBuySku;
    }

    @Override
    public PageInfo<PartnershipBuyConfigListDTO> list(PartnershipBuyConfigListQueryDTO queryDTO) {
        Integer pageIndex = queryDTO.getPageIndex();
        Integer pageSize = queryDTO.getPageSize();
        List<Integer> areaNos = queryDTO.getAreaNos();
        List<SeriesOfAreaDTO> seriesOfAreas = new ArrayList<>();
        List<Long> configIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(areaNos)){
            seriesOfAreas = seriesOfAreaMapper.selectBySeriesTypeAndAreaNos(SeriesTypeEnum.PARTNERSHIP_BUY.getType(),
                    areaNos);
            if (CollectionUtils.isNotEmpty(seriesOfAreas)) {
                configIds = seriesOfAreas.stream().map(SeriesOfAreaDTO::getSeriesId).collect(Collectors.toList());
            }else{
                return PageInfoHelper.createPageInfo(new ArrayList<>());
            }
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<PartnershipBuyConfigListDTO> list = partnershipBuyConfigMapper.selectListByCondtion(queryDTO, configIds);
        for (PartnershipBuyConfigListDTO partnershipBuyConfigListDTO : list) {
            LocalDateTime startTime = partnershipBuyConfigListDTO.getStartTime();
            if(PartnershipBuyConfigStatus.EFFECTIVE.getStatus() == partnershipBuyConfigListDTO.getStatus() &&
                    startTime.isAfter(LocalDateTime.now())){
                partnershipBuyConfigListDTO.setStatus(PartnershipBuyConfigStatus.WAIT_EFFECTIVE.getStatus());
            }
            // 设置区域集合
            List<SeriesOfAreaDTO> seriesOfAreaDTOS = seriesOfAreaMapper.selectByTypeAndSeriesId(SeriesTypeEnum.PARTNERSHIP_BUY.getType(),
                    partnershipBuyConfigListDTO.getId());
            if (CollectionUtils.isEmpty(seriesOfAreaDTOS)) {
                continue;
            }
            List<AreaDTO> areas = new ArrayList<>();
            for (SeriesOfAreaDTO seriesOfArea : seriesOfAreaDTOS) {
                if(seriesOfArea == null){
                    continue;
                }
                AreaDTO areaDTO = new AreaDTO();
                areaDTO.setAreaName(seriesOfArea.getAreaName());
                areaDTO.setAreaNo(seriesOfArea.getAreaNo());
                areas.add(areaDTO);
            }
            partnershipBuyConfigListDTO.setAreas(areas);
        }
        return PageInfoHelper.createPageInfo(list);
    }

    @Override
    public AjaxResult detail(Long id){
        PartnershipBuyConfig partnershipBuyConfig = partnershipBuyConfigMapper.selectByPrimaryKey(id);
        if (partnershipBuyConfig == null) {
            return AjaxResult.getErrorWithMsg("多人拼团配置不存在");
        }
        PartnershipBuyConfigDetailDTO detail = PartnershipBuyConfigDetailDTO.build(partnershipBuyConfig);
        List<PartnershipBuySkuDTO> partnershipBuySkuDTOS = partnershipBuySkuMapper.selectByConfigId(id);
        for (PartnershipBuySkuDTO partnershipBuySkuDTO : partnershipBuySkuDTOS) {
            if(InventoryAdjustPriceTypeEnum.APPOINT.getType().intValue() == partnershipBuySkuDTO.getType()){
                partnershipBuySkuDTO.setType(APPOINT);
            }
            String weight = partnershipBuySkuDTO.getWeight();
            partnershipBuySkuDTO.setWeight(weight);
        }
        List<SeriesOfAreaDTO> seriesOfAreaDTOS = seriesOfAreaMapper.selectByTypeAndSeriesId(SeriesTypeEnum.PARTNERSHIP_BUY.getType(), detail.getId());
        List<AreaDTO> areaDTOS = new ArrayList<>();
        for (SeriesOfAreaDTO seriesOfAreaDTO : seriesOfAreaDTOS) {
            AreaDTO areaDTO = new AreaDTO();
            areaDTO.setAreaName(seriesOfAreaDTO.getAreaName());
            areaDTO.setAreaNo(seriesOfAreaDTO.getAreaNo());
            areaDTOS.add(areaDTO);
        }
        detail.setPartnershipBuySkus(partnershipBuySkuDTOS);
        detail.setAreas(areaDTOS);
        return AjaxResult.getOK(detail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult close(Long id){
        PartnershipBuyConfig partnershipBuyConfig = partnershipBuyConfigMapper.selectByPrimaryKey(id);
        if (partnershipBuyConfig == null) {
            return AjaxResult.getErrorWithMsg("多人拼团配置不存在");
        }
        if(2 == partnershipBuyConfig.getStatus()){
            return AjaxResult.getErrorWithMsg("多人拼团配置已经关闭，请不要重复关闭");
        }

        Integer adminId = baseService.getAdminId();
        int num = partnershipBuyConfigMapper.updateStatusById(id, 2, adminId);
        if (num == 0) {
            throw new DefaultServiceException("多人拼团配置已经关闭，请不要重复关闭");
        }

        // 如果拼团还没开始，则没有待成团的拼团，不发送拼团关闭消息
        if(LocalDateTime.now().isBefore(partnershipBuyConfig.getStartTime())){
            return AjaxResult.getOK();
        }

        MQData mqData = new MQData();
        mqData.setType(MType.PARTNERSHIP_CONFIG_END.name());
        mqData.setData(partnershipBuyConfig.getId());
        mqProducer.send(RocketMqMessageConstant.MALL_DELAY_LIST,null,JSON.toJSONString(mqData));

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult editPartnershipBuySku(Long configId,
                                            List<PartnershipBuySkuDTO> partnershipBuySkus) {
        PartnershipBuyConfig partnershipBuyConfig = partnershipBuyConfigMapper.selectByPrimaryKey(configId);
        if(partnershipBuyConfig.getStatus() == PartnershipBuyConfigStatus.EXPIRED.getStatus()){
            return AjaxResult.getErrorWithMsg("活动已关闭，不可编辑");
        }
        // 当前已有的活动sku
        List<PartnershipBuySku> oldPartnershipBuySkus = partnershipBuySkuMapper.selectAllByConfigId(configId);

        Integer adminId = baseService.getAdminId();
        LocalDateTime now = LocalDateTime.now();
        for (PartnershipBuySkuDTO partnershipBuySkuDTO : partnershipBuySkus) {
            String sku = partnershipBuySkuDTO.getSku();

            Optional<PartnershipBuySku> oldData = oldPartnershipBuySkus.stream().filter(item -> item.getSku().equals(sku))
                    .findFirst();
            if (oldData.isPresent()){
                Long id = oldData.get().getId();
                PriceStrategy priceStrategy = new PriceStrategy();
                priceStrategy.setType(PriceStrategyTypeEnum.PARTNERSHIP_BUY.getType());
                if(APPOINT == partnershipBuySkuDTO.getType()){
                    priceStrategy.setAdjustType(InventoryAdjustPriceTypeEnum.APPOINT.getType());
                }else{
                    priceStrategy.setAdjustType(partnershipBuySkuDTO.getType());
                }
                priceStrategy.setAmount(partnershipBuySkuDTO.getAmount());
                priceStrategy.setUpdateTime(now);
                priceStrategy.setUpdater(adminId);
                priceStrategy.setBusinessId(id);
                priceStrategyMapper.updateSelectiveByBusinessId(priceStrategy);
                partnershipBuySkuMapper.updateMinSaleNumByConfigIdAndSku(configId, sku, adminId, partnershipBuySkuDTO.getMinSaleNum());
            }else {
                // 写入活动商品数据
                PartnershipBuySku partnershipBuySku = savePartnership(adminId, configId, partnershipBuySkuDTO);

                // 写入活动商品定价数据
                savePartnershipBuySku(adminId, partnershipBuySkuDTO, partnershipBuySku);
            }
        }

        return AjaxResult.getOK();
    }
}
