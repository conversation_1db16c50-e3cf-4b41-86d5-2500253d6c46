package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastRequest;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponse;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponseBody;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingdingFormBO;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import net.summerfarm.dingding.bo.TerminateInstanceProcessBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.DTO.FinanceAuditRecordDTO;
import net.summerfarm.model.DTO.PurchaseSupplierDTO;
import net.summerfarm.model.PurchaseBindingPrepayment;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.dingding.DingdingProcessFlow;
import net.summerfarm.model.input.SupplierReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.*;
import org.apache.commons.math3.dfp.DfpField;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 预付单业务层
 * @date 2022/1/13 17:38
 */
@Service
@Slf4j
public class PurchaseAdvancedOrderServiceImpl extends BaseService implements PurchaseAdvancedOrderService {

    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private PurchasesService purchasesService;
    @Resource
    private PurchasesMapper purchasesMapper;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private SettlementConfigMapper settlementConfigMapper;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private PurchaseAdvancedOrderMapper purchaseAdvancedOrderMapper;
    @Resource
    private FinanceAuditRecordMapper financeAuditRecordMapper;
    @Resource
    private PurchaseBindingPrepaymentMapper purchaseBindingPrepaymentMapper;
    @Resource
    private FinanceAuditRecordService financeAuditRecordService;
    @Resource
    private PurchaseSupplierPaymentService purchaseSupplierPaymentService;
    @Resource
    private PurchaseSupplierPaymentMapper purchaseSupplierPaymentMapper;
    @Resource
    private FinancePaymentOrderService financePaymentOrderService;
    @Resource
    private PurchasesBackDetailMapper purchasesBackDetailMapper;
    @Resource
    private FinancePaymentOrderMapper financePaymentOrderMapper;
    @Resource
    private SupplierAccountMapper supplierAccountMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private FinanceOperatorLogMapper financeOperatorLogMapper;
    @Resource
    private DepartmentStaffMapper departmentStaffMapper;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private DingdingProcessFlowMapper dingdingProcessFlowMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private FinanceAccountStatementMapper financeAccountStatementMapper;

    /**
     * 关联采购单
     */
    private static final Integer ASSOCIATED = 1;

    /**
     * 2
     */
    private static final Integer TWO = NumberUtils.INTEGER_TWO;

    /**
     * 十位数
     */
    private static final BigDecimal TEN_DIGIT = BigDecimal.valueOf(9999999999L);

    /**
     * 已经预付满的状态
     */
    private static final Integer IN_SETTLE = 1;

    /**
     * 未预付满的状态
     */
    private static final Integer UN_SETTLE = 0;

    /**
     * 钉钉
     */
    private static final Integer DING_DING = 0;

    /**
     * 预付类型
     */
    private static final Integer ADVANCE_TYPE = 1;

    private static final String ADVANCE_DETAIL = "预付详情";

    /**
     * 生鲜标识
     */
    private Integer fresh = 1;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult savePurchaseAdvancedOrder(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO) {
        //校验参数
        Integer supplierId = purchaseAdvancedOrderVO.getSupplierId();
        SupplierReq supplierReq = supplierMapper.selectBill(supplierId);
        if (Objects.isNull(supplierReq)) {
            return AjaxResult.getErrorWithMsg("请选择正确的供应商");
        }
        Integer supplierAccountId = purchaseAdvancedOrderVO.getSupplierAccountId();
        SupplierAccount supplierAccount = supplierAccountMapper.selectById(supplierAccountId);
        if (Objects.isNull(supplierAccount)) {
            return AjaxResult.getErrorWithMsg("未查询到供应商付款账号信息");
        }
        BigDecimal totalAmount = purchaseAdvancedOrderVO.getTotalAmount();
        if (totalAmount.scale() > TWO || totalAmount.compareTo(TEN_DIGIT) > 0 || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return AjaxResult.getErrorWithMsg("请重新输入符合条件的预付金额");
        }
        if (!CollectionUtils.isEmpty(purchaseAdvancedOrderVO.getPurchasesVOList())) {
            List<String> purchaseNoList = new ArrayList<>();
            for (PurchasesVO purchasesVO : purchaseAdvancedOrderVO.getPurchasesVOList()) {
                purchaseNoList.add(purchasesVO.getPurchaseNo());
            }
            List<String> purchaseNos = purchaseBindingPrepaymentMapper.selectProcessAdvance(purchaseNoList, purchaseAdvancedOrderVO.getSupplierId());
            if (!CollectionUtils.isEmpty(purchaseNos)) {
                List<String> list = purchaseNos.stream().distinct().collect(Collectors.toList());
                return AjaxResult.getErrorWithMsg("以下采购单在预付流程中，请先结束预付流程后发起：" + list);
            }
            //增加对账流程校验
            List<String> inProcessPurchaseList = financeAccountStatementMapper.queryInProcessPurchaseList(purchaseNoList, purchaseAdvancedOrderVO.getSupplierId());
            if (!CollectionUtils.isEmpty(inProcessPurchaseList)) {
                List<String> list = inProcessPurchaseList.stream().distinct().collect(Collectors.toList());
                return AjaxResult.getErrorWithMsg("以下采购单在对账流程中，请先结束预付流程后发起：" + list);
            }
        }
        AdminAuthExtend creatorExtend = adminAuthExtendRepository.selectByAdminId(DING_DING, getAdminId());

        if (Objects.isNull(creatorExtend) || Objects.isNull(creatorExtend.getUserId())) {
            return AjaxResult.getErrorWithMsg("检测到您没有绑定钉钉后台，流程无法继续，请绑定后再尝试发起");
        }

        if (Objects.equals(purchaseAdvancedOrderVO.getType(), ASSOCIATED)) {
            for (PurchasesVO purchasesVO : purchaseAdvancedOrderVO.getPurchasesVOList()) {
                purchasesService.checkAdvancePrepayment(purchasesVO.getPurchaseNo(), supplierId, purchasesVO.getAdvanceAmount());
            }
        }

        //模拟审批情况 如果有审批人则是需要预付审核状态 否则直接进入付款审核状态 并且发送不同的钉钉消息
        //预付单不同的状态 预付单状态 1、预付审核 4、付款审核中 5、待付款 6、已付款 7、作废 操作记录在接口回调更新
        //预付审核
        purchaseAdvancedOrderVO.setStatus(PurchaseAdvancedOrderStatusEnum.PREPAYMENT_REVIEW.ordinal());

        purchaseAdvancedOrderVO.setCurrentProcessor("请到 钉钉-OA审批—我发起的 中查看");
        //插入预付单表数据
        PurchaseAdvancedOrder purchaseAdvancedOrder = generatePurchaseAdvanceOrderInfo(purchaseAdvancedOrderVO);
        purchaseAdvancedOrderMapper.insertByAll(purchaseAdvancedOrder);

        //预付审批钉钉审批流
        createProcessInstance(purchaseAdvancedOrder, supplierReq);


        //预付单创建人
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setOperatorId(getAdminId());
        financeOperatorLog.setOperator(getAdminName());
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        financeOperatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

        if (!CollectionUtils.isEmpty(purchaseAdvancedOrderVO.getPurchasesVOList())) {
            for (PurchasesVO purchasesVO : purchaseAdvancedOrderVO.getPurchasesVOList()) {
                //如果是绑定的话  插入绑定关系
                if (Objects.equals(purchaseAdvancedOrderVO.getType(), ASSOCIATED)) {
                    PurchaseBindingPrepayment purchaseBindingPrepayment = new PurchaseBindingPrepayment(purchaseAdvancedOrder.getId(), purchasesVO.getPurchaseNo(), purchasesVO.getAdvanceAmount(), PurchaseBindingEnum.UNBOUNDED.ordinal(), getAdminName(), supplierId);
                    purchaseBindingPrepaymentMapper.insert(purchaseBindingPrepayment);
                }
            }
        }
        logger.info("管理员：{}新增了预付单：{}", getAdminName(), purchaseAdvancedOrder.getId());
        return AjaxResult.getOK();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult recall(PurchaseAdvancedOrder purchaseAdvancedOrder) {
        //检查预付单状态
        PurchaseAdvancedOrder advancedOrder = purchaseAdvancedOrderMapper.queryById(purchaseAdvancedOrder.getId());
        if (!Objects.equals(PurchaseAdvancedOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal(), advancedOrder.getStatus())) {
            return AjaxResult.getErrorWithMsg("该预付单不在预付审核中，无法撤销！");
        }
        FinanceOperatorLog operatorLog = new FinanceOperatorLog();
        operatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        operatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        operatorLog.setPersonnelType(FinancePersonnelType.PAYMENT_REVIEWER.ordinal());
        operatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        operatorLog.setOperationResults(CommonNumbersEnum.ONE.getNumber());
        operatorLog.setOperator(getAdminName());
        operatorLog.setOperatorId(getAdminId());
        operatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLogMapper.insertSelective(operatorLog);
        FinanceOperatorLog financeOperatorLog = financeOperatorLogMapper.selectById(operatorLog);
        if (!Objects.equals(financeOperatorLog.getOperatorId(), getAdminId())) {
            return AjaxResult.getErrorWithMsg("该预付单仅可由发起人撤销！");
        }

        //改变预付单状态
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(advancedOrder.getId());
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        updateOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.WITHDRAWAL_OF_APPLICATION.ordinal());
        updateOrder.setCurrentProcessor("--");
        purchaseAdvancedOrderMapper.update(updateOrder);
        settleFlagReset(advancedOrder.getId(), purchaseAdvancedOrder.getSupplierId());
//        supplierPaymentReset(purchaseAdvancedOrder);

        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setAdditionalId(advancedOrder.getId());
        financePaymentOrder.setType(ADVANCE_TYPE);
        financePaymentOrder.setStatus(FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        FinancePaymentOrder finance = financePaymentOrderMapper.selectOne(financePaymentOrder);

        //作废预付单付款单
        FinancePaymentOrder financePayment = new FinancePaymentOrder();
        financePayment.setId(finance.getId());
        financePayment.setStatus(FinancePaymentOrderStatusEnum.CANCEL.ordinal());
        financePayment.setDeleteReason(FinancePaymentOrderReasonEnum.PREPAYMENT_WITHDRAWAL.ordinal());
        financePaymentOrderMapper.update(financePayment);

        //撤回审批流(预付单付款单)
        try {
            TerminateInstanceProcessBO terBo = new TerminateInstanceProcessBO();
            terBo.setAdminId(financeOperatorLog.getOperatorId());
            terBo.setBizTypeEnum(ProcessInstanceBizTypeEnum.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT);
            terBo.setBizId(finance.getId());
            terBo.setRemark("采购撤销" + getAdminName() + getAdminId());
            log.info("撤销预付单付款单钉钉审批:" + finance.getId());
            dingdingProcessInstanceService.terminateProcess(terBo);
        } catch (Exception e) {
            log.error("预付单撤销异常,adminId:{}", getAdminId(), e);
        }

        //发送钉钉消息给采购
        //sendOfferMessage(advancedOrder, advancedOrder.getSupplierName() + "的预付单在待付款状态被取消付款");

        return AjaxResult.getOK();
    }

    /**
     * 钉钉消息通知采购
     * @param purchaseAdvancedOrder
     * @param reason
     */
    private void sendOfferMessage(PurchaseAdvancedOrder purchaseAdvancedOrder, String reason) {
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setType(ADVANCE_TYPE);
        financeOperatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        FinanceOperatorLog operatorLog = financeOperatorLogMapper.selectById(financeOperatorLog);
        if (ObjectUtils.isEmpty(operatorLog)) {
            return;
        }
        //查询所属销售的钉钉对应信息
        AdminAuthExtend creatorInfo = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), operatorLog.getOperatorId());

        String title = "【采购预付通知】";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append(reason).append("\n");
        text.append("> ###### 供应商：").append(purchaseAdvancedOrder.getSupplierName()).append("\n");
        text.append("> ###### 预付号：").append(purchaseAdvancedOrder.getId()).append("\n");
        text.append("> ###### 预付金额：").append(purchaseAdvancedOrder.getTotalAmount()).append("\n");
        if (Objects.nonNull(creatorInfo)) {
            logger.info("【采购预付通知】钉钉Id{}" + creatorInfo.getUserId());
            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), creatorInfo.getUserId(), title, text.toString());
            dingTalkMsgSender.sendMessage(dingTalkMsgBO);
        }
    }

    /**
     * 插入预付单信息
     *
     * @param purchaseAdvancedOrderVO
     * @return
     */
    private PurchaseAdvancedOrder generatePurchaseAdvanceOrderInfo(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO) {
        PurchaseAdvancedOrder purchaseAdvancedOrder = new PurchaseAdvancedOrder();
        purchaseAdvancedOrder.setSupplierId(purchaseAdvancedOrderVO.getSupplierId());
        purchaseAdvancedOrder.setSupplierName(purchaseAdvancedOrderVO.getSupplierName());
        purchaseAdvancedOrder.setRemark(purchaseAdvancedOrderVO.getRemark());
        purchaseAdvancedOrder.setPayType(purchaseAdvancedOrderVO.getPayType());
        purchaseAdvancedOrder.setSupplierAccountId(purchaseAdvancedOrderVO.getSupplierAccountId());
        purchaseAdvancedOrder.setTotalAmount(purchaseAdvancedOrderVO.getTotalAmount());
        purchaseAdvancedOrder.setType(purchaseAdvancedOrderVO.getType());
        purchaseAdvancedOrder.setStatus(purchaseAdvancedOrderVO.getStatus());
        purchaseAdvancedOrder.setCurrentProcessor(purchaseAdvancedOrderVO.getCurrentProcessor());
        return purchaseAdvancedOrder;
    }

    @Override
    public AjaxResult selectDetail(Long purchaseInAdvanceId) {
        PurchaseAdvancedOrderVO purchaseAdvancedOrderVO = purchaseAdvancedOrderMapper.selectDetail(purchaseInAdvanceId);
        if (Objects.isNull(purchaseAdvancedOrderVO)) {
            return AjaxResult.getErrorWithMsg("未查询到预付单详情");
        }
        Integer supplierAccountId = purchaseAdvancedOrderVO.getSupplierAccountId();
        SupplierAccount supplierAccount = supplierAccountMapper.selectById(supplierAccountId);
        if (Objects.nonNull(supplierAccount)) {
            purchaseAdvancedOrderVO.setAccountAscription(supplierAccount.getAccountAscription());
            purchaseAdvancedOrderVO.setAccount(supplierAccount.getAccount());
            purchaseAdvancedOrderVO.setAccountBank(supplierAccount.getAccountBank());
            purchaseAdvancedOrderVO.setAccountName(supplierAccount.getAccountName());
        }
        FinancePaymentOrder query = new FinancePaymentOrder();
        query.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        query.setAdditionalId(purchaseInAdvanceId);
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.selectOne(query);
        if (Objects.nonNull(financePaymentOrder)) {
            purchaseAdvancedOrderVO.setPaymentVoucher(financePaymentOrder.getPaymentVoucher());
        }

        List<PurchaseBindingPrepaymentVO> bindingDetailList = purchaseAdvancedOrderVO.getPurchaseInAdvanceDetail();
        if (CollectionUtils.isEmpty(bindingDetailList)) {
            return AjaxResult.getOK(purchaseAdvancedOrderVO);
        }
        for (PurchaseBindingPrepaymentVO item : bindingDetailList) {
            String warehouseName = purchasesMapper.selectWarehouseName(item.getPurchaseNo());
            item.setWarehouseName(warehouseName);
            PurchaseSupplierDTO purchaseSupplierDTO = purchasesService.querySupplierSummary(item.getPurchaseNo(), purchaseAdvancedOrderVO.getSupplierId());
            item.setAdjustAmount(purchaseSupplierDTO.getAdjustAmount());
            item.setActualPrice(purchaseSupplierDTO.getActualPrice());
            item.setTotalAmount(purchaseSupplierDTO.getTotalAmount());
            List<PurchasesPlan> planList = purchasesPlanMapper.selectBySupplierAndNo(item.getPurchaseNo(), purchaseAdvancedOrderVO.getSupplierId());
            List<PurchasesPlan> purchasesPlanList = planList.stream().filter(plan -> Objects.isNull(plan.getOriginId())).collect(Collectors.toList());
            for (PurchasesPlan plan : purchasesPlanList) {
                BigDecimal backAmount = Optional.ofNullable(purchasesBackDetailMapper.queryBackAmount(plan.getPurchaseNo(), plan.getSku(), PurchasesBackTypeEnum.UN_IN_BACK.ordinal())).orElse(BigDecimal.ZERO);
                Integer backQuantity = purchasesBackDetailMapper.queryBackQuantity(plan.getPurchaseNo(), plan.getSku(), PurchasesBackTypeEnum.UN_IN_BACK.ordinal());
                plan.setAvgPrice(plan.getPrice().divide(BigDecimal.valueOf(plan.getQuantity()), 2, BigDecimal.ROUND_HALF_UP));
                plan.setPrice(plan.getPrice().subtract(backAmount));
                plan.setQuantity(plan.getQuantity() - backQuantity);
                plan.setAvgPriceStr(plan.getAvgPrice()==null?null:plan.getAvgPrice().toPlainString());
            }
            item.setPurchasesPlan(purchasesPlanList);
        }
        return AjaxResult.getOK(purchaseAdvancedOrderVO);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult audit(Long id, Integer flag) {
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(id);
        if (Objects.isNull(purchaseAdvancedOrder)) {
            return AjaxResult.getErrorWithMsg("未查询到预付单信息");
        }
        if (!Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.PREPAYMENT_REVIEW.ordinal())) {
            return AjaxResult.getErrorWithMsg("待审核状态下才可以进行审核");
        }
        FinanceAuditRecord financeAuditRecord = new FinanceAuditRecord();
        financeAuditRecord.setAdditionalId(id);
        financeAuditRecord.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        FinanceAuditRecord record = financeAuditRecordMapper.selectOne(financeAuditRecord);
        if (!Objects.equals(record.getAuditorAdminId(), getAdminId())) {
            return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + record.getAuditor());
        }
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(id);
        SettlementConfig config = settlementConfigMapper.selectByType(purchaseAdvancedOrder.getPdType());
        if (Objects.equals(flag, AuditFlagEnum.SUCCESS.getFlag())) {
            Boolean skipApproveFlag = financeAuditRecordService.checkSkipApprove(config, purchaseAdvancedOrder.getTotalAmount());
            if (skipApproveFlag) {
                //免审批
                updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal());
                record.setApprover(null);
                record.setApproverAdminId(null);
                //生成付款单
                financePaymentOrderService.generatePaymentInfo(purchaseAdvancedOrder);
            } else {
                updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.WAIT_APPROVE.ordinal());
            }
        } else {
            settleFlagReset(id, purchaseAdvancedOrder.getSupplierId());
            //供应商金额回退
//            supplierPaymentReset(purchaseAdvancedOrder);
            updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.AUDIT_FAIL.ordinal());
        }
        record.setAuditTime(LocalDateTime.now());

        //更新预付单状态和审核记录
        purchaseAdvancedOrderMapper.update(updateOrder);
        financeAuditRecordMapper.update(record);
        logger.info("管理员：{}审核了预付单：{}", getAdminName(), id);
        return AjaxResult.getOK();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult approve(Long id, Integer flag) {
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(id);
        if (Objects.isNull(purchaseAdvancedOrder)) {
            return AjaxResult.getErrorWithMsg("未查询到预付单信息");
        }
        if (!Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.WAIT_APPROVE.ordinal())) {
            return AjaxResult.getErrorWithMsg("待审批状态下才可以进行审批");
        }
        FinanceAuditRecord financeAuditRecord = new FinanceAuditRecord();
        financeAuditRecord.setAdditionalId(id);
        financeAuditRecord.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        FinanceAuditRecord record = financeAuditRecordMapper.selectOne(financeAuditRecord);
        if (!Objects.equals(record.getApproverAdminId(), getAdminId())) {
            return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + record.getApprover());
        }
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(id);
        if (Objects.equals(flag, AuditFlagEnum.SUCCESS.getFlag())) {
            updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal());
            //生成付款单
            financePaymentOrderService.generatePaymentInfo(purchaseAdvancedOrder);
        } else {
            settleFlagReset(id, purchaseAdvancedOrder.getSupplierId());
//            supplierPaymentReset(purchaseAdvancedOrder);
            updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        }
        record.setApproveTime(LocalDateTime.now());
        //更新预付单状态和审核记录
        purchaseAdvancedOrderMapper.update(updateOrder);
        financeAuditRecordMapper.update(record);
        logger.info("管理员：{}审批了预付单：{}", getAdminName(), id);
        return AjaxResult.getOK();
    }

    /**
     * 发起预付标识更新
     *
     * @param id
     */
    private void settleFlagReset(Long id, Integer supplierId) {
        PurchaseBindingPrepayment query = new PurchaseBindingPrepayment();
        query.setPurchaseAdvancedOrderId(id);
        List<PurchaseBindingPrepayment> purchaseBindingPrepayments = purchaseBindingPrepaymentMapper.queryAll(query);
        PurchasesPlan updatePlan = new PurchasesPlan();
        for (PurchaseBindingPrepayment prepayment : purchaseBindingPrepayments) {
            List<PurchasesPlanVO> purchasesPlanVOS = purchasesPlanMapper.queryAdvanceAble(prepayment.getPurchaseNo(), supplierId, NumberUtils.INTEGER_ONE);
            for (PurchasesPlanVO plan : purchasesPlanVOS) {
                if (Objects.equals(plan.getSettleFlag(), IN_SETTLE)) {
                    updatePlan.setId(plan.getId());
                    updatePlan.setSettleFlag(UN_SETTLE);
                    purchasesPlanMapper.update(updatePlan);
                }
            }
        }
    }

    @Override
    public AjaxResult cancel(Long id) {
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(id);
        if (Objects.isNull(purchaseAdvancedOrder)) {
            return AjaxResult.getErrorWithMsg("未查询到预付单信息");
        }
        if (Objects.equals(PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal(), purchaseAdvancedOrder.getStatus()) || Objects.equals(PurchaseAdvancedOrderStatusEnum.HAS_PAY.ordinal(), purchaseAdvancedOrder.getStatus())) {
            return AjaxResult.getErrorWithMsg("该预付单已不可撤销");
        }
        FinanceOperatorLog operatorLog = new FinanceOperatorLog();
        operatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        operatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        operatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        operatorLog.setAdditionalId(id);
        operatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        FinanceOperatorLog financeOperatorLog = financeOperatorLogMapper.selectById(operatorLog);
        if (!Objects.equals(financeOperatorLog.getOperator(), getAdminName())) {
            return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + financeOperatorLog.getOperator());
        }

        settleFlagReset(id, purchaseAdvancedOrder.getSupplierId());
        //supplierPaymentReset(purchaseAdvancedOrder);
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(id);
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        updateOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.WITHDRAWAL_OF_APPLICATION.ordinal());

        //撤销人操作记录
        FinanceOperatorLog operator = new FinanceOperatorLog();
        operator.setAdditionalId(id);
        operator.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        operator.setStatus(CommonNumbersEnum.ZERO.getNumber());
        if (Objects.equals(PurchaseAdvancedOrderStatusEnum.PREPAYMENT_REVIEW.ordinal(), purchaseAdvancedOrder.getStatus())) {
            operator.setPersonnelType(FinancePersonnelType.CHECKER.ordinal());
        } else if (Objects.equals(PurchaseAdvancedOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal(), purchaseAdvancedOrder.getStatus())) {
            operator.setPersonnelType(FinancePersonnelType.PAYMENT_REVIEWER.ordinal());
        }
        operator.setOperator(getAdminName());
        operator.setOperatorId(getAdminId());
        operator.setOperatorTime(LocalDateTime.now());
        operator.setOperationResults(CommonNumbersEnum.ONE.getNumber());
        financeOperatorLogMapper.insertSelective(operator);

        //撤回审批流 采购预付审批
        try {
            TerminateInstanceProcessBO terBo = new TerminateInstanceProcessBO();
            terBo.setAdminId(financeOperatorLog.getOperatorId());
            terBo.setBizTypeEnum(ProcessInstanceBizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT);
            terBo.setBizId(id);
            terBo.setRemark("采购预付单审批撤销" + getAdminName() + getAdminId());
            dingdingProcessInstanceService.terminateProcess(terBo);
        } catch (Exception e) {
            log.error("预付单审批撤销异常,adminId:{}", getAdminId(), e);
        }

        //更新预付单状态和审核记录
        purchaseAdvancedOrderMapper.update(updateOrder);
        logger.info("管理员：{}，撤销了预付单：{}", getAdminName(), id);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult listAll(int pageIndex, int pageSize, PurchaseAdvancedOrderVO purchaseAdvancedOrderVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseAdvancedOrderVO> purchaseAdvancedOrderVOList = purchaseAdvancedOrderMapper.listAll(purchaseAdvancedOrderVO);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseAdvancedOrderVOList));
    }

    /**
     * 关联了采购单的需要回退金额
     *
     * @param purchaseAdvancedOrder
     */
    public void supplierPaymentReset(PurchaseAdvancedOrder purchaseAdvancedOrder) {
        if (!Objects.equals(purchaseAdvancedOrder.getType(), ASSOCIATED)) {
            return;
        }
        PurchaseBindingPrepayment queryBind = new PurchaseBindingPrepayment();
        queryBind.setPurchaseAdvancedOrderId(purchaseAdvancedOrder.getId());
        List<PurchaseBindingPrepayment> purchaseBindingPrepayments = purchaseBindingPrepaymentMapper.queryAll(queryBind);
        if (CollectionUtils.isEmpty(purchaseBindingPrepayments)) {
            return;
        }
        PurchaseSupplierPayment updatePayment = new PurchaseSupplierPayment();
        PurchaseSupplierPayment queryPayment = new PurchaseSupplierPayment();
        for (PurchaseBindingPrepayment purchaseBindingPrepayment : purchaseBindingPrepayments) {
            String purchaseNo = purchaseBindingPrepayment.getPurchaseNo();
            Integer supplierId = purchaseAdvancedOrder.getSupplierId();
            BigDecimal advanceAmount = purchaseBindingPrepayment.getAdvanceAmount();
            queryPayment.setPurchaseNo(purchaseNo);
            queryPayment.setSupplierId(supplierId);
            PurchaseSupplierPayment purchaseSupplierPayment = purchaseSupplierPaymentMapper.selectOne(queryPayment);
            if (Objects.isNull(purchaseSupplierPayment)) {
                continue;
            }
            updatePayment.setId(purchaseSupplierPayment.getId());
            updatePayment.setAdvanceAmount(advanceAmount.negate());
            purchaseSupplierPaymentService.updateAmount(updatePayment);
            logger.info("预付单：{}审核不通过，供应商：{}，采购单号：{}，回退了预付金额：{}元", purchaseAdvancedOrder.getId(), purchaseAdvancedOrder.getSupplierName(), purchaseNo, advanceAmount);
        }
    }

    @Override
    public AjaxResult updateTemporaryRemark(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO) {
        Long id = purchaseAdvancedOrderVO.getId();
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(id);
        if (Objects.isNull(purchaseAdvancedOrder)) {
            return AjaxResult.getErrorWithMsg("未查询到该预付单");
        }
        PurchaseAdvancedOrder updateOrder = PurchaseAdvancedOrder.builder().id(id).temporaryRemark(purchaseAdvancedOrderVO.getTemporaryRemark()).build();
        purchaseAdvancedOrderMapper.update(updateOrder);
        logger.info("管理员：{}更新了预付单：{}的临时备注", getAdminName(), id);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approvedPurchaseAdvancedOrder(Long bizId, String handlerUserId) {
        logger.info("预付单审批回调通过" + bizId);
        //查询预付单信息
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(bizId);
        if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.INVALID.ordinal())) {
            //如果已经作废则不用处理
            return;
        }

        sendPayBill(bizId, purchaseAdvancedOrder);
        log.info("预付单审批回调通过" + bizId);


    }

    /**
     * 处理预付单 生成付款单
     * @param bizId
     * @param purchaseAdvancedOrder
     */
    private void sendPayBill(Long bizId,PurchaseAdvancedOrder purchaseAdvancedOrder){
        //改变预付单状态  并且发起付款审核
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(bizId);
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        updateOrder.setCurrentProcessor("请到 钉钉-OA审批—我发起的 中查看");

        purchaseAdvancedOrderMapper.update(updateOrder);
        financePaymentOrderService.generatePaymentInfo(purchaseAdvancedOrder);
    }

    /**
     * 付款单下个审批人查询
     *
     * @param total
     * @param type
     * @param supplierName
     * @param processCode
     * @param deptId
     * @param userId
     * @return
     */
    private ProcessForecastRequest submitPayForm(BigDecimal total, String type, String supplierName, String processCode, Integer deptId, String userId) {
        List<ProcessForecastRequest.ProcessForecastRequestFormComponentValues> processForecastRequestFormComponentValues = new ArrayList<>(16);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesSeven = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("打款对象")
                .setValue(supplierName);
        processForecastRequestFormComponentValues.add(formComponentValuesSeven);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesOne = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("发起人")
                .setValue("发起人");
        processForecastRequestFormComponentValues.add(formComponentValuesOne);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesTwo = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("来源类型")
                .setValue(type);
        processForecastRequestFormComponentValues.add(formComponentValuesTwo);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesThree = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("结算方式")
                .setValue("结算方式");
        processForecastRequestFormComponentValues.add(formComponentValuesThree);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFour = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("应付总额")
                .setValue(String.valueOf(total));
        processForecastRequestFormComponentValues.add(formComponentValuesFour);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFive = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("来源单号")
                .setValue("查询");
        processForecastRequestFormComponentValues.add(formComponentValuesFive);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesSix = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("单据详情")
                .setValue("详情");
        processForecastRequestFormComponentValues.add(formComponentValuesSix);
        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                .setProcessCode(processCode)
                .setDeptId(deptId)
                .setUserId(userId)
                .setFormComponentValues(processForecastRequestFormComponentValues);
        return processForecastRequest;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void refusePurchaseAdvancedOrder(Long bizId, String handlerUserId) {
        logger.info("预付单审批回调拒绝" + bizId);
        //查询预付单信息
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(bizId);
        if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.INVALID.ordinal())) {
            //如果已经作废则不用处理
            return;
        }

        //哪个环境生成的数据 只有在那个环境才能生成 修改相应的数据
        StringBuilder stringBuilder = new StringBuilder();
        DingdingProcessFlow flow = dingdingProcessFlowMapper.selectOneByBizIdAndBizType(bizId, BizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT.getId());
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = DingTalkUtils.processInstanceDetail(flow.getProcessInstanceId());
        for (OapiProcessinstanceGetResponse.FormComponentValueVo formComponentValueVo : processInstanceTopVo.getFormComponentValues()) {
            if (Objects.equals(formComponentValueVo.getName(), ADVANCE_DETAIL)) {
                stringBuilder.append(formComponentValueVo.getValue());
                continue;
            }
        }

        String[] split = stringBuilder.toString().split("\\?");
        if (SpringContextUtil.isProduct() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_CHECK)) {
            failAdvance(bizId, purchaseAdvancedOrder, handlerUserId);
        } else if (SpringContextUtil.isQa() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_QA_CHECK)) {
            failAdvance(bizId, purchaseAdvancedOrder, handlerUserId);
            log.info("qa预付单审批回调拒绝" + bizId);
        } else if (SpringContextUtil.isDev() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV_CHECK)) {
            failAdvance(bizId, purchaseAdvancedOrder, handlerUserId);
            log.info("dev预付单审批回调拒绝" + bizId);
        } else if (SpringContextUtil.isDev2() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2_CHECK)) {
            failAdvance(bizId, purchaseAdvancedOrder, handlerUserId);
            log.info("dev2预付单审批回调拒绝" + bizId);
        }
    }

    /**
     * 预付单审批回调拒绝
     * @param bizId
     * @param purchaseAdvancedOrder
     * @param handlerUserId
     */
    private void failAdvance(Long bizId, PurchaseAdvancedOrder purchaseAdvancedOrder, String handlerUserId) {
        //改变预付单状态
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(bizId);
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        updateOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.APPROVE_FAIL.ordinal());
        updateOrder.setCurrentProcessor("--");
        purchaseAdvancedOrderMapper.update(updateOrder);
        settleFlagReset(bizId, purchaseAdvancedOrder.getSupplierId());
//        supplierPaymentReset(purchaseAdvancedOrder);
        advancedMessage(handlerUserId, bizId, FinancePersonnelType.CHECKER.ordinal(), FinancePurchaseTypeEnum.ADVANCE.ordinal(), CommonNumbersEnum.ONE.getNumber());
    }

    /**
     * 预付单操作人
     *
     * @param handlerUserId
     * @param additionalId
     * @param personnelType
     * @param type
     * @param operationResults
     */
    private void advancedMessage(String handlerUserId, Long additionalId, Integer personnelType, Integer type, Integer operationResults) {
        //预付单审核人人
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLog.setPersonnelType(personnelType);
        financeOperatorLog.setAdditionalId(additionalId);
        financeOperatorLog.setType(type);
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        //查询创建人
        Admin admin = adminMapper.selectByAid(Integer.valueOf(handlerUserId));
        if (!ObjectUtils.isEmpty(admin)) {
            financeOperatorLog.setOperationResults(operationResults);
            financeOperatorLog.setOperatorId(admin.getAdminId());
            financeOperatorLog.setOperator(admin.getRealname());
            financeOperatorLogMapper.insertSelective(financeOperatorLog);
        }
    }

    @Override
    public void terminatePurchaseAdvancedOrder(Long bizId) {
        logger.info("预付单审批回调撤销" + bizId);
        //查询预付单信息
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(bizId);
        if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.INVALID.ordinal())) {
            //如果已经作废则不用处理
            return;
        }


        //哪个环境生成的数据 只有在那个环境才能生成 修改相应的数据
        StringBuilder stringBuilder = new StringBuilder();
        DingdingProcessFlow flow = dingdingProcessFlowMapper.selectOneByBizIdAndBizType(bizId, BizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT.getId());
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = DingTalkUtils.processInstanceDetail(flow.getProcessInstanceId());
        for (OapiProcessinstanceGetResponse.FormComponentValueVo formComponentValueVo : processInstanceTopVo.getFormComponentValues()) {
            if (Objects.equals(formComponentValueVo.getName(), ADVANCE_DETAIL)) {
                stringBuilder.append(formComponentValueVo.getValue());
                continue;
            }
        }

        String[] split = stringBuilder.toString().split("\\?");
        if (SpringContextUtil.isProduct() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_CHECK)) {
            cancelAdvance(bizId, purchaseAdvancedOrder);
        } else if (SpringContextUtil.isQa() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_QA_CHECK)) {
            cancelAdvance(bizId, purchaseAdvancedOrder);
            log.info("qa预付单审批回调撤销" + bizId);
        } else if (SpringContextUtil.isDev() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV_CHECK)) {
            cancelAdvance(bizId, purchaseAdvancedOrder);
            log.info("dev预付单审批回调撤销" + bizId);
        } else if (SpringContextUtil.isDev2() && Objects.equals(split[CommonNumbersEnum.ZERO.getNumber()], Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2_CHECK)) {
            cancelAdvance(bizId, purchaseAdvancedOrder);
            log.info("dev2预付单审批回调撤销" + bizId);
        }

    }

    /**
     * 撤销预付单回调修改数据
     * @param bizId
     * @param purchaseAdvancedOrder
     */
    private void cancelAdvance(Long bizId, PurchaseAdvancedOrder purchaseAdvancedOrder) {
        //改变预付单状态
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(bizId);
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        updateOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.WITHDRAWAL_OF_APPLICATION.ordinal());
        updateOrder.setCurrentProcessor("--");
        purchaseAdvancedOrderMapper.update(updateOrder);
        settleFlagReset(bizId, purchaseAdvancedOrder.getSupplierId());
//        supplierPaymentReset(purchaseAdvancedOrder);

        //预付单撤销人
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        financeOperatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        financeOperatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        //查询创建人
        FinanceOperatorLog creator = financeOperatorLogMapper.selectById(financeOperatorLog);
        if (!ObjectUtils.isEmpty(creator)) {
            financeOperatorLog.setOperationResults(CommonNumbersEnum.ONE.getNumber());
            financeOperatorLog.setOperatorId(creator.getOperatorId());
            financeOperatorLog.setOperator(creator.getOperator());
            financeOperatorLog.setPersonnelType(FinancePersonnelType.REVOKE.ordinal());
            financeOperatorLogMapper.insertSelective(financeOperatorLog);
        }

    }

    /**
     * 审核状态信息改变 加入预付审核人信息
     *
     * @param id
     * @param handlerUserId
     */
    private void updateByRecord(Long id, String handlerUserId) {
        //审核状态信息改变 加入预付审核人信息
        FinanceAuditRecord financeAuditRecord = new FinanceAuditRecord();
        financeAuditRecord.setAdditionalId(id);
        financeAuditRecord.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        Admin admin = adminMapper.selectByAid(Integer.valueOf(handlerUserId));
        financeAuditRecord.setAuditor(admin.getRealname());
        financeAuditRecord.setAuditTime(LocalDateTime.now());
        financeAuditRecord.setAuditorAdminId(Integer.valueOf(handlerUserId));
        financeAuditRecordMapper.updateByPrimaryKeySelective(financeAuditRecord);
    }

    @Override
    public AjaxResult auditInformation(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO) {
        //校验参数
        Integer supplierId = purchaseAdvancedOrderVO.getSupplierId();
        Supplier supplier = supplierMapper.selectByPrimaryKey(supplierId);
        if (Objects.isNull(supplier)) {
            return AjaxResult.getErrorWithMsg("请选择正确的供应商");
        }
        Integer supplierAccountId = purchaseAdvancedOrderVO.getSupplierAccountId();
        SupplierAccount supplierAccount = supplierAccountMapper.selectById(supplierAccountId);
        if (Objects.isNull(supplierAccount)) {
            return AjaxResult.getErrorWithMsg("未查询到供应商付款账号信息");
        }
        BigDecimal totalAmount = purchaseAdvancedOrderVO.getTotalAmount();
        if (totalAmount.scale() > TWO || totalAmount.compareTo(TEN_DIGIT) > 0 || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return AjaxResult.getErrorWithMsg("请重新输入符合条件的预付金额");
        }
        AdminAuthExtend creatorExtend = adminAuthExtendRepository.selectByAdminId(DING_DING, getAdminId());

        if (Objects.isNull(creatorExtend) || Objects.isNull(creatorExtend.getUserId())) {
            return AjaxResult.getErrorWithMsg("检测到您没有绑定钉钉后台，流程无法继续，请绑定后再尝试发起");
        }

        if (Objects.equals(purchaseAdvancedOrderVO.getType(), ASSOCIATED)) {
            for (PurchasesVO purchasesVO : purchaseAdvancedOrderVO.getPurchasesVOList()) {
                purchasesService.checkAdvancePrepaymentBefore(purchasesVO.getPurchaseNo(), supplierId, purchasesVO.getAdvanceAmount());

            }
        }

        DepartmentStaff departmentStaff = departmentStaffMapper.findOneByUserId(creatorExtend.getUserId());
        Long departmentId = null;
        if (departmentStaff != null) {
            departmentId = departmentStaff.getDeptId();
        }
        // 如果从组织架构中未成功获取到组织架构信息，则调用钉钉职工查询接口进行查询
        if (departmentId == null) {
            OapiV2UserGetResponse.UserGetResponse dingdingUserInfo = DingTalkUtils.getDingdingUserInfo(creatorExtend.getUserId());
            if (dingdingUserInfo == null) {
                try {
                    throw new DingdingProcessException("未查询到钉钉用户信息:" + creatorExtend.getUserId());
                } catch (DingdingProcessException e) {
                    e.printStackTrace();
                }
            }
            List<Long> deptIdList = dingdingUserInfo.getDeptIdList();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(deptIdList)) {
                departmentId = deptIdList.get(0);
            }
        }
        int value = departmentId.intValue();

        //审批流信息
        Config config = configMapper.selectOne(DingdingConstantKey.PURCHASE_ADVANCED_ORDER_APPROVAL_CODE);

        //获取审批单流程中的节点信息
        try {
            ProcessForecastRequest processForecastRequest = submitForm(purchaseAdvancedOrderVO, supplier, config.getValue(), value, creatorExtend.getUserId(), getAdminName());
            ProcessForecastResponse processForecastResponse = DingTalkUtils.approvalDocumentProcess(processForecastRequest);
            ProcessForecastResponseBody.ProcessForecastResponseBodyResult result = processForecastResponse.getBody().getResult();
            if (!CollectionUtils.isEmpty(result.getWorkflowActivityRules().get(0).getWorkflowActor().getActorSelectionRange().getApprovals())) {
                ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRulesWorkflowActorActorSelectionRangeApprovals approvals = result.getWorkflowActivityRules().get(0).getWorkflowActor().getActorSelectionRange().getApprovals().get(0);
                String workNo = approvals.getWorkNo();
                logger.info("对账单钉钉账号" + workNo);
                String userName = approvals.getUserName();
                logger.info("对账单钉钉账号名称" + userName);
                AdminAuthExtend adminAuthExtend = adminAuthExtendRepository.selectByUserId(CommonNumbersEnum.ZERO.getNumber(), workNo);
                if (ObjectUtils.isEmpty(adminAuthExtend)) {
                    //为空说明流程直接跳过
                    return AjaxResult.getErrorWithMsg("无下一位审批人");
                }
                JSONObject data = new JSONObject();
                data.put("userId", workNo);
                data.put("audit", userName);
                data.put("auditorAdminId", adminAuthExtend.getAdminId());
                return AjaxResult.getOK(data);
            }
            return AjaxResult.getOK();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增预付单审批人展示出错", e);
        }

        return AjaxResult.getOK();
    }

    /**
     * 组装表单
     *
     * @param purchaseAdvancedOrderVO
     * @param supplier
     * @param processCode
     * @param deptId
     * @param userId
     * @param userName
     * @return
     */
    private ProcessForecastRequest submitForm(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO, Supplier supplier, String processCode, Integer deptId, String userId, String userName) {
        List<ProcessForecastRequest.ProcessForecastRequestFormComponentValues> processForecastRequestFormComponentValues = new ArrayList<>(16);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesOne = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("供应商")
                .setValue(String.valueOf(supplier.getName()));
        processForecastRequestFormComponentValues.add(formComponentValuesOne);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesTwo = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("采购员")
                .setValue(userName);
        processForecastRequestFormComponentValues.add(formComponentValuesTwo);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesThree = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("预付总额")
                .setValue(String.valueOf(purchaseAdvancedOrderVO.getTotalAmount()));
        processForecastRequestFormComponentValues.add(formComponentValuesThree);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFour = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("预付单号")
                .setValue("查询");
        processForecastRequestFormComponentValues.add(formComponentValuesFour);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFive = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("预付详情")
                .setValue("详情");
        processForecastRequestFormComponentValues.add(formComponentValuesFive);
        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                .setProcessCode(processCode)
                .setDeptId(deptId)
                .setUserId(userId)
                .setFormComponentValues(processForecastRequestFormComponentValues);
        return processForecastRequest;
    }

    /**
     * 审批预付单钉钉审批发起
     *
     * @param purchaseAdvancedOrder
     * @param supplierReq
     */
    private void createProcessInstance(PurchaseAdvancedOrder purchaseAdvancedOrder, SupplierReq supplierReq) {

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(super.getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(purchaseAdvancedOrder.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(16);

        DingdingFormBO supplierName = new DingdingFormBO();
        supplierName.setFormName("供应商");
        supplierName.setFormValue(supplierReq.getName());
        dingForms.add(supplierName);

        DingdingFormBO purchaser = new DingdingFormBO();
        purchaser.setFormName("采购员");
        purchaser.setFormValue(getAdminName());
        dingForms.add(purchaser);

        DingdingFormBO settlementMethod = new DingdingFormBO();
        settlementMethod.setFormName("结算方式");
        StringBuilder stringBuilder = new StringBuilder();
        if (!ObjectUtils.isEmpty(supplierReq.getSettleType())) {
            if (Objects.equals(supplierReq.getSettleType(), 0)) {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 30天账期")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            } else {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 ")
                        .append(supplierReq.getCustomCycle())
                        .append("天账期 ")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            }

        } else {
            stringBuilder.append("暂无合同");
        }
        settlementMethod.setFormValue(stringBuilder.toString());
        dingForms.add(settlementMethod);

        DingdingFormBO totalBill = new DingdingFormBO();
        totalBill.setFormName("预付总额");
        totalBill.setFormValue(String.valueOf(purchaseAdvancedOrder.getTotalAmount()));
        dingForms.add(totalBill);

        DingdingFormBO financeAccountId = new DingdingFormBO();
        financeAccountId.setFormName("预付单号");
        financeAccountId.setFormValue(String.valueOf(purchaseAdvancedOrder.getId()));
        dingForms.add(financeAccountId);

        DingdingFormBO detail = new DingdingFormBO();
        detail.setFormName("预付详情");
        if (SpringContextUtil.isProduct()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS + purchaseAdvancedOrder.getId());
        } else if (SpringContextUtil.isQa()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_QA + purchaseAdvancedOrder.getId());
        } else if (SpringContextUtil.isDev()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV + purchaseAdvancedOrder.getId());
        } else {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2 + purchaseAdvancedOrder.getId());
        }
        dingForms.add(detail);

        processInstanceCreateBO.setDingdingForms(dingForms);
        try {
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        } catch (DingdingProcessException e) {
            logger.info("钉钉调用失败:{}", e.getMessage(), e);
            throw new DefaultServiceException(e.getMessage());
        }
    }

    @Override
    public AjaxResult operationRecord(Long id) {
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setAdditionalId(id);
        financeOperatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        List<FinanceOperatorLog> financeOperatorLogs = financeOperatorLogMapper.selectByAdvance(financeOperatorLog);
        return AjaxResult.getOK(financeOperatorLogs);
    }


    @Override
    public void approvedPurchaseAdvancedOrderTask(Long bizId, String handlerUserId) {
        //查询预付单信息
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(bizId);
        if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.INVALID.ordinal())) {
            //如果已经作废则不用处理
            return;
        }
        logger.info("预付单审批任务回调通过" + bizId);
        advancedMessage(handlerUserId, bizId, FinancePersonnelType.CHECKER.ordinal(), FinancePurchaseTypeEnum.ADVANCE.ordinal(), CommonNumbersEnum.ZERO.getNumber());
    }

    @Override
    public void refusePurchaseAdvancedOrderTask(Long bizId, String handlerUserId) {
        logger.info("预付单审批任务回调拒绝" + bizId);
        advancedMessage(handlerUserId, bizId, FinancePersonnelType.CHECKER.ordinal(), FinancePurchaseTypeEnum.ADVANCE.ordinal(), CommonNumbersEnum.ONE.getNumber());
    }


}
