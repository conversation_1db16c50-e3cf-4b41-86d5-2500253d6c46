package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.XianmuCardStatisticsMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.MerchantCouponService;
import net.summerfarm.service.RechargeRecordService;
import net.summerfarm.service.RechargeService;
import net.xianmu.common.exception.ProviderException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class RechargeServiceImpl extends BaseService implements RechargeService {

    private static final Logger logger = LoggerFactory.getLogger(RechargeService.class);

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private RechargeMapper rechargeMapper;
    @Lazy
    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;

    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private RechargePicMapper rechargePicMapper;
    @Resource
    private CouponConfigMapper couponConfigMapper;
    @Resource
    private CouponConfigDetailMapper couponConfigDetailMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private FinanceBankFlowingWaterMapper financeBankFlowingWaterMapper;
    @Resource
    private XianmuCardStatisticsMapper xianmuCardStatisticsMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(RechargeQuery recharge) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(recharge.getmId());
        String mname = merchant.getMname();
        if (ObjectUtils.isEmpty(merchant)){
            return AjaxResult.getErrorWithMsg("该用户不存在!");
        }
        if ("大客户".equals(merchant.getSize()) && !ObjectUtils.isEmpty(merchant.getDirect()) && Objects.equals(merchant.getDirect(),1)){
            return AjaxResult.getErrorWithMsg("账期门店客户无充值/退还功能!");
        }
        if (CollectionUtils.isEmpty(recharge.getPic())) {
            return AjaxResult.getErrorWithMsg("充值必须上传打款凭证");
        }
        if (Objects.equals(recharge.getFundType(), RechargeFundTypeEnum.RECHARGE_CARD_PURCHASE.getStatus())){
            if (ObjectUtils.isEmpty(recharge.getSendCoupon())){
                return AjaxResult.getErrorWithMsg("sendCoupon不可为空");
            }
            rechargeCardPurchase(recharge,mname);
            return AjaxResult.getOK();
        }else if(Objects.equals(recharge.getFundType(), RechargeFundTypeEnum.BALANCE_REFUND.getStatus())){
            balanceRefund(recharge,mname);
            return AjaxResult.getOK();
        }else {
            return AjaxResult.getError("不存在充值/退还外的功能");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult batchSave(RechargeQuery rechargeQueries) {

        //判断收款流水的类型是否是鲜沐卡
        FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(rechargeQueries.getFinanceBankFlowingWaterId());
        if (Objects.equals(financeBankFlowingWater.getPayType(), FinanceReceiptPayTypeEnum.BILL.ordinal())) {
            return AjaxResult.getError("该收款流水已经有过认领，与当前认领类型不同,请认领相应的类型");
        }

        //查询该收款流水可认领金额和充值金额总和的对比
        BigDecimal waterMoney = rechargeMapper.selectWater(rechargeQueries.getFinanceBankFlowingWaterId());
        BigDecimal rechargeNum = rechargeQueries.getRechargesList().stream().map(RechargeQuery::getRechargeNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.equals((financeBankFlowingWater.getTransactionAmount().subtract(waterMoney)).compareTo(rechargeNum), -1)) {
            return AjaxResult.getErrorWithMsg("认领金额大于可认领金额，请调整后重新申请");
        }

        for (RechargeQuery rechargeQuery : rechargeQueries.getRechargesList()) {
            Merchant merchant = merchantMapper.selectByPrimaryKey(rechargeQuery.getmId());
            String mname = merchant.getMname();
            if (ObjectUtils.isEmpty(merchant)) {
                return AjaxResult.getErrorWithMsg("该用户不存在!");
            }
            if ("大客户".equals(merchant.getSize()) && !ObjectUtils.isEmpty(merchant.getDirect()) && Objects.equals(merchant.getDirect(), 1)) {
                return AjaxResult.getErrorWithMsg("账期门店客户" + mname + "无充值功能!");
            }
            if (CollectionUtils.isEmpty(rechargeQuery.getPic())) {
                return AjaxResult.getErrorWithMsg(mname + "充值必须上传打款凭证");
            }
            rechargeQuery.setFinanceBankFlowingWaterId(rechargeQueries.getFinanceBankFlowingWaterId());
            rechargeCardPurchase(rechargeQuery, mname);
        }

        if (!Objects.equals(financeBankFlowingWater.getPayType(), FinanceReceiptPayTypeEnum.CARD.ordinal())) {
            //为空说明第一次认领，修改
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setId(rechargeQueries.getFinanceBankFlowingWaterId());
            flowingWater.setPayType(FinanceReceiptPayTypeEnum.CARD.ordinal());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
        }

        //查询收款流水是否认领完，认领完改变状态为全部认领，有认领则为部分认领
        //查询该收款流水可认领金额和充值金额总和的对比
        if (Objects.equals(waterMoney.compareTo(financeBankFlowingWater.getTransactionAmount()), 0)) {
            //相等则全部认领
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.CLAIM_ALL.ordinal());
            flowingWater.setId(rechargeQueries.getFinanceBankFlowingWaterId());
            flowingWater.setUpdater(getAdminName());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
            return AjaxResult.getOK();
        }
        //否则部分认领
        FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
        flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
        flowingWater.setId(rechargeQueries.getFinanceBankFlowingWaterId());
        flowingWater.setUpdater(getAdminName());
        financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);

        return AjaxResult.getOK();
    }

    /**
     * 充值申请
     * @param recharge
     * @param mname
     */
    private void rechargeCardPurchase(RechargeQuery recharge,String mname){
        Recharge insert = new Recharge();
        insert.setmId(recharge.getmId());
        insert.setRechargeNum(recharge.getRechargeNum());
        insert.setRechargeType(0);
        insert.setApplicant(getAdminId());
        insert.setStatus(0);
        insert.setAddtime(LocalDateTime.now());
        insert.setUpdatetime(LocalDateTime.now());
        insert.setSendCoupon(recharge.getSendCoupon());
        insert.setFundType(RechargeFundTypeEnum.RECHARGE_CARD_PURCHASE.getStatus());
        insert.setLevel(recharge.getLevel());
        if (!ObjectUtils.isEmpty(recharge.getFinanceBankFlowingWaterId())) {
            insert.setFinanceBankFlowingWaterId(recharge.getFinanceBankFlowingWaterId());
        }

        String rechargeNo = String.valueOf(SnowflakeUtil.nextId());
        Recharge selectKey = new Recharge();
        selectKey.setRechargeNo(rechargeNo);
        Recharge record = rechargeMapper.selectOne(selectKey);
        if (!ObjectUtils.isEmpty(record)){
            throw new DefaultServiceException("充值编号已存在!");
        }
        insert.setRechargeNo(rechargeNo);
        rechargeMapper.insert(insert);
        RechargePic rechargePic = new RechargePic();
        rechargePic.setRechargeId(insert.getId());
        rechargePic.setStatus(0);
        rechargePic.setCreator(getAdminName());
        rechargePic.setUpdater(getAdminName());
        rechargePic.setCreateTime(LocalDateTime.now());
        rechargePic.setUpdateTime(LocalDateTime.now());
        for (String pic : recharge.getPic()) {
            rechargePic.setPic(pic);
            rechargePicMapper.insertSelective(rechargePic);
        }
        logger.info("管理员{}发起了{}的充值申请,充值编号为：{}",getAdminName(),mname,insert.getRechargeNo());
    }

    /**
     * 退款申请
     * @param recharge
     * @param mname
     */
    private void balanceRefund(RechargeQuery recharge,String mname){
        Recharge add = new Recharge();
        add.setmId(recharge.getmId());
        add.setRechargeNum(recharge.getRechargeNum());
        add.setRechargeType(0);
        add.setApplicant(getAdminId());
        add.setStatus(0);
        add.setAddtime(LocalDateTime.now());
        add.setUpdatetime(LocalDateTime.now());
        add.setFundType(RechargeFundTypeEnum.BALANCE_REFUND.getStatus());
        add.setRemarks(recharge.getRemarks());
        String rechargeNo = String.valueOf(SnowflakeUtil.nextId());
        Recharge selectKey = new Recharge();
        selectKey.setRechargeNo(rechargeNo);
        Recharge record = rechargeMapper.selectOne(selectKey);
        if (!ObjectUtils.isEmpty(record)){
            throw new DefaultServiceException("退款编号已存在!");
        }
        add.setRechargeNo(rechargeNo);
        rechargeMapper.insertRefund(add);
        RechargePic rechargePic = new RechargePic();
        rechargePic.setRechargeId(add.getId());
        rechargePic.setStatus(0);
        rechargePic.setCreator(getAdminName());
        rechargePic.setUpdater(getAdminName());
        rechargePic.setCreateTime(LocalDateTime.now());
        rechargePic.setUpdateTime(LocalDateTime.now());
        if (!CollectionUtils.isEmpty(recharge.getPic())) {
            for (String pic : recharge.getPic()) {
                rechargePic.setPic(pic);
                rechargePicMapper.insertSelective(rechargePic);
            }
        }
        logger.info("管理员{}发起了{}的退款申请,退款编号为：{}",getAdminName(),mname,add.getRechargeNo());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult handle(Recharge recharge) {
        Recharge record = rechargeMapper.selectByPrimaryKey(recharge.getId());
        Recharge update = updateHandle(recharge,record);
        Recharge selectByPrimaryKey = rechargeMapper.selectByPrimaryKey(recharge.getId());
        if (RechargeStatus.SUCCESS.getStatus().equals(recharge.getStatus())){
            logger.info("管理员{}通过了{}的充值申请",getAdminName(),record.getRechargeNo());
            rechargeMapper.update(update);
            rechargeRecordService.insert(record.getmId(), RechargeRecordType.RECHARGE.getId(),record.getRechargeNo(),record.getRechargeNum());
            //当充值申请通过后，根据不同的充值金额，发放不同的优惠券,不满足充送直接跳过
            if (!Objects.equals(record.getLevel(),0) && !ObjectUtils.isEmpty(record.getLevel())){
                merchantCouponService.autoSendCoupon(record);
            }
            //查询收款流水是否认领完，认领完改变状态为全部认领，有认领则为部分认领
            //查询该收款流水可认领金额和充值金额总和的对比
            BigDecimal waterMoney = rechargeMapper.selectWater(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            FinanceBankFlowingWater bankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            if (ObjectUtils.isEmpty(bankFlowingWater) || !Objects.equals(bankFlowingWater.getPayType(), FinanceReceiptPayTypeEnum.CARD.ordinal())){
                throw new DefaultServiceException("该充值记录关联的收款流水收款类型不为鲜沐卡");
            }
            if (Objects.equals(waterMoney.compareTo(bankFlowingWater.getTransactionAmount()), 0)) {
                //相等则全部认领
                FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.CLAIM_ALL.ordinal());
                flowingWater.setId(selectByPrimaryKey.getFinanceBankFlowingWaterId());
                flowingWater.setUpdater(getAdminName());
                financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
                return AjaxResult.getOK();
            }
            //否则部分认领
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
            flowingWater.setId(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            flowingWater.setUpdater(getAdminName());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
            return AjaxResult.getOK();
        }else {
            logger.info("管理员{}拒绝了{}的充值申请",getAdminName(),recharge.getRechargeNo());
            rechargeMapper.update(update);
            //查询收款流水是否无对应鲜沐卡认领且没有新的账期账单认领，更改状态为待认领
            BigDecimal waterMoney = rechargeMapper.selectWater(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            FinanceBankFlowingWater bankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            if (ObjectUtils.isEmpty(bankFlowingWater) || !Objects.equals(bankFlowingWater.getPayType(), FinanceReceiptPayTypeEnum.CARD.ordinal())){
                throw new DefaultServiceException("该充值记录关联的收款流水收款类型不为鲜沐卡");
            }
            if (Objects.equals(waterMoney.compareTo(BigDecimal.ZERO), 0)) {
                //如果收款流水认领为0 则带给待收款
                FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal());
                flowingWater.setId(selectByPrimaryKey.getFinanceBankFlowingWaterId());
                flowingWater.setUpdater(getAdminName());
                flowingWater.setPayType(null);
                financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
                return AjaxResult.getOK();
            }
            //否则部分认领
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
            flowingWater.setId(selectByPrimaryKey.getFinanceBankFlowingWaterId());
            flowingWater.setUpdater(getAdminName());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
            return AjaxResult.getOK();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(ReceiptBillConfirmInput input) {
        for (ReceiptBillConfirmInfo info : input.getReceiptBillDetailEntities()) {
            boolean isSuccess=ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId().equals(input.getWriteOffStatus());
            Integer status = isSuccess ? RechargeStatus.SUCCESS.getStatus() : RechargeStatus.FAIL.getStatus();
            Recharge record = rechargeMapper.selectByPrimaryKey(info.getFinanceOrderId());
            record.setStatus(status);
            record.setHandler(input.getAuditorId());
            record.setUpdatetime(input.getAuditTime());
            int update = rechargeMapper.update(record);
            if (isSuccess && update > 0) {
                rechargeRecordService.insert(record.getmId(), RechargeRecordType.RECHARGE.getId(), record.getRechargeNo(), record.getRechargeNum());
                //当充值申请通过后，根据不同的充值金额，发放不同的优惠券,不满足充送直接跳过
                if (!Objects.equals(record.getLevel(), 0) && !ObjectUtils.isEmpty(record.getLevel())) {
                    merchantCouponService.autoSendCoupon(record);
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult refund(Recharge recharge) {
        Recharge record = rechargeMapper.selectByPrimaryKey(recharge.getId());
        Recharge update = updateHandle(recharge,record);
        if (RechargeStatus.SUCCESS.getStatus().equals(recharge.getStatus())){
            logger.info("管理员{}通过了{}的退款申请",getAdminName(),record.getRechargeNo());
            rechargeMapper.update(update);
            rechargeRecordService.update(record.getmId(), ConsumptionTypeEnum.MANUAL_REFUND.getId(),record.getRechargeNo(),record.getRechargeNum());
        }else {
            logger.info("管理员{}拒绝了{}的退款申请",getAdminName(),recharge.getRechargeNo());
            rechargeMapper.update(update);
        }
        return AjaxResult.getOK();
    }

    /**
     * 充值/退还审批
     * @return
     */
    private Recharge updateHandle(Recharge recharge,Recharge record){
        if (ObjectUtils.isEmpty(record)){
            throw new DefaultServiceException("参数有误!");
        }
        if (!Objects.equals(record.getStatus(),RechargeStatus.WAIT_HANDLE.getStatus())){
            throw new DefaultServiceException("该申请已被审核!");
        }
        Recharge update = new Recharge();
        update.setId(record.getId());
        update.setHandler(getAdminId());
        update.setStatus(recharge.getStatus());
        update.setUpdatetime(LocalDateTime.now());
        update.setRemark(recharge.getRemark());
        return update;
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, RechargeVO selectKey) {
        PageHelper.startPage(pageIndex,pageSize);
        List<RechargeVO> rechargeVOS = rechargeMapper.select(selectKey);
        if (CollectionUtils.isEmpty(rechargeVOS)) {
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(Lists.newArrayList()));
        }
        List<Long> mIds = rechargeVOS.stream().map(x -> x.getmId()).distinct()
                .collect(Collectors.toList());
        List<Merchant> merchants = merchantMapper.listByMIds(mIds);
        Map<Long, Merchant> merchantMap = merchants.stream().distinct()
                .collect(Collectors.toMap(Merchant::getmId, Function.identity()));
        for (RechargeVO vo : rechargeVOS) {
            Merchant merchant = merchantMap.get(vo.getmId());
            if (merchant == null) {
                continue;
            }
            vo.setRechargeAmount(merchant.getRechargeAmount());
            vo.setMname(merchant.getMname());
            vo.setPhone(merchant.getPhone());
            vo.setRechargeAmount(merchant.getRechargeAmount());
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(rechargeVOS));
    }

    @Override
    public AjaxResult rechargeRecordSelect(int pageIndex, int pageSize, RechargeRecordVO selectKey) {
        PageHelper.startPage(pageIndex, pageSize);
        List<RechargeRecordVO> rechargeRecordVOS = rechargeRecordMapper.selectRechargeRecord(selectKey);
        rechargeRecordVOS.forEach(o -> {
            if (RechargeRecordType.RECHARGE.getId().equals(o.getType())) {
                if (o.getRecordNo()==null){
                    return;
                }
                RechargeVO rechargeVO = rechargeMapper.selectVO(o.getRecordNo());
                if (rechargeVO != null) {
                    o.setRecorder(rechargeVO.getApplicantName());
                }
            } else {
                MerchantSubAccount account = merchantSubAccountMapper.selectByPrimaryKeyIgnoreDel(o.getAccountId());
                Optional.ofNullable(account).ifPresent(el -> o.setRecorder(account.getContact()));
            }
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(rechargeRecordVOS));
    }

    @Override
    public AjaxResult rechargeConfiguration() {
        List<CouponConfigVO> couponConfigVOS = couponConfigMapper.selectAll();
        CouponConfigVOS list = new CouponConfigVOS();
        list.setCouponConfigVOS(couponConfigVOS);
        Config singleSwitch = configMapper.selectOne("singleSwitch");
        Config brandSwitch = configMapper.selectOne("brandSwitch");
        Config customAmount = configMapper.selectOne("customAmount");
        list.setSingleSwitch(Integer.valueOf(singleSwitch.getValue()));
        list.setBrandSwitch(Integer.valueOf(brandSwitch.getValue()));
        list.setCustomAmount(Integer.valueOf(customAmount.getValue()));
        return AjaxResult.getOK(list);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public AjaxResult update(CouponConfigVOS couponConfigVOS) {
        //将原有规则过期
        couponConfigMapper.deleteByPrimaryKey();
        couponConfigDetailMapper.deleteByPrimaryKey();
        for (CouponConfigVO c :couponConfigVOS.getCouponConfigVOS()){
            if (ObjectUtils.isEmpty(c.getBuyMoney())){
                return AjaxResult.getErrorWithMsg("购卡金额不能为空");
            }
            c.setCreator(getAdminName());
            c.setUpdater(getAdminName());
            //插入每个level优惠券充送信息
            couponConfigMapper.insertSelective(c);
            for (CouponConfigDetail d :c.getCouponConfigDetails()){
                if (!Objects.equals(d.getName(),ChargingTypeEnum.NONE.getId())){
                    if (ObjectUtils.isEmpty(d.getMoney())){
                        return AjaxResult.getErrorWithMsg("金额不能为空");
                    }
                    if (ObjectUtils.isEmpty(d.getThreshold())){
                        return AjaxResult.getErrorWithMsg("门槛不能为空");
                    }
                    if (ObjectUtils.isEmpty(d.getNumber())){
                        return AjaxResult.getErrorWithMsg("张数不能为空");
                    }
                    if (ObjectUtils.isEmpty(d.getEffectiveTime())){
                        return AjaxResult.getErrorWithMsg("有效天数不能为空");
                    }
                }
                //生成每个挡位所有优惠券
                d.setCouponConfigId(c.getId());
                d.setUpdater(getAdminName());
                d.setCreator(getAdminName());
                couponConfigDetailMapper.insertSelective(d);
                //如果没有设置赠券类别则直接不过判断优惠券
                if(!ObjectUtils.isEmpty(d.getName()) && !Objects.equals(d.getName(),ChargingTypeEnum.NONE.getId())){
                    rechargeCoupon(d);
                }
            }
        }
        configMapper.updateValue("singleSwitch",couponConfigVOS.getSingleSwitch().toString());
        configMapper.updateValue("brandSwitch",couponConfigVOS.getBrandSwitch().toString());
        configMapper.updateValue("customAmount",couponConfigVOS.getCustomAmount().toString());
        return AjaxResult.getOK();
    }

    /**
     * 充送优惠券
     */
    private void rechargeCoupon(CouponConfigDetail couponConfigDetail){
        Coupon coupon = new Coupon();
        coupon.setMoney(couponConfigDetail.getMoney());
        coupon.setThreshold(couponConfigDetail.getThreshold());
        coupon.setVaildTime(couponConfigDetail.getEffectiveTime());
        if (Objects.equals(couponConfigDetail.getName(),ChargingTypeEnum.FRUITS.getId())){
            //水果
            coupon.setName(ChargingTypeEnum.FRUITS.getStatus());
            Config freshFruit = configMapper.selectOne("freshFruit");
            if (!ObjectUtils.isEmpty(freshFruit)){
                coupon.setCategoryId(freshFruit.getValue());
            }
        }else if (Objects.equals(couponConfigDetail.getName(),ChargingTypeEnum.DAIRY.getId())){
            coupon.setName(ChargingTypeEnum.DAIRY.getStatus());
            //乳制品
            Config dairyFilling = configMapper.selectOne("dairyFilling");
            if (!ObjectUtils.isEmpty(dairyFilling)){
                coupon.setCategoryId(dairyFilling.getValue());
            }
        }else {
            coupon.setName(ChargingTypeEnum.WHOLE_PRODUCT.getStatus());
            coupon.setCategoryId("{}");
        }
        coupon.setSku("{}");
        int selectCoupon = couponMapper.selectCoupon(coupon);
        if(Objects.equals(selectCoupon,0)){
            coupon.setQuantityClaimed(CouponEnum.LimitNumberOrSheets.UNLIMITED.ordinal());
            coupon.setGrantLimit(CouponEnum.LimitNumberOrSheets.UNLIMITED.ordinal());
            coupon.setCreator(getAdminName());
            couponMapper.insertAuto(coupon);
            Integer id = coupon.getId();
            couponConfigDetailMapper.updateByCouponId(id,couponConfigDetail.getId());
        }else {
            //如果优惠券存在则将优惠券id存入
            Coupon selectByCoupon = couponMapper.selectByCoupon(coupon);
            couponConfigDetailMapper.updateByCouponId(selectByCoupon.getId(),couponConfigDetail.getId());
        }
    }

    @Override
    public void download(RechargeRecordVO rechargeRecordVO) {
        List<RechargeRecordVO> rechargeRecordVOS = rechargeRecordMapper.selectRechargeRecord(rechargeRecordVO);
        Map<String,RechargeVO>  prePayMap = null;
        Map<Long,MerchantSubAccount>  merchantSubAccountMap = null;
        if (!CollectionUtils.isEmpty(rechargeRecordVOS)){
            //批量查询记录人
            List<String> recordNoList = rechargeRecordVOS.stream().map(RechargeRecordVO::getRecordNo).collect(Collectors.toList());
            List<RechargeVO> rechargeVOList = rechargeMapper.selectVOList(recordNoList);
            prePayMap = rechargeVOList.stream().collect(Collectors.toMap(RechargeVO::getRechargeNo, Function.identity()));
            //批量查询子账号信息
            List<Long> accountIdList = rechargeRecordVOS.stream().map(RechargeRecordVO::getAccountId).collect(Collectors.toList());
            List<MerchantSubAccount> subAccountList = merchantSubAccountMapper.selectByPrimaryKeyList(accountIdList);
            merchantSubAccountMap = subAccountList.stream().collect(Collectors.toMap(MerchantSubAccount::getAccountId, Function.identity()));
        }

        Map<String, RechargeVO> finalPrePayMap = prePayMap;
        Map<Long, MerchantSubAccount> finalMerchantSubAccountMap = merchantSubAccountMap;
        rechargeRecordVOS.forEach(o -> {
            if (RechargeRecordType.RECHARGE.getId().equals(o.getType())) {
                RechargeVO rechargeVO = finalPrePayMap.get(o.getRecordNo());
                if (rechargeVO != null) {
                    o.setRecorder(rechargeVO.getApplicantName());
                }
            } else {
                MerchantSubAccount account = finalMerchantSubAccountMap.get(o.getAccountId());
                Optional.ofNullable(account).ifPresent(el -> o.setRecorder(account.getContact()));
            }
        });
        if (CollectionUtils.isEmpty(rechargeRecordVOS)) {
            throw new DefaultServiceException("暂无数据");
        }
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 8000);
        sheet.setColumnWidth(1, 3000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);
        sheet.setColumnWidth(6, 8000);
        sheet.setColumnWidth(7, 4000);
        sheet.setColumnWidth(8, 9000);

        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("流水号");
        title.createCell(1).setCellValue("店铺编号");
        title.createCell(2).setCellValue("店铺名称");
        title.createCell(3).setCellValue("类型");
        title.createCell(4).setCellValue("金额");
        title.createCell(5).setCellValue("剩余金额");
        title.createCell(6).setCellValue("订单编号");
        title.createCell(7).setCellValue("记录人");
        title.createCell(8).setCellValue("记录时间");

        int index = 1;
        if (rechargeRecordVOS.size()>1){
            for (RechargeRecordVO r : rechargeRecordVOS){
                Row row = sheet.createRow(index);
                row.createCell(0).setCellValue(r.getRechargeRecordNo());
                row.createCell(1).setCellValue(r.getmId());
                row.createCell(2).setCellValue(r.getMname());
                row.createCell(3).setCellValue(consumptionType(r.getType()));
                row.createCell(4).setCellValue(r.getNewAmount().subtract(r.getOldAmount()).toString());
                row.createCell(5).setCellValue(r.getNewAmount().toString());
                row.createCell(6).setCellValue( ObjectUtils.isEmpty(r.getRecordNo()) ? " " : r.getRecordNo() );
                row.createCell(7).setCellValue(r.getRecorder());
                row.createCell(8).setCellValue(BaseDateUtils.localDateTimeToString(r.getAddtime()));
                index ++;
            }
        }

        StringBuffer stringBuffer = new StringBuffer();
        String fileName = stringBuffer.append("鲜沐卡明细")
                .append(":")
                .append(BaseDateUtils.localDateTimeToString(rechargeRecordVO.getStartTime()))
                .append(BaseDateUtils.localDateTimeToString(rechargeRecordVO.getEndTime()))
                .append(".xls")
                .toString();
        try {
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 消费类型判断
     * @param type
     * @return
     */
    private String consumptionType(Integer type){
        if (Objects.equals(type, ConsumptionTypeEnum.CONSUMPTION.getId())){
            return ConsumptionTypeEnum.CONSUMPTION.getStatus();
        }else if (Objects.equals(type, ConsumptionTypeEnum.MALL_REFUND.getId())){
            return ConsumptionTypeEnum.MALL_REFUND.getStatus();
        }else if (Objects.equals(type, ConsumptionTypeEnum.MANUAL_RECHARGE.getId())){
            return ConsumptionTypeEnum.MANUAL_RECHARGE.getStatus();
        }else if (Objects.equals(type, ConsumptionTypeEnum.MANUAL_REFUND.getId())){
            return ConsumptionTypeEnum.MANUAL_REFUND.getStatus();
        }else{
            return ConsumptionTypeEnum.MALL_RECHARGE.getStatus();
        }
    }

    @Deprecated
    @Override
    public AjaxResult timePointData(RechargeTimeQuery rechargeTimeQuery) {
        LocalDateTime nowTime = rechargeTimeQuery.getNowTime();
        LocalDateTime lastWork = rechargeTimeQuery.getLastWork();
        //所求日期
        List<RechargeRecord> cardSurveyVOList = rechargeRecordMapper.selectDateForXianMu(nowTime);
        CardSurveyVO cardSurveyVO = new CardSurveyVO();
        cardSurveyVO.setTotalBalance(cardSurveyVOList.stream().map(RechargeRecord::getNewAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        cardSurveyVO.setBalanceStore(cardSurveyVOList.size());
        //所求日期七天前数据
        List<RechargeRecord>  oldCardSurveyVOList = rechargeRecordMapper.selectDateForXianMu(lastWork);
        CardSurveyVO oldCardSurveyVO = new CardSurveyVO();
        oldCardSurveyVO.setTotalBalance(oldCardSurveyVOList.stream().map(RechargeRecord::getNewAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        oldCardSurveyVO.setBalanceStore(oldCardSurveyVOList.size());

        //门店数
        //int newStoreNum = merchantMapper.selectStoreNum(nowTime);
        //店均余额
        BigDecimal newAverageStoreBalance = cardSurveyVO.getTotalBalance().divide(BigDecimal.valueOf(cardSurveyVO.getBalanceStore()), 2, BigDecimal.ROUND_HALF_UP);
        //七天前门店数
        //int oldStoreNum = merchantMapper.selectStoreNum(lastWork);
        //七天前店均余额
        BigDecimal oldAverageStoreBalance = oldCardSurveyVO.getTotalBalance().divide(BigDecimal.valueOf(oldCardSurveyVO.getBalanceStore()), 2, BigDecimal.ROUND_HALF_UP);
        //鲜沐卡总余额周环比
        BigDecimal totalBalanceRatio = proportion(cardSurveyVO.getTotalBalance(),oldCardSurveyVO.getTotalBalance());
        //有余额的门店数量周环比
        BigDecimal balanceStoreRatio = proportion(BigDecimal.valueOf(cardSurveyVO.getBalanceStore()), BigDecimal.valueOf(oldCardSurveyVO.getBalanceStore()) );
        //店均余额周环比
        BigDecimal averageStoreBalanceRatio = proportion(newAverageStoreBalance,oldAverageStoreBalance);
        CardSurveyVO c = new CardSurveyVO();
        //鲜沐卡总余额
        c.setTotalBalance(cardSurveyVO.getTotalBalance());
        //有余额的门店数量
        c.setBalanceStore(cardSurveyVO.getBalanceStore());
        //店均余额
        c.setAverageStoreBalance(newAverageStoreBalance);
        c.setTotalBalanceRatio(totalBalanceRatio);
        c.setBalanceStoreRatio(balanceStoreRatio);
        c.setAverageStoreBalanceRatio(averageStoreBalanceRatio);
        return AjaxResult.getOK(c);
    }

    @Override
    public AjaxResult timePointDataFromBI(RechargeTimeQuery rechargeTimeQuery) {
        LocalDateTime nowTime = rechargeTimeQuery.getNowTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String nowTimeStr = nowTime.format(formatter);
        XianmuCardStatistics xianmuCardStatistics = xianmuCardStatisticsMapper.queryByStatDate(nowTimeStr);
        if(Objects.isNull(xianmuCardStatistics)) {
            return AjaxResult.getError("暂无数据");
        }
        CardSurveyVO c = new CardSurveyVO();
        //鲜沐卡总余额
        c.setTotalBalance(xianmuCardStatistics.getTotalBalance());
        //有余额的门店数量
        c.setBalanceStore(xianmuCardStatistics.getStoreCountWithBalance());
        //店均余额
        c.setAverageStoreBalance(xianmuCardStatistics.getAvgStoreBalance());
        c.setTotalBalanceRatio(xianmuCardStatistics.getTotalBalanceWeeklyRatio());
        c.setBalanceStoreRatio(xianmuCardStatistics.getStoreCountWeeklyRatio());
        c.setAverageStoreBalanceRatio(xianmuCardStatistics.getAvgStoreBalanceWeeklyRatio());
        return AjaxResult.getOK(c);
    }

    @Override
    public AjaxResult marketTrend(RechargeTimeQuery rechargeTimeQuery) {
        String startTime = rechargeTimeQuery.getStartTime();
        String endTime = rechargeTimeQuery.getEndTime();
        List<String> betweenDate = DateUtils.getBetweenDate(startTime,endTime);
        List<CardSurveyVO> cardSurveyVOS = new ArrayList<>();
        betweenDate.stream().forEach( b -> {
            LocalDateTime localDateTime = endTime(b);
            CardSurveyVO cardSurveyVO = new CardSurveyVO();
            List<RechargeRecord> rechargeRecords = rechargeRecordMapper.selectDateForXianMu(localDateTime);
            cardSurveyVO.setTotalBalance(rechargeRecords.stream().map(RechargeRecord::getNewAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            cardSurveyVO.setBalanceStore(rechargeRecords.size());
            cardSurveyVO.setDateTime(localDateTime);
            cardSurveyVO.setAverageStoreBalance(cardSurveyVO.getTotalBalance().divide(BigDecimal.valueOf(cardSurveyVO.getBalanceStore()), 2, BigDecimal.ROUND_HALF_UP));
            cardSurveyVOS.add(cardSurveyVO);
        });
        return AjaxResult.getOK(cardSurveyVOS);
    }

    @Override
    public AjaxResult marketTrendFromBI(RechargeTimeQuery rechargeTimeQuery) {
        // 转换下日期格式
        transferTimeFormat(rechargeTimeQuery);
        List<XianmuCardStatistics> xianmuCardStatistics = xianmuCardStatisticsMapper.queryByStatDates(rechargeTimeQuery.getStartTime(), rechargeTimeQuery.getEndTime());
        List<CardSurveyVO> cardSurveyVOS = new ArrayList<>();
        xianmuCardStatistics.stream().map(x -> {
            CardSurveyVO cardSurveyVO = new CardSurveyVO();
            cardSurveyVO.setTotalBalance(x.getTotalBalance());
            cardSurveyVO.setBalanceStore(x.getStoreCountWithBalance());
            cardSurveyVO.setDateTime(convertToLocalDateTime(x.getStatDate()));
            cardSurveyVO.setAverageStoreBalance(x.getAvgStoreBalance());
            return cardSurveyVO;
        }).forEach(cardSurveyVOS::add);
        return AjaxResult.getOK(cardSurveyVOS);
    }

    private void transferTimeFormat(RechargeTimeQuery rechargeTimeQuery) {
        String startTime = timeFormat(rechargeTimeQuery.getStartTime());
        String endTime = timeFormat(rechargeTimeQuery.getEndTime());
        rechargeTimeQuery.setStartTime(startTime);
        rechargeTimeQuery.setEndTime(endTime);
    }

    private String  timeFormat(String time) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            // 将字符串解析为 LocalDate
            LocalDate date = LocalDate.parse(time, inputFormatter);
            // 将 LocalDate 格式化为目标字符串格式
            return date.format(outputFormatter);
        } catch (Exception e) {
            logger.error("时间格式转换异常", e);
            throw new ProviderException("时间格式转换异常");
        }
    }

    public static LocalDateTime convertToLocalDateTime(String dateStr) {
        // 定义输入的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        try {
            // 将字符串解析为 LocalDate
            LocalDate date = LocalDate.parse(dateStr, formatter);
            // 将 LocalDate 转换为 LocalDateTime（默认时间为当天的开始，即零点）
            return date.atStartOfDay();
        } catch (DateTimeParseException e) {
            logger.error("时间格式转换异常", e);
            throw new ProviderException("时间格式转换异常");
        }
    }

    @Override
    public AjaxResult amountData(RechargeTimeQuery rechargeTimeQuery) {
        //返回对象
        CardSurveyVO cardSurvey = new CardSurveyVO();
        LocalDateTime startTime = startTime(rechargeTimeQuery.getStartTime());
        LocalDateTime endTime = endTime(rechargeTimeQuery.getEndTime());
        LocalDateTime ratioStartTime = startTime(rechargeTimeQuery.getRatioStartTime());
        LocalDateTime ratioEndTime = endTime(rechargeTimeQuery.getRatioEndTime());
        LocalDateTime yearOnYearStartTime = startTime(rechargeTimeQuery.getYearOnYearStartTime());
        LocalDateTime yearOnYearEndTime = endTime(rechargeTimeQuery.getYearOnYearEndTime());
        RechargeAmountVO nowRecharge = rechargeRecordMapper.selectRecharge(startTime, endTime);
        RechargeAmountVO ratioRecharge = rechargeRecordMapper.selectRecharge(ratioStartTime, ratioEndTime);
        RechargeAmountVO yearOnYearRecharge = rechargeRecordMapper.selectRecharge(yearOnYearStartTime, yearOnYearEndTime);
        nowRecharge.setRechargeRatio(proportion(nowRecharge.getRechargeAmount(),ratioRecharge.getRechargeAmount()));
        nowRecharge.setRechargeYearOnYear(proportion(nowRecharge.getRechargeAmount(),yearOnYearRecharge.getRechargeAmount()));
        cardSurvey.setRechargeAmountVO(nowRecharge);
        //消费金额模块
        ConsumptionAmountVO nowConsumption = rechargeRecordMapper.selectConsumption(startTime, endTime);
        ConsumptionAmountVO ratioConsumption =rechargeRecordMapper.selectConsumption(ratioStartTime, ratioEndTime);
        ConsumptionAmountVO yearOnYearConsumption =rechargeRecordMapper.selectConsumption(yearOnYearStartTime, yearOnYearEndTime);
        nowConsumption.setConsumptionRatio(proportion(nowConsumption.getConsumptionAmount(),ratioConsumption.getConsumptionAmount()));
        nowConsumption.setConsumptionYearOnYear(proportion(nowConsumption.getConsumptionAmount(),yearOnYearConsumption.getConsumptionAmount()));
        cardSurvey.setConsumptionAmountVO(nowConsumption);
        //订单退款模块
        OrderRefundAmountVO nowRefund = rechargeRecordMapper.selectRefund(startTime, endTime);
        OrderRefundAmountVO ratioRefund = rechargeRecordMapper.selectRefund(ratioStartTime, ratioEndTime);
        OrderRefundAmountVO yearOnYearRefund = rechargeRecordMapper.selectRefund(yearOnYearStartTime, yearOnYearEndTime);
        nowRefund.setRefundRatio(proportion(nowRefund.getOrderRefundAmount(),ratioRefund.getOrderRefundAmount()));
        nowRefund.setRefundYearOnYear(proportion(nowRefund.getOrderRefundAmount(),yearOnYearRefund.getOrderRefundAmount()));
        cardSurvey.setOrderRefundAmountVO(nowRefund);
        //余额退还模块
        BalanceRefundAmountVO nowBalanceRefund = rechargeRecordMapper.selectBalanceRefund(startTime, endTime);
        BalanceRefundAmountVO ratioBalanceRefund = rechargeRecordMapper.selectBalanceRefund(ratioStartTime, ratioEndTime);
        BalanceRefundAmountVO yearOnYearBalanceRefund = rechargeRecordMapper.selectBalanceRefund(yearOnYearStartTime, yearOnYearEndTime);
        nowBalanceRefund.setBalanceRefundRatio(proportion(nowBalanceRefund.getBalanceRefundAmount(),ratioBalanceRefund.getBalanceRefundAmount()));
        nowBalanceRefund.setBalanceRefundYearOnYear(proportion(nowBalanceRefund.getBalanceRefundAmount(),yearOnYearBalanceRefund.getBalanceRefundAmount()));
        cardSurvey.setBalanceRefundAmountVO(nowBalanceRefund);
        return AjaxResult.getOK(cardSurvey);
    }

    /**
     * String加时间转换成localDateTime 00:00:00
     * @return
     */
    private LocalDateTime startTime(String time){
        StringBuffer dayStart = new StringBuffer();
        LocalDateTime start = BaseDateUtils.stringToLocalDateTime(dayStart.append(time).append(" 00:00:00").toString());
        return start;
    }

    /**
     * String加时间转换成localDateTime 23:59:59
     * @return
     */
    private LocalDateTime endTime(String time){
        StringBuffer dayEnd = new StringBuffer();
        LocalDateTime end = BaseDateUtils.stringToLocalDateTime(dayEnd.append(time).append(" 23:59:59").toString());
        return end;
    }

    /**
     * 两个BigDecimal类型的数据增减比例
     * @param molecule
     * @param denominator
     * @return
     */
    private BigDecimal proportion(BigDecimal molecule,BigDecimal denominator){
        BigDecimal bigDecimal = new BigDecimal(1);
        if(Objects.equals(denominator,BigDecimal.ZERO.setScale(2))){
            return BigDecimal.ZERO;
        }
        BigDecimal result = molecule.divide(denominator, 2, BigDecimal.ROUND_HALF_UP).subtract(bigDecimal);
        return result;
    }

    /**
     * 充值申请
     *
     * @param input
     */
    public Recharge rechargeCardPurchase(SaveReceiptDetailInput input,Long flowingWaterId) {
        Recharge insert = new Recharge();
        insert.setmId(input.getAdminId());
        insert.setRechargeNum(input.getReceiptAmount());
        insert.setRechargeType(0);
        insert.setApplicant(getAdminId());
        insert.setStatus(0);
        insert.setAddtime(LocalDateTime.now());
        insert.setUpdatetime(LocalDateTime.now());
        insert.setSendCoupon(input.getSendCoupon());
        insert.setFundType(RechargeFundTypeEnum.RECHARGE_CARD_PURCHASE.getStatus());
        insert.setLevel(NumberUtils.INTEGER_ZERO);
        insert.setFinanceBankFlowingWaterId(flowingWaterId);
        insert.setLevel(input.getLevel());

        String rechargeNo = String.valueOf(SnowflakeUtil.nextId());
        Recharge selectKey = new Recharge();
        selectKey.setRechargeNo(rechargeNo);
        Recharge record = rechargeMapper.selectOne(selectKey);
        if (!ObjectUtils.isEmpty(record)) {
            throw new DefaultServiceException("充值编号已存在!");
        }
        insert.setRechargeNo(rechargeNo);
        rechargeMapper.insert(insert);

        List<SaveReceiptListInput> receiptList = input.getReceiptList();
        receiptList.forEach(receipt->{
            receipt.setFinanceOrderId(Long.valueOf(insert.getId()));
            receipt.setSourceNo(rechargeNo);
        });
        logger.info("管理员{}发起了{}的充值申请,充值编号为：{}", getAdminName(), input.getNameRemakes(), insert.getRechargeNo());
        return insert;
    }

}

