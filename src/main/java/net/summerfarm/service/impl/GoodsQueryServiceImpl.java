package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductQueryReq;
import com.cosfo.manage.client.product.resp.ProductInfoResp;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.service.GoodsQueryService;
import net.summerfarm.service.InventoryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Description
 * @Date 2023/4/25 15:31
 * @<AUTHOR>
 */
@Service
@Slf4j
public class GoodsQueryServiceImpl implements GoodsQueryService {

    @DubboReference
    private ProductProvider productProvider;
    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public Long sku2SaasSkuId(String sku) {
        if (Objects.isNull(sku)) {
            return null;
        }
        Inventory inventory = inventoryMapper.selectOneBySku(sku);
        if (Objects.isNull(inventory)) {
            return null;
        }
        Long skuId = inventory.getInvId();
        ProductQueryReq req = new ProductQueryReq();
        req.setSummerfarmSkuIds(Arrays.asList(skuId));
        DubboResponse<ProductInfoResp> resp = productProvider.batchQueryBySummerfarmSkuIds(req);
        if (!resp.isSuccess()) {
            log.error("invoke productProvider.batchQueryBySummerfarmSkuIds error, req:{}, msg:{}", JSONUtil.toJsonStr(req), resp.getMsg());
            return null;
        }
        ProductInfoResp productInfoResp = resp.getData();
        if (Objects.isNull(productInfoResp) || Objects.isNull(productInfoResp.getSkuMappingMap())) {
            log.warn("未匹配到sku对应的saas货品信息, req:{}, resp:{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(resp));
            return null;
        }
        return productInfoResp.getSkuMappingMap().get(skuId);
    }
}
