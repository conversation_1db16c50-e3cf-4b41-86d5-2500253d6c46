package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceCreateRequest;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceCreateResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.github.pagehelper.PageHelper;
import com.taobao.api.ApiException;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.PurchaseBindingPrepaymentMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.PurchasePlanCostChangeReq;
import net.summerfarm.model.vo.FinanceAccountStatementVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PurchasePlanCostChangeVO;
import net.summerfarm.model.vo.StockTaskProcessVO;
import net.summerfarm.module.pms.model.dto.CostChangeDTO;
import net.summerfarm.service.*;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class PurchasePlanCostChangeServiceImpl extends BaseService implements PurchasePlanCostChangeService {
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private PurchasePlanCostChangeMapper purchasePlanCostChangeMapper;
    @Resource
    private PurchasesMapper purchasesMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    private PriceAdjustmentTriggerMapper priceAdjustmentTriggerMapper;
    @Resource
    @Lazy
    private InterestRateRecordService interestRateRecordService;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private PurchasesAccountPlanMapper purchasesAccountPlanMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private AdminService adminService;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;

    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private FinanceAccountStatementService financeAccountStatementService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private PurchaseBindingPrepaymentMapper purchaseBindingPrepaymentMapper;
    @Resource
    private PurchaseBindingPrepaymentService purchaseBindingPrepaymentService;
    @Resource
    private MqProducer mqProducer;

    private static final Logger logger = LoggerFactory.getLogger(PurchasePlanCostChangeService.class);

    @Override
    public AjaxResult select(int pageIndex, int pageSize, PurchasePlanCostChangeReq req) {
        PageHelper.startPage(pageIndex, pageSize);
        req.setTenantId(BaseConstant.XIANMU_TENANT_ID);
        List<PurchasePlanCostChangeVO> list = purchasePlanCostChangeMapper.selectVO(req);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(list));
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public AjaxResult add(Integer purchasePlanId, BigDecimal newTotalCost, String remark) {
        PurchasesPlan purchasesPlan = purchasesPlanMapper.selectByPrimaryKey(purchasePlanId);
        String purchaseNo = purchasesPlan.getPurchaseNo();
        Purchases purchases = purchasesMapper.selectByNo(purchaseNo);

        //判断结算单状态
        if (Objects.equals(1, purchasesPlan.getSettleFlag())) {
            return AjaxResult.getErrorWithMsg("成本变更发起失败，当前采购项已发起结算或者预付");
        }

        //判断结算单状态
        if (Objects.equals(PurchasesPlanPriceType.SUPPLIER_OFFER_PRICE.ordinal(), purchasesPlan.getPriceType())) {
            return AjaxResult.getErrorWithMsg("报价单价格不可发起成本变更");
        }

        //判断核算单状态
        PurchasesAccountPlan purchasesAccountPlan = purchasesAccountPlanMapper.selectPurchaseById(purchasesPlan.getSku(), purchasesPlan.getPurchaseNo());
        if (purchasesAccountPlan != null) {
            return AjaxResult.getErrorWithMsg("成本变更发起失败，当前采购项已发起核算");
        }
        List<FinanceAccountStatementVO> financeAccountStatementVOList = financeAccountStatementService.selectByPurchaseNoAndSupplierId(purchasesPlan.getPurchaseNo(), purchasesPlan.getSupplierId());
        if (!CollectionUtils.isEmpty(financeAccountStatementVOList)) {
            for (FinanceAccountStatementVO financeAccountStatementDetailVO : financeAccountStatementVOList) {
                //付款中/已付款 状态判断：采购单-供应商存在 所属对账单状态！=已作废，则不可以变更成本 报错提示‘‘该供应商已付款，不允许变更成本’
                if (!Objects.equals(financeAccountStatementDetailVO.getStatus(), FinanceAccountStatementStatusEnum.INVALID.ordinal())){
                    return AjaxResult.getErrorWithMsg("该供应商已付款，不允许变更成本");
                }
            }
        }
        //判断是否有审核成中和审核成功的采购变更
        PurchasePlanCostChange query = new PurchasePlanCostChange();
        query.setPurchasePlanId(purchasePlanId);
        List<PurchasePlanCostChange> list = purchasePlanCostChangeMapper.select(query);
        if (list.stream().anyMatch(el -> Objects.equals(CostChangeStatus.UN_AUDIT.ordinal(), el.getStatus()))) {
            return AjaxResult.getErrorWithMsg("成本变更发起失败，不可重复发起");
        }

        //存在预付不可进行变更成本
        BigDecimal bindAmount = purchaseBindingPrepaymentService.checkBind(purchasesPlan.getPurchaseNo(), purchasesPlan.getSupplierId());
        if (bindAmount.compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO){
            return AjaxResult.getErrorWithMsg("成本变更发起失败，" + purchasesPlan.getSku() + "已发起预付");
        }

        PurchasePlanCostChange record = new PurchasePlanCostChange();
        record.setPurchasePlanId(purchasePlanId);
        record.setNewTotalCost(newTotalCost);
        record.setOldTotalCost(purchasesPlan.getPrice());
        record.setStatus(CostChangeStatus.UN_AUDIT.ordinal());
        record.setRemark(remark);
        record.setCreator(getAdminName());
        record.setCreateTime(LocalDateTime.now());
        purchasePlanCostChangeMapper.insertSelective(record);
        //发送钉钉审核消息(saas的成本变更不需要审核)
        if (!SaasThreadLocalUtil.isSaasRequest()) {
            createDingTalkFlow(record, purchasesPlan.getSku());
        }else {
            //saas的成本变更默认通过
            handelAudit(record.getId(),1,null);
            CostChangeDTO costChangeDTO = new CostChangeDTO();
            costChangeDTO.setTenantId(SaasThreadLocalUtil.getTenantId());
            costChangeDTO.setNewTotalCost(newTotalCost);
            costChangeDTO.setPurchaseNo(purchaseNo);
            costChangeDTO.setSkuCode(purchasesPlan.getSku());
            costChangeDTO.setQuantity(purchasesPlan.getQuantity());
            costChangeDTO.setWarehouseNo(purchases.getAreaNo());
            sendMsgToWms(costChangeDTO);
        }
        return AjaxResult.getOK();
    }

    /** 给wms发送成本变更消息 **/
    private void sendMsgToWms(CostChangeDTO costChangeDTO) {
        final String INV_INVENTORY_COST_CHANGE_TOPIC = "topic_wms_stock_task";
        final String INV_INVENTORY_COST_CHANGE_TAG = "tag_inventory_cost_change";
        logger.info("开始发送成本变更消息:{}", JSON.toJSONString(costChangeDTO));
        mqProducer.send(INV_INVENTORY_COST_CHANGE_TOPIC, INV_INVENTORY_COST_CHANGE_TAG, JSON.toJSONString(costChangeDTO));
        logger.info("结束发送成本变更消息");
    }

    @Override
    public AjaxResult updateRemark(Integer id, String remark) {
        PurchasePlanCostChange record = new PurchasePlanCostChange();
        record.setId(id);
        record.setRemark(remark);
        purchasePlanCostChangeMapper.updateByPrimaryKeySelective(record);

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackOn = RuntimeException.class)
    public AjaxResult audit(Integer id, Integer flag) {
        return handelAudit(id, flag, getAdminName());
    }

    @Override
    public void correctCost(String purchaseNo, String sku) {
        Purchases purchases = purchasesMapper.selectByNo(purchaseNo);
        //判断是否需要重新分摊运费
        if (purchases.getLogisticsCost() != null && purchases.getLogisticsCost().compareTo(BigDecimal.ZERO) > 0) {
            List<PurchasesPlan> ppList = purchasesPlanMapper.selectOriginPlan(purchaseNo);
            BigDecimal totalPrice = BigDecimal.ZERO;
            for (PurchasesPlan plan : ppList) {
                //处理未入库退货数据
//                StockTaskItem item = stockTaskItemMapper.selectOne(StockTaskType.PURCHASE_IN.getId(), purchaseNo, plan.getSku());
//                if (item != null && item.getOldQuantity() != null) {
//                    if (item.getQuantity() == 0) {
//                        continue;
//                    }
//
//                    plan.setQuantity(item.getQuantity());
//                }

                totalPrice = totalPrice.add(plan.getPrice());
            }
            if (BigDecimal.ZERO.equals(totalPrice)) {
                return;
            }

            for (PurchasesPlan el : ppList) {
                BigDecimal actualLogistics = purchases.getLogisticsCost().multiply(el.getPrice()).divide(totalPrice, 2, BigDecimal.ROUND_HALF_EVEN);
                BigDecimal singleCost = actualLogistics.add(el.getPrice()).divide(BigDecimal.valueOf(el.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
                updateCost(purchaseNo, el.getSku(), singleCost);
            }
        } else {
            PurchasesPlan plan = purchasesPlanMapper.selectSingleOriginPlan(purchaseNo, sku);
            if (plan != null && plan.getQuantity() != 0) {
                BigDecimal singleCost = plan.getPrice().divide(BigDecimal.valueOf(plan.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
                updateCost(purchaseNo, sku, singleCost);
            }
        }
    }

    @Override
    public AjaxResult handelAudit(Integer id, Integer flag, String auditor) {
        PurchasePlanCostChange costChange = purchasePlanCostChangeMapper.selectByPrimaryKey(id);
        if (CostChangeStatus.UN_AUDIT.ordinal() != costChange.getStatus()) {
            return AjaxResult.getErrorWithMsg("审核失败，该成本变更已被审批");
        }

        PurchasePlanCostChange update = new PurchasePlanCostChange();
        update.setId(id);
        update.setAuditor(auditor);
        update.setAuditTime(LocalDateTime.now());
        if (Objects.equals(0, flag)) {
            update.setStatus(CostChangeStatus.FAIL.ordinal());
            purchasePlanCostChangeMapper.updateByPrimaryKeySelective(update);

            return AjaxResult.getOK();
        } else if (Objects.equals(1, flag)) {
            //校验结算单
            PurchasesPlan purchasesPlan = purchasesPlanMapper.selectByPrimaryKey(costChange.getPurchasePlanId());
            if (Objects.equals(1, purchasesPlan.getSettleFlag())) {
                return AjaxResult.getErrorWithMsg("审核失败，该采购项已发起结算或者预付");
            }

            //判断核算单状态
            PurchasesAccountPlan purchasesAccountPlan = purchasesAccountPlanMapper.selectPurchaseById(purchasesPlan.getSku(), purchasesPlan.getPurchaseNo());
            if (purchasesAccountPlan != null) {
                return AjaxResult.getErrorWithMsg("成本变更发起失败，当前采购项已发起核算");
            }

            //存在预付不可进行变更成本
            BigDecimal bindAmount = purchaseBindingPrepaymentService.checkBind(purchasesPlan.getPurchaseNo(), purchasesPlan.getSupplierId());
            if (bindAmount.compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO){
                return AjaxResult.getErrorWithMsg("成本变更发起失败，" + purchasesPlan.getSku() + "已发起预付");
            }

            update.setStatus(CostChangeStatus.SUCCESS.ordinal());
            purchasePlanCostChangeMapper.updateByPrimaryKeySelective(update);

            //修改采购计划成本
            PurchasesPlan planUpdate = new PurchasesPlan();
            planUpdate.setId(costChange.getPurchasePlanId());
            planUpdate.setPrice(costChange.getNewTotalCost());
            if (purchasesPlan.getInQuantity() != null && purchasesPlan.getInQuantity() != 0) {
                BigDecimal inPrice = costChange.getNewTotalCost()
                        .multiply(BigDecimal.valueOf(purchasesPlan.getInQuantity()))
                        .divide(BigDecimal.valueOf(purchasesPlan.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
                planUpdate.setInPrice(inPrice);
            }
            purchasesPlanMapper.update(planUpdate);

            //修正成本数据
            correctCost(purchasesPlan.getPurchaseNo(), purchasesPlan.getSku());

            return AjaxResult.getOK();
        } else {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
    }

    /**
     * 更新批次成本
     *
     * @param purchaseNo
     * @param sku
     * @param singleCost
     */
    private void updateCost(String purchaseNo, String sku, BigDecimal singleCost) {
        Purchases purchases = purchasesMapper.selectByNo(purchaseNo);

        if (Objects.equals(StockTaskProcessEnum.PART_IN_WAREHOUSE.getId(),purchases.getProcessState())
                || Objects.equals(StockTaskProcessEnum.TOTAL_IN_WAREHOUSE.getId(),purchases.getProcessState())) {
            //更新store_record表中的各种出库成本
            logger.info("更新采购成本价，批次：{}，sku：{}，成本价：{}", purchaseNo, sku, singleCost);
            storeRecordMapper.updateCost(purchaseNo, sku, singleCost);

            //查询各仓库最新调价批次
            List<StoreRecord> list = storeRecordMapper.selectLastList(sku);
            list.stream()
                    .filter(el -> Objects.equals(purchaseNo, el.getBatch()))
                    .forEach(el -> {
                        PurchasesPlan originPlan = purchasesPlanMapper.selectSingleOriginPlan(purchaseNo, sku);

                        Long businessId = SnowflakeUtil.nextId();
                        PriceAdjustmentTrigger trigger = new PriceAdjustmentTrigger();
                        trigger.setBusinessId(businessId);
                        trigger.setQuantity(originPlan.getInQuantity());
                        trigger.setPurchaseNo(purchaseNo);
                        trigger.setCostPrice(singleCost);
                        trigger.setMarketPrice(originPlan.getMarketPrice());
                        trigger.setValid(1);
                        priceAdjustmentTriggerMapper.insertSelective(trigger);

                        //触发自动调价：修改采购成本
                        logger.info("修改采购成本，触发自动调价，批次：{}，sku：{}，仓库：{}，成本价：{}", purchaseNo, sku, el.getAreaNo(), singleCost);
                        interestRateRecordService.autoChangePrice(el.getAreaNo(), sku, trigger.getCostPrice(), trigger.getMarketPrice(), businessId);
                    });

            //更新转换批次store_record表中的成本
            List<StockTaskProcessDetail> detailList = stockTaskProcessDetailMapper.selectByPurchaseNoAndSku(StockTaskType.TRANSFER_TASK.getId(), purchaseNo, sku);
            for (StockTaskProcessDetail detail : detailList) {
                if (detail.getTransferSku() != null) {
                    BigDecimal cost = singleCost.multiply(BigDecimal.valueOf(detail.getTransferQuantity()))
                            .divide(BigDecimal.valueOf(detail.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);

                    //更新store_record表中的成本
                    logger.info("更新采购成本价，批次：{}，sku：{}，成本价：{}", purchaseNo, detail.getSku(), cost);
                    storeRecordMapper.updateCost(purchaseNo, detail.getSku(), cost);

                    //计算市场价
                    Long businessId = SnowflakeUtil.nextId();
                    PriceAdjustmentTrigger trigger = new PriceAdjustmentTrigger();
                    trigger.setBusinessId(businessId);
                    trigger.setCostPrice(cost);
                    trigger.setQuantity(detail.getQuantity());
                    trigger.setValid(1);
                    PurchasesPlan pp = purchasesPlanMapper.selectSingleOriginPlan(purchaseNo, detail.getTransferSku());
                    if (pp != null && pp.getMarketPrice() != null) {
                        //转入市场价 = 转出市场价*转出数量 / 转入数量
                        BigDecimal marketPrice = pp.getMarketPrice().multiply(BigDecimal.valueOf(detail.getTransferQuantity()))
                                .divide(BigDecimal.valueOf(detail.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
                        trigger.setMarketPrice(marketPrice);
                    }
                    priceAdjustmentTriggerMapper.insertSelective(trigger);

                    StockTaskProcessVO processVO =  stockTaskProcessMapper.selectByPrimaryKey(detail.getStockTaskProcessId());

                    //触发自动调价：修改采购成本
                    logger.info("修改转换成本，触发自动调价，批次：{}，sku：{}，仓库：{}，成本价：{}", purchaseNo, detail.getSku(), processVO.getAreaNo(), cost);
                    interestRateRecordService.autoChangePrice(processVO.getAreaNo(), detail.getSku(), trigger.getCostPrice(), trigger.getMarketPrice(), businessId);
                }
            }
        }
    }

    private void createDingTalkFlow(PurchasePlanCostChange record, String sku) {
        try {
            OapiProcessinstanceCreateRequest request = new OapiProcessinstanceCreateRequest();
            request.setAgentId(Long.valueOf(Global.AGENT_ID));
            request.setProcessCode(Global.COST_CHANGE_CODE);
            request.setDeptId(-1L);

            Config originator = configMapper.selectOne("originator");
            //默认为系统工作人
            request.setOriginatorUserId("5957642744985517013");
            if (originator != null && originator.getValue() != null) {
                request.setOriginatorUserId(originator.getValue());
            }

            List<OapiProcessinstanceCreateRequest.FormComponentValueVo> formComponentValues = new ArrayList<OapiProcessinstanceCreateRequest.FormComponentValueVo>();

            OapiProcessinstanceCreateRequest.FormComponentValueVo vo0 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo0.setName("成本变更编号");
            vo0.setValue(record.getId().toString());
            formComponentValues.add(vo0);

            OapiProcessinstanceCreateRequest.FormComponentValueVo vo1 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo1.setName("发起人");
            vo1.setValue(record.getCreator() + "提交了采购成本变更，请审核");
            formComponentValues.add(vo1);

            OapiProcessinstanceCreateRequest.FormComponentValueVo vo2 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo2.setName("详细说明");
            InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
            vo2.setValue("将" + sku + "-" + inventoryVO.getPdName() + "-" + inventoryVO.getWeight() + "原成本" + record.getOldTotalCost().setScale(2, BigDecimal.ROUND_HALF_EVEN) + "元，变更为" + record.getNewTotalCost().setScale(2, BigDecimal.ROUND_HALF_EVEN) + "元。");
            formComponentValues.add(vo2);

            if (StringUtils.isNotBlank(record.getRemark())) {
                OapiProcessinstanceCreateRequest.FormComponentValueVo vo3 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
                vo3.setName("备注：");
                vo3.setValue(record.getRemark());
                formComponentValues.add(vo3);
            }

            request.setFormComponentValues(formComponentValues);

            DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
            OapiProcessinstanceCreateResponse response = client.execute(request, DingTalkUtils.init().getToken());
            response.isSuccess();
        } catch (ApiException e) {
            logger.info("创建成本变更审批失败 err={}", e.getErrMsg());
        }
    }

    @Override
    public void handleDingTalkAudit(String processInstanceId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
        request.setProcessInstanceId(processInstanceId);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiProcessinstanceGetResponse response = client.execute(request, accessToken);

            Integer updateId = 0;
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response.getProcessInstance();
            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();
            for (OapiProcessinstanceGetResponse.FormComponentValueVo formComponentValue : formComponentValues) {
                if (Objects.equals(formComponentValue.getName(), "成本变更编号")) {
                    updateId = Integer.valueOf(formComponentValue.getValue());
                }
            }

            OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = null;
            Integer auditFlag = null;
            List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
            for (OapiProcessinstanceGetResponse.TaskTopVo task : tasks) {
                if (Objects.equals(task.getTaskResult(), "AGREE") || Objects.equals(task.getTaskResult(), "REFUSE")) {
                    taskTopVo = task;

                    if (Objects.equals(task.getTaskResult(), "AGREE")) {
                        auditFlag = 1;
                    } else {
                        auditFlag = 0;
                    }
                }
            }

            if (taskTopVo == null) {
                logger.error("成本变更审核没有审批人");
            }

            if (auditFlag == null) {
                logger.error("状态异常，成本变更审核状失败");
            }

            AdminAuthExtend adminAuthExtend = null;
            if (taskTopVo != null) {
                adminAuthExtend = adminAuthExtendRepository.selectByUserId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), taskTopVo.getUserid());

            }
            String adminName = "钉钉审批";
            if (adminAuthExtend != null) {
                Admin select = adminService.select(adminAuthExtend.getAdminId());
                adminName = select.getRealname();
            }

            handelAudit(updateId, auditFlag, adminName);

        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }
}
