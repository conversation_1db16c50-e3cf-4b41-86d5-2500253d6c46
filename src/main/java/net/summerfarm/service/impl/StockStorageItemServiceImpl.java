package net.summerfarm.service.impl;

import net.summerfarm.contexts.Global;
import net.summerfarm.enums.StockTaskState;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.StockAllocationItemDetail;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.model.domain.StockTask;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.module.pms.biz.service.AllocationOrderService;
import net.summerfarm.module.pms.facade.warehouse.WarehousePmsFacade;
import net.summerfarm.module.pms.facade.warehouse.dto.WarehouseInfoPmsDTO;
import net.summerfarm.module.pms.infrastructure.model.AllocationOrderItemEntity;
import net.summerfarm.module.pms.model.vo.AllocationOrderEntityVO;
import net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.StockStorageItemService;
import net.summerfarm.service.StockTaskService;
import net.summerfarm.service.WarehouseConfigService;
import net.summerfarm.warehouse.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemCreateDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemDetailCreateDTO;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiang
 * create at:  2022-02-24
 */
@Service
public class StockStorageItemServiceImpl  implements StockStorageItemService {

    @Resource
    private StockStorageItemMapper stockStorageItemMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockStorageItemDetailMapper stockStorageItemDetailMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private StockShipmentItemDetailMapper stockShipmentItemDetailMapper;
    @Resource
    private AllocationOrderService allocationOrderService;
    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    private InventoryService inventoryService;
    @Resource
    @Lazy
    StockTaskService stockTaskService;
    @Resource
    WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;

    @Resource
    WarehousePmsFacade warehousePmsFacade;
    @Resource
    private WarehouseConfigService warehouseConfigService;


    @Override
    public void insertStockAllocationItems(AllocationOrderEntityVO listRecord, List<AllocationOrderItemEntityVO> items,Long tenantId,Integer adminId,String adminName) {
        List<String> skus = items.stream().map(AllocationOrderItemEntity::getSku).collect(Collectors.toList());
        String category = inventoryService.getCategory(skus);
        Integer outStore = listRecord.getOutStore();
         //生成入库任务
        StockTask stockTask = new StockTask();
        stockTask.setTaskNo(listRecord.getListNo());
        stockTask.setAreaNo(listRecord.getInStore());
        stockTask.setType(StoreRecordType.STORE_ALLOCATION_IN.getId());
        stockTask.setExpectTime(listRecord.getExpectTime());
        stockTask.setState(StockTaskState.WAIT_IN_OUT.getId());
        stockTask.setAddtime(LocalDateTime.now());
        stockTask.setCategory(category);
        stockTask.setTenantId(tenantId);
        StockStorageCreateReqDTO createReqDTO = new StockStorageCreateReqDTO();
        List<Integer> warehouseNos = Lists.newArrayList();
        warehouseNos.add(listRecord.getOutStore());
        warehouseNos.add(listRecord.getInStore());
        Map<Integer, WarehouseInfoPmsDTO> warehouseInfoPmsDTOMap = warehousePmsFacade.getWarehouseInfoMapByWarehouseNoList(null, warehouseNos);
        WarehouseInfoPmsDTO inWarehouseInfoPmsDTO = warehouseInfoPmsDTOMap.get(listRecord.getInStore());
        if(inWarehouseInfoPmsDTO!=null){
            createReqDTO.setInWarehouseServiceName(inWarehouseInfoPmsDTO.getServiceProviderName());
        }
        WarehouseInfoPmsDTO outWarehouseInfoPmsDTO = warehouseInfoPmsDTOMap.get(listRecord.getOutStore());
        if(outWarehouseInfoPmsDTO!=null){
            createReqDTO.setOutWarehouseServiceName(outWarehouseInfoPmsDTO.getServiceProviderName());
        }
        createReqDTO.setTenantId(tenantId);
        createReqDTO.setOperatorName(adminName);
        createReqDTO.setInWarehouseNo(listRecord.getInStore());
        createReqDTO.setOutWarehouseNo(outStore);
        createReqDTO.setInWarehouseName(listRecord.getInStoreName());
        createReqDTO.setOutWarehouseName(Global.warehouseMap.get(listRecord.getOutStore()));
        createReqDTO.setSourceNo(listRecord.getListNo());
        createReqDTO.setType(StockStorageTypeEnums.STORE_ALLOCATION_IN.getId());
        createReqDTO.setExpectTime(listRecord.getExpectTime());
        stockTaskMapper.insert(stockTask);
        createReqDTO.setStockTaskId(stockTask.getId().longValue());
        ArrayList<StockStorageItemCreateDTO> itemCreateDTOS = new ArrayList<>();

        boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(outStore);
        for (AllocationOrderItemEntityVO item : items) {
            // 无出库单不生成入库条目
            InventoryVO inventoryVO = inventoryMapper.selectSkuType(item.getSku());
            List<StockAllocationItemDetail> details = item.getStockAllocationItemDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            // 生成入库明细 实到数量默认为0
            int sumOutQuantity = details.stream().mapToInt(StockAllocationItemDetail::getActualOutQuantity).sum();
            item.setOutQuantity(sumOutQuantity);
            /*stockStorageItemMapper.insert(stockTask.getId(), item);
            details.forEach(detail->{detail.setActualOutQuantity(NumberUtils.INTEGER_ZERO);});
            stockStorageItemDetailMapper.insertBatch(item.getId(), details);
           */
            String sku = item.getSku();
            StockStorageItemCreateDTO itemCreateDTO = StockStorageItemCreateDTO.builder().quantity(sumOutQuantity)
                    .sku(sku).pdName(item.getPdName()).build();
            List<StockStorageItemDetailCreateDTO> detailCreateDTOList = new ArrayList<>();
            for (StockAllocationItemDetail detail : details) {
                StockStorageItemDetailCreateDTO detailCreateDTO = StockStorageItemDetailCreateDTO.builder().sku(sku)
                        .quantity(detail.getActualOutQuantity())
                        .qualityDate(detail.getQualityDate())
                        .productionDate(detail.getProductionDate())
                        .purchaseNo(detail.getPurchaseNo())
                        .tenantId(tenantId)
                        .build();
                detailCreateDTOList.add(detailCreateDTO);
            }
            // 库位精细化合并
            if (openCabinetManagement) {
                Map<String, StockStorageItemDetailCreateDTO> mapTmp = detailCreateDTOList.stream()
                        .collect(Collectors.toMap(
                                s -> s.getSku() + ":" + s.getQualityDate() + ":" + s.getProductionDate()
                                    + ":" + s.getPurchaseNo() + ":" + s.getTenantId(),
                                Function.identity(),
                                (a, b) -> {
                                    a.setQuantity(a.getQuantity() + b.getQuantity());
                                    return a;
                                }
                        ));
                detailCreateDTOList = new ArrayList<>(mapTmp.values());
            }
            itemCreateDTO.setCategoryType(inventoryVO.getCategoryType());
            itemCreateDTO.setTenantId(tenantId);
            itemCreateDTO.setSkuType(inventoryVO.getType());
            itemCreateDTO.setSpecification(inventoryVO.getWeight());
            itemCreateDTO.setPdName(inventoryVO.getPdName());
            itemCreateDTO.setPackaging(inventoryVO.getUnit());
            itemCreateDTO.setStorageItemDetailCreateDTOList(detailCreateDTOList);
            itemCreateDTOS.add(itemCreateDTO);
        }
        createReqDTO.setStorageItemCreateDTOList(itemCreateDTOS);
        createReqDTO.setExpectTime(listRecord.getExpectTime());
        stockTaskService.sendCrateInStore(createReqDTO);
    }

    @Override
    public List<AllocationOrderItemEntityVO> select(String taskNo) {
        List<AllocationOrderItemEntityVO> result = stockStorageItemMapper.select(taskNo);
            result.forEach(item -> {
                //获取同步状态
                Integer outStore = item.getOutStore();
                Integer inStore = item.getInStore();
                AreaStore outAreaStore = areaStoreMapper.selectOneNoAuth(new AreaStore(outStore, item.getSku()));
                AreaStore inAreaStore = areaStoreMapper.selectOneNoAuth(new AreaStore(inStore, item.getSku()));
                item.setOutSync(outAreaStore.getSync());
                item.setInSync(inAreaStore.getSync());

                InventoryVO inventory = inventoryMapper.selectSkuType(item.getSku());
                item.setProducingArea(Objects.equals(NumberUtils.INTEGER_ZERO, inventory.getIsDomestic()) ? "进口" : "国产");
                item.setPdName(inventory.getPdName());
                item.setWeight(inventory.getWeight());
                item.setUnit(inventory.getUnit());
                item.setSkuType(inventory.getType());
                item.setType(inventory.getType());
                item.setStorageLocation(inventory.getStorageLocation());
                List<StockAllocationItemDetail> details = item.getStockAllocationItemDetails();

                //分组
                //details.stream().collect(Collectors.toMap())
                    for (StockAllocationItemDetail detail : details) {
                        // 查询出库实出数量
                        StockShipmentItemDetail select = stockStorageItemMapper.selectShipmentItem(item.getSku(), item.getListNo(), detail.getPurchaseNo(), detail.getQualityDate());
                        if (Objects.isNull(select)){
                            continue;
                        }
                        detail.setInQuantity(detail.getActualOutQuantity());
                        detail.setActualOutQuantity(select.getActualOutQuantity());
                    }
            });

        return result;
    }
}
