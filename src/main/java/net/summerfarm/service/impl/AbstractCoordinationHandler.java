package net.summerfarm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.exceptions.BizAssert;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.model.vo.PurchasesResultVO;
import net.summerfarm.model.vo.PurchasesVO;
import net.summerfarm.model.vo.SupplierCoordinationVO;
import net.summerfarm.service.PurchasesService;
import net.summerfarm.service.SupplierCoordinationService;
import net.summerfarm.service.SupplierService;
import net.xianmu.common.exception.BizException;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 供应商协同处理处理骨架流程
 * @Date create in 2023/3/30 11:28
 */
public abstract class AbstractCoordinationHandler<T> implements SupplierCoordinationService<T> {

    /**
     * 协同标记
     */
    public static final Integer COORDINATION = 1;
    private final SupplierService supplierService;

    protected final PurchasesService purchasesService;

    protected AbstractCoordinationHandler(PurchasesService purchasesService, SupplierService supplierService) {
        this.purchasesService = purchasesService;
        this.supplierService = supplierService;
    }

    /**
     * 根据采购订单详情处理协同
     *
     * @param t 领域实体类
     */
    public void execute(T t) {
        if (precheck(t)) {
            Integer supplierId = getSupplierId(t);
            if (isNeedCoordination(queryCoordination(supplierId))) {
                ErrorCode errorCode = throwErrorMsg();
                throw new BizException(errorCode.getCode(), errorCode.getMessage());
            }
        }
    }

    /**
     * 转换单据类型执行协同
     *
     * @param documentId 源单据
     */
    public void execute(Long documentId) {
        if (Objects.nonNull(documentId)) {
            Integer supplierId = getSupplierId(documentId);
            if (supplierId == null) {
                return;
            }
            if (isNeedCoordination(queryCoordination(supplierId))) {
                ErrorCode errorCode = throwErrorMsg();
                throw new BizException(errorCode.getCode(), errorCode.getMessage());
            }
        }
    }

    /**
     * 查询供应商协同配置
     *
     * @param supplierId 供应商id
     * @return 协同配置
     */
    protected SupplierCoordinationVO queryCoordination(Integer supplierId) {
        return supplierService.queryCoordination(supplierId.longValue());
    }

    /**
     * 查询采购单详情
     *
     * @param purchaseNo 采购单号
     * @return 采购单详情
     */
    protected PurchasesResultVO getPoDetail(String purchaseNo) {
        BizAssert.notBlank(purchaseNo, ErrorCode.PURCHASE_NO_IS_BLANK);
        return purchasesService.selectDetail(purchaseNo);
    }

    /**
     * saas非法操作(saas操作鲜沐采购单)
     *
     * @param purchaseNo 采购订单号
     */
    protected void sassIllegalOp(String purchaseNo) {
        if (StrUtil.isEmpty(purchaseNo)) {
            return;
        }
        Long tenantId = SaasThreadLocalUtil.getTenantId();
        PurchasesVO purchasesParam = new PurchasesVO();
        purchasesParam.setTenantId(tenantId);
        purchasesParam.setPurchaseNo(purchaseNo);
        PageInfo<PurchasesResultVO> pageInfo = purchasesService.select(1, 20, purchasesParam, null);
        BizAssert.notEmpty(pageInfo.getList(), ErrorCode.SAAS_REQUEST_OPERATE_XIANMU_PO);
    }
}
