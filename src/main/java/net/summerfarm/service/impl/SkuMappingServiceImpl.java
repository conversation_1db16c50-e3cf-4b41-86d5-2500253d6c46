package net.summerfarm.service.impl;

import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.CommonFileUtils;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.enums.PushSwitchEnum;
import net.summerfarm.enums.PushTypeEnum;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.OuterPlatformMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.OuterPlatformVo;
import net.summerfarm.model.vo.SkuMappingVO;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.SkuMappingMapper;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.SkuMapping;
import net.summerfarm.service.OrderOuterInfoService;
import net.summerfarm.service.SkuMappingService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class SkuMappingServiceImpl extends BaseService implements SkuMappingService {

    @Resource
    private SkuMappingMapper skuMappingMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private OuterPlatformMapper outerPlatformMapper;

    @Resource
    private OrderOuterInfoService orderOuterInfoService;

    @Resource
    private ConfigMapper configMapper;

    @Override
    public AjaxResult selectList(SkuMappingVO skuMappingVO) {
        List<SkuMappingVO> skuMappingVOS = skuMappingMapper.selectList(skuMappingVO);
        return AjaxResult.getOK(skuMappingVOS);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult insert(SkuMapping skuMapping){
        Inventory inventory = inventoryMapper.selectSkuType(skuMapping.getSku());
        if (inventory.getSku() == null){
            return AjaxResult.getErrorWithMsg("sku编号错误!");
        }
        skuMapping.setStatus(1);
        skuMapping.setAddtime(LocalDateTime.now());
        // 检查商品映射是否已存在
        SkuMapping querySkuMapping = checkSkuMapping(skuMapping);
        if (querySkuMapping != null){
            return AjaxResult.getErrorWithMsg("该鲜沐sku，在该外部平台，已经映射了一个sku。");
        }
        int result = skuMappingMapper.insert(skuMapping);
        if (result == 1){
            // 外部对接，推送商品上下架
            OuterPlatformVo outerPlatformVo = outerPlatformMapper.selectOuterPlatformById(skuMapping.getOuterPlatformId());
            if (outerPlatformVo != null && outerPlatformVo.getPushGoodsSwitch() == PushSwitchEnum.OPEN.getSwitchStatus().intValue()) {
                orderOuterInfoService.skuMappingPushOnSaleUpdatePrice(PushTypeEnum.ONSALE.getPushType(), skuMapping.getSku(), skuMapping.getMapping(), skuMapping.getOuterPlatformId(), skuMapping.getAdminId());
            }
            return AjaxResult.getOK();
        }else {
            throw new DefaultServiceException("保存错误");
        }
    }

    /**
     * 检查商品映射是否已存在
     * @param skuMapping
     * @return
     */
    public SkuMapping checkSkuMapping(SkuMapping skuMapping) {
        SkuMapping queryXmSkuMapping = new SkuMapping();
        queryXmSkuMapping.setAdminId(skuMapping.getAdminId());
        queryXmSkuMapping.setSku(skuMapping.getSku());
        queryXmSkuMapping.setStatus(1);
        queryXmSkuMapping.setOuterPlatformId(skuMapping.getOuterPlatformId());
        SkuMapping xmSkuMapping = skuMappingMapper.selectOne(queryXmSkuMapping);
        if (xmSkuMapping != null) {
            return xmSkuMapping;
        }
        SkuMapping queryOuterSkuMapping = new SkuMapping();
        OuterPlatformVo outerPlatformVo = outerPlatformMapper.selectOuterPlatformById(skuMapping.getOuterPlatformId());
        // 外部对接，商品映射不需要推送商品上下架的sku查重加品牌
        if (outerPlatformVo != null && outerPlatformVo.getPushGoodsSwitch() == 0) {
            queryOuterSkuMapping.setAdminId(skuMapping.getAdminId());
        }
        queryOuterSkuMapping.setSku(skuMapping.getMapping());
        queryOuterSkuMapping.setStatus(1);
        queryOuterSkuMapping.setOuterPlatformId(skuMapping.getOuterPlatformId());
        SkuMapping outerSkuMapping = skuMappingMapper.selectOne(queryOuterSkuMapping);
        if (outerSkuMapping != null) {
            return outerSkuMapping;
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult delete(Integer id) {
        SkuMappingVO querySkuMapping=skuMappingMapper.selectById(id);
        int result = skuMappingMapper.delete(id);
        if (result == 1){
            // 外部对接，推送商品上下架
            OuterPlatformVo outerPlatformVo = outerPlatformMapper.selectOuterPlatformById(querySkuMapping.getOuterPlatformId());
            if (outerPlatformVo != null && outerPlatformVo.getPushGoodsSwitch() == PushSwitchEnum.OPEN.getSwitchStatus().intValue()) {
                orderOuterInfoService.skuMappingPushOnSaleUpdatePrice(PushTypeEnum.NOTONSALE.getPushType(), querySkuMapping.getSku(), querySkuMapping.getMapping(), querySkuMapping.getOuterPlatformId(), querySkuMapping.getAdminId());
            }
            return AjaxResult.getOK();
        }else {
            throw new DefaultServiceException("删除失败");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(SkuMapping skuMapping) {
        Inventory inventory = inventoryMapper.selectSkuType(skuMapping.getSku());
        if (inventory.getSku() == null){
            return AjaxResult.getErrorWithMsg("sku编号错误!");
        }
        skuMapping.setStatus(1);
        skuMapping.setAddtime(LocalDateTime.now());
        // 检查商品映射是否已存在
        SkuMapping querySkuMapping = checkSkuMapping(skuMapping);
        if (querySkuMapping != null){
            return AjaxResult.getErrorWithMsg("该鲜沐sku，在该外部平台，已经映射了一个sku。");
        }
        int result = skuMappingMapper.update(skuMapping);
        if (result == 1){
            return AjaxResult.getOK();
        }else {
            throw new DefaultServiceException("修改失败");
        }
    }

    @Override
    public AjaxResult templateDownload(HttpServletResponse response) {
        try {
            CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.TEMPLATE_DIR, "sku映射模板.xls");
        } catch (Exception e) {
            throw new DefaultServiceException("模板导出错误");
        }
        return null;
    }

    @Override
    public AjaxResult skuMappingTemplateUrl() {
        Config config = configMapper.selectOne(ConfigValueEnum.PRODUCT_MAPPING_TEMPLATE_URL.getKey());
        if(Objects.isNull(config)){
            return AjaxResult.getErrorWithMsg("模板不存在，请联系管理员上传至模板仓库");
        }
        return AjaxResult.getOK(config);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult templateUpload(MultipartFile file, Integer adminId) {
        Workbook workbook;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            ExcelUtils excelUtils = new ExcelUtils(workbook);
            List<Map<String,String>> datas = excelUtils.getMapData();
            if (CollectionUtils.isEmpty(datas)){
                throw new DefaultServiceException("请先填写数据");
            }
            Set<String> xmDistinctSet = new HashSet<>();
            Set<String> outerDistinctSet = new HashSet<>();
            List<SkuMapping> insertList = new ArrayList<>();
            for (Map<String,String> data: datas){
                if (StringUtils.isEmpty(data.get("*商品名称")) && StringUtils.isEmpty(data.get("*sku编码")) &&
                        StringUtils.isEmpty(data.get("是否影响大客户账单")) && StringUtils.isEmpty(data.get("外部平台")) &&
                        StringUtils.isEmpty(data.get("*映射商品名称")) && StringUtils.isEmpty(data.get("*映射sku编码")) ){
                    continue;
                }
                if (StringUtils.isEmpty(data.get("*sku编码"))){
                    return AjaxResult.getErrorWithMsg("请先填写sku编码");
                }
                InventoryVO inventoryVO = inventoryMapper.selectSkuType(data.get("*sku编码"));
                if (inventoryVO == null){
                    return AjaxResult.getErrorWithMsg("sku编码错误");
                }
                if (StringUtils.isNotBlank(data.get("*商品名称")) && !inventoryVO.getPdName().equals(data.get("*商品名称"))){
                    return AjaxResult.getErrorWithMsg("商品名称错误");
                }

                if (StringUtils.isEmpty(data.get("是否影响大客户账单"))) {
                    return AjaxResult.getErrorWithMsg("请先选择是否影响大客户账单");
                }

                if (StringUtils.isEmpty(data.get("外部平台"))) {
                    return AjaxResult.getErrorWithMsg("请先选择外部平台");
                }

                OuterPlatformVo outerPlatformVo = outerPlatformMapper.selectOuterPlatformByName(data.get("外部平台"));
                if (!"无".equals(data.get("外部平台")) && outerPlatformVo == null) {
                    return AjaxResult.getErrorWithMsg("外部平台错误");
                }

                if (StringUtils.isEmpty(data.get("*映射商品名称"))){
                    return AjaxResult.getErrorWithMsg("请先填写映射商品名称");
                }

                if (StringUtils.isEmpty(data.get("*映射sku编码"))){
                    return AjaxResult.getErrorWithMsg("请先填写映射sku编码");
                }

                SkuMapping selectKey = new SkuMapping();
                selectKey.setSku(data.get("*sku编码"));
                selectKey.setAdminId(adminId);
                selectKey.setStatus(1);
                selectKey.setOuterPlatformId(outerPlatformVo == null ? 0 : outerPlatformVo.getOuterPlatformId());
                selectKey.setMapping(data.get("*映射sku编码"));
                // 检查商品映射是否已存在
                SkuMapping querySkuMapping = checkSkuMapping(selectKey);
                if (querySkuMapping != null){
                    return AjaxResult.getErrorWithMsg("此sku:"+data.get("*sku编码")+"已存在，请勿填写重复的sku");
                }
                if (xmDistinctSet.contains(data.get("*外部平台")+data.get("*sku编码"))){
                    return AjaxResult.getErrorWithMsg("请勿填写重复的sku:"+data.get("*sku编码"));
                }
                xmDistinctSet.add(data.get("*外部平台")+data.get("*sku编码"));
                if (outerDistinctSet.contains(data.get("*外部平台") + data.get("*映射sku编码"))) {
                    return AjaxResult.getErrorWithMsg("请勿填写重复的sku:" + data.get("*映射sku编码"));
                }
                outerDistinctSet.add(data.get("*外部平台") + data.get("*映射sku编码"));
                SkuMapping skuMapping = new SkuMapping();
                skuMapping.setAdminId(adminId);
                skuMapping.setSku(data.get("*sku编码"));
                skuMapping.setAddtime(LocalDateTime.now());
                skuMapping.setStatus(1);
                skuMapping.setMapping(data.get("*映射sku编码"));
                skuMapping.setMappingName(data.get("*映射商品名称"));
                if (data.get("是否影响大客户账单").equals("是")) {
                    skuMapping.setKaBillSwitch(1);
                } else {
                    skuMapping.setKaBillSwitch(0);
                }
                skuMapping.setOuterPlatformId(outerPlatformVo == null ? 0 : outerPlatformVo.getOuterPlatformId());
                insertList.add(skuMapping);
            }
            skuMappingMapper.insertBatch(insertList);
        } catch (Exception e) {
            throw new DefaultServiceException("模板数据上传失败");
        }
        return AjaxResult.getOK();
    }

}
