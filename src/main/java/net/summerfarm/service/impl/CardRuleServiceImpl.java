package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.CardRuleVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mapper.manage.CardRuleMapper;
import net.summerfarm.model.domain.CardRule;
import net.summerfarm.service.CardRuleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CardRuleServiceImpl implements CardRuleService {

    @Resource
    private CardRuleMapper cardRuleMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(CardRule cardRule) {
        vaildCardRule(cardRule);
        cardRule.setAddTime(LocalDateTime.now());
        cardRuleMapper.insert(cardRule);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(CardRule cardRule) {
        vaildCardRule(cardRule);
        cardRuleMapper.update(cardRule);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, CardRuleVO cardRuleVO) {
        PageHelper.startPage(pageIndex,pageSize);
        List<CardRuleVO> cardRuleVOS = cardRuleMapper.selectVO(cardRuleVO);
        return AjaxResult.getOK(new PageInfo<>(cardRuleVOS));
    }

    void vaildCardRule(CardRule cardRule){
        CardRule select = new CardRule();

        if (cardRule.getId() != null){
            select.setId(cardRule.getId());
            CardRule record = cardRuleMapper.selectByPrimaryKey(cardRule.getId());
            select.setAreaNo(record.getAreaNo());
            select.setMerchantType(record.getMerchantType());
        }else {
            select.setAreaNo(cardRule.getAreaNo());
            select.setMerchantType(cardRule.getMerchantType());
        }
        select.setStartTime(cardRule.getStartTime());
        select.setEndTime(cardRule.getEndTime());
        List<CardRule> cardRules = cardRuleMapper.selectList(select);
        if (!CollectionUtils.isEmpty(cardRules)){
            throw new DefaultServiceException("相同的发放条件下，时间不可和已生效的优惠卡产生交集");
        }
    }
}
