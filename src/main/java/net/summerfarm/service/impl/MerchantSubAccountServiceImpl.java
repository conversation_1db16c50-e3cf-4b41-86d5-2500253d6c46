package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.HttpUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.RandomCodeUtils;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.RechargeRecordType;
import net.summerfarm.enums.SubAccountStatus;
import net.summerfarm.enums.SubAccountType;
import net.summerfarm.facade.mall.MerchantSubFacade;
import net.summerfarm.mall.client.req.merchant.SubAccountQueryReq;
import net.summerfarm.mall.client.resp.MerchantSubAccountResp;
import net.summerfarm.mapper.manage.AfterSaleDeliveryPathMapper;
import net.summerfarm.mapper.manage.AfterSaleOrderMapper;
import net.summerfarm.mapper.manage.ArrivalNoticeMapper;
import net.summerfarm.mapper.manage.ContactMapper;
import net.summerfarm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.mapper.manage.MerchantCouponMapper;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.mapper.manage.MerchantMergeRecordMapper;
import net.summerfarm.mapper.manage.MerchantSubAccountMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.mapper.manage.RechargeMapper;
import net.summerfarm.mapper.manage.RechargeRecordMapper;
import net.summerfarm.mapper.manage.SampleApplyMapper;
import net.summerfarm.mapper.manage.ShoppingCartMapper;
import net.summerfarm.mapper.manage.TrolleyMapper;
import net.summerfarm.mapper.manage.repository.MerchantSubAccountRepository;
import net.summerfarm.model.domain.ArrivalNotice;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.MerchantMergeRecord;
import net.summerfarm.model.domain.MerchantSubAccount;
import net.summerfarm.model.vo.AccountMergeVO;
import net.summerfarm.model.vo.MerchantMergeVO;
import net.summerfarm.service.MemberService;
import net.summerfarm.service.MerchantSubAccountService;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-07-31
 * @description
 */
@Service
public class MerchantSubAccountServiceImpl extends BaseService implements MerchantSubAccountService {
    @Resource
    private MerchantSubAccountMapper accountMapper;
    @Resource
    private MerchantSubAccountRepository merchantSubAccountRepository;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RechargeMapper rechargeMapper;
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private ShoppingCartMapper shoppingCartMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Lazy
    @Resource
    private MemberService memberService;
    @Resource
    private ArrivalNoticeMapper arrivalNoticeMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private MerchantMergeRecordMapper merchantMergeRecordMapper;
    @Resource
    private MerchantSubFacade merchantSubFacade;

    private static final Logger logger = LoggerFactory.getLogger(MerchantSubAccountServiceImpl.class);

    @Override
    public AjaxResult select(Long mId) {
        //店员信息
        List<MerchantSubAccount> subAccountList = accountMapper.selectByMId(mId, null);

        return AjaxResult.getOK(subAccountList);
    }

    @Override
    public AjaxResult<PageInfo<MerchantSubAccount>> selectList(Integer pageIndex,Integer pageSize, Long mId) {
        // 店员信息
        PageHelper.startPage(pageIndex,pageSize);
        List<MerchantSubAccount> subAccountList = accountMapper.selectByMId(mId, null);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(subAccountList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeManager(Long newManagerId) {
        MerchantSubAccount account = accountMapper.selectByPrimaryKey(newManagerId);
        //校验账号状态
        if (account == null || SubAccountStatus.AUDIT_SUC.ordinal() != account.getStatus()) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if (account.getType() == SubAccountType.MANAGER.ordinal()) {
            return AjaxResult.getErrorWithMsg("当前账号已经是店长");
        }

        //店铺信息修改
        Merchant merchant = merchantMapper.selectByPrimaryKey(account.getMId());
        merchant.setMcontact(account.getContact());
        merchant.setPhone(account.getPhone());
        merchant.setUnionid(account.getUnionid());
        merchant.setOpenid(account.getOpenid());
        merchant.setMpOpenid(account.getMpOpenid());
        merchant.setCashAmount(account.getCashAmount());
        merchant.setCashUpdateTime(account.getCashUpdateTime());
        merchant.setLoginTime(account.getLoginTime());
        merchant.setLastOrderTime(account.getLastOrderTime());
        merchantMapper.updateByPrimaryKeySelective(merchant);

        //修改原店长账号类型
        MerchantSubAccount oldManage = accountMapper.selectManageByMid(account.getMId());
        oldManage.setType(SubAccountType.STAFF.ordinal());
        accountMapper.updateSelective(oldManage);

        //修改新店长账号类型
        account.setType(SubAccountType.MANAGER.ordinal());
        accountMapper.updateSelective(account);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult delAccount(Long accountId) {
        MerchantSubAccount account = accountMapper.selectByPrimaryKey(accountId);
        if (account == null){
            return AjaxResult.getErrorWithMsg("店员已被删除");
        }
        if (SubAccountType.MANAGER.ordinal() == account.getType()) {
            return AjaxResult.getErrorWithMsg("店长账号不可移除");
        }
        if (SubAccountStatus.AUDIT_SUC.ordinal() != account.getStatus()) {
            return AjaxResult.getErrorWithMsg("账号未审核，不可移除");
        }

        accountMapper.deleteByPrimaryKey(accountId);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult audit(Long accountId, Integer auditFlag) {
        MerchantSubAccount account = accountMapper.selectByPrimaryKey(accountId);
        if (account == null){
            return AjaxResult.getErrorWithMsg("店员已被删除");
        }
        if (SubAccountStatus.AUDIT_SUC.ordinal() == account.getStatus()) {
            return AjaxResult.getErrorWithMsg("当前账号已通过审批，请勿重复操作");
        }
        List<MerchantSubAccount> accountList = accountMapper.selectByMId(account.getMId(), SubAccountStatus.AUDIT_SUC.ordinal());
        boolean flag = accountList.stream().anyMatch(el -> el.getContact().equals(account.getContact()));
        if (flag) {
            return AjaxResult.getErrorWithMsg("账号名称已存在");
        }

        if (Objects.equals(0, auditFlag)) {
            account.setDeleteFlag(0);
        } else if (Objects.equals(1, auditFlag)) {
            account.setStatus(SubAccountStatus.AUDIT_SUC.ordinal());
        } else {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        account.setAuditTime(new Date());
        accountMapper.updateSelective(account);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectMergeCode(Long mId) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        String channelCode = merchant.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            boolean repeat = true;
            while (repeat) {
                channelCode = RandomCodeUtils.toSerialNumber(6);
                logger.info("新建渠道码：{}", channelCode);
                repeat = merchantMapper.existChannelCode(channelCode);
            }
            merchantMapper.updateChannelCode(mId, channelCode);
        }
        return AjaxResult.getOK(channelCode);
    }

    @Override
    public AjaxResult selectAccountStatus(Long mId, Long accountId) {
        MerchantSubAccount account = accountMapper.selectByPrimaryKey(accountId);
        if (account != null && Objects.equals(mId, account.getMId()) && SubAccountStatus.AUDIT_SUC.ordinal() == account.getStatus()) {
            return AjaxResult.getOK(1);
        }
        return AjaxResult.getOK(0);
    }

    @Override
    public AjaxResult selectMerchantMerge(String mname, String phone, Integer bdId, int pageIndex, int pageNum) {
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(pageIndex, pageNum, () -> merchantSubAccountRepository.selectMerchantMerge(mname, phone, bdId)));
    }

    @Override
    public AjaxResult selectMergeDetail(Long mId) {
        List<MerchantMergeVO> voList = merchantSubAccountRepository.selectMergeDetail(mId);
        return AjaxResult.getOK(voList);
    }

    @Override
    public AjaxResult selectMerchantInfo(MerchantMergeVO vo) {
        List<MerchantMergeVO> result = merchantSubAccountRepository.selectMerchantInfo(vo);
        return AjaxResult.getOK(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult mergeMerchant(AccountMergeVO detailVO) {
        Merchant merchant = merchantMapper.selectByMId(detailVO.getMId());
        if (merchant == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        List<Merchant> mergeList = new ArrayList<>();
        detailVO.getAccountList().forEach(el -> {
            Merchant temp = merchantMapper.selectByPrimaryKey(el);
            mergeList.add(temp);
        });

        //子账号不可包含母账号
        boolean f = mergeList.stream().anyMatch(el -> detailVO.getMId().equals(el.getmId()));
        if(f){
            return AjaxResult.getErrorWithMsg("子账号不可包含母账号");
        }

        //不同区域店铺不能合并
        boolean flag = mergeList.stream().anyMatch(el -> !merchant.getAreaNo().equals(el.getAreaNo()));
        if (flag) {
            return AjaxResult.getErrorWithMsg("不同区域店铺不能合并");
        }

        //大客户和其他店铺类型不可合并
        if ("大客户".equals(merchant.getSize())) {
            boolean matchFlag = mergeList.stream().anyMatch(el -> !"大客户".equals(el.getSize()));
            if (matchFlag) {
                return AjaxResult.getErrorWithMsg("普通客户不能合并到大客户下");
            }
        } else {
            boolean matchFlag = mergeList.stream().anyMatch(el -> "大客户".equals(el.getSize()));
            if (matchFlag) {
                return AjaxResult.getErrorWithMsg("大客户不能合并到普通客户下");
            }
        }

        //直营和加盟不能相互绑定
        if ("大客户".equals(merchant.getSize())) {
            boolean matchFlag = mergeList.stream().anyMatch(el -> !Objects.equals(merchant.getDirect(), el.getDirect()));
            if (matchFlag) {
                return AjaxResult.getErrorWithMsg("直营和加盟不可相互合并");
            }
        }

        //判断待合并子账号下绑定有子账号
        List<Long> voList = detailVO.getAccountList().stream().filter(el -> {
            List<MerchantSubAccount> list = accountMapper.selectByMId(el, null);
            list = list.stream().filter(o -> o.getType() == SubAccountType.STAFF.ordinal()).collect(Collectors.toList());
            return !CollectionUtils.isEmpty(list);
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(voList)) {
            Long subMId = voList.get(0);
            Merchant subMerchant = merchantMapper.selectByPrimaryKey(subMId);
            return AjaxResult.getErrorWithMsg(subMerchant.getMname() + "（" + subMId + "）已经绑定子账号");
        }

        //逐条合并
        for (Long oldMid : detailVO.getAccountList()) {
            Merchant subMerchant = merchantMapper.selectByMId(oldMid);
            if (subMerchant != null && Objects.equals(subMerchant.getIslock(), Byte.valueOf("0"))) {
                //备份商户信息
                accountMapper.bakMerchantInfo(oldMid);

                //插入子账号基本信息
                MerchantSubAccount account = createSubAccount(detailVO.getMId(), subMerchant);

                //订单：将子账号订单mid修改为母账号mid，并添加子账号accountId
                ordersMapper.changeOrderMerchant(subMerchant.getmId(), merchant.getmId(), account.getAccountId());

                //会员：积分相加更新为母账号积分
                merchant.setMemberIntegral(merchant.getMemberIntegral().add(subMerchant.getMemberIntegral()));

                //充值：余额相加更新为母账号积分，流水记录合并为母账号
                merchant.setRechargeAmount(merchant.getRechargeAmount().add(subMerchant.getRechargeAmount()));
                // 合并账户新增充值流水
                rechargeRecordMapper.insertCombineRechargeRecord(String.valueOf(SnowflakeUtil.nextId()),merchant.getmId(),subMerchant.getmId(),account.getAccountId(), RechargeRecordType.RECHARGE.getId());
                // 被账户新增支出流水
                rechargeRecordMapper.insertMergedRechargeRecord(String.valueOf(SnowflakeUtil.nextId()),subMerchant.getmId(),account.getAccountId(),RechargeRecordType.CONSUMPTION.getId());

                //优惠券：各账号当前有效优惠券合并母账号，已使用、已过期券不处理
                merchantCouponMapper.changeCouponMerchant(subMerchant.getmId(), merchant.getmId());

                //售后订单：售后订单归属母账号，下单账号记为子账号
                afterSaleOrderMapper.changeOrderMerchant(subMerchant.getmId(), merchant.getmId(), account.getAccountId());

                //购物车：删除子账号购物车数据
                shoppingCartMapper.deleteByMid(subMerchant.getmId());


                //地址：子账号地址绑定到母账号上（将子账号中默认地址状态修改为普通地址）
                contactMapper.changeContactMerchant(subMerchant.getmId(), merchant.getmId());

                //删除被合并店铺、店长账号、BD绑定信息
                followUpRelationMapper.deleteByMid(subMerchant.getmId());
                merchantMapper.delete(subMerchant.getmId());
                MerchantSubAccount manager = accountMapper.selectManageByMid(subMerchant.getmId());
                accountMapper.deleteByPrimaryKey(manager.getAccountId());

                //删除为你上新数据，合并到货提醒并去重
                arrivalNoticeMapper.deleteByMid(subMerchant.getmId(), 1);
                arrivalNoticeMapper.changeNoticeMerchant(subMerchant.getmId(), merchant.getmId());
                List<ArrivalNotice> repeatList = arrivalNoticeMapper.selectRepeat(merchant.getmId());
                repeatList.forEach(el -> arrivalNoticeMapper.deleteById(el.getId()));

                //更新出样申请归属于母账号
                sampleApplyMapper.updateSampleByMId(subMerchant.getmId(), merchant.getmId(),merchant.getMname());

                //更新补发数据
                afterSaleDeliveryPathMapper.updateMerchantId(merchant.getmId(),subMerchant.getmId());

                //合并记录
                MerchantMergeRecord record = new MerchantMergeRecord();
                record.setMasterMerchantId(detailVO.getMId());
                record.setOldMerchantId(oldMid);
                merchantMergeRecordMapper.insert(record);
            }
        }

        //根据积分重新计算会员等级
        if (!"大客户".equals(merchant.getSize())) {
            Integer grade = memberService.calcGrade(merchant.getAreaNo(), merchant.getMemberIntegral());
            merchant.setGrade(grade);
        }

        //更新母账号
        merchant.setMergeAdmin(getAdminName());
        merchant.setMergeTime(LocalDateTime.now());
        merchantMapper.updateByPrimaryKeySelective(merchant);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult editContact(Long accountId, String contact) {
        MerchantSubAccount account = accountMapper.selectByPrimaryKey(accountId);
        List<MerchantSubAccount> accountList = accountMapper.selectByMId(account.getMId(), SubAccountStatus.AUDIT_SUC.ordinal());
        boolean flag = accountList.stream().anyMatch(el -> el.getContact().equals(contact) && !el.getAccountId().equals(accountId));
        if (flag) {
            return AjaxResult.getErrorWithMsg("账号名称已存在");
        }

        account.setContact(contact);
        accountMapper.updateSelective(account);

        return AjaxResult.getOK();
    }

    @Override
    public MerchantSubAccount getByAccountIdIgnoreDel(Long accountId) {
        return accountMapper.selectByPrimaryKeyIgnoreDel(accountId);
    }

    @Override
    public AjaxResult subAccountInfo(Long accountId) {
        SubAccountQueryReq subAccountQueryReq = new SubAccountQueryReq();
        subAccountQueryReq.setAccountId(accountId);
        MerchantSubAccountResp merchantSubAccountResp = merchantSubFacade.subAccountInfo(subAccountQueryReq);
        return AjaxResult.getOK(merchantSubAccountResp);
    }

    private MerchantSubAccount createSubAccount(Long mId, Merchant oldMerchant) {
        MerchantSubAccount account = new MerchantSubAccount();
        account.setMId(mId);
        account.setContact(oldMerchant.getMcontact());
        account.setPhone(oldMerchant.getPhone());
        if (!Objects.equals("null", oldMerchant.getUnionid()) && StringUtils.isNotBlank(oldMerchant.getUnionid())) {
            account.setUnionid(oldMerchant.getUnionid());
        }
        account.setOpenid(oldMerchant.getOpenid());
        account.setMpOpenid(oldMerchant.getMpOpenid());
        account.setFirstPopView(0);
        account.setCashAmount(oldMerchant.getCashAmount());
        account.setCashUpdateTime(oldMerchant.getCashUpdateTime());
        account.setLoginTime(oldMerchant.getLoginTime());
        account.setLastOrderTime(oldMerchant.getLastOrderTime());
        account.setStatus(SubAccountStatus.AUDIT_SUC.ordinal());
        account.setMInfo(JSON.toJSONString(oldMerchant));
        account.setRegisterTime(oldMerchant.getRegisterTime());
        account.setAuditTime(oldMerchant.getAuditTime());
        account.setAuditUser(oldMerchant.getAuditUser());
        account.setType(SubAccountType.STAFF.ordinal());
        accountMapper.insertSelective(account);
        return account;
    }
}
