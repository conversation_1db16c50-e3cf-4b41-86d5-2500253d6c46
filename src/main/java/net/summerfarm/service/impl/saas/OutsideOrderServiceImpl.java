package net.summerfarm.service.impl.saas;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleOutDTO;
import com.cosfo.ordercenter.client.resp.OrderOutDTO;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.cosfo.summerfarm.api.SummerfarmErrCodeEnum;
import com.cosfo.summerfarm.enums.OrderDeliveryResultEnums;
import com.cosfo.summerfarm.model.SummerfarmResult;
import com.cosfo.summerfarm.model.dto.*;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuMallPriceDTO;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuPriceInfoDTO;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuSupplyStatusDTO;
import com.cosfo.summerfarm.model.input.*;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuMallPriceInput;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuPriceInfoInput;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuSupplyStatusInput;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgType;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.constant.RocketMqConstant;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.*;
import net.summerfarm.enums.saas.SupplyStatusEnum;
import net.summerfarm.enums.saas.TmsDeliveryPlanStatusEnum;
import net.summerfarm.enums.saas.TmsDeliveryPlanTypeEnum;
import net.summerfarm.facade.saas.SaasOrderQueryFacade;
import net.summerfarm.facade.saas.dto.SaasOrderDTO;
import net.summerfarm.facade.saas.input.SaasOrderQueryInput;
import net.summerfarm.facade.tms.TmsDeliveryRuleFacade;
import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.mapper.ProductLabelValueMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.saas.OutsideContactMapper;
import net.summerfarm.mapper.manage.saas.SaasOrdersMapper;
import net.summerfarm.mapper.manage.saas.TmsDeliveryPlanDetailMapper;
import net.summerfarm.mapper.manage.saas.TmsDeliveryPlanMapper;
import net.summerfarm.model.DTO.CategoryDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.saas.*;
import net.summerfarm.model.input.AreaStoreQueryInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.saas.OutsideAfterSaleVO;
import net.summerfarm.service.*;
import net.summerfarm.service.saas.OutsideOrderService;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.TEN;
import static net.summerfarm.contexts.Global.WHITE_SPACE;

/**
 * <AUTHOR> ct
 * create at:  2022/5/13  15:34
 * 外部订单推送
 */
@Slf4j
@Service
public class OutsideOrderServiceImpl implements OutsideOrderService {

    @Resource
    private FenceService fenceService;
    @Resource
    private OrderService orderService;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    @Lazy
    private TmsDeliveryPlanMapper tmsDeliveryPlanMapper;
    @Resource
    private OutsideContactMapper outsideContactMapper;
    @Resource
    private TmsDeliveryPlanDetailMapper tmsDeliveryPlanDetailMapper;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private SaasOrdersMapper saasOrdersMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private CategoryService categoryService;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private WarehouseStorageCenterMapper storageCenterMapper;
    @Resource
    private ProductsPropertyValueService productsPropertyValueService;
    @Resource
    private ProductsService productsService;
    @Resource
    private ProductsPropertyMapper productsPropertyMapper;
    @Resource
    private ProductLabelValueMapper productLabelValueMapper;
    @Resource
    private StockTaskOrderSkuService stockTaskOrderSkuService;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private SaasOrderQueryFacade saasOrderQueryFacade;
    @Resource
    private TmsDeliveryRuleFacade tmsDeliveryRuleFacade;

    private OrderQueryService orderQueryService;

    @Autowired
    MqProducer mqProducer;


    /**
     * 下单
     */
    public static final Integer PLACE_ORDER = 0;

    /**
     * 取消
     */
    public static final Integer CANCEL_ORDER = 1;

    /**
     * city
     */
    public static final String CITY = "city";

    /**
     * area
     */
    public static final String AREA = "area";

    @Value("${saas.domain}")
    private String saasAddress;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendOutsideOrder(SummerfarmOrderInput orderInput) {

        //参数校验
        AjaxResult result = checkOrderPullParam(orderInput);

        //未通过参数校验
        if (!result.isSuccess()) {
            //消息推送
            sendOrderMsg(result.getCode(), orderInput, null, null);
            return;
        }
        //根据地址获取围栏信息
        String area = orderInput.getArea();
        String city = orderInput.getCity();
        FenceVO fenceVO = fenceService.selectFenceByCityArea(area, city);
        if (Objects.isNull(fenceVO)) {
            //消息推送 当前点位不配送
            sendOrderMsg(SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrCode(), orderInput, null, null);
            return;
        }
        //订单是否存在
        TmsDeliveryPlan tmsDeliveryPlan = new TmsDeliveryPlan();
        tmsDeliveryPlan.setOrderNo(orderInput.getOrderNo());
        tmsDeliveryPlan.setStoreId(orderInput.getStoreId());
        tmsDeliveryPlan.setTenantId(orderInput.getTenantId());
        TmsDeliveryPlan queryResultPlan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(tmsDeliveryPlan);

        if (!Objects.isNull(queryResultPlan)) {
            //订单存在
            sendOrderMsg(SummerfarmErrCodeEnum.ORDER_NO_IS_EXIST.getErrCode(), orderInput, null, null);
            return;
        }

        //校验订单是否已经存在
        //城配仓
        Integer storeNo = fenceVO.getStoreNo();
        String orderNo = orderInput.getOrderNo();
        //获取配送时间
        LocalDate orderDeliveryDate = getSaasDeliveryTime(orderInput.getCity(), orderInput.getArea(), storeNo, orderInput.getDeliveryTime());

        List<SummerfarmOrderItemInput> itemInputList = orderInput.getItemInputList();
        //库存扣减
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        itemInputList.sort(Comparator.comparing(SummerfarmOrderItemInput::getSku));
        for (SummerfarmOrderItemInput itemVO : itemInputList) {
            updateAreaStore(itemVO.getSku(), itemVO.getQuantity(), PLACE_ORDER, storeNo, orderNo, recordMap);
        }
        quantityChangeRecordService.insertBatchRecord(recordMap);

        //推送订单完成,并推送配送时间
        sendOrderMsg(SummerfarmErrCodeEnum.SUCCESS.getErrCode(), orderInput, orderDeliveryDate,storeNo);
        //异步处理推送订单
        sendSaasContact(orderInput);
        return;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void outsideOrderCancelDelivery(SummerfarmRefundInput refundInput) {
        //校验sku是否正确
        AjaxResult result = checkRefundPullParam(refundInput);
        if (!result.isSuccess()) {
            //推送外部消息
            sendRefundOrder(refundInput, result.getCode());
            return;
        }
        //校验数量是否能够售后
        TmsDeliveryPlan queryPlan = new TmsDeliveryPlan();
        queryPlan.setStoreId(refundInput.getStoreId());
        queryPlan.setTenantId(refundInput.getTenantId());
        queryPlan.setOrderNo(refundInput.getOrderNo());
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(queryPlan);
        Integer storeNo = tmsDeliveryPlan.getStoreNo();
        Integer planId = tmsDeliveryPlan.getId();
        List<TmsDeliveryPlanDetail> queryDetail = tmsDeliveryPlanDetailMapper.selectDetailByDeliveryPlanId(planId);
        Map<String, List<TmsDeliveryPlanDetail>> skuDetailMap =
                queryDetail.stream().collect(Collectors.groupingBy(TmsDeliveryPlanDetail::getSku));
        //扣减库存
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        String orderNo = refundInput.getOrderNo();
        List<SummerfarmRefundItemInput> refundItemVOS = refundInput.getRefundItemVOS();
        //移除配送单 并返库存
        for (SummerfarmRefundItemInput refundItemVO : refundItemVOS) {
            List<TmsDeliveryPlanDetail> detailList = skuDetailMap.get(refundItemVO.getSku());
//            TmsDeliveryPlanDetail refundDeliveryPlanDetail = detailList.get(0);
            Optional<TmsDeliveryPlanDetail> firstOptional = detailList.stream().filter(e -> Objects.equals(refundItemVO.getSku(), e.getSku())
                    && Objects.equals(refundItemVO.getQuantity(), e.getAmount())
                    && Objects.equals(e.getStatus(), 0)).findFirst();
            if (!firstOptional.isPresent()){
                continue;
            }
            TmsDeliveryPlanDetail refundDeliveryPlanDetail = firstOptional.get();
            refundDeliveryPlanDetail.setStatus(1);

//            Integer amount = refundDeliveryPlanDetail.getAmount();

            //移除配送单信息
            TmsDeliveryPlanDetail updateDetail = new TmsDeliveryPlanDetail();
            updateDetail.setId(refundDeliveryPlanDetail.getId());
            updateDetail.setStatus(1);
            updateDetail.setAmount(refundItemVO.getQuantity());
//            updateDetail.setSku(refundItemVO.getSku());
//            updateDetail.setTmsDeliveryPlanId(tmsDeliveryPlan.getId());
//            if (Objects.equals(amount, refundItemVO.getQuantity())) {
//                updateDetail.setStatus(1);
//            }
            tmsDeliveryPlanDetailMapper.updateTmsDeliveryPlanDetail(refundDeliveryPlanDetail);
            //处理库存
            updateAreaStore(refundItemVO.getSku(), refundItemVO.getQuantity(), CANCEL_ORDER, storeNo, orderNo, recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
        //获取当前是否订单全部售后
        List<TmsDeliveryPlanDetail> detailList = tmsDeliveryPlanDetailMapper.selectEffectiveDetailByPlanId(tmsDeliveryPlan.getId());
        if (CollectionUtils.isEmpty(detailList)) {
            //更新状态
            TmsDeliveryPlan updatePlan = new TmsDeliveryPlan();
            updatePlan.setId(planId);
            updatePlan.setStatus(2);
            tmsDeliveryPlanMapper.updateTmsDeliveryPlan(updatePlan);
        }
        //消息推送
        sendRefundOrder(refundInput, SummerfarmErrCodeEnum.SUCCESS.getErrCode());
    }

    @Override
    public void determineOrder(SummerfarmOrderInput orderInput) {
        //修改订单状态
        String orderNo = orderInput.getOrderNo();
        TmsDeliveryPlan queryPlan = new TmsDeliveryPlan();
        queryPlan.setOrderNo(orderNo);
        queryPlan.setTenantId(orderInput.getTenantId());
        queryPlan.setStoreId(orderInput.getStoreId());
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(queryPlan);
        //重新计算配送时间
        TmsDeliveryPlan updatePlan = new TmsDeliveryPlan();
        updatePlan.setId(tmsDeliveryPlan.getId());
        //状态为待确认
        if (Objects.equals(tmsDeliveryPlan.getStatus(), TmsDeliveryPlanStatusEnum.WAIT_DELIVERY.ordinal())) {
            updatePlan.setDeliveryTime(LocalDate.now().plusDays(1));
            updatePlan.setStatus(1);
            tmsDeliveryPlanMapper.updateTmsDeliveryPlan(updatePlan);
        }
        //取消
        if (Objects.equals(tmsDeliveryPlan.getStatus(), TmsDeliveryPlanStatusEnum.CANCEL_DELIVERY.ordinal())) {
            log.info("orderNo={} 已经取消", orderNo);
        }
    }

    /**
     * 外部推送参数校验
     *
     * @param orderInput
     * @return
     */
    private AjaxResult checkOrderPullParam(SummerfarmOrderInput orderInput) {
        //参数校验
        if (orderInput == null || Objects.isNull(orderInput.getOrderNo())) {
            return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrCode());
        }
        //地址处理
        checkCityWithoutArea(orderInput);
        //查询订单
        //参数校验
        List<SummerfarmOrderItemInput> itemInputList = orderInput.getItemInputList();
        for (SummerfarmOrderItemInput input : itemInputList) {

            //数量不存在
            if (Objects.isNull(input.getQuantity()) || Objects.equals(input.getQuantity(), 0)) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_QUANTITY_IS_NULL.getErrCode());
            }
            if (Objects.isNull(input.getSkuId()) && Objects.isNull(input.getSku())) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_IS_NULL.getErrCode());
            }
            //skuId
            Long skuId = input.getSkuId();
            if (Objects.nonNull(skuId)) {
                Inventory inventory = inventoryService.selectById(skuId);
                if (Objects.isNull(inventory)) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_INV_ID_NON_EXISTENT.getErrCode());
                }
//                if (!Objects.equals(input.getSku(), inventory.getSku())) {
//                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_INV_ID_SKU_UNEQUAL.getErrCode());
//                }
                input.setSku(inventory.getSku());
                continue;
            }
            //sku存在
            if (Objects.nonNull(input.getSku())) {
                AjaxResult result = inventoryService.selectSku(input.getSku());
                if (Objects.isNull(result.getData())) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_NON_EXISTENT.getErrCode());
                }
            }

        }
        return AjaxResult.getOK();

    }

    /**
     * 外部售后推送参数校验
     *
     * @param refundInput
     * @return
     */
    private AjaxResult checkRefundPullParam(SummerfarmRefundInput refundInput) {

        //参数校验
        if (refundInput == null || Objects.isNull(refundInput.getRefundNo())) {
            return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrCode());
        }

        //获取订单数据
        String orderNo = refundInput.getOrderNo();
        TmsDeliveryPlan tmsDeliveryPlan = new TmsDeliveryPlan();
        tmsDeliveryPlan.setTenantId(refundInput.getTenantId());
        tmsDeliveryPlan.setStoreId(refundInput.getStoreId());
        tmsDeliveryPlan.setOrderNo(orderNo);
        TmsDeliveryPlan resultPlan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(tmsDeliveryPlan);
        if (Objects.isNull(resultPlan)) {
            //直接推送售后成功
            return AjaxResult.getError(SummerfarmErrCodeEnum.SUCCESS.getErrCode());
        }
        Integer id = resultPlan.getId();
        LocalDate deliveryTime = resultPlan.getDeliveryTime();

        LocalDateTime canAfterOrderTime = LocalDateTime.of(deliveryTime.minusDays(1), LocalTime.of(20, 25, 30));
        //超过截单时间不可售后
        if (LocalDateTime.now().isAfter(canAfterOrderTime) && Objects.equals(resultPlan.getStatus(), 0)) {
            return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_NON_DELIVERY_PLAN.getErrCode());
        }
        //获取配送单信息
        List<TmsDeliveryPlanDetail> tmsDeliveryPlanDetails = tmsDeliveryPlanDetailMapper.selectDetailByDeliveryPlanId(id);
        List<TmsDeliveryPlanDetail> collect = tmsDeliveryPlanDetails.stream().filter(x -> x.getAmount() > 0).collect(Collectors.toList());
        // 不存在配送数量大于0的返回失败
        if (CollectionUtils.isEmpty(collect)) {
            return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_NON_DELIVERY_PLAN.getErrCode());

        }

        //过滤数量为0的
        List<SummerfarmRefundItemInput> refundItemVOS = refundInput.getRefundItemVOS();
        for (SummerfarmRefundItemInput itemVO : refundItemVOS) {
            //sku skuId都不存在
            if (Objects.isNull(itemVO.getSkuId()) && Objects.isNull(itemVO.getSku())) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_IS_NULL.getErrCode());
            }
            //数量不存在
            if (Objects.isNull(itemVO.getQuantity()) || Objects.equals(itemVO.getQuantity(), 0)) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_QUANTITY_IS_NULL.getErrCode());
            }
            //skuId
            Long skuId = itemVO.getSkuId();
            if (Objects.nonNull(skuId)) {
                Inventory inventory = inventoryService.selectById(skuId);
                if (Objects.isNull(inventory)) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_INV_ID_NON_EXISTENT.getErrCode());
                }
//                if (Objects.equals(itemVO.getSku(), inventory.getSku())) {
//                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_INV_ID_SKU_UNEQUAL.getErrCode());
//                }
                itemVO.setSku(inventory.getSku());
                continue;
            }
            //sku存在
            if (Objects.nonNull(itemVO.getSku())) {
                AjaxResult result = inventoryService.selectSku(itemVO.getSku());
                if (Objects.isNull(result.getData())) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_NON_EXISTENT.getErrCode());
                }
            }

        }
        return AjaxResult.getOK();

    }

    /**
     * 生成配送单详情，配送单地址
     *
     * @param orderInput
     */
    private void sendSaasContact(SummerfarmOrderInput orderInput) {
//        MQData mqData = new MQData();
//        mqData.setType(MType.SAAS_CONTACT.name());
//        String producerMsg = JSON.toJSONString(orderInput);
//        mqData.setData(producerMsg);
//        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        // 2023-07-04 换topic（原来的mall-list用的业务太多了，会堵塞）
//        mqProducer.send(RocketMqConstant.Topic.TOPIC_MANAGE_INSERT_TMS_DELIVERY_PLAN,
//                RocketMqConstant.Tag.TAG_MANAGE_INSERT_TMS_DELIVERY_PLAN,
//                orderInput);
    }

    /**
     * 获取生成外部地址信息
     *
     * @param orderInput
     * @return 外部地址id
     */
    private Integer getOutSideContact(SummerfarmOrderInput orderInput, Integer storeNo) {

        //查询地址是否已经存在
        OutsideContact outsideContact = ConvertUtils.convert(orderInput, OutsideContact.class);
        OutsideContact queryContact = outsideContactMapper.selectOutsideContact(outsideContact);
        if (Objects.nonNull(queryContact)) {
            if(StringUtils.isNotBlank(orderInput.getPoiNote()) && !Objects.equals(queryContact.getPoi(),orderInput.getPoiNote())){
                queryContact.setPoi(orderInput.getPoiNote());
                outsideContactMapper.updatePoi(queryContact);
            }
            return queryContact.getId();
        }
        //获取poi
        String poi = "";
        if(StringUtils.isNotBlank(orderInput.getPoiNote())){
            poi = orderInput.getPoiNote();
        }else{
            StringBuilder address = new StringBuilder();
            address.append(orderInput.getProvince())
                    .append(orderInput.getCity())
                    .append(StringUtils.isNotBlank(orderInput.getArea()) ? orderInput.getArea() : "")
                    .append(orderInput.getAddress());
            JSONObject geoCode = GaoDeUtil.getGeoCode(address.toString());
            JSONArray geocodes = geoCode.getJSONArray("geocodes");
            JSONObject geocode = geocodes.getJSONObject(0);
            poi = geocode.getString("location");
        }

        //计算距离
        //新增地址信息
        BigDecimal distance = BigDecimal.ZERO;
        WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(storeNo);
        logisticsCenter = Optional.ofNullable(logisticsCenter).orElse(new WarehouseLogisticsCenter());
        PoiVO logisticsCenterPoi = SplitUtils.string2poi(logisticsCenter.getPoiNote());
        PoiVO contactPoi = SplitUtils.string2poi(poi);

        if (Objects.nonNull(logisticsCenterPoi) && Objects.nonNull(contactPoi)) {
            double distanceDou = DistanceUtil.getDistance(logisticsCenterPoi.getLon(), logisticsCenterPoi.getLat()
                    , contactPoi.getLon(), contactPoi.getLat());
            distance = new BigDecimal(distanceDou);
        }


        OutsideContact saveContact = ConvertUtils.convert(orderInput, OutsideContact.class);
        saveContact.setMName(orderInput.getStoreName());
        saveContact.setDistance(distance);
        saveContact.setPhone(orderInput.getPhone());
        saveContact.setPoi(poi);
        outsideContactMapper.saveOutsideContact(saveContact);
        return saveContact.getId();
    }

    /**
     * 封装库存操作
     *
     * @param sku  sku信息
     * @param type 操作类型 下单 0  ，售后 1
     */
    private void updateAreaStore(String sku, Integer quantity, Integer type,
                                 Integer storeNo, String orderNo, Map<String, QuantityChangeRecord> recordMap) {
        //虚拟库存
        Integer onlineQuantity = Objects.equals(type, PLACE_ORDER) ? -quantity : quantity;
        //冻结库存
        Integer lockQuantity = Objects.equals(type, PLACE_ORDER) ? quantity : -quantity;
        //扣减类型
        StockChangeType changeType = Objects.equals(type, PLACE_ORDER) ? SaleStockChangeTypeEnum.PLACE_ORDER : SaleStockChangeTypeEnum.CANCEL_ORDER;
        //虚拟库存
        areaStoreService.updateOnlineStockByStoreNo(true, onlineQuantity, sku, storeNo, changeType, orderNo, recordMap, NumberUtils.INTEGER_ZERO);
        //锁定库存
        areaStoreService.updateLockStockByStoreNo(lockQuantity, sku, storeNo, changeType, orderNo, recordMap);

    }

    /**
     * 发消息
     *  @param code
     * @param orderInput
     * @param orderDeliveryDate
     * @param storeNo
     */
    @Override
    public void sendOrderMsg(String code, SummerfarmOrderInput orderInput, LocalDate orderDeliveryDate, Integer storeNo) {
        SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
        msgModel.setMsgType(SummerfarmMsgType.STOCK_LOCK);
        SummerfarmOrderDTO summerfarmOrderDTO = new SummerfarmOrderDTO();
        summerfarmOrderDTO.setOrderNo(orderInput.getOrderNo());
        summerfarmOrderDTO.setTenantId(orderInput.getTenantId());
        summerfarmOrderDTO.setStoreId(orderInput.getStoreId());
        summerfarmOrderDTO.setDeliveryTime(orderDeliveryDate);
        summerfarmOrderDTO.setStoreNo(storeNo);
        summerfarmOrderDTO.setCode(code);
        msgModel.setMsgData(summerfarmOrderDTO);
        mqProducer.send(SummerfarmMQTopic.SUMMRFARM_TO_SAAS,null,JSON.toJSONString(msgModel));
    }

    @Override
    public OrderVO queryOutsideOrder(OrderOutDTO orderInput, Map<Long, Inventory> longInventoryMap) {
        if (Objects.isNull(orderInput) || CollectionUtils.isEmpty(orderInput.getOrderItemOutDTOS())) {
            return null;
        }
        List<OrderItemVO> voList = new ArrayList<>();
        orderInput.getOrderItemOutDTOS().forEach(itemVO -> {
            Inventory inventory = longInventoryMap.get(itemVO.getSupplierSkuId());
            String specification = itemVO.getSpecification();
            String specificationUnit = itemVO.getSpecificationUnit();
            String weight = "";
            String unit = "";
            if (!StringUtils.isEmpty(specification)) {
                weight = specification;
            }
            if (!StringUtils.isEmpty(specificationUnit)) {
                unit = specificationUnit;
            }
            if(itemVO.getNeedSendAmount() != null && itemVO.getNeedSendAmount() > 0){
                OrderItemVO orderItemVO = new OrderItemVO();
                orderItemVO.setPrice(itemVO.getPrice());
                orderItemVO.setSalePrice(itemVO.getPrice());
                orderItemVO.setWeight(weight);
                orderItemVO.setUnit(unit);
                orderItemVO.setPdName(itemVO.getTitle());
                orderItemVO.setSku(inventory != null ? inventory.getSku() : "");
                orderItemVO.setAmount(itemVO.getNeedSendAmount());
                orderItemVO.setOriginalPrice(BigDecimal.ZERO);
                voList.add(orderItemVO);
            }
        });
        OrderVO orderVO = new OrderVO();
        orderVO.setOrderItemVOs(voList);
        return orderVO;
    }

    @Override
    public OrderVO queryAfterOutsideOrder(OrderAfterSaleOutDTO afterOrderItemVO, Map<Long, Inventory> longInventoryMap) {

        Inventory inventory = longInventoryMap.get(afterOrderItemVO.getSupplierSkuId());
        String specification = afterOrderItemVO.getSpecification();
        String specificationUnit = afterOrderItemVO.getSpecificationUnit();
        String weight = "";
        String unit = "";
        if (!StringUtils.isEmpty(specification)) {
            weight = specification;
        }
        if (!StringUtils.isEmpty(specificationUnit)) {
            unit = specificationUnit;
        }
        List<OrderItemVO> voList = new ArrayList<>();
        OrderItemVO orderItemVO = new OrderItemVO();
        //根据订单号和sku商户信息查询sku数量
        Integer skuNum = tmsDeliveryPlanMapper.selectSkuNumByAfterOrderNo(inventory.getSku(), afterOrderItemVO.getOrderAfterSaleNo());
        skuNum = skuNum == null ? 0 : skuNum;
        if (skuNum != 0) {
            orderItemVO.setPrice(afterOrderItemVO.getPrice());
            orderItemVO.setSalePrice(afterOrderItemVO.getPrice());
            orderItemVO.setWeight(weight);
            orderItemVO.setUnit(unit);
            orderItemVO.setPdName(afterOrderItemVO.getTitle());
            orderItemVO.setSku(inventory != null ? inventory.getSku() : "");
            orderItemVO.setAmount(skuNum);
            orderItemVO.setOriginalPrice(BigDecimal.ZERO);
            voList.add(orderItemVO);
        }
        OrderVO orderVO = new OrderVO();
        orderVO.setOrderItemVOs(voList);
        return orderVO;
    }

    /**
     * 获取配送时间
     *
     * @param storeNo 编号
     * @param city    市
     * @param area    区
     * @return
     */
    private LocalDate getDeliveryDate(String city, String area, Integer storeNo) {
       /* Contact contact = new Contact();
        contact.setCity(city);
        contact.setArea(area);
        LocalDateTime startTime = Global.getStartTime(Global.SAAS_ORDER_TIME);
        Integer[] frequent = fenceService.selectDeliveryFrequentByFence(contact);
        LocalDate orderDeliveryDate = fenceService.checkCycleDelivery(storeNo, startTime, frequent);*/
        LocalDate orderDeliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.SAAS_MALL)
                .orderTime(LocalDateTime.now())
                .city(city)
                .area(area)
                .build());
        return orderDeliveryDate;
    }

    /*private boolean checkInputDeliveryTimeValid(String city, String area, Integer storeNo, LocalDate deliveryTime) {
        Contact contact = new Contact();
        contact.setCity(city);
        contact.setArea(area);
        Integer[] frequent = fenceService.selectDeliveryFrequentByFence(contact);
        return fenceService.checkDeliveryTime(storeNo, deliveryTime, frequent, Global.SAAS_ORDER_TIME);
    }*/

    /**
     * 退款消息
     *
     * @param refundInput
     * @param code
     */
    private void sendRefundOrder(SummerfarmRefundInput refundInput, String code) {
        //消息推送
        SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
        msgModel.setMsgType(SummerfarmMsgType.STOCK_UNLOCK);
        SummerfarmRefundDTO refundDTO = new SummerfarmRefundDTO();
        refundDTO.setOrderNo(refundInput.getOrderNo());
        refundDTO.setRefundNo(refundInput.getRefundNo());
        refundDTO.setCode(code);
        refundDTO.setTenantId(refundInput.getTenantId());
        refundDTO.setStoreId(refundInput.getStoreId());
        msgModel.setMsgData(refundDTO);
        mqProducer.send(SummerfarmMQTopic.SUMMRFARM_TO_SAAS,null,JSON.toJSONString(msgModel));
    }

    @Override
    public <T> void checkCityWithoutArea(T contact) {

        Object city = ReflectUtil.getFieldValue(contact, CITY);
        if (Objects.isNull(city)) {
            return;
        }
        String checkCity = String.valueOf(city);
        Config config = configMapper.selectOne(Global.NO_AREA_CITY);
        String value = config.getValue();
        if (!StringUtils.isEmpty(value) && value.contains(checkCity)) {
            ReflectUtil.setFieldValue(contact, AREA, null);
        }

    }

    @Override
    public SummerfarmResult queryAreaStoreQuantityList(SummerfarmStockInput skuData) {
        log.info("线程：{}，开始查询库存", Thread.currentThread().getName());

        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(skuData.getSkuIdList())) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_INV_IDS_IS_NULL.getErrCode(), SummerfarmErrCodeEnum.PARAM_INV_IDS_IS_NULL.getErrMsg());
        }
        if (skuData.getSkuIdList().size() >= TEN) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_INV_IDS_TO_MUCH.getErrCode(), SummerfarmErrCodeEnum.PARAM_INV_IDS_TO_MUCH.getErrMsg());
        }
        //地址处理
        checkCityWithoutArea(skuData);
        List<Inventory> inventories = inventoryService.selectByIds(skuData.getSkuIdList());
        List<SummerfarmStockDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(inventories)) {
            return SummerfarmResult.getOK(result);
        }
        //获城配仓
        FenceVO fenceVO = fenceService.selectFenceByCityArea(skuData.getArea(), skuData.getCity());
        Integer storeNo = Objects.isNull(fenceVO) ? null : fenceVO.getStoreNo();
        // 获取类目信息
        List<Long> pdIdList = inventories.stream().map(Inventory::getPdId).distinct().collect(Collectors.toList());
        List<Products> productsList = productsMapper.selectListByIdList(pdIdList);
        Map<Long, Products> productMap = productsList.stream()
                .collect(Collectors.toMap(Products::getPdId, Function.identity(), (a, b) -> a));
        List<Integer> catetoryIdList = productsList.stream().map(Products::getCategoryId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Category> categoryList = categoryMapper.selectTypeByIds(catetoryIdList);
        Map<Integer, Category> categoryMap = categoryList.stream()
                .collect(Collectors.toMap(Category::getId, Function.identity(), (a, b) -> a));
        //获取信息
        for (Inventory inventory : inventories) {
            Boolean isFruit = Boolean.FALSE;
            Long pdId = inventory.getPdId();
            Products products = productMap.get(pdId);
            if (Objects.nonNull(products)) {
                Integer categoryId = products.getCategoryId();
                Category category = categoryMap.get(categoryId);
                isFruit = Objects.nonNull(category) && Objects.equals(category.getType(), CategoryTypeEnum.FRUIT.getType());
            }

            SummerfarmStockDTO dto = new SummerfarmStockDTO();
            // 查询库存信息
            AreaStoreVO areaStoreVO = areaStoreService.getOnlineQuantityByAddress(storeNo, inventory.getSku());
            dto.setSkuId(inventory.getInvId());
            dto.setStockAmount(areaStoreVO.getOnlineQuantity());
            dto.setFruitFlag(isFruit);
            // 查询最近保质日期
            if (areaStoreVO.getOnlineQuantity() > 0) {
                StoreRecord storeRecord = storeRecordMapper.selectLastQualityDate(inventory.getSku(), areaStoreVO.getAreaNo());
                if (!Objects.isNull(storeRecord)) {
                    LocalDate lastQualityDate = storeRecord.getQualityDate();
                    LocalDate QualityDate = Objects.nonNull(lastQualityDate) &&
                            DateUtil.getBetweenDay(LocalDate.now(), lastQualityDate).intValue() > 0 ? lastQualityDate : null;
                    dto.setQualityDate(QualityDate);
                }
            }
            result.add(dto);

        }


        long endTime = System.currentTimeMillis();
        log.info("线程：{}：查询库存完毕，执行时间：{}ms", Thread.currentThread().getName(), endTime - startTime);
        return SummerfarmResult.getOK(result);
    }

    @Override
    public SummerfarmResult queryDeliveryTime(SummerfarmDeliveryInput data) {
        //地址处理
        checkCityWithoutArea(data);

        LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.SAAS_MALL)
                .orderTime(LocalDateTime.now())
                .city(data.getCity())
                .area(data.getArea())
                .build());
        /*FenceVO fenceVO = fenceService.selectFenceByCityArea(data.getArea(), data.getCity());
        if (Objects.isNull(fenceVO)) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrMsg());
        }
        LocalDateTime startTime = Global.getStartTime(Global.SAAS_ORDER_TIME);
        //查看配送周期表
        Integer[] deliveryFrequent = fenceService.getDeliveryTimeArray(fenceVO);
        if (Objects.isNull(deliveryFrequent)) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.DELIVERY_ARRAY_NON_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.DELIVERY_ARRAY_NON_EXISTENT.getErrMsg());
        }

        LocalDate deliveryDate = fenceService.checkCycleDelivery(fenceVO.getStoreNo(), startTime, deliveryFrequent);*/
        return SummerfarmResult.getOK(deliveryDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTmsDeliveryPlan(SummerfarmOrderInput orderInput) {
        FenceVO fenceVO = fenceService.selectFenceByCityArea(orderInput.getArea(), orderInput.getCity());
        Integer storeNo = fenceVO.getStoreNo();

        Integer contactId = getOutSideContact(orderInput, storeNo);
        //生成配送单
        TmsDeliveryPlan tmsDeliveryPlan = new TmsDeliveryPlan();
        //配送时间
        LocalDate orderDeliveryDate = getSaasDeliveryTime(orderInput.getCity(), orderInput.getArea(), storeNo, orderInput.getDeliveryTime());

        //生成配送单详情
        Integer deliveryType = orderInput.getDeliveryType();
        Long tenantId = orderInput.getTenantId();
        Long storeId = orderInput.getStoreId();
       /* Integer status = LocalTime.now().isAfter(Global.SAAS_ORDER_TIME.minusMinutes(30)) &&
                LocalTime.now().isBefore(Global.SAAS_ORDER_TIME)
                ? TmsDeliveryPlanStatusEnum.WAIT_DELIVERY.ordinal() : TmsDeliveryPlanStatusEnum.IN_DELIVERY.ordinal();*/
        tmsDeliveryPlan.setStatus(TmsDeliveryPlanStatusEnum.WAIT_DELIVERY.ordinal());
        tmsDeliveryPlan.setStoreNo(storeNo);
        tmsDeliveryPlan.setStoreId(storeId);
        tmsDeliveryPlan.setTenantId(tenantId);
        tmsDeliveryPlan.setDeliveryType(deliveryType);
        tmsDeliveryPlan.setDeliveryTime(orderDeliveryDate);
        tmsDeliveryPlan.setContactId(contactId);
        tmsDeliveryPlan.setOrderNo(orderInput.getOrderNo());
        tmsDeliveryPlan.setType(1);
        tmsDeliveryPlanMapper.saveTmsDeliveryPlan(tmsDeliveryPlan);
        //插入详情
        ArrayList<TmsDeliveryPlanDetail> tmsDeliveryPlanDetails = new ArrayList<>();
        List<SummerfarmOrderItemInput> itemInputList = orderInput.getItemInputList();
        for (SummerfarmOrderItemInput input : itemInputList) {
            TmsDeliveryPlanDetail tmsDeliveryPlanDetail = new TmsDeliveryPlanDetail();
            tmsDeliveryPlanDetail.setAmount(input.getQuantity());
            tmsDeliveryPlanDetail.setSku(input.getSku());
            tmsDeliveryPlanDetail.setTmsDeliveryPlanId(tmsDeliveryPlan.getId());
            tmsDeliveryPlanDetail.setStatus(TmsDeliveryPlanStatusEnum.WAIT_DELIVERY.ordinal());
            tmsDeliveryPlanDetail.setDeliveryType(deliveryType);
            tmsDeliveryPlanDetails.add(tmsDeliveryPlanDetail);
        }
        tmsDeliveryPlanDetailMapper.saveBatchTmsDeliveryPlanDetail(tmsDeliveryPlanDetails);

    }

    @Override
    public void orderSynchronized(List<SummerfarmSynchronizedOrderInput> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<SaasOrders> orders = list.stream().map(summerfarmSynchronizedOrderInput -> {
            SaasOrders saasOrders = new SaasOrders();
            BeanUtils.copyProperties(summerfarmSynchronizedOrderInput, saasOrders);
            return saasOrders;
        }).collect(Collectors.toList());

        saasOrdersMapper.batchInsert(orders);
    }

    @Override
    public SummerfarmResult getSkuInfo(SummerfarmSkuInput input) {
        List<SummerfarmSkuDTO> summerfarmSkuDTOS = areaSkuMapper.querySkuPriceInfo(input.getSkuIds());
        return SummerfarmResult.getOK(summerfarmSkuDTOS);
    }

    @Override
    public void skuSynchronizedTask() {
        // 获取昨天的日期
        String yesterDay = TimeUtils.getBeforeTimeString(new Date(), 1);
        // 获取昨天的开始和结束时间
        String[] dayStartAndEndTimeStr = TimeUtils.getDayStartAndEndTimeStr(yesterDay);
        // 获取要同步的sku信息
        List<SummerfarmSynchronizedSkuDTO> summerfarmSynchronizedSkuDTOS = inventoryService.queryNeedSynchronizedSkuInfo(dayStartAndEndTimeStr[0], dayStartAndEndTimeStr[1]);
        // 同步
        skuSynchronized(summerfarmSynchronizedSkuDTOS);
    }

    private void skuSynchronized(List<SummerfarmSynchronizedSkuDTO> summerfarmSynchronizedSkuDTOS) {
        if (!CollectionUtils.isEmpty(summerfarmSynchronizedSkuDTOS)) {
            //消息推送
            List<Long> skuIds = summerfarmSynchronizedSkuDTOS.stream().map(SummerfarmSynchronizedSkuDTO::getSkuId).collect(Collectors.toList());

            SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
            msgModel.setMsgType(SummerfarmMsgType.SKU_SYNCHRONIZED);
            SummerfarmSkuInput input = new SummerfarmSkuInput();
            input.setSkuIds(skuIds);
            msgModel.setMsgData(input);
            mqProducer.send(SummerfarmMQTopic.SUMMRFARM_TO_SAAS,null,JSON.toJSONString(msgModel));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SummerfarmResult firstSynchronizedSku() {
        // 获取今天的日期
        String today = TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT);
        // 获取要同步的sku信息
        List<SummerfarmSynchronizedSkuDTO> summerfarmSynchronizedSkuDTOS = inventoryService.queryNeedSynchronizedSkuInfo("2010-01-01 00:00:00", today);
        // 同步
        summerfarmSynchronizedSkuDTOS.stream().map(SummerfarmSynchronizedSkuDTO::getSkuId).collect(Collectors.toList());
        skuSynchronized(summerfarmSynchronizedSkuDTOS);
        return SummerfarmResult.getOK();
    }

    @Override
    public SummerfarmResult queryNeedSynchronizedSku(SummerfarmSkuInput input) {
        List<Long> skuIds = input.getSkuIds();
        SummerfarmSynchronizedSkuDTO summerfarmSynchronizedSkuDTO = inventoryService.queryNeedSynchronizedSkuInfoBySkuId(skuIds.get(0));

        // 查询 一、二、三级类目
        CategoryVO categoryVO = categoryService.selectDetailById(summerfarmSynchronizedSkuDTO.getThirdCategoryId().intValue());
        if (categoryVO != null) {
            summerfarmSynchronizedSkuDTO.setThirdCategoryName(categoryVO.getCategory());
            if (categoryVO.getParentId() != null) {
                CategoryVO secondCategoryVO = categoryService.selectDetailById(categoryVO.getParentId());
                if (secondCategoryVO != null) {
                    summerfarmSynchronizedSkuDTO.setSecondCategoryName(secondCategoryVO.getCategory());
                    summerfarmSynchronizedSkuDTO.setSecondCategoryId(secondCategoryVO.getId().longValue());
                    if (secondCategoryVO.getParentId() != null) {
                        CategoryVO firstCategoryVO = categoryService.selectDetailById(secondCategoryVO.getParentId());
                        if (firstCategoryVO != null) {
                            summerfarmSynchronizedSkuDTO.setFirstCategoryName(firstCategoryVO.getCategory());
                            summerfarmSynchronizedSkuDTO.setFirstCategoryId(firstCategoryVO.getId().longValue());
                        }
                    }
                }
            }
        }

        // 查询产地、存储温度、品牌等属性
        Long pdId = summerfarmSynchronizedSkuDTO.getSpuId();
        ProductsPropertyValue originProperty = productsPropertyValueService.selectByPdIdAndPropertyId(pdId, Global.ORIGIN);
        ProductsPropertyValue brandProperty = productsPropertyValueService.selectByPdIdAndPropertyId(pdId, Global.BRAND);
        ProductsPropertyValue storageTemperatureProperty = productsPropertyValueService.selectByPdIdAndPropertyId(pdId, Global.STORAGE_TEMPERATURE);

        summerfarmSynchronizedSkuDTO.setOrigin(Objects.isNull(originProperty) ? "" : originProperty.getProductsPropertyValue());
        summerfarmSynchronizedSkuDTO.setBrandName(Objects.isNull(brandProperty) ? "" : brandProperty.getProductsPropertyValue());
        summerfarmSynchronizedSkuDTO.setStorageTemperature(Objects.isNull(storageTemperatureProperty) ? "" : storageTemperatureProperty.getProductsPropertyValue());

        // 查询所有skuId
        List<Inventory> inventories = inventoryService.selectByPdIdAndAdminId(pdId.intValue(), Objects.isNull(summerfarmSynchronizedSkuDTO.getAdminId()) ? null : summerfarmSynchronizedSkuDTO.getAdminId().intValue());
        List<Long> skuIdList = inventories.stream().map(Inventory::getInvId).collect(Collectors.toList());
        summerfarmSynchronizedSkuDTO.setSkuIds(skuIdList);
        return SummerfarmResult.getOK(summerfarmSynchronizedSkuDTO);
    }


    @Override
    public SummerfarmResult queryAdminSkuPricingInfo(List<SummerfarmSkuPriceInfoInput> summerfarmSkuPriceInfoInputs) {
        if (CollectionUtils.isEmpty(summerfarmSkuPriceInfoInputs)) {
            return SummerfarmResult.getOK(new ArrayList<>());
        }

        List<Integer> adminIds = summerfarmSkuPriceInfoInputs.stream().map(e->e.getAdminId().intValue()).distinct().collect(Collectors.toList());
        List<Admin> admins = adminMapper.selectByAdminIds(adminIds);
        if (CollectionUtils.isEmpty(admins)) {
            return SummerfarmResult.getOK(new ArrayList<>());
        }
        Map<Integer,Admin> adminMap = admins.stream().collect(Collectors.toMap(Admin::getAdminId,Function.identity()));

        List<Long> skuIds = summerfarmSkuPriceInfoInputs.stream().map(SummerfarmSkuPriceInfoInput :: getSkuId).distinct().collect(Collectors.toList());
        List<Inventory> inventorys = inventoryService.selectByIds(skuIds);
        if (CollectionUtils.isEmpty(inventorys)) {
            return SummerfarmResult.getOK(new ArrayList<>());
        }
        Map<Long,Inventory> inventoryMap = inventorys.stream().collect(Collectors.toMap(Inventory::getInvId,Function.identity()));


        List<String> cityNames = Lists.newArrayList();
        summerfarmSkuPriceInfoInputs.stream().map(SummerfarmSkuPriceInfoInput::getCityName).distinct().forEach(cityNames::addAll);
        // 查询围栏信息
        List<FenceVO> fenceVOs = fenceService.selectByCityNames(cityNames);
        if (CollectionUtils.isEmpty(fenceVOs)) {
            return SummerfarmResult.getOK(new ArrayList<>());
        }
        //一个城市对应得围栏下可能有多个区域
        Map<String,List<FenceVO>> fenceVOMap = fenceVOs.stream().collect(Collectors.groupingBy(FenceVO :: getSubCity));


        // 查询围栏信息
        List<SummerfarmSkuPriceInfoDTO> dtoList = new ArrayList<>();
        Map<Long, List<SummerfarmSkuPriceInfoInput>> adminIdGroups = summerfarmSkuPriceInfoInputs.stream().collect(Collectors.groupingBy(SummerfarmSkuPriceInfoInput::getAdminId));
        for (Map.Entry<Long, List<SummerfarmSkuPriceInfoInput>> entry : adminIdGroups.entrySet()) {
            Admin admin = adminMap.get(entry.getKey().intValue());
            if (admin == null || admin.getDisabled()) {
                return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_ADMIN_ID_ERR.getErrCode(), SummerfarmErrCodeEnum.PARAM_ADMIN_ID_ERR.getErrMsg());
            }
            List<SummerfarmSkuPriceInfoInput> skuPriceInfoInput = entry.getValue();
            Map<Long, List<SummerfarmSkuPriceInfoInput>> skuIdGroups = skuPriceInfoInput.stream().collect(Collectors.groupingBy(SummerfarmSkuPriceInfoInput::getSkuId));
            for (Map.Entry<Long, List<SummerfarmSkuPriceInfoInput>> skuEntry : skuIdGroups.entrySet()){
                Inventory inventory = inventoryMap.get(skuEntry.getKey());
                if (inventory == null) {
                    continue;
                }
                List<String> skuCityNames = Lists.newArrayList();
                skuEntry.getValue().stream().map(SummerfarmSkuPriceInfoInput::getCityName).forEach(skuCityNames::addAll);
                List<FenceVO> skuFenceVOS   = Lists.newArrayList();
                for (String skuCityName :skuCityNames){
                    if (!CollectionUtils.isEmpty(fenceVOMap.get(skuCityName))){
                        skuFenceVOS.addAll(fenceVOMap.get(skuCityName));
                    }
                }
                // 查询围栏信息
                if (CollectionUtils.isEmpty(skuFenceVOS)) {
                    continue;
                }
                List<Integer> areaNos = skuFenceVOS.stream().map(FenceVO :: getAreaNo).collect(Collectors.toList());
                //获取sku 在所有区域下价格
                List<AreaSku> areaSkus = areaSkuMapper.queryListSkuPrice(inventory.getSku(), areaNos);
                Map<Integer,AreaSku> areaSkuMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(areaSkus)){
                    areaSkuMap = areaSkus.stream().collect(Collectors.toMap(AreaSku :: getAreaNo , Function.identity()));
                }
                //获取sku 在所有区域下报价单
                List<MajorPriceDO> majorPrices = majorPriceMapper.queryListMajorPrice(admin.getAdminId(), inventory.getSku(), areaNos);
                Map<Integer,MajorPriceDO> areaMajarPriceMap = Maps.newHashMap();
                if (!CollectionUtils.isEmpty(majorPrices)){
                    areaMajarPriceMap = majorPrices.stream().collect(Collectors.toMap(MajorPriceDO :: getAreaNo , Function.identity()));
                }
                for (SummerfarmSkuPriceInfoInput priceInfoInput : skuEntry.getValue()){
                    SummerfarmSkuPriceInfoDTO dto = new SummerfarmSkuPriceInfoDTO();
                    dto.setCitySupplyPriceId(priceInfoInput.getCitySupplyPriceId());
                    dto.setSkuId(priceInfoInput.getSkuId());
                    List<AreaSku> areaSkuList = Lists.newArrayList();
                    List<MajorPriceDO> majorPriceList = Lists.newArrayList();
                    // 查询围栏信息
                    for (String cityName : priceInfoInput.getCityName()){
                        List<FenceVO> fenceVOList = fenceVOMap.get(cityName);
                        if (CollectionUtils.isEmpty(fenceVOList)){
                            continue;
                        }
                        for (FenceVO fenceVO:fenceVOList){
                            AreaSku areaSku = areaSkuMap.get(fenceVO.getAreaNo());
                            if (Objects.nonNull(areaSku)){
                                areaSkuList.add(areaSku);
                            }
                            MajorPriceDO majorPrice = areaMajarPriceMap.get(fenceVO.getAreaNo());
                            if (Objects.nonNull(majorPrice)){
                                majorPriceList.add(majorPrice);
                            }
                        }
                    }
                    BigDecimal minMajarPrice = null;
                    BigDecimal maxMajarPrice = null;
                    if (!CollectionUtils.isEmpty(majorPriceList)){
                        minMajarPrice = majorPriceList.stream().min(Comparator.comparing(MajorPriceDO::getMinPrice)).get().getMinPrice();
                        maxMajarPrice = majorPriceList.stream().max(Comparator.comparing(MajorPriceDO::getMaxPrice)).get().getMaxPrice();
                    }
                    if (!CollectionUtils.isEmpty(areaSkuList)){
                        BigDecimal minSkuPrice = null;
                        BigDecimal maxSkuPrice = null;
                        minSkuPrice = areaSkuList.stream().min(Comparator.comparing(AreaSku::getPrice)).get().getPrice();
                        maxSkuPrice = areaSkuList.stream().max(Comparator.comparing(AreaSku::getPrice)).get().getPrice();
                        minMajarPrice = (minMajarPrice != null && minMajarPrice.compareTo(minSkuPrice) < 0) ? minMajarPrice : minSkuPrice;
                        maxMajarPrice = (maxMajarPrice != null && maxMajarPrice.compareTo(maxSkuPrice) > 0) ? maxMajarPrice : maxSkuPrice;
                    }
                    dto.setMinPrice(minMajarPrice);
                    dto.setMaxPrice(maxMajarPrice);
                    dtoList.add(dto);
                }
            }
        }
        return SummerfarmResult.getOK(dtoList);
    }

    @Override
    public SummerfarmResult querySkuMallPrice(SummerfarmSkuMallPriceInput input) {
        //处理特殊地址
        checkCityWithoutArea(input);
        //根据地址匹配围栏信息，获取运营服务区编号
        FenceVO fenceVO = fenceService.selectFenceByCityArea(input.getArea(), input.getCity());
        if (fenceVO == null) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrMsg());
        }
        Integer areaNo = fenceVO.getAreaNo();

        List<SummerfarmSkuMallPriceDTO> priceDTOList = new ArrayList<>();
        for (Long skuId : input.getSkuIds()) {
            BigDecimal price = queryMallPrice(input.getAdminId(), areaNo, skuId);

            SummerfarmSkuMallPriceDTO dto = new SummerfarmSkuMallPriceDTO();
            dto.setSkuId(skuId);
            dto.setPrice(price);
            priceDTOList.add(dto);
        }

        return SummerfarmResult.getOK(priceDTOList);
    }

    /**
     * 查询商城价格，优先级：查账期报价、现结报价、商城定价
     *
     * @param adminId 大客户ID
     * @param areaNo  运营服务区域
     * @param skuId   skuId
     * @return 价格
     */
    private BigDecimal queryMallPrice(Long adminId, Integer areaNo, Long skuId) {
        Inventory inventory = inventoryService.selectById(skuId);
        if (inventory == null) {
            return null;
        }

        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, inventory.getSku());
        if (Objects.isNull(areaSku) || Objects.isNull(areaSku.getOnSale()) || !areaSku.getOnSale()) {
            return null;
        }

        //报价单优先级：账期报价单、现结报价单
        MajorPrice billMajorPrice = majorPriceMapper.selectValidMajorPrice(adminId.intValue(), areaNo, inventory.getSku(), MajorDirectEnum.PERIOD.getType());
        MajorPrice nowMajorPrice = majorPriceMapper.selectValidMajorPrice(adminId.intValue(), areaNo, inventory.getSku(), MajorDirectEnum.CASH.getType());

        if (billMajorPrice != null && Objects.equals(billMajorPrice.getPriceType(), MajorPriceType.MALL_PRICE.ordinal())) {
            billMajorPrice.setPrice(areaSku.getPrice());
        }

        if (nowMajorPrice != null && Objects.equals(nowMajorPrice.getPriceType(), MajorPriceType.MALL_PRICE.ordinal())) {
            nowMajorPrice.setPrice(areaSku.getPrice());
        }

        if (billMajorPrice == null && nowMajorPrice != null) {
            return nowMajorPrice.getPrice();
        } else if (nowMajorPrice == null && billMajorPrice != null) {
            return billMajorPrice.getPrice();
        } else if (billMajorPrice != null && nowMajorPrice != null) {
            return billMajorPrice.getPrice().compareTo(nowMajorPrice.getPrice()) > 0 ? nowMajorPrice.getPrice() : billMajorPrice.getPrice();
        }

        // 如果没有创建报价或者都是商城价，使用原价
        return areaSku.getPrice();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SummerfarmResult orderTurnSelf(SummerfarmOrderInput input) {
        log.info("saas订单转自提，input:{}", JsonUtil.toJson(input));
        // 校验可否自提
        SummerfarmResult summerfarmResult = checkSelfPullParam(input);
        if (!summerfarmResult.isSuccess()) {
            return summerfarmResult;
        }
        TmsDeliveryPlan queryPlan = new TmsDeliveryPlan();
        queryPlan.setStoreId(input.getStoreId());
        queryPlan.setTenantId(input.getTenantId());
        queryPlan.setOrderNo(input.getOrderNo());
        TmsDeliveryPlan plan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(queryPlan);
        TmsDeliveryPlan update = new TmsDeliveryPlan();
        update.setId(plan.getId());
        update.setDeliveryType(TmsDeliveryPlanTypeEnum.SELF.ordinal());
        update.setStatus(TmsDeliveryPlanStatusEnum.CANCEL_DELIVERY.ordinal());
        // 修改配送类型为自提
        tmsDeliveryPlanMapper.updateTmsDeliveryPlan(update);

        // 生成自提出库任务
        createStockTask(input, plan);

        return SummerfarmResult.getOK();
    }

    @Override
    public SummerfarmResult queryOrderItemWarehouseAddress(SummerfarmOrderInput input) {
        if (Objects.isNull(input) || StringUtils.isEmpty(input.getOrderNo()) || Objects.isNull(input.getStoreId()) || Objects.isNull(input.getTenantId())) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.ORDER_IS_NULL.getErrCode(), SummerfarmErrCodeEnum.ORDER_IS_NULL.getErrMsg());
        }

        TmsDeliveryPlan queryPlan = new TmsDeliveryPlan();
        queryPlan.setStoreId(input.getStoreId());
        queryPlan.setTenantId(input.getTenantId());
        queryPlan.setOrderNo(input.getOrderNo());
        TmsDeliveryPlan plan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(queryPlan);
        if (Objects.isNull(plan)) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrCode(), SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrMsg());
        }
        List<TmsDeliveryPlanDetailVO> details = tmsDeliveryPlanDetailMapper.selectWarehouseDetailByDeliveryPlanId(plan.getId());
        Iterator<Map.Entry<Integer, List<TmsDeliveryPlanDetailVO>>> iterator = details.stream().collect(Collectors.groupingBy(TmsDeliveryPlanDetailVO::getWarehouseNo)).entrySet().iterator();
        List<SummerfarmSelfOrderDTO> summerfarmSelfOrderDTOS = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<TmsDeliveryPlanDetailVO>> entry = iterator.next();
            Integer warehouseNo = entry.getKey();
            List<TmsDeliveryPlanDetailVO> detailVos = entry.getValue();
            List<Integer> skuList = detailVos.stream().map(TmsDeliveryPlanDetailVO::getInvId).collect(Collectors.toList());
            WarehouseStorageCenter center = storageCenterMapper.selectByWarehouseNo(warehouseNo);
            Admin admin = adminMapper.selectByPrimaryKey(center.getManageAdminId());
            SummerfarmSelfOrderDTO orderDTO = new SummerfarmSelfOrderDTO();
            orderDTO.setWarehouseNo(warehouseNo);
            orderDTO.setSkuList(skuList);
            orderDTO.setAddress(center.getAddress() + WHITE_SPACE + admin.getRealname() + WHITE_SPACE + admin.getPhone());
            summerfarmSelfOrderDTOS.add(orderDTO);
        }

        return SummerfarmResult.getOK(summerfarmSelfOrderDTOS);
    }

    /**
     * 生成自提出库任务
     *
     * @param input
     * @param plan
     */
    private void createStockTask(SummerfarmOrderInput input, TmsDeliveryPlan plan) {
        List<TmsDeliveryPlanDetailVO> details = tmsDeliveryPlanDetailMapper.selectWarehouseDetailByDeliveryPlanId(plan.getId());
        Map<Integer, List<TmsDeliveryPlanDetailVO>> tmsDeliveryPlanDetailMap = details.stream().collect(Collectors.groupingBy(TmsDeliveryPlanDetailVO::getWarehouseNo));
        Set<Integer> warehouseNoSet = tmsDeliveryPlanDetailMap.keySet();
        List<SummerfarmOrderSelfLiftingDTO> orderSelfLiftingDTOList = input.getOrderSelfLiftingDTOList();
        for (SummerfarmOrderSelfLiftingDTO selfLiftingDTO : orderSelfLiftingDTOList) {
            if (!warehouseNoSet.contains(selfLiftingDTO.getWarehouseNo())) {
                log.error("saas自提库存仓编号异常编号为{}", selfLiftingDTO.getWarehouseNo());
                return;
            }
            StockTask stockTask = new StockTask();
            stockTask.setTaskNo(input.getOrderNo());
            stockTask.setAreaNo(selfLiftingDTO.getWarehouseNo());
            stockTask.setType(StoreRecordType.OWN_SALE_OUT.getId());
            stockTask.setExpectTime(selfLiftingDTO.getExpectTime());
            stockTask.setAddtime(input.getCreateTime());
            stockTask.setState(StockTaskState.WAIT_IN_OUT.getId());
            stockTask.setOutStoreNo(plan.getStoreNo());
            stockTask.setOutType(SaleOutTypeEnum.NORMAL.ordinal());
            stockTaskMapper.insert(stockTask);

            //根据sku分组处理库存信息
            Map<String, List<TmsDeliveryPlanDetailVO>> tmsDeliveryPlanBySku = tmsDeliveryPlanDetailMap.get(selfLiftingDTO.getWarehouseNo()).stream().collect(Collectors.groupingBy(TmsDeliveryPlanDetailVO::getSku));
            Set<String> keySet = tmsDeliveryPlanBySku.keySet();
            for (String key : keySet) {
                List<TmsDeliveryPlanDetailVO> tmsDeliveryPlanDetailVOList = tmsDeliveryPlanBySku.get(key);
                Integer quantity = tmsDeliveryPlanDetailVOList.stream().mapToInt(TmsDeliveryPlanDetailVO::getAmount).sum();
                StockTaskItem stockTaskItem = new StockTaskItem(stockTask.getId(), key, quantity, NumberUtils.INTEGER_ZERO);
                stockTaskItemMapper.insert(stockTaskItem);
                StockTaskOrderSku orderSku = this.buildTaskOrderSku(stockTask, stockTaskItem);
                stockTaskOrderSkuService.insertStockTaskOrderSku(orderSku);
                areaStoreService.updateAreaStoreStatus(selfLiftingDTO.getWarehouseNo(), key);
            }

        }
    }

    private StockTaskOrderSku buildTaskOrderSku(StockTask stockTask, StockTaskItem stockTaskItem) {
        return StockTaskOrderSku.builder()
                .stockTaskId(stockTask.getId().longValue())
                .outOrderNo(stockTask.getTaskNo())
                .sku(stockTaskItem.getSku())
                .quantity(stockTaskItem.getQuantity())
                .actualQuantity(NumberUtils.INTEGER_ZERO)
                .creator("系统-自提")
                .operator("系统-自提").build();
    }

    /**
     * 外部自提参数校验
     *
     * @param input
     * @return
     */
    private SummerfarmResult checkSelfPullParam(SummerfarmOrderInput input) {
        if (Objects.isNull(input) || StringUtils.isEmpty(input.getOrderNo()) || Objects.isNull(input.getStoreId()) || Objects.isNull(input.getTenantId())) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrCode(), SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrMsg());
        }
        TmsDeliveryPlan queryPlan = new TmsDeliveryPlan();
        queryPlan.setStoreId(input.getStoreId());
        queryPlan.setTenantId(input.getTenantId());
        queryPlan.setOrderNo(input.getOrderNo());
        TmsDeliveryPlan plan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(queryPlan);
//        LocalDate deliveryTime = plan.getDeliveryTime();
//        LocalDateTime canAfterOrderTime = LocalDateTime.of(deliveryTime.minusDays(1), LocalTime.of(20, 0, 0));
//        // 超过截单时间不可自提
//        if (LocalDateTime.now().isAfter(canAfterOrderTime) && Objects.equals(plan.getStatus(), 0)) {
//            return SummerfarmResult.getError(SummerfarmErrCodeEnum.ORDER_AFTER_END_TIME.getErrCode(), SummerfarmErrCodeEnum.ORDER_AFTER_END_TIME.getErrMsg());
//        }
        if (Objects.equals(TmsDeliveryPlanTypeEnum.SELF.ordinal(), plan.getDeliveryType())) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.ORDER_SELF_IS_EXIST.getErrCode(), SummerfarmErrCodeEnum.ORDER_SELF_IS_EXIST.getErrMsg());
        }
        if (!Objects.equals(TmsDeliveryPlanStatusEnum.IN_DELIVERY.ordinal(), plan.getStatus())) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.ORDER_IS_NOT_LOCK.getErrCode(), SummerfarmErrCodeEnum.ORDER_IS_NOT_LOCK.getErrMsg());
        }

        List<TmsDeliveryPlanDetail> tmsDeliveryPlanDetails = tmsDeliveryPlanDetailMapper.selectDetailByDeliveryPlanId(plan.getId());
        List<TmsDeliveryPlanDetail> collect = tmsDeliveryPlanDetails.stream().filter(x -> x.getAmount() > 0).collect(Collectors.toList());
        // 不存在配送数量大于0的返回失败
        if (CollectionUtils.isEmpty(collect)) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_NON_DELIVERY_PLAN.getErrCode(), SummerfarmErrCodeEnum.PARAM_NON_DELIVERY_PLAN.getErrMsg());
        }
        return SummerfarmResult.getOK();
    }

    /**
     * 自提订单消息
     *
     * @param input
     * @param code
     */
    private void sendSelfOrder(SummerfarmOrderInput input, String code) {
        //消息推送
        SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
        msgModel.setMsgType(SummerfarmMsgType.ORDER_TURN_SELF);
        SummerfarmOrderDTO orderDTO = new SummerfarmOrderDTO();
        orderDTO.setOrderNo(input.getOrderNo());
        orderDTO.setCode(code);
        orderDTO.setStoreId(input.getStoreId());
        orderDTO.setTenantId(input.getTenantId());
        msgModel.setMsgData(orderDTO);
        mqProducer.send(SummerfarmMQTopic.SAAS_MANAGE,null,JSON.toJSONString(msgModel));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void afterSaleDelivery(SummerfarmAfterSaleOrderInput input) {
        //参数校验
        AjaxResult result = checkAfterSaleOrderPullParam(input);

        //未通过参数校验
        if (!result.isSuccess()) {
            //消息推送
            sendAfterSaleOrderMsg(result.getCode(), input, null, null);
            return;
        }
        //根据地址获取围栏信息
        String area = input.getArea();
        String city = input.getCity();
        FenceVO fenceVO = fenceService.selectFenceByCityArea(area, city);
        if (Objects.isNull(fenceVO)) {
            //消息推送 当前点位不配送
            sendAfterSaleOrderMsg(SummerfarmErrCodeEnum.AREA_CITY_NON_EXISTENT.getErrCode(), input, null, null);
            return;
        }
        //判断订单是否已经存在
        if (cheakOrderIsHave(input)) {
            return;
        }
        //城配仓
        Integer storeNo = fenceVO.getStoreNo();
        //库存操作
        lockStockOperate(input, storeNo);
        //生成售后配送计划
        createTmsAfterSaleDeliveryPlan(input);
        //推送订单完成,并推送配送时间
        sendAfterSaleOrderMsg(SummerfarmErrCodeEnum.SUCCESS.getErrCode(), input, getDeliveryDate(city, area, storeNo),storeNo);
    }

    /**
     * 库存操作
     *
     * @param input
     * @param storeNo
     */
    private void lockStockOperate(SummerfarmAfterSaleOrderInput input, Integer storeNo) {
        log.info("库存操作进入方法：请求报文", input);
        try {
            List<SummerfarmAfterSaleOrderItemInput> itemInputList = input.getItemInputList();
            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            for (SummerfarmAfterSaleOrderItemInput summerfarmAfterSaleOrderItemInput : itemInputList) {
                //类型为配送时，需要调用方法锁定库存
                if (TmsDeliveryPlanTypeEnum.DELIVERY.ordinal() == summerfarmAfterSaleOrderItemInput.getDeliveryType()) {
                    Integer quantity = summerfarmAfterSaleOrderItemInput.getQuantity();
                    String sku = summerfarmAfterSaleOrderItemInput.getSku();
                    log.info("库存进入配送判断方法：sku是{},数量是{}", sku, quantity);
                    //加冻结
                    areaStoreService.updateLockStockByStoreNo(quantity, sku, storeNo, SaleStockChangeTypeEnum.SAAS_AFTER_SALE, input.getOrderNo(), recordMap);
                    //加虚拟库存
                    areaStoreService.updateOnlineStockByStoreNo(true, -quantity, sku, storeNo, SaleStockChangeTypeEnum.SAAS_AFTER_SALE, input.getOrderNo(), recordMap, 0);
                    //插入数据
                    quantityChangeRecordService.insertRecord(recordMap);

                    log.info("库存冻结、加虚拟完毕：sku是{},数量是{}", sku, quantity);
                }
            }
        } catch (Exception e) {
            sendAfterSaleOrderMsg(SummerfarmErrCodeEnum.QUANTITY_INSUFFICIENT.getErrCode(), input, null, storeNo);
            throw new RuntimeException(SummerfarmErrCodeEnum.QUANTITY_INSUFFICIENT.getErrCode());
        }
    }

    private boolean cheakOrderIsHave(SummerfarmAfterSaleOrderInput input) {
        //订单是否存在
        TmsDeliveryPlan tmsDeliveryPlan = new TmsDeliveryPlan();
        tmsDeliveryPlan.setOrderNo(input.getOrderNo());
        tmsDeliveryPlan.setStoreId(input.getStoreId());
        tmsDeliveryPlan.setTenantId(input.getTenantId());
        TmsDeliveryPlan queryResultPlan = tmsDeliveryPlanMapper.selectTmsDeliveryPlan(tmsDeliveryPlan);

        if (!Objects.isNull(queryResultPlan)) {
            //订单存在
            sendAfterSaleOrderMsg(SummerfarmErrCodeEnum.ORDER_NO_IS_EXIST.getErrCode(), input, null, null);
            return true;
        }
        return false;
    }

    public void createTmsAfterSaleDeliveryPlan(SummerfarmAfterSaleOrderInput orderInput) {
        FenceVO fenceVO = fenceService.selectFenceByCityArea(orderInput.getArea(), orderInput.getCity());
        Integer storeNo = fenceVO.getStoreNo();

        SummerfarmOrderInput summerfarmOrderInput = ConvertUtils.convert(orderInput, SummerfarmOrderInput.class);
        Integer contactId = getOutSideContact(summerfarmOrderInput, storeNo);
        //生成配送单
        TmsDeliveryPlan tmsDeliveryPlan = new TmsDeliveryPlan();
        //配送时间
        LocalDate orderDeliveryDate = getDeliveryDate(orderInput.getCity(), orderInput.getArea(), storeNo);
        //生成配送单详情
        Integer deliveryType = orderInput.getDeliveryType();
        Long tenantId = orderInput.getTenantId();
        Long storeId = orderInput.getStoreId();
        tmsDeliveryPlan.setStatus(TmsDeliveryPlanStatusEnum.IN_DELIVERY.ordinal());
        tmsDeliveryPlan.setStoreNo(storeNo);
        tmsDeliveryPlan.setStoreId(storeId);
        tmsDeliveryPlan.setTenantId(tenantId);
        tmsDeliveryPlan.setDeliveryType(deliveryType);
        tmsDeliveryPlan.setDeliveryTime(orderDeliveryDate);
        tmsDeliveryPlan.setContactId(contactId);
        tmsDeliveryPlan.setOrderNo(orderInput.getOrderNo());
        tmsDeliveryPlan.setType(2);
        tmsDeliveryPlanMapper.saveTmsDeliveryPlan(tmsDeliveryPlan);
        //插入详情
        ArrayList<TmsDeliveryPlanDetail> tmsDeliveryPlanDetails = new ArrayList<>();
        List<SummerfarmAfterSaleOrderItemInput> itemInputList = orderInput.getItemInputList();
        for (SummerfarmAfterSaleOrderItemInput input : itemInputList) {
            TmsDeliveryPlanDetail tmsDeliveryPlanDetail = new TmsDeliveryPlanDetail();
            tmsDeliveryPlanDetail.setAmount(input.getQuantity());
            tmsDeliveryPlanDetail.setSku(input.getSku());
            tmsDeliveryPlanDetail.setTmsDeliveryPlanId(tmsDeliveryPlan.getId());
            tmsDeliveryPlanDetail.setStatus(TmsDeliveryPlanStatusEnum.WAIT_DELIVERY.ordinal());
            tmsDeliveryPlanDetail.setDeliveryType(input.getDeliveryType());
            tmsDeliveryPlanDetails.add(tmsDeliveryPlanDetail);
        }
        tmsDeliveryPlanDetailMapper.saveBatchTmsDeliveryPlanDetail(tmsDeliveryPlanDetails);
    }

    private LocalDate getSaasDeliveryTime(String city, String area, Integer storeNo, String requireDeliveryTimeStr) {
        LocalDate requireDeliveryTime = null;
        if (StrUtil.isNotBlank(requireDeliveryTimeStr)){
            requireDeliveryTime = LocalDate.parse(requireDeliveryTimeStr, DateTimeFormatter.ofPattern(TimeUtils.FORMAT_DATE));
        }
        if (requireDeliveryTime != null && requireDeliveryTime.equals(getDeliveryDate(city, area, storeNo))){
            //saas侧指定配送时间 且订单归属围栏支持该配送时间
            log.info("【saas侧】指定配送时间设置成功,requireDeliveryTime:{},指定围栏【{}-{}】校验配送时间成功",requireDeliveryTime,city,area);
            return requireDeliveryTime;
        }else {
            LocalDate deliveryDate = getDeliveryDate(city, area, storeNo);
            log.info("【saas侧】指定配送时间设置失败,requireDeliveryTime:{},获取指定围栏【{}-{}】的下个有效配送日,deliveryTime:{}",requireDeliveryTime,city,area,deliveryDate);
            return deliveryDate;
        }
    }


    /**
     * 外部推送参数校验
     *
     * @param orderInput
     * @return
     */
    private AjaxResult checkAfterSaleOrderPullParam(SummerfarmAfterSaleOrderInput orderInput) {
        //参数校验
        if (orderInput == null || Objects.isNull(orderInput.getOrderNo())) {
            return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_IS_NULL.getErrCode());
        }
        //地址处理
        checkCityWithoutArea(orderInput);
        //查询订单
        //参数校验
        List<SummerfarmAfterSaleOrderItemInput> itemInputList = orderInput.getItemInputList();
        for (SummerfarmAfterSaleOrderItemInput input : itemInputList) {

            //数量不存在
            if (Objects.isNull(input.getQuantity()) || Objects.equals(input.getQuantity(), 0)) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_QUANTITY_IS_NULL.getErrCode());
            }
            if (Objects.isNull(input.getSkuId()) && Objects.isNull(input.getSku())) {
                return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_IS_NULL.getErrCode());
            }
            //skuId
            Long skuId = input.getSkuId();
            if (Objects.nonNull(skuId)) {
                Inventory inventory = inventoryService.selectById(skuId);
                if (Objects.isNull(inventory)) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_INV_ID_NON_EXISTENT.getErrCode());
                }
                input.setSku(inventory.getSku());
                continue;
            }
            //sku存在
            if (Objects.nonNull(input.getSku())) {
                AjaxResult result = inventoryService.selectSku(input.getSku());
                if (Objects.isNull(result.getData())) {
                    return AjaxResult.getError(SummerfarmErrCodeEnum.PARAM_SKU_NON_EXISTENT.getErrCode());
                }
            }

        }
        return AjaxResult.getOK();

    }

    /**
     * 售后发消息
     *  @param code
     * @param orderInput
     * @param orderDeliveryDate
     * @param storeNo
     */
    @Override
    public void sendAfterSaleOrderMsg(String code, SummerfarmAfterSaleOrderInput orderInput, LocalDate orderDeliveryDate, Integer storeNo) {
        SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
        msgModel.setMsgType(SummerfarmMsgType.AFTER_SALE_DELIVERY);
        SummerfarmAfterSaleDeliveryDTO summerfarmAfterSaleDeliveryDTO = new SummerfarmAfterSaleDeliveryDTO();
        summerfarmAfterSaleDeliveryDTO.setOrderNo(orderInput.getOrderNo());
        summerfarmAfterSaleDeliveryDTO.setDeliveryTime(orderDeliveryDate);
        summerfarmAfterSaleDeliveryDTO.setStoreNo(storeNo);
        summerfarmAfterSaleDeliveryDTO.setCode(code);
        summerfarmAfterSaleDeliveryDTO.setCodeMsg(SummerfarmErrCodeEnum.typeMap.get(code));
        msgModel.setMsgData(summerfarmAfterSaleDeliveryDTO);
        mqProducer.send(SummerfarmMQTopic.SUMMRFARM_TO_SAAS,null,JSON.toJSONString(msgModel));
    }

    @Override
    public List<OutsideAfterSaleVO> getAfterSalePassData(LocalDate deliveryDate, Integer type, Integer storeNo) {
        if (deliveryDate == null) {
            throw new DefaultServiceException("查询时间不能为空");
        }
        if (type == null) {
            throw new DefaultServiceException("查询类型不能为空");
        }
        //type 0 入库查询（回收） 1 出库查询（配送）
        //deliveryType 0配送 1回收
        //根据日期查询需要配送的售后单没有取消的数据
        List<OutsideAfterSaleVO> outsideAfterSaleVOS = Objects.equals(type, 1) ? tmsDeliveryPlanMapper.getAfterSalePassData(deliveryDate, storeNo) :
                tmsDeliveryPlanMapper.getInAfterSalePassData(deliveryDate, storeNo);
        return outsideAfterSaleVOS;
    }

    @Override
    public List<OutsideAfterSaleVO> getAfterSalePassDataForCross(LocalDate deliveryDate, Integer type, Integer storeNo) {
        if (deliveryDate == null) {
            throw new DefaultServiceException("查询时间不能为空");
        }
        if (type == null) {
            throw new DefaultServiceException("查询类型不能为空");
        }
        //type 0 入库查询（回收） 1 出库查询（配送）
        //deliveryType 0配送 1回收
        //根据日期查询需要配送的售后单没有取消的数据
        List<OutsideAfterSaleVO> outsideAfterSaleVOS = Objects.equals(type, 1) ? tmsDeliveryPlanMapper.getAfterSalePassDataForCross(deliveryDate, storeNo) :
                tmsDeliveryPlanMapper.getInAfterSalePassDataForCross(deliveryDate, storeNo);
        return outsideAfterSaleVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SummerfarmResult cancelAfterSale(CancleAfterSale cancleAfterSale) {
        String orderNo = cancleAfterSale.getOrderNo();
        LocalDateTime time = cancleAfterSale.getTime();
        //根据售后单号查询数据
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectAfterByOrderNo(orderNo);
        if (tmsDeliveryPlan == null) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrMsg());
        }
        //校验时间 是否在配送日前一天晚8点之前
        LocalDate deliveryTime = tmsDeliveryPlan.getDeliveryTime();
        LocalDateTime lastDateTime = deliveryTime.plusDays(-1).atTime(20, 0, 0);
        if (time.isAfter(lastDateTime)) {
            return SummerfarmResult.getError(SummerfarmErrCodeEnum.AFTER_SALE_TIME_OVER_ERR.getErrCode(), SummerfarmErrCodeEnum.AFTER_SALE_TIME_OVER_ERR.getErrMsg());
        }
        //如果是售后配送的需要释放库存
        freedStock(orderNo, tmsDeliveryPlan);

        //更新售后状态 取消
        tmsDeliveryPlan.setStatus(TmsDeliveryPlanStatusEnum.CANCEL_DELIVERY.ordinal());
        tmsDeliveryPlanMapper.updateTmsDeliveryPlan(tmsDeliveryPlan);

        return SummerfarmResult.getOK();
    }

    /**
     * 释放库存
     *
     * @param orderNo
     * @param tmsDeliveryPlan
     */
    private void freedStock(String orderNo, TmsDeliveryPlan tmsDeliveryPlan) {
        if (tmsDeliveryPlan.getDeliveryType() == TmsDeliveryPlanTypeEnum.DELIVERY.ordinal() || tmsDeliveryPlan.getDeliveryType() == TmsDeliveryPlanTypeEnum.DELIVERY_RECOVERY.ordinal()) {
            Integer storeNo = tmsDeliveryPlan.getStoreNo();
            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            //查询配送详情
            List<TmsDeliveryPlanDetail> tmsDeliveryPlanDetails = tmsDeliveryPlanDetailMapper.selectDetailByDeliveryPlanId(tmsDeliveryPlan.getId());
            for (TmsDeliveryPlanDetail tmsDeliveryPlanDetail : tmsDeliveryPlanDetails) {
                //只有是配送才需要释放库存
                if (tmsDeliveryPlanDetail.getDeliveryType() == TmsDeliveryPlanTypeEnum.DELIVERY.ordinal()) {
                    String sku = tmsDeliveryPlanDetail.getSku();
                    Integer quantity = tmsDeliveryPlanDetail.getAmount();
                    try {
                        //减冻结
                        areaStoreService.updateLockStockByStoreNo(-quantity, sku, storeNo, SaleStockChangeTypeEnum.SAAS_AFTER_SALE, orderNo, recordMap);
                        //加虚拟库存
                        areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, storeNo, SaleStockChangeTypeEnum.SAAS_AFTER_SALE, orderNo, recordMap, 0);
                        //插入数据
                        quantityChangeRecordService.insertRecord(recordMap);
                    } catch (Exception e) {
                        throw new DefaultServiceException("释放库存失败");
                    }
                }
            }
        }
    }

    @Override
    public SummerfarmResult getDeliveryTime(String orderNo) {
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectAfterByOrderNo(orderNo);
        if (tmsDeliveryPlan == null) {
            throw new DefaultServiceException(SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrMsg());
        }

        SummerfarmAfterSaleDeliveryDTO afterSaleDeliveryDTO = new SummerfarmAfterSaleDeliveryDTO();
        afterSaleDeliveryDTO.setOrderNo(tmsDeliveryPlan.getOrderNo());
        afterSaleDeliveryDTO.setDeliveryTime(tmsDeliveryPlan.getDeliveryTime());
        return SummerfarmResult.getOK(afterSaleDeliveryDTO);
    }

    @Override
    public String getOrderNoByAfterSaleNo(String afterSaleNo) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("afterSaleOrderNo", afterSaleNo);
            String s = jsonObject.toJSONString();
            String body = HttpUtil.createPost(Global.SAAS_QUERY_AFTER_SALE).body(s).execute().body();
            SummerfarmResult summerfarmResult = JSONObject.parseObject(body, SummerfarmResult.class);

            if (Objects.isNull(summerfarmResult.getData())) {
                return null;
            }
            com.cosfo.summerfarm.model.dto.order.OrderVO vo = JSONObject.parseObject(JSON.toJSONString(summerfarmResult.getData()), com.cosfo.summerfarm.model.dto.order.OrderVO.class);
            return vo.getOrderNo();
        } catch (Exception e) {
            log.error("查询失败 e={}", e.getMessage());
        }
        return null;
    }

    @Override
    public SummerfarmResult queryCitySupplyStatus(List<SummerfarmSkuSupplyStatusInput> summerfarmSkuSupplyStatusInputs) {
        if (CollectionUtils.isEmpty(summerfarmSkuSupplyStatusInputs)) {
            return SummerfarmResult.getOK(new ArrayList<>());
        }

        List<SummerfarmSkuSupplyStatusDTO> summerfarmSkuSupplyStatusDTOS = new ArrayList<>(10);
        for (SummerfarmSkuSupplyStatusInput summerfarmSkuSupplyStatusInput : summerfarmSkuSupplyStatusInputs) {
            SummerfarmSkuSupplyStatusDTO summerfarmSkuSupplyStatusDTO = new SummerfarmSkuSupplyStatusDTO();
            summerfarmSkuSupplyStatusDTOS.add(summerfarmSkuSupplyStatusDTO);
            // 默认没有库存
            summerfarmSkuSupplyStatusDTO.setSupplyStatus(SupplyStatusEnum.NOT_HAVING_INVENTORY.getCode());
            summerfarmSkuSupplyStatusDTO.setCitySupplyPriceId(summerfarmSkuSupplyStatusInput.getCitySupplyPriceId());
            // 查询围栏信息
            List<FenceVO> fenceVOS = fenceService.selectByCityName(summerfarmSkuSupplyStatusInput.getCityName());
            Inventory inventory = inventoryService.selectById(summerfarmSkuSupplyStatusInput.getSkuId());
            // 查询库存信息
            if (!CollectionUtils.isEmpty(fenceVOS) && !Objects.isNull(inventory)) {
                for (FenceVO fenceVO : fenceVOS) {
                    if (!Objects.isNull(fenceVO.getStoreNo())) {
                        AreaStoreVO onlineQuantity = areaStoreService.getOnlineQuantityByAddress(fenceVO.getStoreNo(), inventory.getSku());
                        if (!Objects.isNull(onlineQuantity) && onlineQuantity.getOnlineQuantity() > 0) {
                            summerfarmSkuSupplyStatusDTO.setSupplyStatus(SupplyStatusEnum.HAVING_INVENTORY.getCode());
                            break;
                        }
                    }
                }
            }
        }

        return SummerfarmResult.getOK(summerfarmSkuSupplyStatusDTOS);
    }

    @Override
    public SummerfarmResult queryAreaStoreQuantity(SummerfarmAgentSkuWarehouseDataInput dataInput) {
        List<Long> skuId = dataInput.getSkuId();
        List<Long> warehouseIds = dataInput.getWarehouseIds();
        if(CollectionUtils.isEmpty(skuId) || CollectionUtils.isEmpty(warehouseIds)){
            return SummerfarmResult.getError("","异常数据");
        }
        List<Inventory> inventories = inventoryService.selectByIds(skuId);
        AreaStoreQueryInput storeQueryInput = AreaStoreQueryInput.builder()
                .pageNum(dataInput.getPageNum())
                .pageSize(dataInput.getPageSize())
                .skuList(inventories)
                .warehouseNoList(dataInput.getWarehouseIds())
                .saleOut(dataInput.getSaleOut()).build();
        PageInfo<SummerfarmAgentSkuWarehouseDataDTO> pageInfo = areaStoreService.queryInput(storeQueryInput);
        return SummerfarmResult.getOK(pageInfo);
    }

    @Override
    public SummerfarmResult queryCategory(Set<Long> ids) {
        List<Integer> idList = Collections.emptyList();
        if(!CollectionUtils.isEmpty(ids)){
            idList = ids.stream().map(Long::intValue).collect(Collectors.toList());
        }
        List<CategoryDTO> dtos = Collections.emptyList();
        List<Category> categories = categoryMapper.selectTypeByIds(idList);
        if(!CollectionUtils.isEmpty(categories)){
            dtos = categories.stream().map(e->{
                CategoryDTO categoryDTO = new CategoryDTO();
                BeanUtils.copyProperties(e,categoryDTO);
                return categoryDTO;
            }).collect(Collectors.toList());
        }
        return SummerfarmResult.getOK(dtos);
    }

    @Override
    public SummerfarmResult queryDeliveryTime(List<String> orderNos) {
        if(CollectionUtils.isEmpty(orderNos)){
            throw new DefaultServiceException(SummerfarmErrCodeEnum.ORDER_IS_NULL.getErrCode(), SummerfarmErrCodeEnum.ORDER_IS_NULL.getErrMsg());
        }
        List<TmsDeliveryPlan> tmsDeliveryPlanList = tmsDeliveryPlanMapper.queryListByOrderNos(orderNos);
        if (CollectionUtils.isEmpty(tmsDeliveryPlanList)) {
            throw new DefaultServiceException(SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrCode(), SummerfarmErrCodeEnum.PARAM_ORDER_NO_EXISTENT.getErrMsg());
        }
        List<SummerfarmOrderDTO> summerfarmOrderDTOS = new ArrayList<>();
        for (TmsDeliveryPlan tmsDeliveryPlan : tmsDeliveryPlanList) {
            SummerfarmOrderDTO summerfarmOrderDTO = new SummerfarmOrderDTO();
            summerfarmOrderDTO.setId(Long.parseLong(String.valueOf(tmsDeliveryPlan.getId())));
            summerfarmOrderDTO.setStoreId(tmsDeliveryPlan.getStoreId());
            summerfarmOrderDTO.setTenantId(tmsDeliveryPlan.getTenantId());
            summerfarmOrderDTO.setOrderNo(tmsDeliveryPlan.getOrderNo());
            summerfarmOrderDTO.setDeliveryTime(tmsDeliveryPlan.getDeliveryTime());
            summerfarmOrderDTO.setStoreNo(tmsDeliveryPlan.getStoreNo());

            summerfarmOrderDTOS.add(summerfarmOrderDTO);
        }

        return SummerfarmResult.getOK(summerfarmOrderDTOS);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OrderDeliveryResultEnums payConfirm(SummerFarmOrderPayResultInput summerFarmOrderPayResultInput) {
        SaasOrderDTO saasOrderDTO = saasOrderQueryFacade.querySaasOrderInfo(SaasOrderQueryInput.builder()
                .orderNo(summerFarmOrderPayResultInput.getOrderNo())
                .build());
        if(saasOrderDTO == null){
            log.info("支付验证校验查询不存在Saas订单信息:{}",summerFarmOrderPayResultInput.getOrderNo());
            throw new BizException("查询Saas接口不存在此订单信息"+summerFarmOrderPayResultInput.getOrderNo());
        }
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectPlanByOuterNo(summerFarmOrderPayResultInput.getOrderNo());
        if(tmsDeliveryPlan == null){
            log.info("tms_delivery_plan不存在此订单信息:{}",summerFarmOrderPayResultInput.getOrderNo());
            throw new BizException("表中不存在此订单信息"+summerFarmOrderPayResultInput.getOrderNo());
        }
        OutsideContact outsideContact = outsideContactMapper.selectOutsideContactById(tmsDeliveryPlan.getContactId());
        LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.SAAS_MALL)
                .orderTime(LocalDateTime.now().plusMinutes(-30))
                .city(outsideContact.getCity())
                .area(outsideContact.getArea())
                .build());

        OrderDeliveryResultEnums resultEnums = OrderDeliveryResultEnums.NORMAL;
        //正常配送
        tmsDeliveryPlan.setStatus(TmsDeliveryPlanStatusEnum.IN_DELIVERY.ordinal());
        if(deliveryDate.compareTo(tmsDeliveryPlan.getDeliveryTime()) > 0){
            tmsDeliveryPlan.setDeliveryTime(deliveryDate);
            summerFarmOrderPayResultInput.setDeliveryTime(deliveryDate.atStartOfDay());
            resultEnums = OrderDeliveryResultEnums.NEXT_CYCLE;
        }
        tmsDeliveryPlanMapper.updateTmsDeliveryPlan(tmsDeliveryPlan);

        return resultEnums;
    }
}
