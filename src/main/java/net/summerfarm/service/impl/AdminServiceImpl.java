package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dms_enterprise20181101.models.*;
import com.aliyun.ram20150501.models.CreateLoginProfileRequest;
import com.aliyun.ram20150501.models.CreateUserRequest;
import com.aliyun.ram20150501.models.CreateUserResponse;
import com.aliyun.ram20150501.models.CreateUserResponseBody;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.taobao.api.internal.toplink.embedded.websocket.util.StringUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.redis.KeyConstant;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.dingding.bo.*;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.*;
import net.summerfarm.facade.auth.AuthBaseUserFacade;
import net.summerfarm.facade.auth.AuthRoleFacade;
import net.summerfarm.facade.auth.AuthUserBaseFacade;
import net.summerfarm.facade.auth.AuthUserFacade;
import net.summerfarm.facade.crm.AdminTurnOperationFacade;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.mapper.manage.repository.AdminRepository;
import net.summerfarm.mapper.srm.SrmSupplierUserMapper;
import net.summerfarm.model.DTO.AdminMerchantDTO;
import net.summerfarm.model.bo.DefaultPwdInitResult;
import net.summerfarm.model.bo.VerificationCodeBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.srm.SrmSupplierUser;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.service.*;
import net.summerfarm.task.MailUtil;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.summerfarm.mq.constant.CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST;
import static net.summerfarm.mq.constant.MessageBusiness.POI_UPDATE;
import static net.summerfarm.mq.constant.MessageType.MERCHANT_POI_UPDATE;
import static net.summerfarm.mq.constant.MessageType.WECOM_USER_CREATE;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 后台管理员相关业务
 * @author: <EMAIL>
 * @Date: 2016/7/23
 */
@Service(value = "adminService")
public class AdminServiceImpl extends BaseService implements AdminService {
    @Resource
    private AdminMapper adminMapper;

    @Resource
    private AdminRepository adminRepository;
    @Lazy
    @Resource
    private InvitecodeService invitecodeService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private AdminDataPermissionMapper adminDataPermissionMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    DistributionFreeRuleMapper distributionFreeRuleMapper;
    @Resource
    DistributionRuleMapper distributionRuleMapper;
    @Resource
    RedisTemplate<String, String> redisTemplate;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private MerchantUpdateRecordMapper merchantUpdateRecordMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    MsgAdminService msgAdminService;
    @Resource
    MailUtil mailUtil;
    @Resource
    BaseService baseService;
    @Resource
    RoleMapper roleMapper;

    @Resource
    AdminSkinMapper adminSkinMapper;
    @Resource
    private com.aliyun.ram20150501.Client ramClient;
    @Resource
    private com.aliyun.dms_enterprise20181101.Client dmsClient;

    @Resource
    private EnterpriseInformationMapper enterpriseInformationMapper;
    @Resource
    private TianYanChaService tianYanChaService;

    @Resource
    private FinanceSettlementMapper financeSettlementMapper;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Resource
    private QiNiuService qiNiuService;
    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;

    @Resource
    private SrmSupplierUserMapper srmSupplierUserMapper;
    @Resource
    private InvitecodeMapper invitecodeMapper;
    @Resource
    private ConfigMapper configMapper;

    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    AuthUserFacade authUserFacade;
    @Resource
    AuthRoleFacade authRoleFacade;
    @Resource
    AuthBaseUserFacade authBaseUserFacade;
    @Resource
    private AuthUserBaseFacade baseUserFacade;
    @Resource
    AdminTurnOperationFacade adminTurnOperationFacade;
    @Resource
    MerchantLabelMapper merchantLabelMapper;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ConfigService configService;

    /**
     * 销售主管role id
     */
    private static final int SALE_MAANGER_ROLE_ID = 20;

    /**
     * 公海客户
     */
    private static final String NO_ATTRIBUTION_BD = "无归属BD";

    /**
     * config表中配置的仓id，用于报表展示仓销售维数据
     */
    private static final String CONFIG_KEY_REPORT_SALE_AREA = "sale_report_area_id";

    private static final Logger logger = LoggerFactory.getLogger(AdminService.class);

    private static final Splitter DEFAULT_SPLITTER = Splitter.on(",").omitEmptyStrings();

    private static final String TYPE_PHONE = "0";

    @Override
    public Admin select(String username) {
        return adminMapper.select(username);
    }

    @Override
    public Admin select(Integer id) {
        return adminMapper.selectByPrimaryKey(id);
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, AdminVO selectKeys) {
        List<AdminVO> admins;
        PageInfo returndata;

        //结算方式
        if (selectKeys.getSettlementType() != null) {
            selectKeys.setContractMethod("\"" + selectKeys.getSettlementType() + "\":");
        }
        if (selectKeys.getType() != null && selectKeys.getType() == 2) {
            if (Objects.nonNull(selectKeys.getCrmType()) && selectKeys.getCrmType() && isBD()
                    && !isSaleSA() && !isAreaSA() && !isSA() && !isParterSA()) {
                selectKeys.setSalerId(getAdminId());
            }
            PageHelper.startPage(pageIndex, pageSize);
            admins = adminRepository.selectMajorPageInfo(selectKeys);
            if (CollectionUtils.isEmpty(admins)) {
                returndata = PageInfoHelper.createPageInfo(admins);
                AjaxResult.getOK(returndata);
            } else {
                for (AdminVO admin : admins) {
                    List<AdminDataPermission> adminDataPermissions = adminDataPermissionMapper.selectByAdminId(admin.getAdminId());
                    admin.setDataPermissions(adminDataPermissions);
                    //品牌的结算方式 0 半月结 1-28 每月X号月结
                    List<FinanceSettlement> financeSettlement = financeSettlementMapper.selectByPrimaryKey(admin.getAdminId());
                    if (CollectionUtil.isEmpty(financeSettlement)) {
                        admin.setSettlementMethod(null);
                        continue;
                    }
                    admin.setSettlementMethod(financeSettlement.get(0).getSettlementMethod());
                    List<Integer> typeList = financeSettlement.stream().map(FinanceSettlement::getType).collect(Collectors.toList());
                    admin.setSettlementSourceType(typeList);
                }
            }
            returndata = PageInfoHelper.createPageInfo(admins);
        } else {
            PageHelper.startPage(pageIndex, pageSize);
            admins = adminMapper.selectPageInfo(selectKeys);
            returndata = PageInfoHelper.createPageInfo(admins);
        }
        //查询不到用户返回
        if (CollectionUtils.isEmpty(admins)) {
            return AjaxResult.getOK(returndata);
        }

        List<Integer> list = new ArrayList<>();
        admins.forEach((x) -> list.add(x.getAdminId()));
        List<DistributionRuleVO> distributionRuleVOS = distributionRuleMapper.queryRuleList(list);
        //
        if (!CollectionUtils.isEmpty(distributionRuleVOS)) {
            for (AdminVO admin : admins) {
                List<DistributionRuleVO> distributionRuleVOs = new ArrayList<>();
                for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {

                    if (Objects.equals(distributionRuleVO.getAdminId(), admin.getAdminId())) {
                        distributionRuleVOs.add(distributionRuleVO);
                    }
                }
                if (!CollectionUtils.isEmpty(distributionRuleVOs)) {
                    String rule = JSON.toJSONString(distributionRuleVOs);

                    char[] chars = rule.toCharArray();

                    for (int i = 1; i < chars.length - 1; i++) {
                        if (Objects.equals(chars[i], '[')) {
                            if (Objects.equals(chars[i - 1], '"')) {
                                chars[i - 1] = '\\';
                            }

                        }
                        if (Objects.equals(chars[i], ']')) {
                            if (Objects.equals(chars[i + 1], '"')) {
                                chars[i + 1] = '\\';
                            }

                        }
                    }
                    String rules = String.valueOf(chars);
                    admin.setDistributionRuleVOS(StringEscapeUtils.unescapeJavaScript(rules));
                }
            }
        }

        return AjaxResult.getOK(returndata);
    }
  @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public AjaxResult saveAdmin(AdminVO record){
        AjaxResult save = save(record);
        if (save.isSuccess() && record.getAdminTurningConfig()!=null){
            Object data = save.getData();
            Integer adminId = Integer.valueOf(data.toString());
            if (record.getAdminTurningConfig()!=null){
                adminTurnOperationFacade.addAdminTurnAction(adminId, record.getAdminTurningConfig().getBdId());
            }
        }
        return save;
    }
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult save(AdminVO record) {

        // 新建的类型为大客户，校验在不同阶段下的登录邮箱和密码的必填情况
        if (Objects.equals(record.getType(), 2)) {
            Integer cooperationStage = record.getCooperationStage();
            if (Objects.isNull(cooperationStage)) {
                throw new DefaultServiceException("新建客户为大客户类型，当前阶段不能为空！");
            }
            //大客户当前阶段为3，4，5时登录邮箱为null则报异常
            if (cooperationStage >= AdminCooperationStageEnum.DELIVERY_STAGE.getStageId() && Objects.isNull(record.getUsername())
                    && cooperationStage <= AdminCooperationStageEnum.HARD_STAGE.getStageId()) {
                throw new DefaultServiceException("选择阶段为试配，合作稳定期，合作困难期时，登录邮箱必填");
            }

        }

        //当填入的邮箱不为null时做重复校验
        if (StringUtils.isNotBlank(record.getUsername()) && Objects.nonNull(adminMapper.select(record.getUsername()))) {
            return AjaxResult.getErrorWithMsg("邮箱名称不能重复");
        }

        if (record.getNameRemakes() != null) {
            if (record.getNameRemakes().length() > 20) {
                return AjaxResult.getErrorWithMsg("名称备注长度不能大于20");
            }
        }

        String distributionRule = record.getDistributionRuleVOS();
        List<DistributionRuleVO> distributionRuleVOS = JSON.parseArray(distributionRule, DistributionRuleVO.class);
        //校验金额
        if (!CollectionUtils.isEmpty(distributionRuleVOS)) {
            for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
                if (distributionRuleVO.getDeliveryFee() != null) {
                    if (distributionRuleVO.getDeliveryFee().compareTo(new BigDecimal(0.01)) < 0) {
                        return AjaxResult.getErrorWithMsg("配送费金额必须大于0.01元");

                    }
                }
            }
        }

        record.setCreateTime(new Date());
        //todo 这里的分布式事物是有问题的

        List<RoleVO> roleVOList = record.getRoleVOs();

        if (!CollectionUtils.isEmpty(roleVOList)){
            List<Integer> roleIds = roleVOList.stream().map(RoleVO::getRoleId).collect(Collectors.toList());
            if (roleIds.contains(5)) {
                if (record.getType() != null && record.getType() == 2) {
                    throw new DefaultServiceException("角色分配不能既是大客户，又是销售BD");
                }
            }
        }

        UserBase userBase = authUserFacade.addUser(record);
        if (userBase == null){
            throw new DefaultServiceException("当前手机号/用户名已经存在");
        }

        /**
         * TODO 此处应当做校验，创建者不可给被创建者授予超出其权限范围的角色/权限.
         */
        if (Objects.nonNull(record.getPassword())) {
            record.setPassword(MD5Util.string2MD5(record.getPassword().trim()));
        }

        record.setBaseUserId(userBase.getUserBaseId());
        record.setAdminId(userBase.getId().intValue());
        int rs = adminMapper.insertSelective(record);
        if (rs == 1) {

            if (record.getType() != null && record.getType() == 2) {
                //1.配置角色
                Set<Integer> aimRoleIds = new HashSet<>();
                aimRoleIds.add(Global.BIG_ROLEID);
               // adminRoleMapper.insertBatch(record.getAdminId(), aimRoleIds);
                //2.分配邀请码
                invitecodeService.enableInvitecode(record.getAdminId());

                // 创建大客户同步创建标签
                MerchantLabel merchantLabel = new MerchantLabel();
                merchantLabel.setName(record.getNameRemakes());
                merchantLabel.setType(MerchantLabelTypeEnum.AFTER_SALE.getCode());
                merchantLabelMapper.insertSelective(merchantLabel);
            }
            if (!CollectionUtils.isEmpty(roleVOList)) {
                Set<Integer> aimRoleIds = new HashSet<>();
                for (RoleVO releVO : roleVOList) {
                    aimRoleIds.add(releVO.getRoleId());
                }
                // 大客户创建
                if (aimRoleIds.contains(5)) {
                    invitecodeService.enableInvitecode(record.getAdminId());
                }
                if (!aimRoleIds.contains(14)) {
                    // 普通用户创建
                    MQData mqData = new MQData();
                    mqData.setType(WECOM_USER_CREATE);
                    mqData.setBusiness(WECOM_USER_CREATE);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("adminId", record.getAdminId());
                    jsonObject.put("name", record.getRealname());
                    jsonObject.put("mobile", record.getPhone());
                    mqData.setData(jsonObject.toJSONString());
                    mqProducer.send(TOPIC_CRM_MALL_LIST, null, mqData);
                }
                //adminRoleMapper.insertBatch(record.getAdminId(), aimRoleIds);
            }
            Integer adminId = record.getAdminId();
            //数据权限S
            Set<String> dataPermissionSet = record.getDataPermissions().stream().map(p -> p.getPermissionValue()).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(dataPermissionSet)) {
                if (dataPermissionSet.contains("0")) {  // 全部的情况
                    Set<Integer> adminDataPermissions = getDataPermission();
                    if (isSA() || adminDataPermissions.contains(0)) {
                        AdminDataPermission adminDataPermission = new AdminDataPermission();
                        adminDataPermission.setPermissionValue("0");
                        adminDataPermission.setPermissionName("全部");
                        adminDataPermission.setType("0");
                        adminDataPermission.setAdminId(record.getAdminId());
                        adminDataPermissionMapper.insert(adminDataPermission);
                    }
                } else {
                    for (String dataPermission : dataPermissionSet) {
                        Area area = areaMapper.selectByAreaNo(Integer.valueOf(dataPermission));
                        if (area != null) {
                            AdminDataPermission adminDataPermission = new AdminDataPermission();
                            adminDataPermission.setPermissionValue(String.valueOf(area.getAreaNo()));
                            adminDataPermission.setPermissionName(area.getAreaName());
                            adminDataPermission.setType("0");
                            adminDataPermission.setAdminId(record.getAdminId());
                            adminDataPermissionMapper.insert(adminDataPermission);
                        }
                    }
                }
            }
            //添加配送费规则
            if (!CollectionUtils.isEmpty(distributionRuleVOS)) {
                for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
                    distributionRuleVO.setAdminId(adminId);
                    distributionRuleVO.setStatus(0);
                }
                distributionRuleMapper.insertBathRuleVO(distributionRuleVOS);
                ArrayList<DistributionFreeRule> distributionFreeRules = new ArrayList<>();
                //遍历设置
                for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
                    List<DistributionFreeRule> distributionFreeRuleS = distributionRuleVO.getDistributionFreeRules();
                    for (DistributionFreeRule disVO : distributionFreeRuleS) {
                        disVO.setDistributionId(distributionRuleVO.getId());
                        disVO.setStatus(0);
                        if (!StringUtils.isEmpty(disVO.getRule())) {
                            distributionFreeRules.add(disVO);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(distributionFreeRules)) {
                    distributionFreeRuleMapper.insertFreeRule(distributionFreeRules);
                }
            }
            // 如果是账期大客户，生成结算信息
            if (!StringUtils.isEmpty(record.getContractMethod())) {
                Map<String, Integer> recordJson = null;
                if (StringUtils.isNotBlank(record.getContractMethod())) {
                    recordJson = (Map) JSON.parseObject(record.getContractMethod());
                }
                if (recordJson.get("1") != null) {
                    //新增品牌结算方式 0 半月结 1-28 每月X号月结;当前版本无法修改，所以先查询是否存在，不存在插入，存在不做操作
                    List<FinanceSettlement> financeSettlement = financeSettlementMapper.selectByPrimaryKey(record.getAdminId());
                    if (CollectionUtil.isEmpty(financeSettlement) && CollectionUtil.isNotEmpty(record.getSettlementSourceType())) {
                        record.getSettlementSourceType().forEach(me->{
                            FinanceSettlement adminFinanceSettlement = new FinanceSettlement();
                            adminFinanceSettlement.setAdminId(record.getAdminId());
                            adminFinanceSettlement.setSettlementMethod(record.getSettlementMethod());
                            adminFinanceSettlement.setType(me);
                            adminFinanceSettlement.setCreator(getAdminName());
                            financeSettlementMapper.insert(adminFinanceSettlement);

                        });
                    }
                }
            }
            //数据权限E
            return AjaxResult.getOK(userBase.getId());
        }

        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult lock(int id, Admin admin) {
        admin.setAdminId(id);
        if (!admin.getDisabled()) {
            admin.setLoginFailTimes(0);
        }
        adminMapper.updateByPrimaryKeySelective(admin);
        Admin updateAdmin = new Admin();
        updateAdmin.setAdminId(admin.getAdminId());
        updateAdmin.setIsDisabled(admin.getIsDisabled());
        authBaseUserFacade.updateUserBase(SystemOriginEnum.ADMIN, updateAdmin);
        int rs = adminMapper.updateByPrimaryKeySelective(admin);
        /// 大客户被禁用不在拉黑门店，从门店登录时校验门店所属大客户被禁用时禁止进入商城
       /*Set<Integer> roleIds = adminRoleMapper.getRoles(admin.getAdminId());
        if (!CollectionUtils.isEmpty(roleIds)) {
            for (Integer roleId : roleIds) {
                //大客户被禁用 所有直营店都被禁用
                if (roleId.equals(Global.BIG_ROLEID)) {
                    merchantMapper.updateByAdminId(admin.getAdminId());
                }
            }
        }*/
        if (rs == 1) {
            return AjaxResult.getOK();
        }
        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult specifiedRoles(int adminId, List<Integer> roleIds, List<AdminDataPermission> dataPermissions) {
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new DefaultServiceException("用户只要分配一个角色");
        }
        AdminVO adminVO = adminRepository.selectWithRoles(adminId);
        //管理员不存在
        if (adminVO == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        String man = adminMsg(adminVO);

        //数据权限S
        //查询原有地区权限,放入String数组
        List<AdminDataPermission> adminData = adminDataPermissionMapper.selectByAdminId(adminId);
        String[] oldAdminDataPermission = adminData.stream().map(AdminDataPermission::getPermissionName).toArray(String[]::new);
        int oldAll = Arrays.binarySearch(oldAdminDataPermission, "全部");
        String oldAdminData = oldDate(oldAdminDataPermission);
        //粗暴的删除原有数据权限
        adminDataPermissionMapper.deleteByAdminId(adminId);
        //添加权限
        String[] newAdminDataPermission = new String[dataPermissions.size()];
        if (!CollectionUtils.isEmpty(dataPermissions)) {
            // 全部的情况
            if (dataPermissions.stream().anyMatch(el -> Objects.equals("0", el.getPermissionValue()))) {
                AdminDataPermission adminDataPermission = new AdminDataPermission();
                adminDataPermission.setPermissionValue("0");
                adminDataPermission.setPermissionName("全部");
                adminDataPermission.setType("0");
                adminDataPermission.setAdminId(adminId);
                adminDataPermissionMapper.insert(adminDataPermission);
                newAdminDataPermission[0] = "全部";
            } else {
                for (AdminDataPermission dataPermission : dataPermissions) {
                    dataPermission.setAdminId(adminId);
                    adminDataPermissionMapper.insert(dataPermission);
                    newAdminDataPermission = dataPermissions.stream().map(AdminDataPermission::getPermissionName).toArray(String[]::new);
                }
            }
        }
        int newAll = Arrays.binarySearch(newAdminDataPermission, "全部");
        String newAdminData = newDate(newAdminDataPermission);
        //数据权限E
        List<RoleVO> roleVOs = adminVO.getRoleVOs();
        Set<Integer> aimRoleIds = new HashSet<>(roleIds);
        //取消所有角色
        if (CollectionUtils.isEmpty(roleIds)) {
           // adminRoleMapper.deleteAll(adminId);
            if (aimRoleIds.contains(5)) {
                invitecodeService.cancelInvitecode(adminId);
            }
            return AjaxResult.getOK();
        }

        List<Role> deleteRole = new ArrayList<>();
        List<Role> addRole = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleIds)) {
            authUserFacade.updateUserRole(adminVO.getAuthId(), roleIds);
        }
        //取消、授权操作
        if (!CollectionUtils.isEmpty(roleVOs)) {
            Set<Integer> roleVOIds = roleVOs.stream().map(RoleVO::getRoleId).collect(Collectors.toSet());
            Set<Integer> roleVOIdsCopy = new HashSet<>(roleVOIds);
            roleVOIds.removeAll(aimRoleIds);
            if (roleVOIds.size() > 0) {
                //5为角色“地推人员”的id
                //取消地推码
                if (roleVOIds.contains(5)) {
                    invitecodeService.cancelInvitecode(adminId);
                }
                deleteRole = authRoleFacade.getRoles(roleVOIds);
                //adminRoleMapper.deleteBatch(adminId, roleVOIds);
            }
            aimRoleIds.removeAll(roleVOIdsCopy);
        }
        if (aimRoleIds.size() > 0) {
            //分配地推码
            if (aimRoleIds.contains(5)) {
                invitecodeService.enableInvitecode(adminId);
            }
            addRole = authRoleFacade.getRoles(aimRoleIds);
            //adminRoleMapper.insertBatch(adminId, aimRoleIds);
        }
        String text = adminFunctionAuthority(deleteRole, addRole, oldAll, newAll, oldAdminData, newAdminData, man);
        sendAuthorityMsg(text);
        logger.info("修改角色信息 {}", text);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult resetPassword(String username, String old, String target) {
        if (StringUtils.isBlank(old, target, username)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        Admin admin = adminMapper.select(username);
        if (admin == null) {
            return AjaxResult.getError(ResultConstant.USER_OR_PASSWORD_WRONG);
        }
        //校验原密码
        if (!admin.getPassword().equals(MD5Util.string2MD5(old))) {
            return AjaxResult.getError(ResultConstant.USER_OR_PASSWORD_WRONG);
        }
        //原密码不能与新密码相同
        if (old.equals(target)) {
            return AjaxResult.getError(ResultConstant.SAME_PARAM);
        }
        //校验新密码
        if (!StringUtils.isAdminPassword(target)) {
            return AjaxResult.getErrorWithMsg("密码必须为：数字+字母+特殊字符");
        }

        //保存
        Admin updateParam = new Admin();
        updateParam.setAdminId(admin.getAdminId());
        updateParam.setPassword(MD5Util.string2MD5(target));

        Admin param = new Admin();
        param.setAdminId(admin.getAdminId());
        param.setPassword(target);
        authBaseUserFacade.updateUserBase(SystemOriginEnum.ADMIN, param);

        int rs = adminMapper.updateByPrimaryKeySelective(updateParam);
        if (rs == 1) {
            return AjaxResult.getOK(ResultConstant.RESET_SUCCESS);
        }
        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
    }

    @Override
    public AjaxResult selectWithRole(Integer id) {
        AdminVO adminVO = adminRepository.selectWithRoles(id);
        if (adminVO == null){
            throw new DefaultServiceException("adminVO is null");
        }
        //查询数据权限 并判断查询人是否有权限查看
        List<AdminDataPermission> adminDataPermissions = adminDataPermissionMapper.selectByAdminId(adminVO.getAdminId());
        adminVO.setDataPermissions(adminDataPermissions);
        adminVO.setPassword(null);

        return AjaxResult.getOK(adminVO);
    }

    @Override
    public AjaxResult selectAll(Integer roleType) {
        List<AdminVO> adminVOs = adminRepository.selectAll(roleType);

        if(Objects.equals(NumberUtils.INTEGER_FIVE,roleType)){
            AdminVO adminVO = new AdminVO();
            adminVO.setAdminId(NumberUtils.INTEGER_ZERO);
            adminVO.setRealname(NO_ATTRIBUTION_BD);
            adminVOs.add(NumberUtils.INTEGER_ZERO,adminVO);
        }
        return AjaxResult.getOK(adminVOs);
    }


    @Override
    public AjaxResult selectMajor() {
        List<AdminVos> adminList = adminRepository.selectMajor();
        return AjaxResult.getOK(adminList);
    }


    @Override
    public AjaxResult selectPurchase() {
        List<AdminVO> adminVOs = adminRepository.selectAll(9);
        return AjaxResult.getOK(adminVOs);
    }

    @Override
    public AjaxResult selectByRoleTypes(String roleTypes) {
        List<Integer> roleIds = Arrays.asList(roleTypes.split(Global.SEPARATING_SYMBOL)).stream().map(o -> Integer.valueOf(o)).collect(Collectors.toList());
        List<AdminVO> adminVOS = adminRepository.selectByRoleTypes(roleIds);
        return AjaxResult.getOK(adminVOS);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(AdminVO adminVO) {
        String password = adminVO.getPassword();
        if (StringUtils.isNotBlank(adminVO.getPassword())) {
            adminVO.setPassword(MD5Util.string2MD5(adminVO.getPassword()));
        }
        Admin record = adminMapper.selectByPrimaryKey(adminVO.getAdminId());

        if (adminVO.getNameRemakes() != null) {
            if (adminVO.getNameRemakes().length() > 20) {
                return AjaxResult.getErrorWithMsg("名称备注长度不能大于20");
            }
        }

        //处理 大客户子客户属性 因为只有2个才用这种方法  垃圾代码
        if (!StringUtils.isEmpty(adminVO.getContractMethod())) {
            Map<String, Integer> recordJson = null;
            if (StringUtils.isNotBlank(record.getContractMethod())) {
                recordJson = (Map) JSON.parseObject(record.getContractMethod());
            }
            Map<String, Integer> adminJson = (Map) JSON.parseObject(adminVO.getContractMethod());
            if (recordJson == null || recordJson.size() == 0 || adminJson.size() == 0) {
                return AjaxResult.getError("合作方式至少一个");
            }
            if (adminJson.size() == 1) {
                //代表把所有的都变成这个
                Map.Entry<String, Integer> entry = getFirst(adminJson);
                merchantMapper.updateSkuShowByAdminId(Integer.valueOf(entry.getKey()), entry.getValue(), adminVO.getAdminId());
            } else if (adminJson.size() == 2 && recordJson.size() >= 1) {
                for (Map.Entry<String, Integer> entry : recordJson.entrySet()) {
                    merchantMapper.updateSkuShowByDirect(Integer.valueOf(entry.getKey()), adminJson.get(entry.getKey()), adminVO.getAdminId());
                }
            }
        }
        //修改大客户提前截单
        if (adminVO.getCloseOrderType() != null && !record.getCloseOrderType().equals(adminVO.getCloseOrderType())) {
            if (DateUtils.checkCloseOrderTypeTime()) {
                return AjaxResult.getError("当前时间不可修改提前截单类型");
            }
        }
        /// 大客户被禁用不在拉黑门店，从门店登录时校验门店所属大客户被禁用时禁止进入商城
       /*//修改了大客户账号状态
        if (adminVO.getDisabled() != null && !record.getIsDisabled().equals(adminVO.getIsDisabled())) {
            //大客户被禁用 所有直营店都被禁用（店铺、子账号进入待审核状态）
            if (adminVO.getIsDisabled()) {
                HashMap<String, Integer> query = new HashMap<>();
                query.put("adminId", adminVO.getAdminId());
                List<Long> mIdList = merchantMapper.selectMerchantByAdminId(query).stream().map(Merchant::getmId).collect(Collectors.toList());
                merchantMapper.updateByAdminId(adminVO.getAdminId());
            }
        }*/

        //验证工商名称
        EnterpriseInformation enterpriseInformation = enterpriseInformationMapper.select(adminVO.getRealname(), adminVO.getCreditCode());
        if (ObjectUtils.isEmpty(enterpriseInformation)) {
            //天眼查查询该企业工商信息
            String invoiceTitle = tianYanChaService.addressAssembly(adminVO.getRealname());
            String key = tianYanChaService.secretKey();
            tianYanChaService.addData(invoiceTitle, key);
            EnterpriseInformation e = enterpriseInformationMapper.select(adminVO.getRealname(), adminVO.getCreditCode());
            if (ObjectUtils.isEmpty(e)) {
                return AjaxResult.getError("企业名称和统一社会信用代码不匹配，请重新填写");
            }
        }

        //修改品牌结算方式 0 半月结 1-28 每月X号月结;当前版本无法修改，所以先查询是否存在，不存在插入，存在不做操作
        if(CollectionUtil.isNotEmpty(adminVO.getSettlementSourceType())){
            for (Integer type : adminVO.getSettlementSourceType()) {
                FinanceSettlement financeSettlement = financeSettlementMapper.selectByAdminAndType(adminVO.getAdminId(), type);
                if (financeSettlement==null){
                    FinanceSettlement settlement = new FinanceSettlement();
                    settlement.setAdminId(adminVO.getAdminId());
                    settlement.setSettlementMethod(adminVO.getSettlementMethod());
                    settlement.setType(type);
                    settlement.setCreator(getAdminName());
                    financeSettlementMapper.insert(settlement);
                }
            }
            financeSettlementMapper.deleteByAdminIdAndType(adminVO.getAdminId(), adminVO.getSettlementSourceType());
        }

        Admin updateAdmin = new Admin();
        updateAdmin.setAdminId(adminVO.getAdminId());
        updateAdmin.setRealname(adminVO.getRealname());
        updateAdmin.setPhone(adminVO.getPhone());
        updateAdmin.setIsDisabled(adminVO.getDisabled());
        updateAdmin.setPassword(password);
        authBaseUserFacade.updateUserBase(SystemOriginEnum.ADMIN, updateAdmin);

        int rs = adminMapper.updateByPrimaryKeySelective(adminVO);
        if (rs != 1) {
            return AjaxResult.getError("PARAM_FAULT");
        }
        //先删除所属皮肤
        adminSkinMapper.deleteByAdminId(adminVO.getAdminId());
        //再生成
        AdminSkin adminSkin = new AdminSkin(adminVO.getAdminId(), adminVO.getShowFlag(), adminVO.getLogo(), adminVO.getBackgroundImage(), LocalDateTime.now());
        adminSkinMapper.insert(adminSkin);

        //失效所有配送费规则，重新添加规则
        Integer adminId = adminVO.getAdminId();
        String distributionRuleVOS = adminVO.getDistributionRuleVOS();
        List<DistributionRule> distributionRules = distributionRuleMapper.selectByAdminId(adminId);

        updateDisRule(adminId, distributionRuleVOS, !CollectionUtils.isEmpty(distributionRules));

        //更新大客户城市权限,先全部删除，后逐个添加
        adminDataPermissionMapper.deleteByAdminId(adminId);
        //添加权限
        if (!CollectionUtils.isEmpty(adminVO.getDataPermissions())) {
            if (adminVO.getDataPermissions().stream().anyMatch(el -> Objects.equals(el.getPermissionValue(), "0"))) {  // 全部的情况
                AdminDataPermission adminDataPermission = new AdminDataPermission();
                adminDataPermission.setPermissionValue("0");
                adminDataPermission.setPermissionName("全部");
                adminDataPermission.setType("0");
                adminDataPermission.setAdminId(adminId);
                adminDataPermissionMapper.insert(adminDataPermission);
            } else {
                for (AdminDataPermission dataPermission : adminVO.getDataPermissions()) {
                    dataPermission.setAdminId(adminId);
                    adminDataPermissionMapper.insert(dataPermission);
                }
            }
        }
        if (adminVO.getAdminTurningConfig()!=null && adminVO.getAdminId()!=null){
            adminTurnOperationFacade.addAdminTurnAction(adminVO.getAdminId(), adminVO.getAdminTurningConfig().getBdId());
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectAdminByMerchantId(Long id) {

        Merchant merchant = merchantMapper.selectByPrimaryKey(id);

        if (merchant != null && merchant.getAdminId() != null) {
            Admin admin = adminMapper.selectByPrimaryKey(merchant.getAdminId());
            return AjaxResult.getOK(admin);
        }

        return AjaxResult.getErrorWithMsg("非大客户");
    }


    private static Map.Entry<String, Integer> getFirst(Map<String, Integer> map) {
        Map.Entry obj = null;
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            obj = entry;
            if (obj != null) {
                break;
            }
        }
        return obj;
    }


    private void updateDisRule(Integer adminId, String distribution, Boolean isDistributionRuleNull) {
        logger.info("---" + distribution);
        //更改前是否存在配送费规则
        if (isDistributionRuleNull) {
            DistributionRule distributionRule = new DistributionRule();
            distributionRule.setAdminId(adminId);
            distributionRule.setStatus(1);
            distributionRuleMapper.updateRule(distributionRule);
        }

        if (!StringUtils.isEmpty(distribution)) {

            List<DistributionRuleVO> distributionRuleVOS = JSON.parseArray(distribution, DistributionRuleVO.class);

            for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
                distributionRuleVO.setAdminId(adminId);
                distributionRuleVO.setStatus(0);
            }
            distributionRuleMapper.insertBathRuleVO(distributionRuleVOS);
            ArrayList<DistributionFreeRule> distributionFreeRules = new ArrayList<>();
            //遍历设置
            for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
                List<DistributionFreeRule> distributionFreeRuleS = distributionRuleVO.getDistributionFreeRules();
                if (!CollectionUtils.isEmpty(distributionFreeRuleS)) {
                    for (DistributionFreeRule disVO : distributionFreeRuleS) {
                        disVO.setDistributionId(distributionRuleVO.getId());
                        disVO.setStatus(0);
                        if (!StringUtils.isEmpty(disVO.getRule())) {
                            distributionFreeRules.add(disVO);
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(distributionFreeRules)) {
                distributionFreeRuleMapper.insertFreeRule(distributionFreeRules);
            }

        }

    }

    @Override
    public AjaxResult queryBigMerchantDataMonthly(Admin selectKeys) {
        Integer adminId = selectKeys.getAdminId();
        if (Objects.isNull(adminId)) {
            throw new DefaultServiceException("adminId 不能为null！");
        }
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        if (Objects.isNull(adminId) || Objects.isNull(admin) || Objects.isNull(admin.getKp())) {
            throw new DefaultServiceException("adminId入参错误！");
        }

        //1.获取现有大客户的所有可用的门店信息
        MerchantVO queryMerchant = new MerchantVO();
        queryMerchant.setAdminId(adminId);
        queryMerchant.setIslock((byte) 0);
        List<MerchantVO> merchantVOList = merchantMapper.selectMerchantsBySelectKeys(queryMerchant);

        //校验门店数为0的情况
        BigDecimal currentCount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(merchantVOList)) {
            currentCount = new BigDecimal(merchantVOList.size());
        }
        // 2.获取上个月末的门店数
        //获取开始时间 现在此刻的时间 和 这个月初的时间 00:00:00
        HashMap<String, LocalDateTime> timeMap = new HashMap<>(2);
        LocalDateTime now = LocalDateTime.now().withNano(0);
        // 计算出本月1号的零点时间
        LocalDateTime nowOfFirstDayOfMonth = now.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0).withNano(0);
        timeMap.put("startTime", nowOfFirstDayOfMonth);
        timeMap.put("endTime", now);
        MerchantUpdateRecordVO queryMerchantUpdateRecord = new MerchantUpdateRecordVO();

        //大客户下新增门店记录总和
        queryMerchantUpdateRecord.setStartTime(timeMap.get("startTime"));
        queryMerchantUpdateRecord.setEndTime(timeMap.get("endTime"));
        queryMerchantUpdateRecord.setNewAdmin(adminId.longValue());
        int inCounts = merchantUpdateRecordMapper.countMerchantByAdmin(queryMerchantUpdateRecord);

        //大客户下转出门店记录总和
        queryMerchantUpdateRecord.setNewAdmin(null);
        queryMerchantUpdateRecord.setOldAdmin(adminId.longValue());
        int outCounts = merchantUpdateRecordMapper.countMerchantByAdmin(queryMerchantUpdateRecord);

        BigDecimal inCount = new BigDecimal(inCounts);
        BigDecimal outCount = new BigDecimal(outCounts);

        //在一段时间内总变动数
        BigDecimal changeCount = inCount.subtract(outCount);

        //上个月最后一天门店数
        BigDecimal lastMonthCount = currentCount.subtract(changeCount);

        BigDecimal merchantCountRatio = null;
        //计算比率：
        if (lastMonthCount.compareTo(BigDecimal.ZERO) > 0) {
            merchantCountRatio = changeCount.divide(lastMonthCount, 4, BigDecimal.ROUND_HALF_UP);
        } else if (lastMonthCount.compareTo(BigDecimal.ZERO) < 0) {
            throw new DefaultServiceException("计算的上个月的门店数异常不能为负数");
        }
        logger.info("现有门店数：{}，上月门店:{},门店变化{}", currentCount, lastMonthCount, changeCount);
        /**
         * 分别计算现有门店的订单和订单详情统计（交易额：订单实付金额；交易sku品种，需要去重)还要考虑部分售后退货的商品
         */
        //确定三个节点： nowTime(现在的时间), lastMonthTime(上一个月时间节点), preLastMonthTime(上上个月时间节点)
        //时间节点分钟处理：为截单时间，截单之前算这个月的，截单之后算下个月的；默认这个月日期到上个月日期，例如12-28至 11-28
        // 特殊情况：这个月比上个月短且为当月最后一天，则开始日期为上个月的最后一天， 比如 10月31号 - 11月30号
        timeMap.clear();
        LocalDateTime nowTime;
        LocalDateTime lastMonthDateTime;
        //本月一号零点到目前
        nowTime = LocalDateTime.now().withNano(0);
        LocalDateTime currentMonthFirstDay = nowTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);

        //上月1号零点到上月对应的日期的23:59:59
        LocalDateTime lastMonthFirstDay = currentMonthFirstDay.minusMonths(1);
        lastMonthDateTime = handleDateTime(nowTime);

        //查询在这段时间内曾经为某个大客户下的所有门店下的订单
        Map<String, BigDecimal> dataMapCurrentMonthly = adminGMVAndSKU(adminId, currentMonthFirstDay, nowTime);
        BigDecimal skuSetSizeCurrent = dataMapCurrentMonthly.get("skuSetSize");
        BigDecimal adminGMVCurrent = dataMapCurrentMonthly.get("adminGMV");

        //查询在这段时间内的某个大客户下的所有门店下的订单
        Map<String, BigDecimal> dataMapLastMonthly = adminGMVAndSKU(adminId, lastMonthFirstDay, lastMonthDateTime);
        BigDecimal skuSetSizeLast = dataMapLastMonthly.get("skuSetSize");
        BigDecimal adminGMVLast = dataMapLastMonthly.get("adminGMV");

        logger.info("GMV及sku查询时间点分别为{},{},{},{}", currentMonthFirstDay, nowTime, lastMonthFirstDay, lastMonthDateTime);
        BigDecimal skuVarietyRatio = null;
        BigDecimal adminGVMRatio = null;
        if (adminGMVLast.compareTo(BigDecimal.ZERO) > 0) {
            skuVarietyRatio = (skuSetSizeCurrent.subtract(skuSetSizeLast)).divide(skuSetSizeLast, 4, BigDecimal.ROUND_HALF_UP);
            adminGVMRatio = (adminGMVCurrent.subtract(adminGMVLast)).divide(adminGMVLast, 4, BigDecimal.ROUND_HALF_UP);
        }


        /**
         * 将求得的数据放入到封装中返回
         */
        BigMerchantDataMonthlyVO bigMerchantDataMonthlyVO = new BigMerchantDataMonthlyVO();
        bigMerchantDataMonthlyVO.setAdminId(adminId.longValue());
        bigMerchantDataMonthlyVO.setMerchantCountCurrentMonth(currentCount.intValue());
        bigMerchantDataMonthlyVO.setMerchantCountLastMonth(lastMonthCount.intValue());
        bigMerchantDataMonthlyVO.setMerchantCountRatio(merchantCountRatio);

        bigMerchantDataMonthlyVO.setGmvCurrentMonth(adminGMVCurrent);
        bigMerchantDataMonthlyVO.setGmvLastMonth(adminGMVLast);
        bigMerchantDataMonthlyVO.setGmvRatio(adminGVMRatio);

        bigMerchantDataMonthlyVO.setSkuVarietyCurrentMonth(skuSetSizeCurrent.intValue());
        bigMerchantDataMonthlyVO.setSkuVarietyLastMonth(skuSetSizeLast.intValue());
        bigMerchantDataMonthlyVO.setSkuVarietyRatio(skuVarietyRatio);

        logger.info("GMV及sku输出的参数{}", bigMerchantDataMonthlyVO.toString());
        return AjaxResult.getOK(bigMerchantDataMonthlyVO);
    }

    @Override
    public AjaxResult sendVerificationCode(String phoneOrEmail, String type) {

        if (StringUtils.isEmpty(phoneOrEmail)) {
            return AjaxResult.getErrorWithMsg("手机号或邮箱为空");
        }
        //获取随机数
        int code = RandomCodeUtils.buildCode(6);
        // 验证码存放redis中
        VerificationCodeBO verificationCodeBO = new VerificationCodeBO();
        verificationCodeBO.setType(type);
        verificationCodeBO.setPhoneOrEmail(phoneOrEmail);
        verificationCodeBO.setCode(code);
        redisTemplate.opsForHash().put(KeyConstant.VERIFICATION_CODE, phoneOrEmail + "_" + code, JSON.toJSONString(verificationCodeBO));
        logger.info("phoneOrEmail:" + phoneOrEmail + "," + code);
        //发送验证码
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        //手机号
        if (Objects.equals(type, TYPE_PHONE)) {
            List<Admin> admins = adminMapper.selectByPhone(phoneOrEmail);
            if (CollectionUtils.isEmpty(admins) || admins.size() > 1) {
                return AjaxResult.getErrorWithMsg("手机号不存在或绑定了多个账号");
            }
            Admin admin = admins.get(0);
            if (admin == null || admin.getDisabled()) {
                return AjaxResult.getErrorWithMsg("账户不存在或被锁定（账号被锁定，请联系主管解锁。请在个人中心处点击权限配置，搜索到相应用户，点击启用按钮）");
            }
            msgAdminService.sms(2L, Arrays.asList(String.valueOf(code)), phoneOrEmail, SMSType.NOTIFY);
            //邮箱
        } else {
            Admin admin = adminMapper.select(phoneOrEmail);
            if (admin == null || admin.getDisabled()) {
                return AjaxResult.getErrorWithMsg("账户不存在或被锁定（账号被锁定，请联系主管解锁。请在个人中心处点击权限配置，搜索到相应用户，点击启用按钮）");
            }
            mailUtil.sendMail("验证码", String.valueOf(code), new String[]{phoneOrEmail}, null);
        }

        return AjaxResult.getOK();
    }


    @Override
    public AjaxResult updatePassword(String phoneOrEmail, String code, String password) {
        //从redis中获取验证码
        String verificationCodeStr = (String) redisTemplate.opsForHash().get(KeyConstant.VERIFICATION_CODE, phoneOrEmail + "_" + code);
        VerificationCodeBO verificationCodeBO = JSON.parseObject(verificationCodeStr, VerificationCodeBO.class);
        if (verificationCodeBO == null) {
            return AjaxResult.getErrorWithMsg("验证码错误");
        }
        String type = verificationCodeBO.getType();
        String codeMsg = verificationCodeBO.getCode() + "";
        if (!StringUtils.isEmpty(codeMsg)) {
            if (!Objects.equals(codeMsg, code)) {
                return AjaxResult.getErrorWithMsg("验证码错误");
            }
        } else {
            return AjaxResult.getErrorWithMsg("验证码错误");
        }
        if (StringUtils.isEmpty(password)) {
            return AjaxResult.getErrorWithMsg("密码不能为空");
        }
        Admin admin;
        if (Objects.equals(type, TYPE_PHONE)) {
            List<Admin> admins = adminMapper.selectByPhone(phoneOrEmail);
            if (CollectionUtils.isEmpty(admins) || admins.size() > 1) {
                return AjaxResult.getErrorWithMsg("手机号不存在或绑定了多个账号");
            }
            admin = admins.get(0);
            //邮箱
        } else {
            admin = adminMapper.select(phoneOrEmail);
        }
        if (admin == null || admin.getDisabled()) {
            return AjaxResult.getErrorWithMsg("账户不存在或被锁定（账号被锁定，请联系主管解锁。请在个人中心处点击权限配置，搜索到相应用户，点击启用按钮）");
        }
        redisTemplate.opsForHash().delete(KeyConstant.VERIFICATION_CODE, phoneOrEmail + "_" + code);

        Admin passwordAdmin = new Admin();
        passwordAdmin.setAdminId(admin.getAdminId());
        passwordAdmin.setPassword(password);
        authBaseUserFacade.updateUserBase(SystemOriginEnum.ADMIN, passwordAdmin);

        String MD5password = MD5Util.string2MD5(password);
        Admin updateAdmin = new Admin();
        updateAdmin.setAdminId(admin.getAdminId());
        updateAdmin.setPassword(MD5password);
        adminMapper.updateByPrimaryKeySelective(updateAdmin);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional
    public void autoUpdateCloseTime() {
        logger.info("开始更新大客户截单时间");
        List<Admin> admins = adminMapper.selectUpdateCloseTime();
        admins.forEach(admin -> {
            String updateTime = admin.getUpdateCloseOrderTime();
            if (!StringUtils.isEmpty(updateTime)) {
                adminMapper.updateCloseTime(admin.getAdminId(), admin.getUpdateCloseOrderTime());
            }
        });
        logger.info("更新大客户截单时间完成");
    }

    /**
     * 根据adminId
     *
     * @param adminId
     * @param startTime
     * @param endTime
     * @return
     */
    public Map<String, BigDecimal> adminGMVAndSKU(Integer adminId, LocalDateTime startTime, LocalDateTime endTime) {
        HashMap<String, BigDecimal> adminDataMap = new HashMap<>(2);
        OrderVO orderQuery = new OrderVO();
        orderQuery.setStartTime(startTime);
        orderQuery.setEndTime(endTime);
        orderQuery.setAdminId(adminId);
        BigDecimal adminGMV = BigDecimal.ZERO;
        BigDecimal skuSetSize = BigDecimal.ZERO;

        HashSet<String> skuSet = new HashSet<>();
        //订单类型为0,1,3   订单状态为 2，3，6 ; 注意在订单为这种状态下还要筛去sku状态不在2，3，6的情况，记录其sku 和总实付价
        List<OrderVO> ordersList = ordersMapper.selectOrdersByAdminId(orderQuery);
        if (!CollectionUtils.isEmpty(ordersList)) {
            //每一个订单内的 实付金额， 退款金额
            for (OrderVO orderVO : ordersList) {
                List<OrderItemVO> orderItemVOs = orderVO.getOrderItemVOs();
                HashSet<String> orderItemSkus = new HashSet<>();
                BigDecimal subGMV = BigDecimal.ZERO;
                List<OrderItemVO> subOrderItem = orderItemVOs.stream().filter(item -> (item.getStatus() != 2 && item.getStatus() != 3 && item.getStatus() != 6)).collect(Collectors.toList());
                //得到不为2，3，6状态的订单子项，并扣除
                if (!CollectionUtils.isEmpty(subOrderItem)) {
                    for (OrderItemVO orderItemVO : subOrderItem) {
                        BigDecimal price = orderItemVO.getPrice();
                        Integer amount = orderItemVO.getAmount();
                        BigDecimal totalPrice = price.multiply(new BigDecimal(amount));
                        subGMV = subGMV.add(totalPrice);
                    }
                }

                BigDecimal total = orderVO.getTotalPrice().subtract(subGMV);
                adminGMV = adminGMV.add(total);

                for (OrderItemVO itemVO : orderItemVOs) {
                    if (itemVO.getStatus() == 2 || itemVO.getStatus() == 3 || itemVO.getStatus() == 6) {
                        orderItemSkus.add(itemVO.getSku());
                    }
                }

                skuSet.addAll(orderItemSkus);
            }
        }
        if (!CollectionUtils.isEmpty(skuSet)) {
            skuSetSize = new BigDecimal(skuSet.size());
        }
        adminDataMap.put("adminGMV", adminGMV);
        adminDataMap.put("skuSetSize", skuSetSize);
        return adminDataMap;
    }

    /**
     * 日期判断处理；判断是本月的最后一天并且本月比上月短,
     *
     * @return
     */
    public LocalDateTime handleDateTime(LocalDateTime dateTime) {
        LocalDateTime lastMonthDateTime = null;

        LocalDate nowDay = dateTime.toLocalDate();
        LocalDate lastDay = nowDay.with(TemporalAdjusters.lastDayOfMonth());
        int nowMonthLength = nowDay.lengthOfMonth();
        int lastMonthLength = nowDay.minusMonths(1).lengthOfMonth();

        //判断是否为当月最后一天，并且当月比上月短的情况
        if (nowDay.isEqual(lastDay) && nowMonthLength < lastMonthLength) {
            lastMonthDateTime = nowDay.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
        } else {
            lastMonthDateTime = nowDay.minusMonths(1).atTime(23, 59, 59);
        }

        return lastMonthDateTime;
    }

    /**
     * 查询行政同学
     */
    @Override
    public List<Admin> selectAdministration() {
        //人事行政部门在钉钉中的部门id
        int departmentId = 364426701;
        //查询出行政童鞋邮箱信息
        List<Admin> admins = adminMapper.selectAdministration(departmentId);
        return admins;
    }

    @Override
    public AjaxResult selectByUsername(String username) {
        List<Admin> adminList = adminMapper.selectLikeUsername(username);
        return AjaxResult.getOK(adminList);
    }

    /**
     * 该管理员信息
     */
    public String adminMsg(AdminVO adminVO) {
        //当前登录用户对象
        Admin currentUser = baseService.getCurrentUser();
        //当前登录用户对象用户名
        String realName = currentUser.getRealname();
        //当前登录用户对象邮箱账号
        String userName = currentUser.getUsername();
        String realname = adminVO.getRealname();
        String username = adminVO.getUsername();
        StringBuffer person = new StringBuffer();
        person.append("管理员---> ");
        person.append(realName);
        person.append(":");
        person.append(userName);
        person.append(" ,对账号---> ");
        person.append(realname);
        person.append(":");
        person.append(username);
        person.append(" 进行修改。");
        String man = person.toString();
        return man;
    }

    /**
     * 功能权限情况
     */
    private String adminFunctionAuthority(List<Role> deleteRole, List<Role> addRole, int oldAll, int newAll, String oldAdminData, String newAdminData, String man) {
        String[] del = new String[deleteRole.size()];
        String[] add = new String[addRole.size()];
        if (!ObjectUtils.isEmpty(deleteRole)) {
            del = deleteRole.stream().map(Role::getRolename).toArray(String[]::new);
        }
        if (!ObjectUtils.isEmpty(addRole)) {
            add = addRole.stream().map(Role::getRolename).toArray(String[]::new);
        }
        StringBuffer delete = new StringBuffer();
        delete.append(" 该账号被删除： ");
        delete.append(ArrayUtils.toString(del, ","));
        delete.append(" 权限。 ");
        String delRoleMsg = delete.toString();
        if (ObjectUtils.isEmpty(del)) {
            delRoleMsg = "";
        }
        StringBuffer addMsg = new StringBuffer();
        addMsg.append(" 该账号被添加： ");
        addMsg.append(ArrayUtils.toString(add, ","));
        addMsg.append(" 权限。 ");
        String addRoleMsg = addMsg.toString();
        if (ObjectUtils.isEmpty(add)) {
            addRoleMsg = "";
        }
        if (oldAll >= 0 && newAll >= 0) {
            oldAdminData = "";
            newAdminData = "";
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(man);
        stringBuffer.append(oldAdminData);
        stringBuffer.append(newAdminData);
        stringBuffer.append(delRoleMsg);
        stringBuffer.append(addRoleMsg);
        String text = stringBuffer.toString();
        if (oldAll >= 0 && newAll >= 0 && Objects.equals(delRoleMsg, "") && Objects.equals(addRoleMsg, "")) {
            return "";
        }
        return text;
    }

    /**
     * 发送修改信息邮件
     */
    private void sendAuthorityMsg(String text) {
        if (Objects.equals(text, "")) {
            return;
        }
        //List<Admin> list = selectAdministration();
        //String[] dd = list.stream().map(Admin::getUsername).toArray(String[]::new);

        String[] cc = {"<EMAIL>"};
        try {
            mailUtil.sendMail("后台权限变更通知", text, cc, null, null);
        } catch (Exception e) {
            logger.error(Global.collectExceptionStackMsg(e));
        }
    }

    /**
     * 旧地区权限
     *
     * @return
     */
    private String oldDate(String[] oldAdminDataPermission) {
        StringBuffer old = new StringBuffer();
        old.append(" 该账号数据权限原本为： ");
        old.append(ArrayUtils.toString(oldAdminDataPermission, ","));
        old.append("， ");
        String oldAdminData = old.toString();
        if (ObjectUtils.isEmpty(oldAdminDataPermission)) {
            oldAdminData = "";
        }
        return oldAdminData;
    }

    /**
     * 新地区权限
     *
     * @return
     */
    private String newDate(String[] newAdminDataPermission) {
        StringBuffer newAdmin = new StringBuffer();
        newAdmin.append(" 该账号数据权限被改为: ");
        newAdmin.append(ArrayUtils.toString(newAdminDataPermission, ","));
        newAdmin.append("。 ");
        String newAdminData = newAdmin.toString();
        if (ObjectUtils.isEmpty(newAdminDataPermission)) {
            newAdminData = "";
        }
        return newAdminData;
    }

    @Override
    public AjaxResult selectByNameRemakes(String nameRemakes){
        return AjaxResult.getOK( adminRepository.selectByNameRemakes(nameRemakes));
    }

    @Override
    public AjaxResult selectNotMajorAdmin() {
        List<Admin> adminList = adminRepository.selectNotMajorAdmin();
        return AjaxResult.getOK(adminList);
    }
    @Override
    public void createDmsAccount(String reason,String department){
        Integer adminId = getAdminId();
        String dmsAuditProcess = this.createDmsAuditProcess(adminId, reason,department);
        logger.info("钉钉审批信息:{}",dmsAuditProcess);
        if(!Objects.equals(Global.SUCCESS_FLAG,dmsAuditProcess)){
            throw new DefaultServiceException("DMS账号申请发起失败");
        }
    }

    @Override
    public void createDmsCallBack(Integer adminId) {
        logger.info("createDmsCallBack执行：{}",adminId);
        Admin admin = adminMapper.selectByAid(adminId);
        try {
            this.createUserAndGrandPer(admin);
        }catch (Exception e){
            logger.error("adminId:{},错误信息：{}",adminId,e.getMessage());
            if (Boolean.TRUE.equals(redisTemplate.hasKey(KeyConstant.DMS_REDIS_PRE + admin.getAdminId()))) {
                redisTemplate.delete(KeyConstant.DMS_REDIS_PRE + admin.getAdminId());
            }
            String username = admin.getUsername();
            String name = username.substring(0, username.indexOf("@"));
            String title = "DMS账号申请失败";
            JSONObject msgBody = new JSONObject();
            msgBody.put("title", title);
            StringBuilder sb = new StringBuilder("##### " + title + "\n");
            sb.append("> ###### 登录名称：").append(name).append("@summerfarm.onaliyun.com ").append("已存在,请勿重复创建!").append("\n");
            msgBody.put("text",sb.toString());
            Integer[] userList = {admin.getAdminId()};
            List<String> dingUserIdList = new ArrayList<>();
            Arrays.stream(userList).forEach(id -> {
                AdminAuthExtend auth = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), id);
                if (auth != null) {
                    dingUserIdList.add(auth.getUserId());
                }
            });
            if (CollectionUtils.isEmpty(dingUserIdList)) {
                logger.info("钉钉私信发送失败，用户不存在");
                return;
            }
            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), StringUtil.join(",", dingUserIdList), title, sb.toString());
            DingTalkMsgReceiverIdBO fushuMsgBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
            fushuMsgBO.setReceiverIdList(Collections.singletonList(adminId.longValue()));
            dingTalkMsgSender.sendMessageWithFeiShu(fushuMsgBO);
        }
    }

    @Override
    public CommonResult<Void> defaultPwdInit(List<Integer> adminIds) {
        logger.info("默认密码统一初始化开始：{}",JSON.toJSONString(adminIds));
        if(adminIds == null || adminIds.isEmpty()){
            return CommonResult.ok();
        }
        List<DefaultPwdInitResult> results = new ArrayList<>();
        try {
            defaultPwdInitHandle(adminIds, results);
        }catch (Exception e){
            logger.error("默认密码初始化处理异常：{}", JSON.toJSONString(adminIds), e);
        }
        logger.info("默认密码初始化处理结果：{}", JSON.toJSONString(results));
        handleResultToExcel(results);
        logger.info("上传默认密码初始化处理结果到外部存储成功");
        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> srmDefaultPwdInit() {
        logger.info("SRM系统默认密码统一初始化开始");
        List<SrmSupplierUser> srmSupplierUsers = srmSupplierUserMapper.selectAll();
        String defaultMd5Pwd = MD5Util.string2MD5(Global.SRM_DEFAULT_PASSWORD);
        List<DefaultPwdInitResult> results = new ArrayList<>();
        for (SrmSupplierUser srmSupplierUser : srmSupplierUsers) {
            DefaultPwdInitResult result = new DefaultPwdInitResult(null,null, srmSupplierUser.getPhone());
            String password = srmSupplierUserMapper.selectPassword(srmSupplierUser.getId());
            if (!defaultMd5Pwd.equals(password)){
                result.handle(DefaultPwdInitEnum.USER_PWD_NORMAL);
                results.add(result);
                continue;
            }
            //生成6为随机数，修改用户当前密码
            String randomPwd = PasswordUtil.randomPassword(6);
            BaseUserUpdateInput baseUserUpdateInput = new BaseUserUpdateInput();
            baseUserUpdateInput.setBaseUserId(srmSupplierUser.getBaseUserId());
            baseUserUpdateInput.setPassword(randomPwd);
            baseUserFacade.updateUserBase(SystemOriginEnum.SRM, baseUserUpdateInput);
            result.setPassword(randomPwd);
            result.handle(DefaultPwdInitEnum.SUCCESS);
            results.add(result);
        }
        logger.info("SRM系统密码初始化处理结果：{}", JSON.toJSONString(results));
        handleResultToExcel(results);
        logger.info("上传默认密码初始化处理结果到外部存储成功");
        return CommonResult.ok();
    }

    @Override
    public CommonResult<List<Admin>> listByName(String name) {
        List<Admin> list = adminMapper.listByRealName(name);
        return CommonResult.ok(list);
    }

    @Override
    public CommonResult<AdminVO> personalInfo() {
        //获取登录用户
        Admin adminBO = getCurrentUser();
        if (adminBO == null){

        }
        AdminVO adminVO = new AdminVO();
        adminVO.setAdminId(adminBO.getAdminId());
        adminVO.setCreateTime(adminBO.getCreateTime());
        adminVO.setLoginFailTimes(adminBO.getLoginFailTimes());
        adminVO.setIsDisabled(adminBO.getIsDisabled());
        adminVO.setUsername(adminBO.getUsername());
        adminVO.setLoginTime(adminBO.getLoginTime());
        adminVO.setRealname(adminBO.getRealname());
        adminVO.setGender(adminBO.getGender());
        adminVO.setPhone(adminBO.getPhone());
        adminVO.setDepartment(adminBO.getDepartment());
       // adminVO.setRoleIds(adminBO.getRoleIds());

        //查询邀请码
        for (Integer roleId : adminBO.getRoleIds()) {
            if (Objects.equals(roleId, Global.BIG_ROLEID)) {
                adminVO.setType(2);
            }
            if (roleId == 5) {
                Map<String, Object> selectKeys = new HashMap<>();
                selectKeys.put("adminId", adminVO.getAdminId());
                Invitecode invitecode = invitecodeMapper.selectUnique(selectKeys);
                if (invitecode != null && invitecode.getStatus() == 0) {
                    adminVO.setInvitecode(invitecode.getInvitecode());
                }
                List<AdminDataPermission> set = adminDataPermissionMapper.selectByAdminId(adminVO.getAdminId());
                for (AdminDataPermission adminDataPermission : set) {
                    if ("0".equals(adminDataPermission.getPermissionValue()) || "1001".equals(adminDataPermission.getPermissionValue())) {
                        adminVO.setOpenSea(true);
                    }
                }
                break;
            }
        }
        //查询数据权限
        List<AdminDataPermission> dataPermissions = adminDataPermissionMapper.selectByAdminId(adminVO.getAdminId());
        dataPermissions.stream().filter(d -> Objects.isNull(d.getWarehouseNo())).forEach(da -> da.setType(org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE.toString()));
        adminVO.setDataPermissions(dataPermissions);

        if (isSA() || isRD()) {
            adminVO.setOpenSea(true);
            //转化全部数据权限
            List<Area> areas = areaMapper.selectAll(new Area());
            List<AdminDataPermission> adminDataPermissions = new ArrayList<>();
            for (Area area : areas) {
                AdminDataPermission adminDataPermission = new AdminDataPermission();
                adminDataPermission.setPermissionValue(String.valueOf(area.getAreaNo()));
                adminDataPermission.setPermissionName(area.getAreaName());
                adminDataPermission.setType("1");
                adminDataPermissions.add(adminDataPermission);
            }
            //获取全部库存仓数据
            List<WarehouseStorageCenter> storageCenters = warehouseStorageService.selectAllData(new WarehouseStorageCenter());
            storageCenters.forEach(center -> {
                AdminDataPermission adminDataPermission = new AdminDataPermission(center);
                adminDataPermissions.add(adminDataPermission);
            });
            adminVO.setDataPermissions(adminDataPermissions);
        }

        // 数据报表-销售主管数据权限处理
        buildSaleManagerInfo(adminVO);
        return CommonResult.ok(adminVO);
    }
    /**
     * 如果是销售主管，数据报表只给所拥有仓的数据
     *
     * @param adminVO
     */
    private void buildSaleManagerInfo(AdminVO adminVO) {
        // 目前所配置的仓数据
        Config areaConfig = configMapper.selectOne(CONFIG_KEY_REPORT_SALE_AREA);

        // 坑点，guava Splitter的splitToList返回的是一个unmodifiableList 修改里面元素会抛异常
        List<String> partArea = Lists.newArrayList(Global.DEFAULT_SPLITTER.splitToList(areaConfig.getValue()));

        // 超管给ta全部的地区
        if (isSA() || isRD()) {
            adminVO.setOnlySaleManager(true);
            adminVO.setPartSaleArae(partArea);
            return;
        }

        // 判断是否是销售主管权限
        List<Integer> roleIds = adminVO.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            adminVO.setOnlySaleManager(false);
            return;
        }
        for (Integer roleId : roleIds) {
            if (SALESA_ROLE_ID.contains(roleId) ) {
                // 如果有销售主管权限
                adminVO.setOnlySaleManager(true);
                List<AdminDataPermission> dataPermissions = adminVO.getDataPermissions();
                if (CollectionUtils.isEmpty(dataPermissions)) {
                    adminVO.setPartSaleArae(null);
                    return;
                }
                // 查询该销售主管的数据权限，数据权限取交集
                List<String> roleDatePermissions = dataPermissions.stream()
                        .map(data -> data.getPermissionValue()).collect(Collectors.toList());
                partArea.retainAll(roleDatePermissions);
                adminVO.setPartSaleArae(partArea);
                return;
            }
        }
        adminVO.setOnlySaleManager(false);
    }

    private void defaultPwdInitHandle(List<Integer> adminIds, List<DefaultPwdInitResult> results) {
        String subject = "【鲜沐系统】您的账号密码存在安全问题，已初始化密码，请尽快修改";
        String contentTemplate = "你好，您的账号密码存在安全问题，已初始化密码，请尽快修改\n" +
                "    姓名：%s\n" +
                "    账号：%s\n" +
                "    密码：%s\n" +
                "    手机：%s\n" +
                "by 鲜沐技术";
        List<Admin> admins = adminMapper.listNameByAdminId(adminIds);
        for (Admin admin : admins) {
            DefaultPwdInitResult result = new DefaultPwdInitResult(admin.getAdminId(),admin.getRealname(), admin.getUsername());
            if (admin.getAdminType() != null){
                result.handle(DefaultPwdInitEnum.USER_TYPE_EXCEPTION);
                results.add(result);
                continue;
            }
            if (admin.getDisabled()){
                result.handle(DefaultPwdInitEnum.USER_DISABLED);
                results.add(result);
                continue;
            }
            //查询密码是否存在异常（重复密码）
            Integer count = adminMapper.selectSamePwdCount(admin.getPassword());
            if (count <= 1){
                result.handle(DefaultPwdInitEnum.USER_PWD_NORMAL);
                results.add(result);
                continue;
            }
            //生成16为随机数，修改用户当前密码
            String randomPwd = PasswordUtil.randomPwd(16);
            admin.setPassword(MD5Util.string2MD5(randomPwd));
            result.setPassword(randomPwd);

            Admin updateAdmin = new Admin();
            updateAdmin.setAdminId(admin.getAdminId());
            updateAdmin.setPassword(randomPwd);
            authBaseUserFacade.updateUserBase(SystemOriginEnum.ADMIN, updateAdmin);

            adminMapper.updateByPrimaryKeySelective(admin);
            result.handle(DefaultPwdInitEnum.SUCCESS);
            results.add(result);

            String content = String.format(contentTemplate, admin.getRealname(), admin.getUsername(), randomPwd, admin.getPhone());
            //邮件通知
            mailUtil.sendMail(subject, content, new String[]{admin.getUsername()}, null);
        }
    }

    private void handleResultToExcel(List<DefaultPwdInitResult> results) {
        String fileNameTemplate = "默认密码初始化-处理结果-%s.xls";
        String fileName = String.format(fileNameTemplate, DateUtils.localDateTimeToString(LocalDateTime.now()));
        String[] titleName = {"adminId", "realName", "username","password","result","reason"};
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 8000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 5000);
        sheet.setColumnWidth(5, 5000);
        //设置标题行
        Row title = sheet.createRow(org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO);
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellValue(titleName[i]);
        }
        for (int i = 0; i < results.size(); i++) {
            DefaultPwdInitResult result = results.get(i);
            Row nextRow = sheet.createRow(i + 1);
            nextRow.createCell(0).setCellValue(String.valueOf(result.getAdminId()));
            nextRow.createCell(1).setCellValue(Objects.isNull(result.getRealName())?"":result.getRealName());
            nextRow.createCell(2).setCellValue(Objects.isNull(result.getUsername())?"":result.getUsername());
            nextRow.createCell(3).setCellValue(Objects.isNull(result.getPassword())?"":result.getPassword());
            nextRow.createCell(4).setCellValue(result.getResult());
            nextRow.createCell(5).setCellValue(Objects.isNull(result.getReason())?"":result.getReason());
        }
        //根据文件名获得token
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);
        if (ObjectUtils.isEmpty(data)) {
            return;
        }
        //上传文件至七牛云
        AjaxResult result = qiNiuService.uploadFile(fileName,workbook);
        Integer status = Objects.equals(result.getCode(), Global.SUCCESS_FLAG) ? Global.SUCCESS : Global.FAIL;
        //插入文件下载记录
        FileDownloadRecord record = new FileDownloadRecord();
        record.setFileName(fileName);
        record.setStatus(status);
        record.setType(FileDownloadRecordEnum.DEFAULT_PWD_INIT.ordinal());
        record.setCreateTime(LocalDateTime.now());
        record.setAdminId(SpringContextUtil.isProduct()?484:2011);
        fileDownloadRecordMapper.insert(record);
    }


   public void createUserAndGrandPer(Admin admin) throws Exception {
       logger.info("创建dms用户并授权开始{}",admin.getUsername());
       // 获取登录名称截取前缀
       String username = admin.getUsername();
       String name = username.substring(0, username.indexOf("@"));
       // 获取部门信息
       String department = "";
       if (Boolean.TRUE.equals(redisTemplate.hasKey(KeyConstant.DMS_REDIS_PRE + admin.getAdminId()))) {
           department = redisTemplate.opsForValue().get(KeyConstant.DMS_REDIS_PRE + admin.getAdminId());
           redisTemplate.delete(KeyConstant.DMS_REDIS_PRE + admin.getAdminId());
       }
       // 1.创建ram用户
       CreateUserRequest createUserRequest = new CreateUserRequest()
               .setUserName(name)
               .setDisplayName(department + "-" + admin.getRealname());
       CreateUserResponse createUserResponse = ramClient.createUser(createUserRequest);
       CreateUserResponseBody.CreateUserResponseBodyUser ramUser = createUserResponse.getBody().getUser();
       logger.info("注册ram用户成功");

       // 2.给用户创建控制台登录
       CreateLoginProfileRequest createLoginProfileRequest = new CreateLoginProfileRequest()
               .setUserName(ramUser.getUserName())
               .setPassword("Xianmu2022!@_@")
               .setPasswordResetRequired(true);
       ramClient.createLoginProfile(createLoginProfileRequest);
       logger.info("给ram用户创建控制台登录成功");
       // -------------------------------------------------------------------------
       // 3.dms注册ram用户
       RegisterUserRequest registerUserRequest = new RegisterUserRequest()
               .setUid(ramUser.getUserId());
       dmsClient.registerUser(registerUserRequest);
       logger.info("dms注册ram用户成功");

       // 4.修改dms用户名称
       UpdateUserRequest updateUserRequest = new UpdateUserRequest()
               .setUid(Long.valueOf(ramUser.getUserId()))
               .setUserNick(department + "-" + admin.getRealname());
       dmsClient.updateUser(updateUserRequest);
       logger.info("dms修改用户名称成功");

       // 查询用户userId值 不是Uid 授权时需要
       GetUserRequest getUserRequest = new GetUserRequest()
               .setUid(ramUser.getUserId());
       GetUserResponse user = dmsClient.getUser(getUserRequest);
       GetUserResponseBody.GetUserResponseBodyUser dmsUser = user.getBody().getUser();
       logger.info("dms userId:{}",dmsUser.getUserId());
       // 5.给用户授权：查询-导出
       LocalDate oneYearAfter = LocalDate.now().plusYears(1L);
       GrantUserPermissionRequest query = new GrantUserPermissionRequest()
               .setUserId(dmsUser.getUserId())
               .setInstanceId(1200340L)// 鲜沐-查询分析库（只读）
               .setDsType("INSTANCE")
               .setPermTypes("QUERY")
               .setExpireDate(oneYearAfter.toString());
       dmsClient.grantUserPermission(query);
       GrantUserPermissionRequest export = new GrantUserPermissionRequest()
               .setUserId(dmsUser.getUserId())
               .setInstanceId(1200340L)// 鲜沐-查询分析库（只读）
               .setDsType("INSTANCE")
               .setPermTypes("EXPORT")
               .setExpireDate(oneYearAfter.toString());
       dmsClient.grantUserPermission(export);
       logger.info("dms授权用户成功");

       logger.info("创建dms用户并授权结束");
       String title = "DMS账号成功申请";
       StringBuilder sb = new StringBuilder("##### " + title + "\n");
       sb.append("> ###### 登录名称：").append(name).append("@summerfarm.onaliyun.com,").append("\n");
       sb.append("> ######    密码：").append("Xianmu2022!@_@").append("\n");
       sb.append("> ###### 登录地址：").append("https://dms.aliyun.com/").append("\n");
       sb.append("> ###### 注意事项：").append("！！！在右下角选择RAM用户登录！！！");
       DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, sb.toString());
       DingTalkMsgReceiverIdBO fushuMsgBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
       fushuMsgBO.setReceiverIdList(Collections.singletonList(admin.getAdminId().longValue()));
       dingTalkMsgSender.sendMessageWithFeiShu(fushuMsgBO);
    }


    /**
     * 发起钉钉审批流程
     * @param reason 理由
     * @return 返回值
     */
    private String createDmsAuditProcess(Integer adminId,String reason,String department) {

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.DMS_ACCOUNT_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(super.getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(adminId.longValue());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(2);

        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("所在部门");
        df1.setFormValue(department);
        dingForms.add(df1);

        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("申请理由");
        df2.setFormValue(reason);
        dingForms.add(df2);

        processInstanceCreateBO.setDingdingForms(dingForms);
        ProcessCreateResultBO processInstance;
        try {
            processInstance = dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
            redisTemplate.opsForValue().set(KeyConstant.DMS_REDIS_PRE + adminId,department,20, TimeUnit.DAYS);
        }catch (DingdingProcessException e){
            logger.info("钉钉调用失败:{}",e.getMessage());
            throw new DefaultServiceException(e.getMessage());
        }

        if(processInstance.isSuccess()){
            return Global.SUCCESS_FLAG;
        }
        return processInstance.getMessage();
    }

    @Override
    public CommonResult<List<AdminMerchantDTO>> queryContactByNameRemakes(String nameRemakes) {
        List<Admin> admins = adminMapper.queryNameRemakes(nameRemakes);

        List<AdminMerchantDTO> adminMerchantDTOList = new ArrayList<>();
        for (Admin admin : admins) {
           List<Long> contactIdList = merchantMapper.queryContactByAdminId(admin.getAdminId());
            AdminMerchantDTO adminMerchantDTO = new AdminMerchantDTO();
            adminMerchantDTO.setAdminId(admin.getAdminId());
            adminMerchantDTO.setNameRemakes(admin.getNameRemakes());
            adminMerchantDTO.setContactIds(contactIdList);

            adminMerchantDTOList.add(adminMerchantDTO);
        }
        return CommonResult.ok(adminMerchantDTOList);
    }

    @Override
    public List<AdminVO> listPopWhite() {
        List<String> values = configService.getValues(Global.POP_ADMIN_ID);
        if (CollectionUtils.isEmpty(values)) {
            return Collections.emptyList();
        }
        List<Integer> adminIds = values.stream().map(Integer::valueOf).collect(Collectors.toList());
        List<Admin> admins = adminMapper.listNameByAdminId(adminIds);
        if (CollectionUtils.isEmpty(admins)) {
            return Collections.emptyList();
        }

        List<AdminVO> adminVOS = new ArrayList<>(admins.size());
        admins.forEach(admin -> {
            AdminVO adminVO = new AdminVO();
            adminVO.setAdminId(admin.getAdminId());
            adminVO.setRealname(admin.getRealname());
            adminVO.setPhone(admin.getPhone());
            adminVO.setNameRemakes(admin.getNameRemakes());
            adminVOS.add(adminVO);
        });
        return adminVOS;
    }
}
