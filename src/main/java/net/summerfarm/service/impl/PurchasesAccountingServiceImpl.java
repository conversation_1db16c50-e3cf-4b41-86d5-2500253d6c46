package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.PurchaseAccountingVO;
import net.summerfarm.model.vo.PurchasesAccountPlanVO;
import net.summerfarm.model.vo.PurchasesPlanVO;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.PurchasesAccountingService;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2020/3/11  10:34
 */
@Service
public class PurchasesAccountingServiceImpl extends BaseService implements PurchasesAccountingService {

    @Resource
    private PurchasesAccountPlanMapper purchasesAccountPlanMapper;
    @Resource
    private PurchaseAccountingMapper purchaseAccountingMapper;
    @Resource
    private PurchasesBackDetailMapper purchasesBackDetailMapper;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private PurchasesMapper purchasesMapper;
    @Resource
    private PurchasesImprestMapper purchasesImprestMapper;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private ConfigMapper configMapper;



    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult createAccount(PurchaseAccountingVO purchaseAccountingVO){
        List<PurchasesPlanVO> purchasesPlanVOs = purchaseAccountingVO.getPurchasesPlanVOs();
        ArrayList<PurchasesAccountPlan> purchasesAccountPlans = new ArrayList<>();
        String invoiceUrl = purchaseAccountingVO.getInvoiceUrl();
        if(StringUtils.isEmpty(invoiceUrl)){
            return AjaxResult.getErrorWithMsg("凭证不能为空");

        }
        //校验采购单项 是否有待审核的退货单，是否已经提交核算单，校验物流费用,校验退货单数量,核算单金额
        List<String> purchasesNoList = purchaseAccountingVO.getPurchasesNoList();

        if(!CollectionUtils.isEmpty(purchasesNoList)){
            for (String purchasesNo : purchasesNoList) {
                PurchasesAccountPlan queryPlan = purchasesAccountPlanMapper.selectAccount(purchasesNo);
                if(queryPlan != null){
                    return AjaxResult.getErrorWithMsg("该订单项物流已经发起核算单");
                }
                PurchasesAccountPlan inertAccountPlan = new PurchasesAccountPlan();
                inertAccountPlan.setType(1);
                inertAccountPlan.setPurchasesNo(purchasesNo);
                purchasesAccountPlans.add(inertAccountPlan);
            }
        }
        if(!CollectionUtils.isEmpty(purchasesPlanVOs)){
            for (PurchasesPlanVO purchasesPlanVO : purchasesPlanVOs) {
                String purchaseNo = purchasesPlanVO.getPurchaseNo();
                Integer id = purchasesPlanVO.getId();
                String sku = purchasesPlanVO.getSku();
                LocalDate now = LocalDate.of(2020,3,10);
                if(purchasesPlanVO.getPurchaseTime() != null && !now.isBefore(purchasesPlanVO.getPurchaseTime())){
                    return  AjaxResult.getErrorWithMsg("2020-03-11 时间以前不可发起核素单");
                }
                List<String> backNo = purchasesBackDetailMapper.selectAudite(purchaseNo, sku);
                if(!CollectionUtils.isEmpty(backNo)){
                    return AjaxResult.getErrorWithMsg("有待审核的退款单");
                }
                PurchasesAccountPlan purchasesAccountPlan = purchasesAccountPlanMapper.selectPurchases(id);
                if(purchasesAccountPlan != null){
                    return AjaxResult.getErrorWithMsg("该订单项已经发起核算单");
                }

                List<PurchasesBackDetail> purchasesBackDetails = purchasesBackDetailMapper.selectByNoAndSku(purchaseNo, sku);
                BigDecimal backCost = BigDecimal.ZERO;
                int backQuantity = 0;
                if(!CollectionUtils.isEmpty(purchasesBackDetails)){
                    for (PurchasesBackDetail purchasesBackDetail : purchasesBackDetails) {
                        backCost =  backCost.add(purchasesBackDetail.getTotalCost());
                        if(purchasesBackDetail.getActualOutQuantity() > 0){
                            backQuantity = backQuantity + purchasesBackDetail.getActualOutQuantity();
                        } else {
                            backQuantity += purchasesBackDetail.getOutQuantity();
                        }
                    }
                }
                if(!Objects.equals(backQuantity,purchasesPlanVO.getBackQuantity())){
                    return AjaxResult.getErrorWithMsg("退回数量发生改变");
                }
                if(backCost.compareTo(purchasesPlanVO.getBackCost()) != 0){
                    return AjaxResult.getErrorWithMsg("退回金额发生改变");
                }

                //生成对应的关联信息
                PurchasesAccountPlan inertAccountPlan = new PurchasesAccountPlan();
                inertAccountPlan.setPlanId(id);
                inertAccountPlan.setType(PurchaseAccountingVO.STATUS_NEW);
                inertAccountPlan.setPurchasesNo(purchaseNo);
                inertAccountPlan.setAccountingCost(purchasesPlanVO.getAccountingCost());
                inertAccountPlan.setAccountingQuantity(purchasesPlanVO.getAccountingQuantity());
                inertAccountPlan.setBackCost(purchasesPlanVO.getBackCost());
                inertAccountPlan.setBackQuantity(purchasesPlanVO.getBackQuantity());
                purchasesAccountPlans.add(inertAccountPlan);

            }
        }


        PurchaseAccounting purchaseAccounting = new PurchaseAccounting();
        purchaseAccounting.setStatus(0);
        purchaseAccounting.setCreateId(getAdminId());
        purchaseAccounting.setCreateName(getAdminName());
        purchaseAccounting.setCreateTime(LocalDateTime.now());
        purchaseAccounting.setInvoiceUrl(purchaseAccountingVO.getInvoiceUrl());
        purchaseAccounting.setPayeeName(purchaseAccountingVO.getPayeeName());
        purchaseAccounting.setPayeeId(purchaseAccountingVO.getPayeeId());
        purchaseAccounting.setTotalAmount(purchaseAccountingVO.getTotalAmount());
        purchaseAccounting.setCreateRemark(purchaseAccountingVO.getCreateRemark());

        //批量插入 关联信息
        //插入 审核单
        purchaseAccountingMapper.createPurchaseAccount(purchaseAccounting);
        purchasesAccountPlans.forEach( x->x.setAccountId(purchaseAccounting.getId()));
        purchasesAccountPlanMapper.insertBatch(purchasesAccountPlans);

        //更新采购项可结算状态
        purchasesAccountPlans.forEach(el -> {
            if (Objects.equals(el.getType(), 0)) {
                PurchasesPlan purchasesPlan = new PurchasesPlan();
                purchasesPlan.setId(el.getPlanId());
                purchasesPlan.setSettleFlag(1);
                purchasesPlanMapper.update(purchasesPlan);
            } else {
                Purchases purchases = new Purchases();
                purchases.setPurchaseNo(el.getPurchasesNo());
                purchases.setLogisticsSettleFlag(1);
                purchasesMapper.updateByPurchaseNo(purchases);
            }
        });

        //发送钉钉消息
        Config purchasesRobotUrl = configMapper.selectOne("purchasesRobotUrl");
        Integer id = purchaseAccounting.getId();
        if(purchasesRobotUrl != null){
            DingTalkLinkMsg dingTalkLinkMsg = new DingTalkLinkMsg();
            dingTalkLinkMsg.setText(getAdminName() + "提交了"+id+"号核算单,请审核");
            dingTalkLinkMsg.setTitle("详细说明:发起核算总计:" + purchaseAccountingVO.getTotalAmount().toString()  +"元,核算项：...");
            dingTalkLinkMsg.setMessageUrl("https://admin." + Global.TOP_DOMAIN_NAME + "/summerfarm/home.html#/login?name=reserveFund&id=" + id);
            dingTalkLinkMsg.setRobotUrl(purchasesRobotUrl.getValue());
            dingTalkService.dingTalkRobotLink(dingTalkLinkMsg);
        }
        return AjaxResult.getOK();

    }

    //审核审批核算单
    @Override
    public AjaxResult auditer(PurchaseAccounting purchaseAccounting,Integer type){

        Integer adminId = getAdminId();
        String adminName = getAdminName();
        Integer id = purchaseAccounting.getId();
        PurchaseAccounting updateAccount = new PurchaseAccounting();
        //查询校验
        PurchaseAccounting queryAccount = purchaseAccountingMapper.selectById(purchaseAccounting.getId());
        Integer status = queryAccount.getStatus();
        if(queryAccount == null){
           return AjaxResult.getErrorWithMsg("审核单不存在");
        }
        if(Objects.equals(PurchaseAccounting.STATUS_PASS,status) || Objects.equals(PurchaseAccounting.STATUS_CLOSE,status)){
            return AjaxResult.getErrorWithMsg("审核单已经审核");
        }
        Integer createId = queryAccount.getCreateId();
        //拒绝
        if(Objects.equals(type,PurchaseAccounting.STATUS_CLOSE)){
            if(!Objects.equals(PurchaseAccounting.STATUS_NEW,status) && !Objects.equals(status,PurchaseAccounting.STATUS_PENDING)){
                return AjaxResult.getErrorWithMsg("审核单状态异常");
            }
            Config purchasesRobotUrl = configMapper.selectOne("purchasesRobotUrl");


            if(Objects.equals(PurchaseAccounting.STATUS_NEW,status)){
                updateAccount.setExamineId(adminId);
                updateAccount.setExamineName(adminName);
                updateAccount.setExamineRemark(purchaseAccounting.getExamineRemark());
                updateAccount.setExamineTime(LocalDateTime.now());
                if(purchasesRobotUrl != null){
                    dingTalkService.dingTalkRobotTxtAT(purchasesRobotUrl.getValue(),"您发起的单号为"+id+"号的核算单审核失败,请重新提交", null);
                }
            }
            if(Objects.equals(PurchaseAccounting.STATUS_PENDING,status)){
                updateAccount.setApprovalId(adminId);
                updateAccount.setApprovalName(adminName);
                updateAccount.setApprovalRemark(purchaseAccounting.getApprovalRemark());
                updateAccount.setApprovalTime(LocalDateTime.now());
                if(purchasesRobotUrl != null){
                    dingTalkService.dingTalkRobotTxtAT(purchasesRobotUrl.getValue(),"您发起的单号为"+id+"号的核算单审批失败,请重新提交", null);
                }
            }
            updateAccount.setStatus(PurchaseAccounting.STATUS_CLOSE);

            //更新采购项可结算状态
            List<PurchasesAccountPlan> planList = purchasesAccountPlanMapper.selectByAccountId(id);
            planList.forEach(el -> {
                if (Objects.equals(el.getType(), 0)) {
                    PurchasesPlan purchasesPlan = new PurchasesPlan();
                    purchasesPlan.setId(el.getPlanId());
                    purchasesPlan.setSettleFlag(0);
                    purchasesPlanMapper.update(purchasesPlan);
                } else {
                    Purchases purchases = new Purchases();
                    purchases.setPurchaseNo(el.getPurchasesNo());
                    purchases.setLogisticsSettleFlag(0);
                    purchasesMapper.updateByPurchaseNo(purchases);
                }
            });
        }
        //审核通过
        if(Objects.equals(type,PurchaseAccounting.STATUS_PENDING)){
            if(!Objects.equals(PurchaseAccounting.STATUS_NEW,status)){
                return AjaxResult.getErrorWithMsg("审核单状态不为待审核");
            }
            updateAccount.setStatus(PurchaseAccounting.STATUS_PENDING);
            updateAccount.setExamineId(adminId);
            updateAccount.setExamineName(adminName);
            updateAccount.setExamineRemark(purchaseAccounting.getExamineRemark());
            updateAccount.setExamineTime(LocalDateTime.now());
            updateAccount.setInvoiceUrl(purchaseAccounting.getInvoiceUrl());
            //发送钉钉消息
            Config purchasesRobotUrl = configMapper.selectOne("purchasesRobotUrl");
            if(purchasesRobotUrl != null){
                DingTalkLinkMsg dingTalkLinkMsg = new DingTalkLinkMsg();
                dingTalkLinkMsg.setText(queryAccount.getCreateName() + "提交了"+id+"号核算单,已审核通过,请审批");
                dingTalkLinkMsg.setTitle("详细说明:发起核算总计:" + queryAccount.getTotalAmount().toString()  +"元,核算项：...");
                dingTalkLinkMsg.setMessageUrl("https://admin." + Global.TOP_DOMAIN_NAME + "/summerfarm/home.html#/login?name=reserveFund&id=" + id);
                dingTalkLinkMsg.setRobotUrl(purchasesRobotUrl.getValue());
                dingTalkService.dingTalkRobotLink(dingTalkLinkMsg);
            }
        }
        //审批通过
        if(Objects.equals(type,PurchaseAccounting.STATUS_PASS)){
            if(!Objects.equals(PurchaseAccounting.STATUS_PENDING,status)){
                return AjaxResult.getErrorWithMsg("审核单状态不为待审批");
            }
            updateAccount.setStatus(PurchaseAccounting.STATUS_PASS);
            updateAccount.setApprovalId(adminId);
            updateAccount.setApprovalName(adminName);
            updateAccount.setApprovalRemark(purchaseAccounting.getApprovalRemark());
            updateAccount.setApprovalTime(LocalDateTime.now());
            updateAccount.setInvoiceUrl(purchaseAccounting.getInvoiceUrl());
            Config purchasesRobotUrl = configMapper.selectOne("purchasesRobotUrl");
            if(purchasesRobotUrl != null){
                dingTalkService.dingTalkRobotTxtAT(purchasesRobotUrl.getValue(),"您发起的单号为"+id+"号的核算单审批成功", null);
            }

        }
        updateAccount.setId(purchaseAccounting.getId());
        purchaseAccountingMapper.update(updateAccount);
        return AjaxResult.getOK();
    }

    @Override
    public void checkDown(String ids, String purchaseNos, HttpServletResponse response){

        Workbook workbook = new HSSFWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        if(!StringUtils.isEmpty(ids)){
            List<String> idList = Arrays.asList(ids.split(","));
            Sheet sheet = workbook.createSheet("sku明细");
            sheet.setColumnWidth(0, 3500);
            sheet.setColumnWidth(1, 5000);
            sheet.setColumnWidth(2, 6000);
            sheet.setColumnWidth(3, 6000);
            sheet.setColumnWidth(4, 5000);
            sheet.setColumnWidth(5, 3500);
            sheet.setColumnWidth(6, 3000);
            sheet.setColumnWidth(7, 3000);
            sheet.setColumnWidth(8, 3000);
            sheet.setColumnWidth(9, 3000);
            sheet.setColumnWidth(10, 3000);
            sheet.setColumnWidth(11, 3000);
            Row title = sheet.createRow(0);
            title.createCell(0).setCellValue("采购单号");
            title.createCell(1).setCellValue("采购时间");
            title.createCell(2).setCellValue("sku编码");
            title.createCell(3).setCellValue("商品名称");
            title.createCell(4).setCellValue("规格");
            title.createCell(5).setCellValue("供应商");
            title.createCell(6).setCellValue("采购数量");
            title.createCell(7).setCellValue("采购金额");
            title.createCell(8).setCellValue("退回数量");
            title.createCell(9).setCellValue("退回金额");
            title.createCell(10).setCellValue("核算数量");
            title.createCell(11).setCellValue("核算金额");
            int size = idList.size();
            Row row;
            for (int i = 0 ; i < size; i++){
                row = sheet.createRow(i + 1);
                String id = idList.get(i);
                PurchasesPlan purchasesPlan = purchasesPlanMapper.selectByPrimaryKey(Integer.valueOf(id));
                Purchases purchases = purchasesMapper.selectByNo(purchasesPlan.getPurchaseNo());
                String purchaseNo = purchasesPlan.getPurchaseNo();
                String sku = purchasesPlan.getSku();
                Integer quantity = purchasesPlan.getQuantity();
                BigDecimal price = purchasesPlan.getPrice();
                row.createCell(0).setCellValue(purchasesPlan.getPurchaseNo());
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                row.createCell(1).setCellValue(df.format(purchases.getPurchaseTime()));
                row.createCell(2).setCellValue(purchasesPlan.getSku());
                row.createCell(3).setCellValue(purchasesPlan.getTitle());
                row.createCell(4).setCellValue(purchasesPlan.getSpecification());
                row.createCell(5).setCellValue(purchasesPlan.getSupplier());
                row.createCell(6).setCellValue(quantity);
                row.createCell(7).setCellValue(price.toString());
                List<PurchasesBackDetail> purchasesBackDetails = purchasesBackDetailMapper.selectByNoAndSku(purchaseNo, sku);
                BigDecimal backCost = BigDecimal.ZERO;
                int backQuantity = 0;
                if(!CollectionUtils.isEmpty(purchasesBackDetails)){
                    for (PurchasesBackDetail purchasesBackDetail : purchasesBackDetails) {
                        backCost =  backCost.add(purchasesBackDetail.getTotalCost());
                        if(purchasesBackDetail.getActualOutQuantity() > 0){
                            backQuantity += purchasesBackDetail.getActualOutQuantity();
                        } else {
                            backQuantity = backQuantity+ purchasesBackDetail.getOutQuantity();
                        }
                    }
                }
                row.createCell(8).setCellValue(backQuantity);
                row.createCell(9).setCellValue(backCost.toString());
                row.createCell(10).setCellValue(quantity- backQuantity);
                row.createCell(11).setCellValue(price.subtract(backCost).toString());
            }
        }
        if(!StringUtils.isEmpty(purchaseNos)){
            List<String> purchasesNoList = Arrays.asList(purchaseNos.split(","));
            Sheet logisticsSheet = workbook.createSheet("物流费明细");
            logisticsDown(logisticsSheet,purchasesNoList);
        }
        try {
            String adminName = getAdminName();
            LocalDate now = LocalDate.now();
            StringBuilder builder = new StringBuilder("核算单");
            builder.append("-")
                    .append(adminName)
                    .append("-")
                    .append(now)
                    .append(".xls");
            ExcelUtils.outputExcel(workbook, builder.toString(), response);
        } catch (IOException e) {

        }
    }

    private void logisticsDown(Sheet logisticsSheet,List<String> purchasesNoList){


        logisticsSheet.setColumnWidth(0, 3500);
        logisticsSheet.setColumnWidth(1, 5000);
        logisticsSheet.setColumnWidth(2, 6000);
        logisticsSheet.setColumnWidth(3, 5000);
        Row cell = logisticsSheet.createRow(0);
        cell.createCell(0).setCellValue("采购单号");
        cell.createCell(1).setCellValue("采购时间");
        cell.createCell(2).setCellValue("费用名称");
        cell.createCell(3).setCellValue("金额(元)");
        Row row;
        int size = purchasesNoList.size();
        for (int i =0 ; i< size ;i++){
            row = logisticsSheet.createRow(i + 1);
            String purchaseNo = purchasesNoList.get(i);
            Purchases purchases = purchasesMapper.selectByNo(purchaseNo);
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            row.createCell(0).setCellValue(purchaseNo);
            row.createCell(1).setCellValue(df.format(purchases.getAddTime()));
            row.createCell(2).setCellValue("物流费用总额");
            row.createCell(3).setCellValue(purchases.getLogisticsCost().toString());
        }

    }

    @Override
    public void accountDown(Integer id,HttpServletResponse response){

        List<PurchasesAccountPlanVO> accountPlanVOS = purchasesAccountPlanMapper.selectAccountPlanVO(id);
        int size = accountPlanVOS.size();
        Workbook workbook = new HSSFWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Sheet sheet = workbook.createSheet("sku明细");
        sheet.setColumnWidth(0, 3500);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 5000);
        sheet.setColumnWidth(5, 3500);
        sheet.setColumnWidth(6, 3000);
        sheet.setColumnWidth(7, 3000);
        sheet.setColumnWidth(8, 3000);
        sheet.setColumnWidth(9, 3000);
        sheet.setColumnWidth(10, 3000);
        sheet.setColumnWidth(11, 3000);
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("采购单号");
        title.createCell(1).setCellValue("采购时间");
        title.createCell(2).setCellValue("sku编码");
        title.createCell(3).setCellValue("商品名称");
        title.createCell(4).setCellValue("规格");
        title.createCell(5).setCellValue("供应商");
        title.createCell(6).setCellValue("采购数量");
        title.createCell(7).setCellValue("采购金额");
        title.createCell(8).setCellValue("退回数量");
        title.createCell(9).setCellValue("退回金额");
        title.createCell(10).setCellValue("核算数量");
        title.createCell(11).setCellValue("核算金额");

        for (int i = 0 ;i< size;i++){
            title = sheet.createRow(i + 1);
            PurchasesAccountPlanVO purchasesAccountPlanVO = accountPlanVOS.get(i);
            title.createCell(0).setCellValue(purchasesAccountPlanVO.getPurchaseNo());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            title.createCell(1).setCellValue(df.format(purchasesAccountPlanVO.getPurchaseTime()));
            title.createCell(2).setCellValue(purchasesAccountPlanVO.getSku());
            title.createCell(3).setCellValue(purchasesAccountPlanVO.getPdName());
            title.createCell(4).setCellValue(purchasesAccountPlanVO.getWeight());
            title.createCell(5).setCellValue(purchasesAccountPlanVO.getSupplier());
            title.createCell(6).setCellValue(purchasesAccountPlanVO.getQuantity());
            title.createCell(7).setCellValue(purchasesAccountPlanVO.getTotalPrice().toString());
            title.createCell(8).setCellValue(purchasesAccountPlanVO.getBackQuantity());
            title.createCell(9).setCellValue(purchasesAccountPlanVO.getBackCost().toString());
            title.createCell(10).setCellValue(purchasesAccountPlanVO.getAccountingQuantity());
            title.createCell(11).setCellValue(purchasesAccountPlanVO.getAccountingCost().toString());
        }
        List<PurchasesAccountPlanVO> logistics = purchasesAccountPlanMapper.selectLogistics(id);
        List<String> purchases = new ArrayList<>();
        logistics.forEach(x -> purchases.add(x.getPurchaseNo()));
        if(!CollectionUtils.isEmpty(logistics)){
            Sheet logisticsSheet = workbook.createSheet("物流费明细");
            logisticsDown(logisticsSheet,purchases);
        }
        PurchaseAccounting purchaseAccounting = purchaseAccountingMapper.selectById(id);
        int accountId = purchaseAccounting.getId();
        try {
            LocalDate now = LocalDate.now();
            StringBuilder builder = new StringBuilder("核算单");
            builder.append("-")
                    .append(accountId)
                    .append("-")
                    .append(now)
                    .append(".xls");
            ExcelUtils.outputExcel(workbook, builder.toString(), response);
        } catch (IOException e) {
                logger.info("数据导出失败");
        }

    }

    @Override
    public AjaxResult selectPurchaseAccount(int pageIndex, int pageSize,PurchaseAccountingVO purchaseAccountingVO){
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseAccounting> query = purchaseAccountingMapper.selectPurchaseAccount(purchaseAccountingVO);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(query));
    }

    @Override
    public AjaxResult selectPurchaseImprest(){
        //查备用金
        PurchaseAccountingVO query = new PurchaseAccountingVO();
        PurchasesImprest purchasesImprest = purchasesImprestMapper.queryImprest();
        BigDecimal totalAmount = purchasesImprest.getAmount();
        query.setTotalImprestAmount(totalAmount);

        BigDecimal surplusAmount = totalAmount;
        BigDecimal amount = purchaseAccountingMapper.selectTotalAmount();
        surplusAmount = amount == null ? surplusAmount : surplusAmount.add(amount);
        LocalDate start =  LocalDate.of(2020,3,11);
        BigDecimal purchaseAmount = purchasesMapper.selectTotalPrice(start);
        surplusAmount = purchaseAmount == null ? surplusAmount : surplusAmount.subtract(purchaseAmount);
        BigDecimal backAmount = purchasesBackDetailMapper.selectBackTotalCost(start);
        surplusAmount = backAmount == null ? surplusAmount: surplusAmount.add(backAmount);
        query.setSurplusAmount(surplusAmount);

        return AjaxResult.getOK(query);
    }

    @Override
    public AjaxResult selectById(Integer id){
        PurchaseAccountingVO purchaseAccountingVO = purchaseAccountingMapper.selectByAccountId(id);
        List<PurchasesAccountPlanVO> accountPlanVOS = purchasesAccountPlanMapper.selectAccountPlanVO(id);
        List<PurchasesAccountPlanVO> logistics = purchasesAccountPlanMapper.selectLogistics(id);
        accountPlanVOS.addAll(logistics);
        purchaseAccountingVO.setPurchasesAccountPlanVOS(accountPlanVOS);
        return AjaxResult.getOK(purchaseAccountingVO);
    }

    @Override
    public AjaxResult deleteUrl(String url){
        String[] urlList = url.split(";");
        for (String fileName : urlList) {
            UploadTokenFactory.deleteKey(fileName);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult updatePurchaseAccount(PurchaseAccounting purchaseAccounting) {

        purchaseAccountingMapper.update(purchaseAccounting);
        return AjaxResult.getOK();
    }
}
