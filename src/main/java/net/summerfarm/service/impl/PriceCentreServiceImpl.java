package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.manage.PriceCentreMapper;
import net.summerfarm.model.DTO.PriceCentreDTO;
import net.summerfarm.model.vo.PriceCentreResult;
import net.summerfarm.service.PriceCentreService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@Service
public class PriceCentreServiceImpl implements PriceCentreService {
    @Resource
    PriceCentreMapper priceCentreMapper;
    @Override
    public AjaxResult select(int pageIndex, int pageSize, PriceCentreDTO selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PriceCentreResult> priceCentreResults = priceCentreMapper.selectByInput(selectKeys);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(priceCentreResults));
    }
}
