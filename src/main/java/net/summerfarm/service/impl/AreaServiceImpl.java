package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.*;
import net.summerfarm.enums.tms.FenceEnums;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.tms.ChangeFenceMsg;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.*;
import net.summerfarm.task.AsyncTaskService;
import net.summerfarm.task.quartz.JobManage;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.quartz.SchedulerException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/8/23
 */
@Service
@Transactional
@Slf4j
public class AreaServiceImpl extends BaseService implements AreaService {
    @Resource
    private AreaMapper areaMapper;
    @Lazy
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private MsgTemplateMapper msgTemplateMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private AreaManageMapper areaManageMapper;
    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private JobManage jobManage;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    private WarehouseStorageService storageService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private ScheduleTaskService scheduleTaskService;
    @Resource
    private FenceService fenceService;
    @Resource
    AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    ChangeFenceMapper changeFenceMapper;
    @Resource
    FenceDeliveryMapper fenceDeliveryMapper;
    @Resource
    TmsStopDeliveryMapper tmsStopDeliveryMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource(name = "fastExecutor")
    private Executor fastExecutor;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private MajorPriceMapper majorPriceMapper;

    private static final String SALE_OUT_TASK ="stockTaskServiceImpl.saleOutStockTask";

    private static final String TIMING_ORDER ="merchantLifecycleServiceImpl.timingDelivryPlan";

    /**
     * status字段
     */
    private static final String STATUS = "status";

    /**
     * id字段
     */
    private static final String ID = "id";

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @XmLock(waitTime = 1000 * 60, key = "(areaServiceImpl.saveNewArea)")
    public AjaxResult save(AreaVO area) {
        verifyParam(area);
        if(Objects.isNull(area.getSupportAddOrder())){
            throw new DefaultServiceException("请选择是否支持加单");
        }

        Integer maxAreaNo = areaMapper.selectMaxAreaNo();
        area.setAreaNo(maxAreaNo + 1);
        area.setParentNo(area.getLargeAreaNo());
        int result = areaMapper.insertSelective(area);

        if (result == 1) {
            //同步库存数据
            if (area.getOriginAreaNo() != null) {
                logger.info("从areaNo：{}同步上架、价格信息、毛利率、类目展示至areaNo：{}", area.getOriginAreaNo(), area.getAreaNo());
                areaSkuService.insertAreaSku(area.getOriginAreaNo(), area.getAreaNo());
            }

            return AjaxResult.getOK(area.getAreaNo());
        }

        return AjaxResult.getError();
    }

    @Override
    public AjaxResult selectAll() {
        List<Area> treeNodes = areaMapper.selectAll(new Area());
        List<Area> result = new ArrayList<>();
        for (Area treeNode : treeNodes) {
            if (treeNode.getLargeAreaNo() == null) {
                result.add(treeNode);
            }
            for (Area treeNode2 : treeNodes) {
                if (treeNode2.getLargeAreaNo() != null && treeNode2.getLargeAreaNo().equals(treeNode.getAreaNo())) {
                    if (treeNode.getChildren() == null) {
                        treeNode.setChildren(new ArrayList<>());
                    }
                    treeNode.getChildren().add(treeNode2);
                }
            }
        }
        return AjaxResult.getOK(result);
    }


    @Override
    public AjaxResult selectList() {
        List<AreaVO> treeNodes = areaMapper.selectListVO(new Area());
        List<AreaVO> result = new ArrayList<>();
        for (AreaVO treeNode : treeNodes) {
            if (treeNode.getLargeAreaNo() != null) {
                continue;
            }
            result.add(treeNode);
            for (AreaVO treeNode2 : treeNodes) {
                if (treeNode2.getLargeAreaNo() != null && treeNode2.getLargeAreaNo().equals(treeNode.getAreaNo())) {
                    if (treeNode.getChildren() == null) {
                        treeNode.setChildren(new ArrayList<>());
                    }

                    treeNode.getChildren().add(treeNode2);
                }
            }
        }
        result = CollectionUtils.isEmpty(result)?treeNodes:result;
        return AjaxResult.getOK(result);
    }


    @Override
    public Map<Integer, String> selectAllMap() {
        Map<Integer, String> map = new HashMap<>();
        List<Area> treeNodes = areaMapper.selectList(new Area());
        for (Area area : treeNodes) {
            map.put(area.getAreaNo(), area.getAreaName());
        }
        return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @XmLock(waitTime = 1000 * 60, key = "(areaServiceImpl.update):{area.areaNo}")
    public AjaxResult update(AreaVO area) {
        verifyParam(area);
        //限制通过这个入口切换仓库
        /*area.set(null);*/

        //校验是否切换仓库
        Area record = areaMapper.selectByAreaNo(area.getAreaNo());
        if (record.getChangeFlag()) {
            throw new DefaultServiceException("该城市已预约切换仓库，暂不可修改");
        }

        //不开放 -> 开放
        if (!Objects.equals(record.getStatus(), area.getStatus())) {
            //发送通知
            if (Objects.equals(area.getWeChatNotify(), 1)) {

                if (StringUtils.isEmpty(area.getNotifyContent()) || StringUtils.isEmpty(area.getNotifyTitle())) {
                    return AjaxResult.getErrorWithMsg("通知标题或通知内容不能为空");
                }
                asyncTaskService.updateAreaStatusMsg(record.getAreaNo(), area.getNotifyTitle(), area.getNotifyContent(), area.getNotifyRemarks());
            }

        }

        if (!record.getCompanyAccountId().equals(area.getCompanyAccountId())) { //修改支付账号,发送消息通知
            if (!isSA()) {
                throw new DefaultServiceException("只有超级管理员可以修改归属支付账户");
            }

            //中银账号处理
            CompanyAccount oldAccount;
            if (Objects.equals(record.getPayChannel(), 1)){
                oldAccount = new CompanyAccount();
                oldAccount.setId(-1);
                oldAccount.setCompanyName("中银支付-系统选择账号");
            } else {
                oldAccount = companyAccountMapper.selectByPrimaryKey(record.getCompanyAccountId());
            }

            CompanyAccount newAccount;
            if(Objects.equals(area.getPayChannel(), 1)){
                newAccount = new CompanyAccount();
                newAccount.setId(-1);
                newAccount.setCompanyName("中银支付-系统选择账号");
            } else {
                newAccount = companyAccountMapper.selectByPrimaryKey(area.getCompanyAccountId());
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("two", area.getAreaName());
            jsonObject.put("three", oldAccount.getCompanyName());
            jsonObject.put("four", newAccount.getCompanyName());
            MsgTemplate template = msgTemplateMapper.selectOne(new MsgTemplate(MsgTemplateEnum.COMPANY_ACCOUNT_CHANGE));
            Admin admin = adminMapper.selectByPrimaryKey(template.getAdminId());
            jsonObject.put("one", admin.getRealname());

            String content = MessageFormat.format(template.getTemplate(), admin.getRealname(), area.getAreaName(), oldAccount.getCompanyName(), newAccount.getCompanyName());
            String keyword = area.getAreaNo() + ":" + oldAccount.getId() + ":" + newAccount.getId();
            msgAdminService.handleMsg(11, admin, null, keyword, content, jsonObject, SMSType.NOTIFY);

        }
        Integer originAreaNo = record.getOriginAreaNo();
        //同步城市信息校验
        if(area.getOriginAreaNo() != null && Objects.isNull(originAreaNo)){

            //pop区域不进行校验
            if (Objects.equals(record.getBusinessLine(), MerchantEnum.BusinessLineEnum.XM.getCode())) {
                Integer storeNo = fenceService.selectStoreNoByAreaNo(record.getAreaNo());
                Integer originStoreNo = fenceService.selectStoreNoByAreaNo(area.getOriginAreaNo());

                //不在同一个配送仓下，继续校验所选库存仓是否完全一致
                if(!Objects.equals(originStoreNo, storeNo)){
                    List<WarehouseStorageCenter> areaWarehouseList = logisticsService.selectStorageByMapping(storeNo);
                    List<WarehouseStorageCenter> originWarehouseList = logisticsService.selectStorageByMapping(originStoreNo);
                    //校验是否完全一致
                    if (!(areaWarehouseList.containsAll(originWarehouseList) && originWarehouseList.equals(areaWarehouseList))){
                        throw new DefaultServiceException("只可同步库存仓相同的城市数据");
                    }
                }
            }

            //同步库存数据
            logger.info("从areaNo：{}同步上架、价格信息、毛利率、类目展示至areaNo：{}", area.getOriginAreaNo(), area.getAreaNo());
            areaSkuService.insertAreaSku(area.getOriginAreaNo(), area.getAreaNo());

        }

        //开放 -> 不开放
        if (record.getStatus() && !area.getStatus()) {
            // 添加异步任务
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    fastExecutor.execute(() -> handleMajorPriceByAreaNo(area.getAreaNo()));
                }
            });
        }
        areaMapper.updateByAreaNo(area);
        return AjaxResult.getOK();
    }

    private void handleMajorPriceByAreaNo(Integer areaNo) {
        log.info("开始失效areaNo:{}的大客户报价单", areaNo);
        int defaultSize = 500;
        // 查询到所有正在生效的报价单
        List<Long> idList = majorPriceMapper.selectValidMajorPriceByAreaNo(areaNo);
        // 批量失效
        // 分批处理,目前areaNo最大量级在2000+
        if (CollUtil.isNotEmpty(idList)) {
            List<List<Long>> split = ListUtil.split(idList, defaultSize);
            split.forEach(majorPriceService::batchExpireMajorPrice);
        }
        log.info("areaNo:{}的大客户报价单处理完毕", areaNo);
    }



    @Override
    public AjaxResult areaManageList(){
        return AjaxResult.getOK(areaManageMapper.selectAll());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult areaManageSave(AreaManage areaManage){
        AreaManage record = areaManageMapper.selectOne(areaManage);
        if (record != null){
            throw new DefaultServiceException("合伙人已存在,请勿重复添加");
        }
        areaManage.setAddtime(LocalDateTime.now());
        int result = areaManageMapper.insert(areaManage);
        if (result == 1){
            return AjaxResult.getOK(areaManage);
        }
        return AjaxResult.getErrorWithMsg("新增失败");
    }

    @Override
    public AjaxResult hasPermission() {
        if (isSA()) {
            return AjaxResult.getOK(true);
        }

        List<WarehouseStorageCenterVO> centerList = storageService.selectAllForSummerFarm(0, null);
        boolean flag = centerList.stream().allMatch(el -> getDataPermission().contains(el.getWarehouseNo()));

        return AjaxResult.getOK(flag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(waitTime = 1000 * 60, key = "(areaServiceImpl.ChangeFence):{changeFence.areaNo}")
    public AjaxResult readyChangeStore(ChangeFence changeFence) {

        //areaMapper.updateByLargeAreaNo(areaNo,changeStoreNo);
        Integer changeStoreNo = changeFence.getChangeToStoreNo();
        changeFence.setStatus(0);
        Integer areaNo = changeFence.getAreaNo();
        Integer fenceId = changeFence.getFenceId();
        FenceVO fenceVO = fenceService.selectFenceById(fenceId);
        //获取当前城市是否有已经存在切换的 库存使用仓是否一致
        List<ChangeFence> changeFences = changeFenceMapper.selectByAreaNo(areaNo);
        Area oldArea = areaMapper.selectByAreaNo(areaNo);
        List<WarehouseStorageCenter> storageCenterList = logisticsService.selectStorageByMapping(changeStoreNo);
        if(!CollectionUtils.isEmpty(changeFences) && Objects.equals(changeFence.getType(),0)){
            //对比库存使用仓，不一致则直接报错
            changeFences.forEach(change -> {
                Integer storeNo = fenceVO.getStoreNo();
                List<WarehouseStorageCenter> storageCenters = logisticsService.selectStorageByMapping(storeNo);
                if(!Objects.equals(storageCenterList.size(),storageCenters.size()) || !storageCenterList.containsAll(storageCenters)){
                    throw new DefaultServiceException("与其他切换的库存使用仓不一致");
                }
            });
        }
        //当前城市是否存在不切仓的围栏 ，数据对比
        List<Fence> fences = new ArrayList<>();

        if(!CollectionUtils.isEmpty(fences) && Objects.equals(changeFence.getType(),0)){
            Integer storeNo = fences.get(0).getStoreNo();
            List<ChangeFence> newChangeFence = changeFenceMapper.selectByChangeToFenceId(changeFence.getFenceId());
            if(Objects.nonNull(newChangeFence)){
                throw new DefaultServiceException("已存在切换任务不可再次发起");
            }
            List<WarehouseStorageCenter> storageCenters = logisticsService.selectStorageByMapping(storeNo);
            if(!Objects.equals(storageCenterList.size(),storageCenters.size()) || !storageCenterList.retainAll(storageCenters)){
                throw new DefaultServiceException("与当前库存使用仓不一致");
            }

        }
        if(Objects.equals(changeFence.getType(),1)){

            ChangeFence queryChangeFence = changeFenceMapper.selectByFenceId(changeFence.getChangeToFenceId());
            if(Objects.nonNull(queryChangeFence)){
                throw new DefaultServiceException("当前围栏已存在切换任务");
            }
            //校验 省市区
            List<AdCodeMsg> msgList = adCodeMsgMapper.selectByFenceId(fenceId, 0);
            String oldCity = msgList.get(0).getCity();
            String newCity = oldCity;
            List<AdCodeMsg> newMsgList = adCodeMsgMapper.selectByFenceId(changeFence.getChangeToFenceId(), 0);
            //获取是否已存在切换的区域信息
            if(!CollectionUtils.isEmpty(newMsgList)){
                newCity = newMsgList.get(0).getCity();
            } else {
                //获取是否存在已经切围栏的任务
                List<ChangeFence> newChangeFence = changeFenceMapper.selectByChangeToFenceId(changeFence.getChangeToFenceId());
                if(!CollectionUtils.isEmpty(newChangeFence)){
                    String changeAcmId = newChangeFence.get(0).getChangeAcmId();
                    List<String> strings = Arrays.asList(changeAcmId.split(","));
                    msgList = adCodeMsgMapper.selectByIds(strings);
                    newCity = msgList.get(0).getCity();
                }
            }
            if(!Objects.equals(newCity,oldCity)){
                throw new DefaultServiceException("不能切换成市不同的围栏中");
            }
            FenceVO changeToStore = fenceService.selectFenceById(changeFence.getChangeToFenceId());
            changeStoreNo = changeToStore.getStoreNo();
        }
        WarehouseLogisticsCenter newLogistics = logisticsService.selectByStoreNo(changeStoreNo);
        if(newLogistics == null){
            return AjaxResult.getError("切换仓库不存在");
        }
        LocalDateTime time = calcChangeStartTime(areaNo,fenceId);
        if(!oldArea.getChangeFlag()){
            //切换城市状态标记为预约中
            Area updateArea = new Area();
            updateArea.setId(oldArea.getId());
            updateArea.setChangeFlag(true);
            updateArea.setChangeStoreNo(changeStoreNo);
            updateArea.setChangeStatus(AreaEnum.ChangeStatus.READY.ordinal());
            areaMapper.updateById(updateArea);
            //添加任务
//            selfService.taskInit(areaNo,fenceId,time);
        }
        changeFence.setExeTime(time);
        changeFence.setOperator(getAdminId());
        changeFenceMapper.insertChangeFence(changeFence);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(waitTime = 1000 * 60, key = "(areaServiceImpl.cancelChangeStore):{fenceId}")
    public AjaxResult cancelChangeStore(Integer fenceId) {

        FenceVO fenceVO = fenceService.selectFenceById(fenceId);
        Integer areaNo = fenceVO.getAreaNo();
        List<ChangeFence> changeFences = changeFenceMapper.selectByAreaNo(areaNo);
        Area area = areaMapper.selectByAreaNo(areaNo);

        //若无其他围栏相同城市切仓则关闭
        if(CollectionUtils.isEmpty(changeFences) || changeFences.size() <= 1){
            if(!area.getChangeFlag()){
                return AjaxResult.getError("该城市未预约切换，不可取消");
            }
            if(area.getChangeStatus() >= AreaEnum.ChangeStatus.KA_CLOSE.ordinal()){
                return AjaxResult.getError("该城市已经开始切换，不可取消");
            }

            //取消城市切仓预约
            Area updateArea = new Area();
            updateArea.setId(area.getId());
            updateArea.setChangeFlag(false);
            updateArea.setChangeStatus(AreaEnum.ChangeStatus.DEFAULT.ordinal());
            updateArea.setChangeStoreNo(-1);
            areaMapper.updateById(updateArea);

            //取消任务
            ScheduleJob job1 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.KA_CLOSE.ordinal(), JobGroupType.CHANGE_STORE);
            ScheduleJob job2 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.ALL_CLOSE.ordinal(), JobGroupType.CHANGE_STORE);
            ScheduleJob job3 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.DEFAULT.ordinal(), JobGroupType.CHANGE_STORE);
            try {
                jobManage.removeJob(job1);
                jobManage.removeJob(job2);
                jobManage.removeJob(job3);
                logger.info("移除城市切仓任务，areaNo:{}", areaNo);
            } catch (SchedulerException e) {
                logger.error("取消{}切换城市预约任务失败", areaNo);
                throw new DefaultServiceException("取消预约失败");
            }

        }
        ChangeFence changeFence = changeFenceMapper.selectByFenceId(fenceId);
        ChangeFence updateFence = new ChangeFence();
        updateFence.setId(changeFence.getId());
        updateFence.setOperator(getAdminId());
        updateFence.setStatus(ChangeFence.NO_TAKE_EFFECT);
        changeFenceMapper.updateSelective(updateFence);
        return AjaxResult.getOK();
    }
    /**
     * 任务初始化
     * @param areaNo 区域
     * @param fenceId 围栏id
     */
    @Override
    @Deprecated
    public void taskInit(Integer areaNo,Integer fenceId,LocalDateTime startChangeTime) {

        logger.info("初始化切仓停服任务,areaNo：{}",areaNo);
        Area area = areaMapper.selectByAreaNo(areaNo);

        FenceVO fenceVO = fenceService.selectFenceById(fenceId);
        //未获取到配送仓
        if(Objects.isNull(fenceVO) || Objects.isNull(fenceVO.getStoreNo())){
            return;
        }
        Integer storeNo = fenceVO.getStoreNo();

        //添加大客户停服任务
        if(area.getChangeStatus() == AreaEnum.ChangeStatus.READY.ordinal()){
            ScheduleJob job1 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.KA_CLOSE.ordinal(), JobGroupType.CHANGE_STORE);
            job1.setCronExpression(startChangeTime.minusMinutes(1).format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
            jobManage.addJob(job1);
            logger.info("创建城市切仓大客户停服任务，areaNo：{}，time：{}", areaNo, startChangeTime.minusMinutes(1).format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));
        }
        //添加城市停服任务
        if(area.getChangeStatus() == AreaEnum.ChangeStatus.READY.ordinal() || area.getChangeStatus() == AreaEnum.ChangeStatus.KA_CLOSE.ordinal()){
            LocalDateTime closeOrderTime = preCutOffOrderUtil.getSaleTimeByStoreNo(storeNo,startChangeTime);
            ScheduleJob job2 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.ALL_CLOSE.ordinal(), JobGroupType.CHANGE_STORE);
            job2.setCronExpression(closeOrderTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
            jobManage.addJob(job2);
            logger.info("创建城市切仓城市停服任务，areaNo：{}，time：{}", areaNo, closeOrderTime.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));
        }

        //添加城市恢复服务
        //获取出库时间
        LocalDateTime saleOutTime = preCutOffOrderUtil.getSaleTimeByStoreNo(storeNo,startChangeTime);

        if(area.getChangeStatus() != AreaEnum.ChangeStatus.DEFAULT.ordinal()){

            LocalDateTime finishTime = saleOutTime.plusMinutes(15);
            ScheduleJob job3 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.DEFAULT.ordinal(), JobGroupType.CHANGE_STORE);
            job3.setCronExpression(finishTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
            jobManage.addJob(job3);
            logger.info("创建城市切仓城市恢复任务，areaNo：{}，time：{}", areaNo, finishTime.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));
        }
    }

    @Override
    @Deprecated
    public void init() {
        Area query = new Area();
        query.setChangeFlag(true);
        List<Area> areaList = areaMapper.selectAll(query);

        for (Area area : areaList) {
            ChangeFence changeFence = changeFenceMapper.selectEffectByAreaNo(area.getAreaNo());
            if(Objects.nonNull(changeFence)){
                taskInit(area.getAreaNo(),changeFence.getFenceId(),changeFence.getExeTime());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @Deprecated
    @XmLock(waitTime = 1000 * 60, key = "(areaServiceImpl.runAreaStopServiceTask):{areaNo}")
    public void runTask(Integer areaNo, Integer status) {
        logger.error("请你用WMS的服务吧", new Exception(String.format("请迁移至WMS的服务:areaServiceImpl.runTask(%d)", areaNo)));
        logger.info("开始执行城市停服任务，areaNo:{}，", areaNo);
        Area old = areaMapper.selectByAreaNo(areaNo);
        Set<Integer> storeNoList = fenceService.selectStoreByAreaNo(areaNo);

        //配送仓截单 默认杭州
        Integer storeNo = 1;
        LocalTime localTime = LocalTime.MIN;
        for (Integer store : storeNoList) {
            LocalTime closeTime = logisticsService.selectCloseTime(store);
            if(closeTime.isAfter(localTime)){
                localTime = closeTime;
                storeNo = store;
            }
        }
        if(status == AreaEnum.ChangeStatus.KA_CLOSE.ordinal()){
            logger.info("执行城市切仓大客户停服任务，areaNo:{}，", areaNo);
            if(old.getChangeStatus() == AreaEnum.ChangeStatus.READY.ordinal()){
                Area update = new Area();
                update.setId(old.getId());
                update.setChangeStatus(AreaEnum.ChangeStatus.KA_CLOSE.ordinal());
                areaMapper.updateById(update);
            } else {
                logger.error("{}切仓状态异常{}，任务执行中止", areaNo, old.getChangeStatus());
            }
        } else if(status == AreaEnum.ChangeStatus.ALL_CLOSE.ordinal()){
            logger.info("执行城市切仓城市停服任务，areaNo:{}，", areaNo);
            if(old.getChangeStatus() == AreaEnum.ChangeStatus.READY.ordinal() || old.getChangeStatus() == AreaEnum.ChangeStatus.KA_CLOSE.ordinal()){
                Area update = new Area();
                update.setId(old.getId());
                update.setChangeStatus(AreaEnum.ChangeStatus.ALL_CLOSE.ordinal());
                areaMapper.updateById(update);
            } else {
                logger.error("{}切仓状态异常{}，任务执行中止", areaNo, old.getChangeStatus());
            }
        } else if(status == AreaEnum.ChangeStatus.DEFAULT.ordinal()){
            logger.info("执行城市切仓城市恢复任务，areaNo:{}", areaNo);
            if (old.getChangeStatus() == AreaEnum.ChangeStatus.ALL_CLOSE.ordinal()) {
                WarehouseLogisticsCenter oldLogisticsCenter = logisticsService.selectByStoreNo(storeNo);
                WarehouseLogisticsCenter changeLogisticsCenter = logisticsService.selectByStoreNo(old.getChangeStoreNo());
                if (oldLogisticsCenter.getSotFinishTime() == null || !oldLogisticsCenter.getSotFinishTime().toLocalDate().isEqual(LocalDate.now()) ||
                        changeLogisticsCenter.getSotFinishTime() == null || !changeLogisticsCenter.getSotFinishTime().toLocalDate().isEqual(LocalDate.now())) {
                    ScheduleJob job3 = new ScheduleJob(areaNo + "_" + AreaEnum.ChangeStatus.DEFAULT.ordinal(), JobGroupType.CHANGE_STORE);
                    LocalDateTime runTime = LocalDateTime.now().plusMinutes(5);
                    job3.setCronExpression(runTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
                    jobManage.addJob(job3);
                    logger.info("出库任务未完成，延迟执行城市切仓城市恢复任务，areaNo：{}，time：{}", areaNo, runTime.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));
                    return;
                }
                try {
                    //处理订单
                    fenceService.changeFenceByAreaNo(areaNo);
                } catch (Exception e) {
                    logger.error("切仓失败 err={}", e.getMessage(), e);
                }
                //处理省心送冻结
                // deliveryPlanService.movePlanToChangeStore(areaNo, storeNo, old.getChangeStoreNo(), LocalDate.now().plusDays(1));

                //修改城市归属、重置切仓状态 修改围栏归属信息
                Area update = new Area();
                update.setId(old.getId());
                update.setChangeFlag(false);
                update.setChangeStoreNo(-1);
                //update.setParentNo(old.getChangeStoreNo());
                update.setChangeStatus(AreaEnum.ChangeStatus.DEFAULT.ordinal());
                areaMapper.updateById(update);
            } else {
                logger.error("{}切仓状态异常{}，任务执行中止", areaNo, old.getChangeStatus());
            }
        }
    }

    @Override
    @Deprecated
    public void runTaskPostHandle() {
        //查询前一天切仓成功的切仓任务
        LocalDate yesterdayDate = LocalDate.now().minusDays(1);
        List<ChangeFence> changeFences = changeFenceMapper.selectSuccessTasks(yesterdayDate);
        if (CollectionUtils.isEmpty(changeFences)){
            return;
        }
        String yesterdayDateStr = yesterdayDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Map<Integer, List<ChangeFence>> areaTasksMap = changeFences.stream().collect(Collectors.groupingBy(ChangeFence::getAreaNo));

        List<ChangeFenceMsg> allChangeFenceMsgList = new ArrayList<>();
        for (Map.Entry<Integer, List<ChangeFence>> entry : areaTasksMap.entrySet()) {
            StringJoiner key = new StringJoiner("-");
            Integer areaNo = entry.getKey();
            List<ChangeFence> areaChangeFenceTasks = entry.getValue();
            key.add("change_fence_after_handle").add(yesterdayDateStr).add(areaNo.toString());
            RLock redissonLock = redissonClient.getLock(key.toString());
            try {
                if (!redissonLock.tryLock(0L, 1L, TimeUnit.DAYS)) {
                    logger.info("切仓成功定时补偿任务：【{}】正在执行或执行完成", key);
                    continue;
                }
                List<ChangeFenceMsg> changeFenceMsgList = fenceService.handleSampleAndAfterSaleByAreaNo(areaChangeFenceTasks);
                if (!CollectionUtils.isEmpty(changeFenceMsgList)){
                    allChangeFenceMsgList.addAll(changeFenceMsgList);
                }
            } catch (Exception e) {
                logger.error("切仓成功定时补偿任务：【{}】执行失败,err={}", key, e.getMessage(), e);
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }
        if (!CollectionUtils.isEmpty(allChangeFenceMsgList)){
            HashMap<String, String> msgMap = new HashMap<>();
            msgMap.put("title","切仓样品售后处理通知");
            msgMap.put("text","切仓样品售后履约单据：" + JSON.toJSONString(allChangeFenceMsgList));
            //配置中发送群机器人的url
            DingTalkRobotUtil.sendMsgAndAtAll("markdown", "https://open.feishu.cn/open-apis/bot/v2/hook/08c9f9b4-ba51-4312-a185-8082c782feb3", () -> msgMap);
        }
    }

    @Override
    public boolean inChange(Integer areaNo, Integer adminId, Long mId) {
        Area area = areaMapper.selectByAreaNo(areaNo);
        if(area.getChangeFlag()){
            if(adminId != null){
                Admin admin = adminMapper.selectByPrimaryKey(adminId);
                return Objects.equals(1, admin.getCloseOrderType()) && area.getChangeStatus() >= AreaEnum.ChangeStatus.KA_CLOSE.ordinal();
            }

            Merchant merchant = merchantMapper.selectByMId(mId);
            if(Objects.equals("大客户", merchant.getSize()) && merchant.getAdminId() != null){
                Admin admin = adminMapper.selectByPrimaryKey(merchant.getAdminId());
                return Objects.equals(1, admin.getCloseOrderType()) && area.getChangeStatus() >= AreaEnum.ChangeStatus.KA_CLOSE.ordinal();
            } else {
                return area.getChangeStatus() >= AreaEnum.ChangeStatus.ALL_CLOSE.ordinal();
            }
        }
        return false;
    }

    @Override
    public AjaxResult getChangeTime(Integer areaNo) {
        ChangeFence changeFence = changeFenceMapper.selectEffectByAreaNo(areaNo);
        LocalDateTime changeTime = calcChangeStartTime(areaNo,changeFence.getFenceId());
        return AjaxResult.getOK(changeTime.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));
    }

    @Override
    public AjaxResult matchAreaNo(Contact contact) {

        Integer areaNo = fenceService.getAreaNo(contact.getPoiNote(),contact);
        if(Objects.nonNull(areaNo)){
            return AjaxResult.getOK(areaNo);
        }

        return AjaxResult.getOK();
    }

    @Override
    public Area matchArea(String province, String city, String area) {
        province = province.replace("省", "").replace("市", "");
        String address = province + city + area;

        List<Area> areas = areaMapper.selectSecond();
        for (Area el : areas) {
            if (!el.getStatus()) {
                continue;
            }

            if (el.getMapSection() != null) {
                String[] sectionList = el.getMapSection().split(",");
                for (String s : sectionList) {
                    String ssq = s.replaceAll("/", "");
                    if (address.contains(ssq)) {
                        return el;
                    }
                }
            }
        }

        //区域匹配不到、匹配城市
        for (Area el : areas) {
            if (el.getMapSection() != null) {
                String[] sectionList = el.getMapSection().split(",");
                for (String s : sectionList) {
                    String ss = s.replaceAll("/", "").replace("其他区域", "");
                    boolean condition = el.getMapSection().contains("其他区域") || StringUtils.countOccurrencesOf(el.getMapSection(), "/") == 1;
                    if (address.contains(ss) && condition) {
                        return el;
                    }
                }
            }
        }

        return null;
    }

    @Override
    public AjaxResult listCompanyAccount() {
        List<CompanyAccount> accountList = companyAccountMapper.selectFixAccount();

        //中银需要配置微信账号
        List<CompanyAccount> bocList = accountList
                .stream()
                .filter(el -> Objects.equals(el.getChannel(), 0))
                .collect(Collectors.toList());
        bocList.forEach(el -> {
            CompanyAccount ca = new CompanyAccount();
            ca.setChannel(1);
            ca.setCompanyName(el.getCompanyName());
            ca.setWxAccountInfo(el.getWxAccountInfo());
            ca.setId(el.getId());
            ca.setAdminId(el.getAdminId());
            ca.setAddtime(el.getAddtime());
            accountList.add(ca);
        });

        return AjaxResult.getOK(accountList);
    }

    @Override
    @Transactional
    public void addScheduleTask(String closeTime) {
        logger.info("开始初始化定时任务");
        LocalTime timingTime = LocalTime.parse(closeTime,BaseDateUtils.DEFAULT_LOCAL_TIME).minusMinutes(Global.PRE_TIME);
        handleScheduleTask(closeTime,SALE_OUT_TASK);
        handleScheduleTask(timingTime.format(BaseDateUtils.DEFAULT_LOCAL_TIME),TIMING_ORDER);
        return;
    }

    @Override
    public void updateCloseTime() {
        logger.info("开始更新定时任务");

        //获取要更改的信息
        List<WarehouseLogisticsCenterVO> centerVOS = logisticsService.selectLogisticsAll(WarehouseStorageCenterEnum.Status.VALID.ordinal());

        for (WarehouseLogisticsCenterVO center : centerVOS) {
            //未更新截单时间且城配仓下没有更新是否支持加单
            if(StringUtils.isEmpty(center.getUpdateCloseTime())){
                continue;
            }
            //处理任务
            updateTask(center);
        }
        logger.info("更新定时任务完成");
    }

    /**
     * 更新运营服务区域 的加单信息
     */
    @Override
    public void updateAddOrder(){
        log.info("开始更新运营服务区域的加单信息");
        List<Area> areasNeedToUpdateAddOrder = areaMapper.listUpdateAddOrderAreas();
        if (CollectionUtils.isEmpty(areasNeedToUpdateAddOrder)){
            log.info("没有需要更新的运营服务区域的加单信息");
            return;
        }
        log.info("更新运营服务区域的加单信息,需要更新的运营服务区域 >>> {}", JSON.toJSONString(areasNeedToUpdateAddOrder));
        areaMapper.updateAddOrder(areasNeedToUpdateAddOrder.stream().map(Area::getAreaNo).collect(Collectors.toSet()));
        log.info("更新运营服务区域的加单信息完成");
    }

    @Override
    public void cancelAddOrder(Integer areaNo) {
        areaMapper.cancelAddOrder(areaNo);
        return;
    }

    private void verifyParam(AreaVO area) {
        if (org.apache.commons.lang3.StringUtils.isBlank(area.getAreaName()) || !area.getAreaName().matches("^([A-Za-z\\u4e00-\\u9fa5]){1,10}$")) {
            throw new DefaultServiceException(ResultConstant.AREA_NAME_ILLEGAL);
        }

        if (area.getLargeAreaNo() == null){
            throw new DefaultServiceException(ResultConstant.PARAM_FAULT);
        }

        String deliveryFrequent = area.getDeliveryFrequent();
        if (!StringUtils.isBlank(deliveryFrequent)) {
            String[] dfs = deliveryFrequent.split(Global.SEPARATING_SYMBOL);
            for (String df : dfs) {
                Integer dfNo = Integer.valueOf(df);
                if (dfNo < 0 || dfNo > 7) {
                    throw new DefaultServiceException(ResultConstant.AREA_DELIVERY_FREQUENT_ILLEGAL);
                }
            }
        }
        DeliveryRuleVO deliveryRuleVO = JSONObject.toJavaObject(JSON.parseObject(area.getDeliveryRule()),DeliveryRuleVO.class);
        if (deliveryRuleVO != null){
            if (deliveryRuleVO.getFruitPrice() != null && deliveryRuleVO.getFruitPrice().compareTo(BigDecimal.valueOf(0)) < 1){
                throw new DefaultServiceException(ResultConstant.DELIVERY_RULE_ERROR);
            }
            if (deliveryRuleVO.getDairyPrice() != null && deliveryRuleVO.getDairyPrice().compareTo(BigDecimal.valueOf(0)) < 1){
                throw new DefaultServiceException(ResultConstant.DELIVERY_RULE_ERROR);
            }
            if (deliveryRuleVO.getTotalPrice() != null && deliveryRuleVO.getTotalPrice().compareTo(BigDecimal.valueOf(0)) < 1){
                throw new DefaultServiceException(ResultConstant.DELIVERY_RULE_ERROR);
            }

            // 固定星期免配送费校验
            if (StringUtils.isNotBlank(deliveryRuleVO.getFreeDeliveryWeek())) {
                List<String> weekList = Global.DEFAULT_SPLITTER.splitToList(deliveryRuleVO.getFreeDeliveryWeek());
                for (String week : weekList) {
                    if (!NumberUtil.isInteger(week)) {
                        throw new DefaultServiceException(ResultConstant.DELIVERY_RULE_ERROR);
                    }
                    int weekNum = Integer.parseInt(week);
                    if (weekNum < 1 || weekNum> 7 ) {
                        throw new DefaultServiceException(ResultConstant.DELIVERY_RULE_ERROR);
                    }
                }
            }
        }
        if (area.getMemberRule() == null){
            area.setMemberRule("[]");
        }
        List<MemberVO> memberVOS = JSON.parseArray(area.getMemberRule(),MemberVO.class);
        if (!CollectionUtils.isEmpty(memberVOS) && memberVOS.size()>0){
            for (MemberVO memberVO: memberVOS){
                if (memberVO.getGrade() == null || memberVO.getThreshold() == null || memberVO.getRefundAmount() == null || memberVO.getOutTimes() == null){
                    throw new DefaultServiceException("会员规则格式不正确!");
                }
            }
        }

        //查重名
        int rs = areaMapper.countByNameAndAreaNo(area.getAreaName(), area.getAreaNo());
        if (rs > 0) {
            throw new DefaultServiceException(ResultConstant.AREA_NAME_REPEAT);
        }

        //支付途径校验
        if(area.getPayChannel() == null){
            area.setPayChannel(PayChannelEnum.WEIX.getPayChannel());
        }
        if (!PayChannelEnum.WEIX.getPayChannel().equals(area.getPayChannel())
                && !PayChannelEnum.BOC.getPayChannel().equals(area.getPayChannel())
                && !PayChannelEnum.CMB.getPayChannel().equals(area.getPayChannel())) {
            throw new DefaultServiceException("收款途径错误");
        }

        //收款账号
        if(area.getCompanyAccountId() == null){
            throw new DefaultServiceException("请选择收款账号");
        }
        if(PayChannelEnum.WEIX.getPayChannel().equals(area.getPayChannel())
                || PayChannelEnum.CMB.getPayChannel().equals(area.getPayChannel())){
            CompanyAccountVO accountVO = companyAccountMapper.selectByPrimaryKey(area.getCompanyAccountId());
            if(accountVO == null){
                throw new DefaultServiceException("收款账号错误");
            }
        }
    }


    @Override
    public LocalDateTime calcChangeStartTime(Integer areaNo,Integer fenceId){
        Area area = areaMapper.selectByAreaNo(areaNo);
        if (null == area) {
            throw new BizException("未找到运营区域:" + areaNo);
        }

        // 查询运营区域下的所有城配仓
        Set<Integer> storeNoList = fenceService.selectStoreByAreaNo(areaNo);
        //正常截单
        List<LocalTime> timeList = new ArrayList<>();
        timeList.add(Global.CLOSING_ORDER_TIME);
        //大客户截单
        timeList.add(Global.CBD_BIG_CLOSING_ORDER_TIME);
        //配送仓截单
        for (Integer store : storeNoList) {
            LocalTime localTime = logisticsService.selectCloseTime(store);
            timeList.add(localTime);
        }

        //计算最早一批截单时间
        timeList.sort(LocalTime::compareTo);
        LocalTime startTime = timeList.get(0);
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectByFenceId(fenceId);
        if (null == fenceDelivery) {
            throw new BizException(String.format("未找到围栏ID为:%d 的取配送数据, areaNo:%d", fenceId, areaNo));
        }
        //计算日期
        LocalDate calcDate = startTime.isAfter(LocalTime.now()) ? LocalDate.now() : LocalDate.now().plusDays(1);
        if(Objects.nonNull(fenceDelivery) && fenceDelivery.getNextDeliveryDate() != null && fenceDelivery.getNextDeliveryDate().isAfter(calcDate)){
            calcDate = fenceDelivery.getNextDeliveryDate();
        }
        //开始切换日期（切换中的日期必然是当天，预约切换的状态需要根据配送日期计算）
        LocalDate startDate = LocalDate.now();
        if(area.getChangeStatus() <= AreaEnum.ChangeStatus.READY.ordinal()){
            //周计算
            if(Objects.equals(fenceDelivery.getFrequentMethod(), FenceEnums.frequentMethod.FREQUENT_WEEK.getValue())){
                String[] split = fenceDelivery.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL);
                Integer[] dfArr = Convert.toIntArray(split);
                if (dfArr != null && dfArr.length != 0) {
                    Arrays.sort(dfArr);
                    if (Objects.equals(dfArr[0], 0)) {
                        startDate = calcDate;
                    } else {
                        int week = calcDate.getDayOfWeek().getValue();
                        int firstWeek = dfArr[0];
                        int lastWeek = dfArr[dfArr.length - 1];
                        if (lastWeek < week) {
                            startDate = calcDate.plusDays(7 + firstWeek - week);
                        } else {
                            for (Integer calcWeek : dfArr) {
                                if (calcWeek >= week) {
                                    startDate = calcDate.plusDays(calcWeek - week);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            //间隔计算
            if(Objects.equals(fenceDelivery.getFrequentMethod(), FenceEnums.frequentMethod.FREQUENT_INTERVAL.getValue())){
                //开始计算日期
                LocalDate beginCalculateDate = fenceDelivery.getBeginCalculateDate();
                //配送间隔周期
                Integer deliveryFrequentInterval = fenceDelivery.getDeliveryFrequentInterval();
                //相等
                if(startDate.isEqual(beginCalculateDate)){
                    startDate = beginCalculateDate;
                }else{
                    //开始计算时间在之前，业务也只会在当前以及以前
                    LocalDate nextDate = beginCalculateDate.plusDays(deliveryFrequentInterval);
                    while(!nextDate.isAfter(startDate) && !startDate.isEqual(nextDate)){
                        nextDate = nextDate.plusDays(deliveryFrequentInterval);
                    }
                    startDate = nextDate;
                }
            }
        }

        return LocalDateTime.of(startDate, startTime);
    }

    @Override
    public AjaxResult changeLargeAreaNo(Integer areaNo, Integer largeAreaNo) {

        Area area = new Area();
        area.setAreaNo(areaNo);
        area.setLargeAreaNo(largeAreaNo);
        area.setParentNo(largeAreaNo);
        areaMapper.updateByAreaNo(area);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult getArea(String city, String area) {
        Integer areaNo = fenceService.getAreaNo(city, area);
        Area result = areaMapper.queryByAreaNo(areaNo);
        return AjaxResult.getOK(result);
    }

    /**
     * 查停运时间
     * @param storeNo 城配仓
     * @return
     */
    @Override
    public AjaxResult queryStopDelivery(Integer storeNo) {
        TmsStopDelivery tmsStopDelivery = tmsStopDeliveryMapper.selectByStoreNo(storeNo);
        return AjaxResult.getOK(tmsStopDelivery);
    }

    /**
     * 新增停运时间
     * @param tmsStopDeliveryVo 停运时间
     * @return 成功提示
     */
    @Override
    @Transactional
    public AjaxResult addStopDelivery(TmsStopDeliveryVo tmsStopDeliveryVo) {
        List<Integer> storeNos = tmsStopDeliveryVo.getStoreNos();
        if (CollectionUtils.isEmpty(storeNos)) {
            return AjaxResult.getErrorWithMsg("城配仓不能为空");
        }
        if (tmsStopDeliveryVo.getShutdownStartTime().isBefore(LocalDate.now()) || LocalDate.now().equals(tmsStopDeliveryVo.getShutdownEndTime())) {
            return AjaxResult.getErrorWithMsg("开始时间必须大于当前");
        }
        for (Integer storeNo : storeNos) {
            tmsStopDeliveryMapper.updateStatus(storeNo);
            tmsStopDeliveryVo.setStoreNo(storeNo);
            tmsStopDeliveryMapper.insertSelective(tmsStopDeliveryVo);
        }
        return AjaxResult.getOK();
    }

    /**
     * 根据ID取消停运时间
     * @param storeNo
     * @return 成功提示
     */
    @Override
    public AjaxResult cancelStopDelivery(Integer storeNo) {
        tmsStopDeliveryMapper.updateStatus(storeNo);
        return AjaxResult.getOK();
    }

    /**
     * 取停运生效的城配仓
     * @return
     */
    @Override
    public AjaxResult queryStopDeliveryStore() {
        LocalDate now = LocalDate.now();
        List<TmsStopDelivery> tmsStopDeliveries  = tmsStopDeliveryMapper.selectStopDeliveryStore(now);
        return AjaxResult.getOK(tmsStopDeliveries);
    }

    /**
    * 新增定时任务
    */
    private ScheduleTask createScheduleTask(String closeTime,String entrance){
        List<String> times = Arrays.asList(closeTime.split(":"));
        Collections.swap(times,0,2);
        StringJoiner cronJoiner = new StringJoiner(" ");
        String name = Objects.equals(entrance,SALE_OUT_TASK) ? "销售出库任务" : "省心送冻结任务" ;
        times.forEach(cronJoiner::add);
        cronJoiner.add("*").add("*").add("?");
        String cron = cronJoiner.toString();
        ScheduleTask scheduleTask = new ScheduleTask();
        scheduleTask.setCron(cron);
        scheduleTask.setEntrance(entrance);
        scheduleTask.setArgs("key=" + closeTime);
        scheduleTask.setName(closeTime + name);
        return scheduleTask;
    }

    /**
    * 处理任务信息
    */
    private void handleScheduleTask(String closeTime , String entrance){

        ScheduleTask scheduleTask = new ScheduleTask();
        scheduleTask.setEntrance(entrance);
        scheduleTask.setArgs("key=" + closeTime);
        // 只根据接口和执行参数 查询
        ScheduleTask result = scheduleTaskService.selectScheduleTask(scheduleTask);
        //是否已经存在任务 存在 不需要调整
        if(Objects.isNull(result)){
            //不存在 新增
            ScheduleTask insertTask = createScheduleTask(closeTime,entrance);
            scheduleTaskService.add(insertTask);
            //任务失效 重新生效任务
        } else if(!Objects.isNull(result) && result.getDeleteFlag()) {
            scheduleTaskService.resetScheduleTask(result.getId());
        }
        return;
    }

    /**
    * 处理任务以及更新城市，城配仓信息
    */
    @Transactional
    public void updateTask(WarehouseLogisticsCenterVO center){
        String updateCloseTime = center.getUpdateCloseTime();
        Integer storeNo = center.getStoreNo();
        String time = StringUtils.isEmpty(updateCloseTime) ? center.getCloseTime() : updateCloseTime;
        LocalTime localTime = LocalTime.parse(time,DateTimeFormatter.ofPattern(BaseDateUtils.DEFULT_LOCAL_TIME));
        int count = areaMapper.sumUpdateSupportAddOrder(storeNo);
        String closeTime = preCutOffOrderUtil.getTime(localTime, count);
        String saleTime = preCutOffOrderUtil.getStringSaleTimeByStoreNo(storeNo);
        //更改前和更改后 出库时间不一致
        try {
            if(!Objects.equals(saleTime,closeTime) && !this.listCloseTime().contains(closeTime)){
                // 发送飞书消息提醒
                WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(storeNo);
                Config config = configMapper.selectOne("closeTimeUpdateNoticeRobot");
                if (Objects.nonNull(config)) {
                    StringBuilder append = new StringBuilder();
                    append.append(Objects.nonNull(logisticsCenter) ? logisticsCenter.getStoreName() : "");
                    append.append("-城配仓新增/修改截单时间为:");
                    append.append(closeTime);
                    append.append(",请及时补偿销售出库&省心送冻结任务");
                    String noticeMsg = append.toString();
                    CommonResult<Boolean> sendResult = FeishuBotUtil.sendTextMsgAndAtAll(config.getValue(), noticeMsg);
                    if (ResultStatusEnum.BAD_REQUEST.getStatus().equals(sendResult.getStatus())
                            || !sendResult.getData()) {
                        log.error("城配仓更新截单时间，发送飞书消息通知失败, webhook:{}, noticeMsg:{}, result:{}",
                                config.getValue(), noticeMsg, JsonUtil.toJson(sendResult));
                    }
                } else {
                    log.warn("城配仓更新截单时间，发送飞书消息通知失败,缺少飞书群机器人配置信息 store:{}", storeNo);
                }

            }
        } catch (Exception e) {
            log.warn("城配仓更新截单时间，消息通知异常 storeNo:{}",storeNo, e);
        }
        //更新城配仓时间
        if(!StringUtils.isEmpty(updateCloseTime)){
            logisticsService.handleUpdateCloseTime(storeNo);
        }
    }

    @Override
    @Deprecated
    public void sendChangeMessage(Integer id) {
        ChangeFence changeFence = changeFenceMapper.selectByPrimaryKey(id);
        Integer fenceId = changeFence.getFenceId();
        Integer areaNo = changeFence.getAreaNo();
        Integer changeStoreNo = changeFence.getChangeToStoreNo();
        FenceVO fenceVO = fenceService.selectFenceById(fenceId);
        // 切仓消息推送
        StringBuffer changeMsg = new StringBuffer();
        if (Objects.equals(changeFence.getType(), 1)) {
            ChangeFence queryChangeFence = changeFenceMapper.selectByFenceId(changeFence.getChangeToFenceId());
            if (Objects.nonNull(queryChangeFence)) {
                logger.error("当前围栏已存在切换任务");
            }
            //校验 省市区
            List<AdCodeMsg> msgList = adCodeMsgMapper.selectByFenceId(fenceId, 0);
            List<AdCodeMsg> newMsgList = adCodeMsgMapper.selectByFenceId(changeFence.getChangeToFenceId(), 0);
            //获取是否已存在切换的区域信息
            if (CollectionUtils.isEmpty(newMsgList)) {
                //获取是否存在已经切围栏的任务
                List<ChangeFence> newChangeFence = changeFenceMapper.selectByChangeToFenceId(changeFence.getChangeToFenceId());
                if (!CollectionUtils.isEmpty(newChangeFence)) {
                    String changeAcmId = newChangeFence.get(0).getChangeAcmId();
                    List<String> strings = Arrays.asList(changeAcmId.split(","));
                    msgList = adCodeMsgMapper.selectByIds(strings);
                }
            }
            FenceVO changeToStore = fenceService.selectFenceById(changeFence.getChangeToFenceId());
            List<String> collect = msgList.stream().map(AdCodeMsg::getArea).collect(Collectors.toList());
            String areaMsg = String.join(StringUtils.SEPARATING_SYMBOL, collect);
            String msgFormat = "%s围栏的%s将由%s变为%s围栏";
            String msg = String.format(msgFormat, fenceVO.getFenceName(), areaMsg, fenceVO.getFenceName(), changeToStore.getFenceName());
            changeMsg.append(msg);
        } else {
            String msgFormat = "%s围栏的城配仓将由%s变为%s";
            //原来的配送仓编号
            Integer oldStoreNo = fenceVO.getStoreNo();
            WarehouseLogisticsCenter oldLogistics = logisticsService.selectByStoreNo(oldStoreNo);
            String msg = String.format(msgFormat, fenceVO.getFenceName(), oldLogistics.getStoreName(), Global.storeMap.get(changeStoreNo));
            changeMsg.append(msg);
        }
        LocalDateTime time = calcChangeStartTime(areaNo, fenceId);
        Integer operatorAdminId = changeFence.getOperator();
        Admin operator = adminMapper.selectByPrimaryKey(operatorAdminId);
        //预约成功钉钉消息
        Config config = configMapper.selectOne("HomeNoticeRobotUrl");
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, config.getValue(), () -> {
            StringBuilder sb = new StringBuilder();
            sb.append("#### ")
                    .append(" 切仓任务预约成功\n");// <font color=#0089FF>@所有人</font>
            sb.append("> ###### ")
                    .append(operator.getRealname())
                    .append("成功预约")
                    .append(fenceVO.getFenceName())
                    .append("围栏的切仓任务，任务将于")
                    .append(time.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)))
                    .append("开始切换，切换后")
                    .append(changeMsg)
                    .append("，请相关人员提前知晓，安排好切仓前的相关事宜！");
            Map<String, String> md = new HashMap<>(2);
            md.put("title", "切仓任务预约成功");
            md.put("text", sb.toString());
            return md;
        });
    }

    @Override
    @Deprecated
    public void sendCancelMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            ChangeFence changeFence = changeFenceMapper.selectByPrimaryKey(Integer.valueOf(id));
            if (!Objects.equals(changeFence.getStatus(), ChangeFence.NO_TAKE_EFFECT)) {
                return;
            }
            Integer operatorAdminId = changeFence.getOperator();
            Admin operator = adminMapper.selectByPrimaryKey(operatorAdminId);
            FenceVO fenceVO = fenceService.selectFenceById(changeFence.getFenceId());
            Integer areaNo = fenceVO.getAreaNo();
            Area area = areaMapper.selectByAreaNo(areaNo);
            Config config = configMapper.selectOne("HomeNoticeRobotUrl");
            //配送仓编号
            Integer storeNo = fenceVO.getStoreNo();
            WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(storeNo);
            DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, config.getValue(), () -> {
                StringBuilder sb = new StringBuilder();
                sb.append("#### ")
                        .append("切仓任务取消通知\n"); //  <font color=#0089FF>@所有人</font>
                sb.append("> ###### ")
                        .append(operator.getRealname())
                        .append("取消了")
                        .append(area.getAreaName())
                        .append("的切仓任务，该城市的城配仓为")
                        .append(logisticsCenter.getStoreName())
                        .append("，请相关人员知晓并安排好相关事宜！");
                Map<String, String> md = new HashMap<>(2);
                md.put("title", "切仓任务取消通知");
                md.put("text", sb.toString());
                return md;
            });
        }
    }

    @Override
    public AjaxResult selectGrade(Integer areaNo, Integer type) {
        List<Area> areas;
        // crm只可见本人有权限的区域,后台需可见所有(不能有数据权限限制)
        if(Objects.equals(SourceUrl.CRM.getId(),type)){
            areas = areaMapper.selectBdArea(getAdminId());
        }else {
            Area area = new Area();
            area.setAreaNo(areaNo);
            areas = areaMapper.selectAll(area);
        }
        if(CollectionUtils.isEmpty(areas)){
            return AjaxResult.getErrorWithMsg("您没有配置责任区域与数据权限区域,请联系主管配置后再来查看");
        }
        // 按区域等级分组
        Map<String, List<Area>> listMap = areas.stream().collect(Collectors.groupingBy(Area::getGrade));
        List<AreaVO> areaVOList = new ArrayList<>();
        Set<Map.Entry<String, List<Area>>> entries = listMap.entrySet();
        for (Map.Entry<String, List<Area>> entry : entries) {
            AreaVO areaVO = new AreaVO();
            areaVO.setGrade(entry.getKey());
            areaVO.setAreaList(entry.getValue());
            areaVOList.add(areaVO);
        }
        areaVOList.sort(Comparator.comparing(Area::getGrade));
        return AjaxResult.getOK(areaVOList);
    }

    @Override
    public AjaxResult saveGrade(AreaVO area) {
        if(Objects.isNull(area)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 批量修改
        areaMapper.updateByAreaNos(area.getGrade(),area.getAreaNos());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult checkAreaLegitimacy(List<String> cityList){
        if(CollectionUtils.isEmpty(cityList)){
            return AjaxResult.getError();
        }
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectByCityList(cityList);
        if(CollectionUtil.isEmpty(adCodeMsgList) || adCodeMsgList.size() != cityList.size()){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"所属行政城市不在围栏中,请更换城市或设置围栏!");
        }
        List<String> list = Arrays.asList(adCodeMsgList.get(0).getProvince(), adCodeMsgList.get(0).getCity());
        return AjaxResult.getOK(list);
    }

    @Override
    public AjaxResult selectAreaNoByAddress(String province, String city, String area) {
        //匹配地址
        Area areaInfo = this.matchArea(province, city, area);
        if (Objects.nonNull(areaInfo)) {
            return AjaxResult.getOK(areaInfo);
        }
        return AjaxResult.getOK();
    }

    public List<String> listCloseTime() {
        List<String> result = Lists.newArrayList();
        List<WarehouseLogisticsCenterVO> centerVOS = logisticsService.selectLogisticsAll(WarehouseStorageCenterEnum.Status.VALID.ordinal());
        if (CollectionUtils.isEmpty(centerVOS)) {
            return Lists.newArrayList();
        }
        List<Integer> storeNoList = centerVOS.stream().map(WarehouseLogisticsCenterVO::getStoreNo).distinct().collect(Collectors.toList());
        List<SupportAddOrderSumDO> supportAddOrderSumDOList = areaMapper.sumSupportAddOrderByStoreNo(storeNoList);
        Map<Integer, Integer> addOrderCountMap = supportAddOrderSumDOList.stream().collect(Collectors.toMap(SupportAddOrderSumDO::getStoreNo, SupportAddOrderSumDO::getSupportAddOrderCount, (a, b) -> a));
        centerVOS.forEach(centerVO -> {
            String updateCloseTime = centerVO.getUpdateCloseTime();
            String time = StringUtils.isEmpty(updateCloseTime) ? centerVO.getCloseTime() : updateCloseTime;
            LocalTime localTime = LocalTime.parse(time,DateTimeFormatter.ofPattern(BaseDateUtils.DEFULT_LOCAL_TIME));
            // int count = areaMapper.sumUpdateSupportAddOrder(centerVO.getStoreNo());
            int count = CollectionUtils.isEmpty(addOrderCountMap) ||
                    Objects.isNull(addOrderCountMap.get(centerVO.getStoreNo())) ? 0 : addOrderCountMap.get(centerVO.getStoreNo());
            String closeTime = preCutOffOrderUtil.getTime(localTime, count);
            result.add(closeTime);
        });
        return result.stream().distinct().collect(Collectors.toList());
    }
}
