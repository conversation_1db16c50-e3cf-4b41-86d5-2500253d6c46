package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ExpenseTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.CarrierStatementService;
import net.summerfarm.warehouse.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname CarrierStatementServiceImpl
 * @Description 配送结算单相关.
 * @Date 2022/1/4 10:43
 * @Created by hx
 */
@Service
public class CarrierStatementServiceImpl extends BaseService implements CarrierStatementService {

    @Resource
    private CarrierStatementMapper carrierStatementMapper;
    @Resource
    private CarrierStatementExtrasMapper carrierStatementExtrasMapper;
    @Resource
    private DeliveryCarPathMapper deliveryCarPathMapper;
    @Resource
    private DeliveryPathMapper deliveryPathMapper;
    @Resource
    private CarrierQuotationMapper carrierQuotationMapper;
    @Resource
    private ExpenseMapper expenseMapper;
    @Resource
    private CarrierQuotationAreaMapper carrierQuotationAreaMapper;
    @Resource
    private DeliveryCarWarehouseFeeMapper deliveryCarWarehouseFeeMapper;
    @Resource
    private DeliveryCarMapper deliveryCarMapper;
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;

    /**
     * 打印费字段名
     */
    private final String PRINTING_FEE = "printing_fee";
    /**
     * 公里价字段
     */
    private final String KM_PRICE = "km_price";

    /**
     * 补贴费
     */
    private final String SUBSIDY_PRICE = "subsidy_price";
    /**
     * 起步价
     */
    private final String START_PRICE = "start_price";

    /**
     * 超点费
     */
    private final String EXCEED_POINT_PRICE = "exceed_point_price";
    /**
     * 起步点位数
     */
    private final String START_POINT_NUM = "start_point_num";
    /**
     * 分页查看承运商结算单
     * @param carrierStatement
     * @param pageIndex
     * @param pageSize
     * @return 承运商结算单
     */
    @Override
    public AjaxResult selectCarrierStatement(CarrierStatementVo carrierStatement, int pageIndex, int pageSize) {
        PageHelper.startPage(pageIndex,pageSize);
        String provinceCity = carrierStatement.getProvinceCity();
        if (StringUtils.isNotBlank(provinceCity)) {
            String[] split = provinceCity.split(Global.SCRIBE);
            carrierStatement.setProvince(split[0]);
            carrierStatement.setCity(split[1]);
        }

        List<CarrierStatementVo> carrierStatements = carrierStatementMapper.selectCarrierStatement(carrierStatement);
        for (CarrierStatementVo statement : carrierStatements) {
            List<CarrierQuotationArea> carrierQuotationAreas = carrierQuotationAreaMapper.selectByCondition(statement.getCarrierQuotationId());
            statement.setCarrierQuotationAreas(carrierQuotationAreas);
            List<ExpenseVO> expenseVOS = expenseMapper.selectByCondition(statement.getDeliveryTime().atTime(0,0,0),statement.getDeliveryCarId(),statement.getStoreNo(),statement.getPath());
            //采购费
            BigDecimal purMoney = BigDecimal.ZERO;
            //打车费
            BigDecimal carMoney = BigDecimal.ZERO;
            for (ExpenseVO expenseVO : expenseVOS) {
                if(Objects.equals(expenseVO.getType(), ExpenseTypeEnum.PURCHASE.getId())){
                    purMoney = purMoney.add(expenseVO.getAmount());
                }
                if(Objects.equals(expenseVO.getType(),ExpenseTypeEnum.TRAFFIC.getId())){
                    carMoney = carMoney.add(expenseVO.getAmount());
                }
            }
            statement.setHelpOrderFee(purMoney);
            statement.setTaxiFare(carMoney);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(carrierStatements));
    }

    /**
     * 查看杂费信息
     * @param carrierStatement
     * @return 杂费信息
     */
    @Override
    public AjaxResult selectCarrierStatementExtras(CarrierStatement carrierStatement) {
        if (Objects.isNull(carrierStatement.getDeliveryTime()) || Objects.isNull(carrierStatement.getStoreNo()) || StringUtils.isBlank(carrierStatement.getPath())) {
            return AjaxResult.getErrorWithMsg("参数传入错误");
        }
        List<CarrierStatementExtras> carrierStatementExtras = carrierStatementExtrasMapper.selectCarrierStatementExtras(carrierStatement);
        return AjaxResult.getOK(carrierStatementExtras);
    }

    /**
     * 新增杂费
     * @param carrierStatementExtras
     * @return
     */
    @Override
    public AjaxResult addExtras(CarrierStatementExtras carrierStatementExtras) {
        List<CarrierStatementVo> carrierStatement = carrierStatementMapper.selectCarrierStatementByExtra(carrierStatementExtras);
        if (CollectionUtils.isEmpty(carrierStatement)) {
            return AjaxResult.getErrorWithMsg("没有关联的结算单");
        }
        carrierStatementExtras.setCreator(getAdminName());
        carrierStatementExtras.setUpdater(getAdminName());
        carrierStatementExtrasMapper.insertSelective(carrierStatementExtras);
        return AjaxResult.getOK();
    }

    /**
     * 更新
     * @param carrierStatementExtras
     * @return
     */
    @Override
    public AjaxResult updateExtras(CarrierStatementExtras carrierStatementExtras) {
        CarrierStatementExtras carrierStatementExtrasInfo = carrierStatementExtrasMapper.selectByPrimaryKey(carrierStatementExtras.getId());
        if (!Objects.isNull(carrierStatementExtrasInfo.getCreateTime()) && !carrierStatementExtrasInfo.getCreateTime().toLocalDate().isEqual(LocalDate.now())) {
            return AjaxResult.getErrorWithMsg("超过新增当天不能编辑");
        }
        carrierStatementExtras.setUpdater(getAdminName());
        carrierStatementExtrasMapper.updateByPrimaryKeySelective(carrierStatementExtras);
        return AjaxResult.getOK();
    }

    /**
     * 导出
     * @param carrierStatement
     * @return
     */
    @Override
    public AjaxResult exportCarrierStatement(CarrierStatementVo carrierStatement) {
        String provinceCity = carrierStatement.getProvinceCity();
        if (StringUtils.isNotBlank(provinceCity)) {
            String[] split = provinceCity.split(Global.SCRIBE);
            carrierStatement.setProvince(split[0]);
            carrierStatement.setCity(split[1]);
        };
        List<CarrierStatementVo> carrierStatementVos = carrierStatementMapper.selectCarrierStatement(carrierStatement);
        if (CollectionUtils.isEmpty(carrierStatementVos)) {
            return AjaxResult.getErrorWithMsg("结算单暂无数据");
        }
        //导出生成
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("配送日期");
        title.createCell(1).setCellValue("城配仓");
        title.createCell(2).setCellValue("路线");
        title.createCell(3).setCellValue("配送司机");
        title.createCell(4).setCellValue("承运商名称");
        title.createCell(5).setCellValue("服务区域");
        title.createCell(6).setCellValue("服务省市");
        title.createCell(7).setCellValue("服务行政区/县");
        title.createCell(8).setCellValue("点位数");
        title.createCell(9).setCellValue("起步点位数");
        title.createCell(10).setCellValue("起步费");
        title.createCell(11).setCellValue("超点数");
        title.createCell(12).setCellValue("超点费");
        title.createCell(13).setCellValue("补贴费");
        title.createCell(14).setCellValue("预估公里数");
        title.createCell(15).setCellValue("预估公里费");
        title.createCell(16).setCellValue("实际公里数");
        title.createCell(17).setCellValue("实际公里费");
        title.createCell(18).setCellValue("打印费");
        title.createCell(19).setCellValue("前置仓费");
        title.createCell(20).setCellValue("帮采费");
        title.createCell(21).setCellValue("打车费");
        title.createCell(22).setCellValue("其他杂费");
        int index = 1;
        for (CarrierStatementVo carrierStatementVo : carrierStatementVos) {
            List<CarrierQuotationArea> carrierQuotationAreas = carrierQuotationAreaMapper.selectByCondition(carrierStatementVo.getCarrierQuotationId());
            StringBuilder areaCounty = new StringBuilder();
            for (CarrierQuotationArea carrierQuotationArea : carrierQuotationAreas) {
                if (StringUtils.isNotBlank(carrierQuotationArea.getArea())) {
                    areaCounty.append(carrierQuotationArea.getArea()).append(Global.SCRIBE);
                }
            }
            List<ExpenseVO> expenseVOS = expenseMapper.selectByCondition(carrierStatementVo.getDeliveryTime().atTime(0,0,0),carrierStatementVo.getDeliveryCarId(),carrierStatementVo.getStoreNo(), carrierStatementVo.getPath());
            //采购费
            BigDecimal purMoney = BigDecimal.ZERO;
            //打车费
            BigDecimal carMoney = BigDecimal.ZERO;
            for (ExpenseVO expenseVO : expenseVOS) {
                if(Objects.equals(expenseVO.getType(), ExpenseTypeEnum.PURCHASE.getId())){
                    purMoney = purMoney.add(expenseVO.getAmount());
                }
                if(Objects.equals(expenseVO.getType(),ExpenseTypeEnum.TRAFFIC.getId())){
                    carMoney = carMoney.add(expenseVO.getAmount());
                }
            }
            carrierStatementVo.setHelpOrderFee(purMoney);
            carrierStatementVo.setTaxiFare(carMoney);
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(carrierStatementVo.getDeliveryTime() == null?null:carrierStatementVo.getDeliveryTime().toString());
            row.createCell(1).setCellValue(carrierStatementVo.getStoreName());
            row.createCell(2).setCellValue(carrierStatementVo.getPath());
            row.createCell(3).setCellValue(carrierStatementVo.getDriver());
            row.createCell(4).setCellValue(carrierStatementVo.getCarrierName());
            row.createCell(5).setCellValue(carrierStatementVo.getServiceArea());
            row.createCell(6).setCellValue(CollectionUtils.isEmpty(carrierQuotationAreas) ? null : carrierQuotationAreas.get(0).getProvince()+"/"+carrierQuotationAreas.get(0).getCity());
            row.createCell(7).setCellValue(areaCounty.length() == 0 ?null:areaCounty.substring(0,areaCounty.length()-1));
            row.createCell(8).setCellValue(Objects.isNull(carrierStatementVo.getPointNum()) ? null : carrierStatementVo.getPointNum().toString());
            row.createCell(9).setCellValue(Objects.isNull(carrierStatementVo.getStartPointNum()) ? null : carrierStatementVo.getStartPointNum().toString());
            row.createCell(10).setCellValue(carrierStatementVo.getStartPrice() == null?null :carrierStatementVo.getStartPrice().toString());
            row.createCell(11).setCellValue(carrierStatementVo.getExceedPointNum() == null ? null : carrierStatementVo.getExceedPointNum().toString());
            row.createCell(12).setCellValue(carrierStatementVo.getExceedPointPrice() == null ? null : carrierStatementVo.getExceedPointPrice().toString());
            row.createCell(13).setCellValue(carrierStatementVo.getSubsidyPrice() == null ? null : carrierStatementVo.getSubsidyPrice().toString());
            row.createCell(14).setCellValue(carrierStatementVo.getKm() == null ? null : carrierStatementVo.getKm().toString());
            row.createCell(15).setCellValue(carrierStatementVo.getKmPrice() == null ? null : carrierStatementVo.getKmPrice().toString());
            row.createCell(16).setCellValue(carrierStatementVo.getRealKm() == null ? null : carrierStatementVo.getRealKm().toString());
            row.createCell(17).setCellValue(carrierStatementVo.getRealKmPrice() == null ? null : carrierStatementVo.getRealKmPrice().toString());
            row.createCell(18).setCellValue(carrierStatementVo.getPrintingFee() == null ? null : carrierStatementVo.getPrintingFee().toString());
            row.createCell(19).setCellValue(carrierStatementVo.getFrontWarehouseFee() == null ? null : carrierStatementVo.getFrontWarehouseFee().toString());
            row.createCell(20).setCellValue(carrierStatementVo.getHelpOrderFee() == null ? null : carrierStatementVo.getHelpOrderFee().toString());
            row.createCell(21).setCellValue(carrierStatementVo.getTaxiFare() == null ? null : carrierStatementVo.getTaxiFare().toString());
            //杂费单独计算.
            BigDecimal extrasPrice = carrierStatementExtrasMapper.selectExtrasPrice(carrierStatementVo);
            row.createCell(22).setCellValue(extrasPrice == null ? null : extrasPrice.toString());
            index ++;
        }
        try {
            ExcelUtils.outputExcel(workbook, "承运商结算单信息"+DateUtils.localDateToString(LocalDate.now(),DateUtils.SPECIFIC_DATE)+".xls", RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
        return AjaxResult.getOK();
    }


    /**
     * 生成承运商结算单定时任务
     * @return
     */

    @Override
    public AjaxResult carrierStatementTask() {
        //取昨天的时间
        LocalDate yesterday = LocalDate.now().minusDays(1L);
        List<DeliveryCarPathVO> yesterdayDeliveryCarPathVOS = deliveryCarPathMapper.selectByDeliveryTime(yesterday);
        List<CarrierStatement> carrierStatements = new LinkedList<>();
        for (DeliveryCarPathVO deliveryCarPathVO : yesterdayDeliveryCarPathVOS) {
            WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenterMapper.selectByStoreNo(deliveryCarPathVO.getStoreNo());
            //获取系统内部
            List<DeliveryPathVO> deliveryPathVOS = deliveryPathMapper.selectListByCondition(deliveryCarPathVO.getDeliveryTime(), deliveryCarPathVO.getPath(), deliveryCarPathVO.getStoreNo());
            //获取saas配送点位
            List<DeliveryPathVO> deliveryPathSaasVOs = deliveryPathMapper.selectSaasListByCondition(deliveryCarPathVO.getDeliveryTime(), deliveryCarPathVO.getPath(), deliveryCarPathVO.getStoreNo());

            deliveryPathVOS.addAll(deliveryPathSaasVOs);
            if (CollectionUtils.isEmpty(deliveryPathVOS)) {
                continue;
            }
            List<CarrierQuotationVo> carrierQuotationVos = carrierQuotationMapper.selectByDeliveryCarId(deliveryCarPathVO.getDeliveryCarId());
            if (CollectionUtils.isEmpty(carrierQuotationVos)) {
                continue;
            }
            CarrierStatement carrierStatement = new CarrierStatement();
            carrierStatement.setDeliveryTime(deliveryCarPathVO.getDeliveryTime());
            carrierStatement.setStoreNo(deliveryCarPathVO.getStoreNo());
            carrierStatement.setPath(deliveryCarPathVO.getPath());
            carrierStatement.setDriver(deliveryCarPathVO.getDriver());
            carrierStatement.setPointNum(deliveryPathVOS.size());
            Integer exceedPointNum = 0;
            //判断是否有按公里计算的
            if (isKmArea(deliveryPathVOS)) {
                //公里价
                CarrierStatementVo kmPriceCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, KM_PRICE, carrierQuotationVos);
                //如果取不到公里价,跳过
                if(Objects.isNull(kmPriceCarrierStatement)){
                    continue;
                }
                carrierStatement.setCarrierName(carrierQuotationVos.get(0).getCarrierName());
                carrierStatement.setServiceArea(kmPriceCarrierStatement.getServiceArea());
                carrierStatement.setCarrierQuotationId(kmPriceCarrierStatement.getCarrierQuotationId());
                //预估公里数
                BigDecimal km = deliveryCarPathVO.getTotalDistance() == null ? BigDecimal.ZERO : deliveryCarPathVO.getTotalDistance();
                carrierStatement.setKm(km);
                //实际距离（米）
                BigDecimal realKm = getTotalDistance(warehouseLogisticsCenter, deliveryPathVOS);
                //实际公里数（转为公里数）
                realKm = realKm.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP);
                carrierStatement.setRealKm(realKm);
                if (!Objects.isNull(kmPriceCarrierStatement.getKmPrice())) {
                    carrierStatement.setKmPrice(km.multiply(kmPriceCarrierStatement.getKmPrice()));
                    carrierStatement.setRealKmPrice(realKm.multiply(kmPriceCarrierStatement.getKmPrice()));
                }
                Set<String> deliveryPathVOSet = deliveryPathVOS.stream().map(el -> el.getProvince() + el.getCity() + (StringUtils.isNotBlank(el.getArea()) ? el.getArea() : null)).collect(Collectors.toSet());
                //判断区域数
                if (deliveryPathVOS.size() > deliveryPathVOSet.size()) {
                    exceedPointNum = deliveryPathVOS.size() - deliveryPathVOSet.size();
                }
            }else{ //按点位计算
                //起步价
                CarrierStatementVo startPriceCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, START_PRICE, carrierQuotationVos);
                //起步价为空的话,跳过循环
                if(Objects.isNull(startPriceCarrierStatement)){
                    continue;
                }
                carrierStatement.setCarrierName(carrierQuotationVos.get(0).getCarrierName());
                carrierStatement.setServiceArea(startPriceCarrierStatement.getServiceArea());
                carrierStatement.setCarrierQuotationId(startPriceCarrierStatement.getCarrierQuotationId());
                //起步点位数
                CarrierStatementVo startPointNumCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, START_POINT_NUM, carrierQuotationVos);

                int startPointNumInt = Objects.isNull(startPointNumCarrierStatement) ? BigDecimal.ZERO.intValue() : startPointNumCarrierStatement.getStartPointNum();
                carrierStatement.setStartPointNum(startPointNumInt);
                carrierStatement.setStartPrice(Objects.isNull(startPriceCarrierStatement) ? BigDecimal.ZERO : startPriceCarrierStatement.getStartPrice());
                if (deliveryPathVOS.size() > startPointNumInt) {
                    exceedPointNum = deliveryPathVOS.size() - startPointNumInt;
                }
                //补贴费
                CarrierStatementVo subsidyPriceCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, SUBSIDY_PRICE, carrierQuotationVos);
                carrierStatement.setSubsidyPrice(Objects.isNull(subsidyPriceCarrierStatement) ? BigDecimal.ZERO : subsidyPriceCarrierStatement.getSubsidyPrice());

            }
            //超点数
            carrierStatement.setExceedPointNum(exceedPointNum);
            CarrierStatementVo  exceedPointPriceCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, EXCEED_POINT_PRICE, carrierQuotationVos);
            //超点价
            carrierStatement.setExceedPointPrice(BigDecimal.valueOf(exceedPointNum).multiply(Objects.isNull(exceedPointPriceCarrierStatement) ? BigDecimal.ZERO : exceedPointPriceCarrierStatement.getExceedPointPrice()));
            //取最高的打印费
            CarrierStatementVo printFeeCarrierStatement = carrierQuotationMapper.selectLastByAreaField(deliveryPathVOS, PRINTING_FEE, carrierQuotationVos);
            BigDecimal printFee = Objects.isNull(printFeeCarrierStatement) ? BigDecimal.ZERO : printFeeCarrierStatement.getPrintingFee();
            carrierStatement.setPrintingFee(printFee.multiply(BigDecimal.valueOf(deliveryPathVOS.size())));
            //查看前置仓费
            DeliveryCarWarehouseFee deliveryCarWarehouseFee = deliveryCarWarehouseFeeMapper.selectByDeliveryCarId(deliveryCarPathVO.getDeliveryCarId());
            carrierStatement.setFrontWarehouseFee(Objects.isNull(deliveryCarWarehouseFee) ? BigDecimal.ZERO : deliveryCarWarehouseFee.getFrontWarehouseFee());
            carrierStatement.setDeliveryCarId(deliveryCarPathVO.getDeliveryCarId());
            carrierStatements.add(carrierStatement);
        }
        //插入结果
        if (CollectionUtils.isEmpty(carrierStatements)) {
            return AjaxResult.getOK();
        }
        carrierStatementMapper.insertBatch(carrierStatements);
        return AjaxResult.getOK();
    }

    /**
     * 根据点位信息调用高德接口获取总距离信息
     * @param warehouseLogisticsCenter
     * @param deliveryPathVOS
     * @return
     */
    private BigDecimal getTotalDistance(WarehouseLogisticsCenter warehouseLogisticsCenter, List<DeliveryPathVO> deliveryPathVOS) {
        //按照完成配送时间进行排序
        List<String> pathPoiNoteList = deliveryPathVOS.stream().filter(deliveryPathVo -> deliveryPathVo.getFinishTime() != null)
                .sorted(Comparator.comparing(DeliveryPathVO::getFinishTime))
                .map(DeliveryPathVO::getPoiNote)
                .collect(Collectors.toList());

        //将城配仓点位放入集合中
        ArrayList<String> poiNoteList = new ArrayList<>();
        poiNoteList.add(warehouseLogisticsCenter.getPoiNote());
        poiNoteList.addAll(pathPoiNoteList);

        BigDecimal km = new BigDecimal(0);
        //说明超过16个点位，需要分批调用，还要获取上一次的最后一个作为出发点
        if((poiNoteList.size() - 1) / 16 > 1){
            Iterator<String> poiIterator = poiNoteList.iterator();
            //上一次的未，新一次的开始
            String lastEnd = null;
            //实际就要list里面要有17个点位
            while((poiNoteList.size() - 1) / 16 > 1){
                ArrayList<String> waypointsList = new ArrayList<>();
                while(poiIterator.hasNext()){
                    waypointsList.add(poiIterator.next());
                    poiIterator.remove();
                    if(waypointsList.size() == 17 || poiNoteList.size() == 0){
                        //出发点位
                        String firstPoi = lastEnd == null ? waypointsList.get(0) : lastEnd;
                        //终点
                        String endPoi = waypointsList.get(waypointsList.size() - 1);
                        //途经点
                        String waypoints = Joiner.on(";").join(waypointsList.subList(0, waypointsList.size() - 1));
                        lastEnd = endPoi;
                        BigDecimal distance = GaoDeUtil.calculWaypointsDistance(firstPoi, endPoi, waypoints, 2);
                        km = km.add(distance);
                        break;
                    }
                }
            }

            if(poiNoteList.size() > 0){
                String endPoi = poiNoteList.get(poiNoteList.size() - 1);
                String waypoints = Joiner.on(";").join(poiNoteList.subList(0, poiNoteList.size() - 1));
                BigDecimal distance = GaoDeUtil.calculWaypointsDistance(lastEnd, endPoi, waypoints, 2);
                km = km.add(distance);
            }
            return km;
        }else if(pathPoiNoteList.size() > 0){
            String firstPoi = poiNoteList.get(0);
            String endPoi = pathPoiNoteList.get(pathPoiNoteList.size() - 1);
            pathPoiNoteList.remove(pathPoiNoteList.size() - 1);
            String waypoints = Joiner.on(";").join(pathPoiNoteList);
            return GaoDeUtil.calculWaypointsDistance(firstPoi, endPoi, waypoints, 2);
        }

        return km;
    }

    /*public static void main(String[] args) {
        //List<String> pathPoiNoteList = new ArrayList<>(Arrays.asList("0", "1", "2", "3", "4", "5", "7", "8", "9", "10", "11", "12", "13", "14", "15","16","17"));
        List<String> pathPoiNoteList = new ArrayList<>(Arrays.asList());

        //将城配仓点位放入集合中
        ArrayList<String> poiNoteList = new ArrayList<>();
        poiNoteList.add("-1");
        poiNoteList.addAll(pathPoiNoteList);

        BigDecimal km = new BigDecimal(0);
        //说明超过16个点位，需要分批调用，还要获取上一次的最后一个作为出发点
        if((poiNoteList.size() - 1) / 5 > 1){
            Iterator<String> poiIterator = poiNoteList.iterator();
            //上一次的未，新一次的开始
            String lastEnd = null;
            //实际就要list里面要有17个点位
            while((poiNoteList.size() - 1) / 5 > 1){
                ArrayList<String> waypointsList = new ArrayList<>();
                while(poiIterator.hasNext()){
                    waypointsList.add(poiIterator.next());
                    poiIterator.remove();
                    if(waypointsList.size() == 6 || poiNoteList.size() == 0){
                        //出发点位
                        String firstPoi = lastEnd == null ? waypointsList.get(0) : lastEnd;
                        //终点
                        String endPoi = waypointsList.get(waypointsList.size() - 1);
                        //途经点
                        String waypoints = Joiner.on(";").join(waypointsList.subList(0, waypointsList.size() - 1));
                        lastEnd = endPoi;
                        System.out.println("起点："+firstPoi+"---途经点："+waypoints+"----终点："+endPoi);
                        break;
                    }
                }
            }

            if(poiNoteList.size() > 0){
                String endPoi = poiNoteList.get(poiNoteList.size() - 1);
                String waypoints = Joiner.on(";").join(poiNoteList.subList(0, poiNoteList.size() - 1));
                System.out.println("起点："+lastEnd+"---途经点："+waypoints+"----终点："+endPoi);
            }
        }else if(pathPoiNoteList.size() > 0){
            String firstPoi = poiNoteList.get(0);
            String endPoi = pathPoiNoteList.get(pathPoiNoteList.size() - 1);
            pathPoiNoteList.remove(pathPoiNoteList.size() - 1);
            String waypoints = Joiner.on(";").join(pathPoiNoteList);
            System.out.println("起点："+firstPoi+"---途经点："+waypoints+"----终点："+endPoi);
        }
    }*/
    /**
     * 定时任务生成前置仓费
     * @return
     */
    @Override
    @Transactional
    public AjaxResult deliveryCarFeeTask() {
        LocalDate now = LocalDate.now().minusMonths(1L);
        LocalDate startTime = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endTime = now.with(TemporalAdjusters.lastDayOfMonth());
        //查找承运商下所有的司机
        DeliveryCar deliveryCar = new DeliveryCar();
        List<DeliveryCar> deliveryCars = deliveryCarMapper.select(deliveryCar);
        for (DeliveryCar car : deliveryCars) {
            if (Objects.isNull(car.getCarrierId())) {
                continue;
            }
            BigDecimal frontWarehouseFees = carrierQuotationMapper.selectByCarrierId(car.getCarrierId());
            if (Objects.isNull(frontWarehouseFees)) {
                continue;
            }
            //判断时间内次数
            int num = deliveryCarPathMapper.selectByTime(startTime, endTime, car.getId());
            if (num != 0) {
                BigDecimal frontWarehouseFee = frontWarehouseFees.divide(BigDecimal.valueOf(num),2,BigDecimal.ROUND_HALF_UP);
                DeliveryCarWarehouseFee deliveryCarWarehouseFee = new DeliveryCarWarehouseFee();
                deliveryCarWarehouseFee.setDeliveryCarId(car.getId());
                deliveryCarWarehouseFee.setFrontWarehouseFee(frontWarehouseFee);
                deliveryCarWarehouseFeeMapper.insertSelective(deliveryCarWarehouseFee);
            }
        }


        return AjaxResult.getOK();
    }

    /**
     * 判断区域是否有按KM计算
     * @param deliveryPathVOS
     * @return
     */
    private Boolean isKmArea(List<DeliveryPathVO> deliveryPathVOS) {
        //判断区域是否有KM计算的
        int isExist = carrierQuotationMapper.existByDeliveryPath(deliveryPathVOS);
        if(isExist > 0){
            return true;
        }
        return false;
    }


}
