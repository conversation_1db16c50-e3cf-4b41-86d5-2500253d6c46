package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.util.Comparator;
import java.util.Map;
import java.util.stream.Collectors;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.LargeAreaMapper;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.LargeArea;
import net.summerfarm.model.input.LargeAreaQuery;
import net.summerfarm.model.vo.AreaVO;
import net.summerfarm.model.vo.LargeAreaVO;
import net.summerfarm.service.LargeAreaService;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/8/14  15:24
 */
@Service
public class LargeAreaServiceImpl extends BaseService implements LargeAreaService {

    @Resource
    private LargeAreaMapper largeAreaMapper;
    @Resource
    private AreaMapper areaMapper;

    @Override
    public void insertLargeArea(LargeArea largeArea) {
        largeArea.setLargeAreaNo(largeAreaMapper.selectMaxLargeAreaNo() + 1);
        largeAreaMapper.insertLargeArea(largeArea);
        return;
    }

    @Override
    public List<LargeAreaVO> selectAll(Integer status) {
        // 根据状态从数据库中查询所有的LargeAreaVO
        List<LargeAreaVO> largeAreas = largeAreaMapper.selectAll(status);

        // 如果查询结果为空，直接返回空列表
        if (CollectionUtil.isEmpty(largeAreas)) {
            return Collections.emptyList();
        }

        // 创建一个空的LargeAreaVO列表
        List<LargeAreaVO> largeAreaVOS = new ArrayList<>(largeAreas.size());

        // 提取所有LargeAreaVO的编号，并去重
        List<Integer> largeAreaNos = largeAreas.stream().map(x -> x.getLargeAreaNo()).distinct()
                .collect(Collectors.toList());

        // 根据编号列表查询对应的AreaVO列表
        List<AreaVO> areaVOList = areaMapper.listAreaVOs(largeAreaNos);

        // 如果小区的查询结果为空，直接返回原始列表
        if (CollectionUtil.isEmpty(areaVOList)) {
            return largeAreas;
        }

        // 将AreaVO列表按LargeAreaNo分组，存入Map中
        Map<Integer, List<AreaVO>> largeAreaMap = areaVOList.stream()
                .collect(Collectors.groupingBy(AreaVO::getLargeAreaNo));

        // 遍历所有的LargeAreaVO
        for (LargeAreaVO la : largeAreas) {
            // 获取当前LargeAreaVO对应的AreaVO列表
            List<AreaVO> areaList = largeAreaMap.get(la.getLargeAreaNo());

            // 如果AreaVO列表不为空，对其按ID进行排序并设置到当前LargeAreaVO中
            if (CollectionUtil.isNotEmpty(areaList)) {
                areaList.sort(Comparator.comparing(AreaVO::getId));
                // 将排序后的AreaVO列表设置到当前LargeAreaVO中
                la.setAreaList(areaList);
            } else {
                logger.warn("areaList is empty for LargeAreaVO: {}", la);
                la.setAreaList(new ArrayList<>(0));
            }
            largeAreaVOS.add(la);
        }
        return largeAreaVOS;
    }



    @Override
    public List<LargeAreaVO> selectAllValidLogistics() {
        List<LargeAreaVO> largeAreaVOS = new ArrayList<>();
        List<LargeAreaVO> largeAreas = largeAreaMapper.selectAll(WarehouseStorageCenterEnum.Status.VALID.ordinal());
        if (CollectionUtil.isEmpty(largeAreas)) {
            return largeAreaVOS;
        }
        List<Integer> largeAreaNos = largeAreas.stream().map(x -> x.getLargeAreaNo()).distinct()
                .collect(Collectors.toList());
        List<AreaVO> areaVOList = areaMapper.listValidAreaVOs(largeAreaNos);
        if (CollectionUtil.isEmpty(areaVOList)) {
            return largeAreaVOS;
        }
        Map<Integer, List<AreaVO>> largeAreaMap = areaVOList.stream().collect(
                Collectors.toMap(AreaVO::getLargeAreaNo, x -> Lists.newArrayList(x), (a, b) -> {
                    a.addAll(b);
                    return a;
                }));
        for (LargeAreaVO la : largeAreas) {
            List<AreaVO> areaList = largeAreaMap.get(la.getLargeAreaNo());
            if (CollectionUtil.isEmpty(areaList)) {
                continue;
            }
            List<AreaVO> sortList = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(areaList)) {
                sortList = areaList.stream().sorted(Comparator.comparing(AreaVO::getId))
                        .collect(Collectors.toList());
            }
            la.setAreaList(sortList);
            largeAreaVOS.add(la);
        }
        return largeAreaVOS;

    }



    @Override
    public PageInfo<LargeAreaVO> selectLargeAreaPage(Integer pageIndex, Integer pageSize, LargeAreaQuery largeArea) {
        PageHelper.startPage(pageIndex,pageSize);
        List<LargeAreaVO> largeAreaVOS = largeAreaMapper.selectLargeArea(largeArea);

        largeAreaVOS.forEach(el -> {
            //查询城市信息
            Area query = new Area();
            query.setLargeAreaNo(el.getLargeAreaNo());
            if(largeArea != null) {
                query.setBusinessLine (largeArea.getBusinessLine ());
                if(largeArea.getStatus ()!=null) {
                    query.setStatus (largeArea.getStatus () == 1);
                }
            }
            List<AreaVO> areaList = areaMapper.selectListVO(query);
            el.setAreaList(areaList);
        });
        return PageInfoHelper.createPageInfo(largeAreaVOS);
    }

    @Override
    public PageInfo<LargeAreaVO> selectLargeAreaPageV2(int pageIndex, int pageSize, LargeAreaQuery largeArea) {
        if(largeArea !=null && (ObjectUtil.isNotNull (largeArea.getBusinessLine ()) || ObjectUtil.isNotNull (largeArea.getStatus ()))){
            PageHelper.startPage(pageIndex,pageSize);
            List<LargeAreaVO> largeAreaVOS = largeAreaMapper.selectLargeAreaWithArea(largeArea);

            largeAreaVOS.forEach(el -> {
                //查询城市信息
                Area query = new Area();
                query.setLargeAreaNo(el.getLargeAreaNo());
                if(largeArea != null) {
                    query.setBusinessLine (largeArea.getBusinessLine ());
                    if(largeArea.getStatus ()!=null) {
                        query.setStatus (largeArea.getStatus () == 1);
                    }
                }
                List<AreaVO> areaList = areaMapper.selectListVO(query);
                el.setAreaList(areaList);
            });
            return PageInfoHelper.createPageInfo(largeAreaVOS);
        }else{
            return selectLargeAreaPage(pageIndex,pageSize,largeArea);
        }
    }

    @Override
    public AjaxResult updateLargeArea(LargeArea largeArea) {
        largeAreaMapper.updateLargeAreaNo(largeArea);
        return AjaxResult.getOK();
    }

    @Override
    public List<LargeAreaVO> selectAllLargeAll(Integer status) {
        return largeAreaMapper.selectAll(status);
    }
}
