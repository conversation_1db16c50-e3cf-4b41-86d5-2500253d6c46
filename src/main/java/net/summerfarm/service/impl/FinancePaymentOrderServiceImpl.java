package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingdingFormBO;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.*;
import net.summerfarm.mapper.bms.BmsPaymentDocumentMapper;
import net.summerfarm.mapper.bms.BmsPaymentInvoiceRelMapper;
import net.summerfarm.mapper.bms.BmsQuotationProcessMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.PurchaseBindingPrepayment;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.FinanceAccountStatementInput;
import net.summerfarm.model.input.FinancePurchaseInvoiceWalletsInput;
import net.summerfarm.model.input.SupplierReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.bms.BmsPaymentDocumentVO;
import net.summerfarm.module.bms.facade.BmsServiceClientFacade;
import net.summerfarm.module.bms.mq.bms.constant.BmsRocketMqConstant;
import net.summerfarm.module.bms.mq.bms.message.FmsInformMessage;
import net.summerfarm.module.pms.model.input.FinanceAccountDetailInput;
import net.summerfarm.module.pms.model.vo.FinanceAccountSkuVO;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.mq.constant.SrmMqConstant;
import net.summerfarm.service.*;
import net.summerfarm.service.bms.BmsPaymentDocumentService;
import net.xianmu.bms.client.req.payment.PaymentDocQueryRequest;
import net.xianmu.bms.client.resp.payment.PaymentDocDetailResponse;
import net.xianmu.bms.client.resp.payment.PaymentDocInvoiceResponse;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 应付Service
 * @date 2022/1/19 18:20
 */
@Service
public class FinancePaymentOrderServiceImpl extends BaseService implements FinancePaymentOrderService {

    @Resource
    private FinancePaymentOrderMapper financePaymentOrderMapper;
    @Resource
    private PurchaseAdvancedOrderMapper purchaseAdvancedOrderMapper;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private PurchaseBindingPrepaymentMapper purchaseBindingPrepaymentMapper;
    @Resource
    private PurchasePrepaymentPoolMapper purchasePrepaymentPoolMapper;
    @Resource
    private PurchasePrepaymentPoolRecordMapper purchasePrepaymentPoolRecordMapper;
    @Resource
    private PurchasePrepaymentPoolService purchasePrepaymentPoolService;
    @Resource
    private SupplierAccountMapper supplierAccountMapper;
    @Resource
    private FinanceAccountStatementMapper financeAccountStatementMapper;
    @Resource
    private PurchaseSupplierPaymentMapper purchaseSupplierPaymentMapper;
    @Resource
    private PurchaseBindingPrepaymentService purchaseBindingPrepaymentService;
    @Resource
    private FinanceAccountStatementDetailMapper financeAccountStatementDetailMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    private PurchaseInvoiceMapper purchaseInvoiceMapper;
    @Resource
    private FinancePurchaseInvoiceWalletsMapper financePurchaseInvoiceWalletsMapper;
    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private FinanceOperatorLogMapper financeOperatorLogMapper;
    @Resource
    private SettlementConfigMapper settlementConfigMapper;
    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;
    @Resource
    private PurchaseSupplierPaymentService purchaseSupplierPaymentService;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private BmsPaymentDocumentService bmsPaymentDocumentService;
    @Resource
    private BmsPaymentInvoiceRelMapper bmsPaymentInvoiceRelMapper;
    @Resource
    private BmsQuotationProcessMapper bmsQuotationProcessMapper;
    @Resource
    private BmsPaymentDocumentMapper bmsPaymentDocumentMapper;
    @Resource
    private CarrierService carrierService;

    @Resource
    private FinanceAccountVerificationService financeAccountVerificationService;
    @Resource
    @Lazy
    private FinanceAccountStatementService financeAccountStatementService;

    @Resource
    private BmsServiceClientFacade bmsServiceClientFacade;

    /**
     * 预付类型
     */
    private static final Integer ADVANCE_TYPE = 1;

    private static final Integer ACCOUNT_STATEMENT = 2;

    private static final Integer BMS_BILL = 3;

    /**
     * 关联采购单
     */
    private static final Integer ASSOCIATED = 1;

    /**
     * 已经预付满的状态
     */
    private static final Integer IN_SETTLE = 1;

    /**
     * 未预付满的状态
     */
    private static final Integer UN_SETTLE = 0;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult uploadAdvancePaymentVoucher(Long id, String paymentVoucher) {
        if (StringUtils.isBlank(paymentVoucher)) {
            return AjaxResult.getErrorWithMsg("请上传凭证");
        }
        String[] voucherArray = paymentVoucher.split(StringUtils.SEPARATING_SYMBOL);
        if (voucherArray.length > NumberUtils.INTEGER_THREE) {
            return AjaxResult.getErrorWithMsg("凭证数量不能超过三个");
        }

        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        if (Objects.isNull(financePaymentOrder)) {
            return AjaxResult.getErrorWithMsg("未查询到付款单信息");
        }
        //预付单处理逻辑
        if (Objects.equals(financePaymentOrder.getType(), ADVANCE_TYPE)) {
            PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(financePaymentOrder.getAdditionalId());
            SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
            Admin select = adminMapper.select(settlementConfig.getPayer());
            if (!Objects.equals(select.getAdminId(), getAdminId())) {
                return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + settlementConfig.getPayer());
            }

            if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal()) && StringUtils.isBlank(paymentVoucher)) {
                return AjaxResult.getErrorWithMsg("请上传支付凭证");
            }


            if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal())) {
                //第一次上传
                firstUploadSuccess(id, paymentVoucher);
                // feat(发货管理优化&财务往来)：srm预付单消息推送
                sendMessage(financePaymentOrder.getId(), MType.SRM_PREPAY_ORDER.name());
            } else if (Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.HAS_PAY.ordinal())) {
                //普通更新凭证
                FinancePaymentOrder updateOrder = new FinancePaymentOrder();
                updateOrder.setId(id);
                updateOrder.setPaymentVoucher(paymentVoucher);
                updateOrder.setUpdater(getAdminName());
                financePaymentOrderMapper.update(updateOrder);
                logger.info("管理员：{}更新了付款单：{}的凭证", getAdminName(), id);
            } else {
                AjaxResult.getErrorWithMsg("预付单的状态非待打款或者已打款，不可以上传凭证");
            }
        }
        //对账单处理逻辑
        else if (Objects.equals(financePaymentOrder.getType(), ACCOUNT_STATEMENT)) {
            FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.select(financePaymentOrder.getAdditionalId());
            SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
            Admin select = adminMapper.select(settlementConfig.getPayer());
            if (!Objects.equals(select.getAdminId(), getAdminId())) {
                return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + settlementConfig.getPayer());
            }


            if (Objects.equals(financeAccountStatement.getStatus(), FinanceAccountStatementStatusEnum.WAIT_PAY.ordinal()) && StringUtils.isBlank(paymentVoucher)) {
                return AjaxResult.getErrorWithMsg("请上传支付凭证");
            }

            if (Objects.equals(financeAccountStatement.getStatus(), FinanceAccountStatementStatusEnum.WAIT_PAY.ordinal())) {
                //第一次上传
                statementStatus(financePaymentOrder.getAdditionalId(), id, paymentVoucher);
                //发消息给srm 让他发送已通过的公众号消息给供应商 & 发钉钉通知给采购
                sendMessage(financeAccountStatement.getId(), MType.SRM_ACCOUNT_MESSAGE_NOTIFICATION.name());
                //srm对账单已完成
                sendMessage(financeAccountStatement.getId(), MType.SRM_COMPLETED_ACCOUNT.name());
            } else if (Objects.equals(financeAccountStatement.getStatus(), FinanceAccountStatementStatusEnum.HAS_PAY.ordinal())) {
                //普通更新凭证
                FinancePaymentOrder updateOrder = new FinancePaymentOrder();
                updateOrder.setId(id);
                updateOrder.setPaymentVoucher(paymentVoucher);
                updateOrder.setUpdater(getAdminName());
                financePaymentOrderMapper.update(updateOrder);
                logger.info("管理员：{}更新了付款单：{}的凭证", getAdminName(), id);
            } else {
                AjaxResult.getErrorWithMsg("对账单的状态非待打款或者已打款，不可以上传凭证");
            }
        } else {
            SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
            Admin select = adminMapper.select(settlementConfig.getPayer());
            if (!Objects.equals(select.getAdminId(), getAdminId())) {
                return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + settlementConfig.getPayer());
            }

            if (StringUtils.isBlank(paymentVoucher)) {
                return AjaxResult.getErrorWithMsg("请上传支付凭证");
            }

            if (Objects.equals(financePaymentOrder.getStatus(), FinancePaymentOrderStatusEnum.WAIT_PAY.ordinal())) {
                //第一次上传
                FinancePaymentOrder updateOrder = new FinancePaymentOrder();
                updateOrder.setId(id);
                updateOrder.setStatus(FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal());
                updateOrder.setPaymentVoucher(paymentVoucher);
                updateOrder.setUpdater(getAdminName());
                financePaymentOrderMapper.update(updateOrder);
                //更新bms打款单状态为打款成功
                // 记录打款成功信息
                Long bmsPaymentDocumentId = financePaymentOrder.getAdditionalId();
                //MQ通知BMS更新打款单状态
                FmsInformMessage fmsInformMessage = new FmsInformMessage();
                fmsInformMessage.setFinancePaymentOrderId(id);
                fmsInformMessage.setBmsPaymentDocumentId(bmsPaymentDocumentId);
                mqProducer.send(BmsRocketMqConstant.Topic.FMS_UPLOAD_PAYMENT_VOUCHER,null,JSON.toJSONString(fmsInformMessage));
            } else if (Objects.equals(financePaymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal())) {
                //普通更新凭证
                FinancePaymentOrder updateOrder = new FinancePaymentOrder();
                updateOrder.setId(id);
                updateOrder.setPaymentVoucher(paymentVoucher);
                updateOrder.setUpdater(getAdminName());
                financePaymentOrderMapper.update(updateOrder);
                logger.info("管理员：{}更新了打款单：{}的凭证", getAdminName(), id);
            }

        }
        return AjaxResult.getOK();
    }

    /**
     * 发送对账单id给srm处理
     * @param id
     * @param key
     */
    private void sendMessage(Long id,String key) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("id", id);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));
    }

    public void firstUploadSuccess(Long id, String paymentVoucher) {
        //付款单状态处理
        FinancePaymentOrder updateOrder = new FinancePaymentOrder();
        updateOrder.setId(id);
        updateOrder.setStatus(FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal());
        updateOrder.setPaymentVoucher(paymentVoucher);
        updateOrder.setUpdater(getAdminName());
        financePaymentOrderMapper.update(updateOrder);

        //预付单状态更新
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        Long additionalId = financePaymentOrder.getAdditionalId();
        PurchaseAdvancedOrder updateAdvanceOrder = new PurchaseAdvancedOrder();
        updateAdvanceOrder.setId(additionalId);
        updateAdvanceOrder.setStatus(PurchaseAdvancedOrderStatusEnum.HAS_PAY.ordinal());
        updateAdvanceOrder.setCurrentProcessor("--");
        purchaseAdvancedOrderMapper.update(updateAdvanceOrder);

        //审核流记录更新
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin admin = adminMapper.select(settlementConfig.getPayer());
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.PAYER.ordinal());
        financeOperatorLog.setAdditionalId(additionalId);
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLog.setOperator(admin.getRealname());
        financeOperatorLog.setOperatorId(admin.getAdminId());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

        //预付单绑定采购单的话 绑定记录更新
        PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(additionalId);
        if (Objects.equals(purchaseAdvancedOrder.getType(), PurchaseAdvanceOrderTypeEnum.ASSOCIATE.ordinal())) {
            PurchaseBindingPrepayment purchaseBindingPrepayment = new PurchaseBindingPrepayment();
            purchaseBindingPrepayment.setPurchaseAdvancedOrderId(additionalId);
            List<PurchaseBindingPrepayment> purchaseBindingPrepayments = purchaseBindingPrepaymentMapper.queryAll(purchaseBindingPrepayment);
            List<String> purchaseNoList = purchaseBindingPrepayments.stream().map(PurchaseBindingPrepayment::getPurchaseNo).collect(Collectors.toList());
            List<String> inProcessPurchaseList = financeAccountStatementMapper.queryInProcessPurchaseList(purchaseNoList, purchaseAdvancedOrder.getSupplierId());
            if (!CollectionUtils.isEmpty(inProcessPurchaseList)) {
                throw new DefaultServiceException("预付单关联的采购单:" + inProcessPurchaseList + "还在对账流程中，出于风险考虑，请在流程结束后再尝试提交");
            }
            PurchaseBindingPrepayment updateBinding = new PurchaseBindingPrepayment();
            for (PurchaseBindingPrepayment bindingPrepayment : purchaseBindingPrepayments) {
                updateBinding.setId(bindingPrepayment.getId());
                updateBinding.setBindingStatus(PurchaseBindingEnum.BINDING.ordinal());
                purchaseBindingPrepaymentMapper.update(updateBinding);
            }
        }

        //预付池金额处理
        PurchasePrepaymentPool queryPool = new PurchasePrepaymentPool();
        queryPool.setSupplierId(purchaseAdvancedOrder.getSupplierId());
        PurchasePrepaymentPool purchasePrepaymentPool = purchasePrepaymentPoolMapper.selectOne(queryPool);
        if (Objects.isNull(purchasePrepaymentPool)) {
            // 初始化预付池
            purchasePrepaymentPoolService.initPurchasePrepaymentPool(purchaseAdvancedOrder);
        } else {
            purchasePrepaymentPoolService.updatePoolByPurchaseAdvanceOrder(purchaseAdvancedOrder);
        }

        //预付池往来记录处理
        PurchasePrepaymentPoolRecord purchasePrepaymentPoolRecord = new PurchasePrepaymentPoolRecord();
        purchasePrepaymentPoolRecord.setSupplierId(purchaseAdvancedOrder.getSupplierId());
        purchasePrepaymentPoolRecord.setSupplierName(purchaseAdvancedOrder.getSupplierName());
        purchasePrepaymentPoolRecord.setBillId(purchaseAdvancedOrder.getId());
        purchasePrepaymentPoolRecord.setType(PurchasePrepaymentPoolRecordTypeEnum.ADVANCE.ordinal());
        purchasePrepaymentPoolRecord.setAmount(purchaseAdvancedOrder.getTotalAmount());
        purchasePrepaymentPoolRecordMapper.insert(purchasePrepaymentPoolRecord);
        logger.info("管理员：{}上传了付款单：{}的凭证", getAdminName(), id);
        return;
    }

    public void statementStatus(Long accountId,Long paymentOrderId, String paymentVoucher) {
        if (financeAccountStatementService.isHistoryAccountData(accountId)) {
            statementStatusHistory(paymentOrderId, paymentVoucher);
        } else {
            statementStatusByVerification(paymentOrderId, paymentVoucher);
        }
    }

    /** 对账并生成核销流水 **/
    private void statementStatusByVerification(Long id, String paymentVoucher) {
        //对账单状态处理
        FinancePaymentOrder updateOrder = new FinancePaymentOrder();
        updateOrder.setId(id);
        updateOrder.setStatus(FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal());
        updateOrder.setPaymentVoucher(paymentVoucher);
        updateOrder.setUpdater(getAdminName());
        financePaymentOrderMapper.update(updateOrder);

        //对账单状态更新
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        Long additionalId = financePaymentOrder.getAdditionalId();
        FinanceAccountStatement financeAccountStatement = new FinanceAccountStatement();
        financeAccountStatement.setId(additionalId);
        financeAccountStatement.setStatus(FinanceAccountStatementStatusEnum.HAS_PAY.ordinal());
        financeAccountStatement.setCurrentProcessor("--");
        financeAccountStatementMapper.updateBack(financeAccountStatement);

        //审核流记录更新
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin admin = adminMapper.select(settlementConfig.getPayer());
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.PAYER.ordinal());
        financeOperatorLog.setAdditionalId(additionalId);
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setOperator(admin.getRealname());
        financeOperatorLog.setOperatorId(admin.getAdminId());
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

        //生成核销流水
        List<FinanceAccountVerification> financeAccountVerifications = financeAccountVerificationService.generateAccountVerificationListByAccountId(additionalId);
        financeAccountVerifications.stream().filter(x->FinanceAccountVerificationTypeEnum.OUTPUT_PAY.getId().equals(x.getOutputType()))
                .forEach(x->x.setOutputSourceId(String.valueOf(id)));
        financeAccountVerificationService.insertBatch(financeAccountVerifications);
    }

    /** 老逻辑，在历史对账流程中的数据对账结束后可去 **/
    public void statementStatusHistory(Long id, String paymentVoucher) {
        //对账单状态处理
        FinancePaymentOrder updateOrder = new FinancePaymentOrder();
        updateOrder.setId(id);
        updateOrder.setStatus(FinancePaymentOrderStatusEnum.PAY_SUCCESS.ordinal());
        updateOrder.setPaymentVoucher(paymentVoucher);
        updateOrder.setUpdater(getAdminName());
        financePaymentOrderMapper.update(updateOrder);

        //对账单状态更新
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        Long additionalId = financePaymentOrder.getAdditionalId();
        FinanceAccountStatement financeAccountStatement = new FinanceAccountStatement();
        financeAccountStatement.setId(additionalId);
        financeAccountStatement.setStatus(FinanceAccountStatementStatusEnum.HAS_PAY.ordinal());
        financeAccountStatement.setCurrentProcessor("--");
        financeAccountStatementMapper.updateBack(financeAccountStatement);

        //审核流记录更新
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin admin = adminMapper.select(settlementConfig.getPayer());
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.PAYER.ordinal());
        financeOperatorLog.setAdditionalId(additionalId);
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setOperator(admin.getRealname());
        financeOperatorLog.setOperatorId(admin.getAdminId());
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

        FinanceAccountStatement select = financeAccountStatementMapper.select(additionalId);
        List<FinanceAccountStatementDetail> financeAccountStatementDetails = financeAccountStatementDetailMapper.selectByPrimaryKey(select.getId());
        //先根据采购单 分组 相同采购单一起处理
        Map<String, List<FinanceAccountStatementDetail>> collect = financeAccountStatementDetails.stream().collect(Collectors.groupingBy(FinanceAccountStatementDetail::getPurchaseNo));
        for (Map.Entry<String, List<FinanceAccountStatementDetail>> map : collect.entrySet()) {
            //已付款对账单，采购单供应商付款表数据修改
            PurchaseSupplierPayment purchaseSupplierPayment = new PurchaseSupplierPayment();
            purchaseSupplierPayment.setSupplierId(select.getSupplierId());
            purchaseSupplierPayment.setPurchaseNo(map.getKey());
            //调整后金额
            BigDecimal amount = BigDecimal.ZERO;
            //调整金额
            BigDecimal adjustAmount = BigDecimal.ZERO;
            for (FinanceAccountStatementDetail financeAccountStatementDetail : map.getValue()) {
                boolean purchaseOrSkipIn = (Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.PURCHASE_IN.getId()) || Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.SKIP_STORE_IN.getId()));
                //查询出供应商采购单下付款情况
                PurchaseSupplierPaymentVO paymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
                //如果付款情况为空，说明该采购单没有预付，需要生成，插入这次的付款情况
                if (ObjectUtils.isEmpty(paymentVO)) {
                    PurchaseSupplierPayment payment = new PurchaseSupplierPayment();
                    payment.setPurchaseNo(financeAccountStatementDetail.getPurchaseNo());
                    payment.setSupplierId(select.getSupplierId());
                    payment.setSupplierName(select.getSupplierName());
                    payment.setAdvanceAmount(BigDecimal.ZERO);
                    payment.setWriteOffAmount(BigDecimal.ZERO);
                    payment.setAdjustAmount(BigDecimal.ZERO);
                    //采购入库和越仓入库为正数，退货出库和越仓出库为负数
                    //负数时创建无付款金额 在付款信息中会扣减成本
                    payment.setPaymentAmount(BigDecimal.ZERO);
                    purchaseSupplierPaymentMapper.insert(payment);
                }
                //同一个采购单的调整金额和调整后总金额统计之后 计算相应要改变的数据
                adjustAmount = adjustAmount.add(financeAccountStatementDetail.getAdjustAmount());
                if (purchaseOrSkipIn) {
                    amount = amount.add(financeAccountStatementDetail.getAmount());
                }else if (Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.PURCHASES_BACK.getId())) {
                    amount = amount.subtract(financeAccountStatementDetail.getAmount());
                }
            }
            //查询出供应商采购单下付款情况
            PurchaseSupplierPaymentVO purchaseSupplierPaymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
            purchaseSupplierPayment.setId(purchaseSupplierPaymentVO.getId());
            BigDecimal advanceAmount = purchaseBindingPrepaymentService.queryAdvanceAmount(purchaseSupplierPayment.getPurchaseNo(), select.getSupplierId());
            purchaseSupplierPaymentVO.setAdvanceAmount(advanceAmount);
            //判断调整后金额的正负 分开处理
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                //用预付金额与对账单调整后金额对比，判断核销金额
                BigDecimal canUserAdvanceAmount = purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount());
                if (canUserAdvanceAmount.subtract(amount).compareTo(BigDecimal.ZERO) < CommonNumbersEnum.ZERO.getNumber() &&
                        BigDecimal.ZERO.compareTo(canUserAdvanceAmount)!=0) {
                    //如果预付金额小于对账单调整后金额， 核销金额为预付金额，付款金额为账单调整后金额-预付金额
                    purchaseSupplierPayment.setWriteOffAmount(canUserAdvanceAmount.add(purchaseSupplierPaymentVO.getWriteOffAmount()));
                    purchaseSupplierPayment.setPaymentAmount(amount.subtract(canUserAdvanceAmount).add(purchaseSupplierPaymentVO.getPaymentAmount()));
                    logger.info("核销金额为预付金额，付款金额为账单调整后金额-预付金额");
                } else if (canUserAdvanceAmount.compareTo(BigDecimal.ZERO)<=0) {
                    //如果没有预付金额，就加入付款金额
                    purchaseSupplierPayment.setPaymentAmount(amount.add(purchaseSupplierPaymentVO.getPaymentAmount()));
                    logger.info("加入付款金额");
                } else {
                    //如果预付金额大于等于对账单调整后金额，不用付款，核销金额全部
                    purchaseSupplierPayment.setWriteOffAmount(amount.add(purchaseSupplierPaymentVO.getWriteOffAmount()));
                    logger.info("核销金额全部");
                }
                purchaseSupplierPayment.setAdjustAmount(adjustAmount.add(purchaseSupplierPaymentVO.getAdjustAmount()));
                purchaseSupplierPayment.setUpdateTime(LocalDateTime.now());
                purchaseSupplierPaymentMapper.update(purchaseSupplierPayment);
                logger.info("对账单调整后金额大于零");
            } else if (amount.compareTo(BigDecimal.ZERO) == 0 && adjustAmount.compareTo(BigDecimal.ZERO) != 0) {
                //处理调整金额
                purchaseSupplierPayment.setAdjustAmount(adjustAmount.add(purchaseSupplierPaymentVO.getAdjustAmount()));
                purchaseSupplierPayment.setUpdateTime(LocalDateTime.now());
                purchaseSupplierPaymentMapper.update(purchaseSupplierPayment);
                logger.info("对账单调整后金额等于零，且有调整金额");
            } else {
                //用核销金额与对账单调整后金额对比，判断核销金额
                if (purchaseSupplierPaymentVO.getWriteOffAmount().subtract(amount).compareTo(BigDecimal.ZERO) >= CommonNumbersEnum.ZERO.getNumber()) {
                    purchaseSupplierPayment.setWriteOffAmount(purchaseSupplierPaymentVO.getWriteOffAmount().subtract(amount).subtract(adjustAmount));
                } else if (purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount()).compareTo(BigDecimal.ZERO)!=0) {
                    //如果没有预付金额，就减去付款金额
                    purchaseSupplierPayment.setPaymentAmount((purchaseSupplierPaymentVO.getPaymentAmount()).subtract(amount));
                } else if (purchaseSupplierPaymentVO.getWriteOffAmount().subtract(amount).compareTo(BigDecimal.ZERO) < CommonNumbersEnum.ZERO.getNumber()) {
                    //如果核销金额小于账单调整后金额 核销清零 付款减少差值
                    purchaseSupplierPayment.setWriteOffAmount(BigDecimal.ZERO);
                    purchaseSupplierPayment.setPaymentAmount((purchaseSupplierPaymentVO.getPaymentAmount()).subtract(amount.subtract(purchaseSupplierPaymentVO.getWriteOffAmount())));
                }
                purchaseSupplierPayment.setAdjustAmount(adjustAmount.add(purchaseSupplierPaymentVO.getAdjustAmount()));
                purchaseSupplierPayment.setUpdateTime(LocalDateTime.now());
                purchaseSupplierPaymentMapper.update(purchaseSupplierPayment);
            }
        }

        logger.info("管理员：{}上传了付款单：{}的凭证", getAdminName(), id);
        return;
    }

    /**
     * 预付池往来记录
     *
     * @param financeAccountStatement
     */
    private void supplierPaymentReset(FinanceAccountStatement financeAccountStatement, BigDecimal refundAmount) {

        //新增预付池往来记录
        PurchasePrepaymentPoolRecord purchasePrepaymentPoolRecord = new PurchasePrepaymentPoolRecord();
        purchasePrepaymentPoolRecord.setSupplierId(financeAccountStatement.getSupplierId());
        purchasePrepaymentPoolRecord.setSupplierName(financeAccountStatement.getSupplierName());
        purchasePrepaymentPoolRecord.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
        purchasePrepaymentPoolRecord.setAmount(refundAmount);
        purchasePrepaymentPoolRecord.setBillId(financeAccountStatement.getId());
        purchasePrepaymentPoolRecordMapper.insert(purchasePrepaymentPoolRecord);

        PurchasePrepaymentPool query = new PurchasePrepaymentPool();
        query.setSupplierId(financeAccountStatement.getSupplierId());
        PurchasePrepaymentPool pool = purchasePrepaymentPoolMapper.selectOne(query);

        //更新预付池金额
        purchasePrepaymentPoolService.updatePoolAmount(pool.getId(), refundAmount, BigDecimal.ZERO, refundAmount);
    }

    @Override
    public AjaxResult listAll(int pageIndex, int pageSize, FinancePaymentOrderVO financePaymentOrderVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinancePaymentOrderVO> financePaymentOrderVOList = financePaymentOrderMapper.queryAll(financePaymentOrderVO);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financePaymentOrderVOList));
    }

    @Override
    public AjaxResult selectDetail(Long id) {
        FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(id);
        FinancePaymentOrderVO financePaymentOrderVO = new FinancePaymentOrderVO(id, financePaymentOrder.getAdditionalId(), financePaymentOrder.getType(), financePaymentOrder.getAmount(), financePaymentOrder.getRemark(), financePaymentOrder.getStatus(), financePaymentOrder.getCreator(), financePaymentOrder.getCreateTime(), financePaymentOrder.getUpdater(), financePaymentOrder.getUpdateTime(), financePaymentOrder.getPaymentVoucher());
        Long additionalId = financePaymentOrderVO.getAdditionalId();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", id);
        jsonObject.put("remark", financePaymentOrder.getRemark());
        jsonObject.put("amount", financePaymentOrder.getAmount());
        jsonObject.put("status", financePaymentOrder.getStatus());
        jsonObject.put("deleteReason", financePaymentOrder.getDeleteReason());
        jsonObject.put("paymentVoucher", financePaymentOrder.getPaymentVoucher());
        jsonObject.put("applicationTime", financePaymentOrder.getApplicationTime());
        jsonObject.put("approvalTime", financePaymentOrder.getApprovalTime());
        jsonObject.put("creator", financePaymentOrder.getCreator());
        jsonObject.put("supplierName", financePaymentOrder.getSupplierName());
        if (Objects.equals(financePaymentOrderVO.getType(), FinancePurchaseTypeEnum.ADVANCE.ordinal())) {
            PurchaseAdvancedOrder purchaseAdvancedOrder = purchaseAdvancedOrderMapper.queryById(additionalId);
            SupplierAccount supplierAccount = supplierAccountMapper.selectById(purchaseAdvancedOrder.getSupplierAccountId());
            supplierAccount(jsonObject, supplierAccount);
            return AjaxResult.getOK(jsonObject);
        }else if (Objects.equals(financePaymentOrderVO.getType(), FinancePurchaseTypeEnum.STATEMENTS.ordinal())) {
            FinanceAccountStatementVO select = financeAccountStatementMapper.select(additionalId);
            SupplierAccount supplierAccount = supplierAccountMapper.selectById(select.getSupplierAccountId());
            supplierAccount(jsonObject, supplierAccount);
            return AjaxResult.getOK(jsonObject);
        }
        //承运商付款信息
        BmsPaymentDocumentVO document = bmsPaymentDocumentService.selectDetailId(financePaymentOrder.getAdditionalId().intValue());
        Integer accountId = document.getCarrierAccountId();
        if(Objects.equals(document.getBusinessType(),"PROXY_WAREHOUSE_BUSINESS")){
            SupplierAccount carrierAccounts = supplierAccountMapper.selectById(accountId);
            if (ObjectUtils.isEmpty(carrierAccounts)){
                return AjaxResult.getOK(jsonObject);
            }
            jsonObject.put("payType", carrierAccounts.getPayType());
            jsonObject.put("accountName", carrierAccounts.getAccountName());
            jsonObject.put("accountBank", carrierAccounts.getAccountBank());
            jsonObject.put("account", carrierAccounts.getAccount());
            jsonObject.put("accountAscription", carrierAccounts.getAccountAscription());
            jsonObject.put("paymentVoucher", financePaymentOrderVO.getPaymentVoucher());
        }else{
            CarrierAccount carrierAccounts = carrierService.selectCarrierAccount(accountId);
            if (ObjectUtils.isEmpty(carrierAccounts)){
                return AjaxResult.getOK(jsonObject);
            }
            jsonObject.put("payType", carrierAccounts.getPayType());
            jsonObject.put("accountName", carrierAccounts.getAccountName());
            jsonObject.put("accountBank", carrierAccounts.getAccountBank());
            jsonObject.put("account", carrierAccounts.getAccount());
            jsonObject.put("accountAscription", carrierAccounts.getAccountAscription());
            jsonObject.put("paymentVoucher", financePaymentOrderVO.getPaymentVoucher());
        }

        return AjaxResult.getOK(jsonObject);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult financeRecall(FinancePaymentOrder financePaymentOrder) {

        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin select = adminMapper.select(settlementConfig.getPayer());
        if (!Objects.equals(select.getAdminId(), getAdminId())) {
            return AjaxResult.getErrorWithMsg("您没有权限，当前权限人为" + settlementConfig.getPayer());
        }

        //检查是否为预付单付款单
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(financePaymentOrder.getId());
        //不为待付款的预付单付款单
        if (!Objects.equals(paymentOrder.getType(), FinancePaymentOrderTypeEnum.ADVANCED_ORDER.ordinal()) || !Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.WAIT_PAY.ordinal())) {
            return AjaxResult.getErrorWithMsg("该付款单不为预付单付款单或该付款单不是待付款状态");
        }
        //预付单状态是否已作废
        PurchaseAdvancedOrderVO purchaseAdvancedOrder = purchaseAdvancedOrderMapper.selectDetail(paymentOrder.getAdditionalId());
        if (!Objects.equals(purchaseAdvancedOrder.getStatus(), PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal())) {
            return AjaxResult.getErrorWithMsg("该付款单的预付单不是待付款状态");
        }

        //作废付款单
        FinancePaymentOrder financePayment = new FinancePaymentOrder();
        financePayment.setId(paymentOrder.getId());
        financePayment.setStatus(FinancePaymentOrderStatusEnum.CANCEL.ordinal());
        financePayment.setDeleteReason(FinancePaymentOrderReasonEnum.FINANCIAL_REJECTION.ordinal());
        financePaymentOrderMapper.update(financePayment);

        //作废预付单 更新操作人
        //改变预付单状态
        PurchaseAdvancedOrder updateOrder = new PurchaseAdvancedOrder();
        updateOrder.setId(purchaseAdvancedOrder.getId());
        updateOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        updateOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.FINANCIAL_PAYMENT_CANCELLATION.ordinal());
        updateOrder.setCurrentProcessor("--");
        purchaseAdvancedOrderMapper.update(updateOrder);
        settleFlagReset(purchaseAdvancedOrder.getId(), purchaseAdvancedOrder.getSupplierId());
//        supplierPaymentBack(purchaseAdvancedOrder);

        //预付单撤销人
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        financeOperatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        financeOperatorLog.setType(FinancePurchaseTypeEnum.ADVANCE.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ONE.getNumber());
        financeOperatorLog.setOperatorId(getAdminId());
        financeOperatorLog.setOperator(getAdminName());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.FINANCE.ordinal());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

        FinanceOperatorLog operatorLog = new FinanceOperatorLog();
        operatorLog.setType(ADVANCE_TYPE);
        operatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        operatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        FinanceOperatorLog log = financeOperatorLogMapper.selectById(operatorLog);
        purchaseAdvancedOrder.setCreatorAdminId(log.getOperatorId());

        //钉钉消息通知采购
        sendOfferMessage(purchaseAdvancedOrder, purchaseAdvancedOrder.getSupplierName() + "的预付单在待付款状态被取消付款");
        return AjaxResult.getOK();
    }

    /**
     * 钉钉消息通知采购
     * @param purchaseAdvancedOrder
     * @param reason
     */
    private void sendOfferMessage(PurchaseAdvancedOrderVO purchaseAdvancedOrder, String reason) {
        //查询所属销售的钉钉对应信息
        AdminAuthExtend creatorInfo = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), purchaseAdvancedOrder.getCreatorAdminId());

        String title = "【采购预付通知】";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append(reason).append("\n");
        text.append("> ###### 供应商：").append(purchaseAdvancedOrder.getSupplierName()).append("\n");
        text.append("> ###### 预付号：").append(purchaseAdvancedOrder.getId()).append("\n");
        text.append("> ###### 预付金额：").append(purchaseAdvancedOrder.getTotalAmount()).append("\n");
        if (Objects.nonNull(creatorInfo)) {
            logger.info("【采购预付通知】钉钉Id{}" + creatorInfo.getUserId());
            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), creatorInfo.getUserId(), title, text.toString());
            dingTalkMsgSender.sendMessage(dingTalkMsgBO);
        }
    }

    /**
     * 供应商账户信息
     *
     * @param jsonObject
     * @param supplierAccount
     */
    private void supplierAccount(JSONObject jsonObject, SupplierAccount supplierAccount) {
        jsonObject.put("payType", supplierAccount.getPayType());
        if (Objects.nonNull(supplierAccount)) {
            jsonObject.put("accountName", supplierAccount.getAccountName());
            jsonObject.put("accountBank", supplierAccount.getAccountBank());
            jsonObject.put("account", supplierAccount.getAccount());
            jsonObject.put("accountAscription", supplierAccount.getAccountAscription());
        }
    }

    @Override
    public void generatePaymentInfo(PurchaseAdvancedOrder purchaseAdvancedOrder) {

        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setAdditionalId(purchaseAdvancedOrder.getId());
        financeOperatorLog.setType(ADVANCE_TYPE);
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.CREATOR.ordinal());
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        FinanceOperatorLog operatorLog = financeOperatorLogMapper.selectById(financeOperatorLog);

        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setAdditionalId(purchaseAdvancedOrder.getId());
        financePaymentOrder.setType(ADVANCE_TYPE);
        financePaymentOrder.setAmount(purchaseAdvancedOrder.getTotalAmount());
        financePaymentOrder.setStatus(FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        financePaymentOrder.setCreator(operatorLog.getOperator());
        financePaymentOrder.setUpdater(operatorLog.getOperator());
        financePaymentOrder.setApplicationTime(purchaseAdvancedOrder.getCreateTime());
        financePaymentOrder.setSupplierId(purchaseAdvancedOrder.getSupplierId());
        financePaymentOrder.setSupplierName(purchaseAdvancedOrder.getSupplierName());
        financePaymentOrderMapper.insertData(financePaymentOrder);
        logger.info("预付单：{}，生成付款单", purchaseAdvancedOrder.getId());

        //预付单 付款单钉钉审批
        paymentCreateProcessInstance(purchaseAdvancedOrder, financePaymentOrder, operatorLog.getOperator(), operatorLog.getOperatorId());
    }

    /**
     * 预付单付款钉钉审批发起
     *
     * @param purchaseAdvancedOrder
     * @param financePaymentOrder
     */
    private void paymentCreateProcessInstance(PurchaseAdvancedOrder purchaseAdvancedOrder, FinancePaymentOrder financePaymentOrder, String creator, Integer adminId) {

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(adminId);
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(financePaymentOrder.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(2);

        DingdingFormBO paymentObject = new DingdingFormBO();
        paymentObject.setFormName("打款对象");
        paymentObject.setFormValue(purchaseAdvancedOrder.getSupplierName());
        dingForms.add(paymentObject);

        DingdingFormBO originator = new DingdingFormBO();
        originator.setFormName("发起人");
        originator.setFormValue(creator);
        dingForms.add(originator);

        DingdingFormBO sourceType = new DingdingFormBO();
        sourceType.setFormName("来源类型");
        sourceType.setFormValue("预付单");
        dingForms.add(sourceType);

        SupplierReq supplierReq = supplierMapper.selectBill(purchaseAdvancedOrder.getSupplierId());
        DingdingFormBO settlementMethod = new DingdingFormBO();
        settlementMethod.setFormName("结算方式");
        StringBuilder stringBuilder = new StringBuilder();
        if (!ObjectUtils.isEmpty(supplierReq.getSettleType())) {
            if (Objects.equals(supplierReq.getSettleType(), 0)) {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 30天账期")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            } else {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 ")
                        .append(supplierReq.getCustomCycle())
                        .append("天账期 ")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            }
        } else {
            stringBuilder.append("暂无合同");
        }
        DingdingFormBO totalAmountPayable = new DingdingFormBO();
        totalAmountPayable.setFormName("应付总额");
        totalAmountPayable.setFormValue(String.valueOf(purchaseAdvancedOrder.getTotalAmount()));
        dingForms.add(totalAmountPayable);

        DingdingFormBO financeAccountId = new DingdingFormBO();
        financeAccountId.setFormName("来源单号");
        financeAccountId.setFormValue(String.valueOf(purchaseAdvancedOrder.getId()));
        dingForms.add(financeAccountId);

        DingdingFormBO detail = new DingdingFormBO();
        detail.setFormName("单据详情");
        if (SpringContextUtil.isProduct()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS + purchaseAdvancedOrder.getId());
        } else if (SpringContextUtil.isQa()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_QA + purchaseAdvancedOrder.getId());
        } else if (SpringContextUtil.isDev()) {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV + purchaseAdvancedOrder.getId());
        } else {
            detail.setFormValue(Global.PURCHASE_ADVANCED_ORDER_ADDRESS_DEV2 + purchaseAdvancedOrder.getId());
        }
        dingForms.add(detail);

        processInstanceCreateBO.setDingdingForms(dingForms);
        try {
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        } catch (DingdingProcessException e) {
            logger.info("钉钉调用失败:{}", e.getMessage(), e);
            throw new DefaultServiceException(e.getMessage());
        }
    }

    /**
     * 对账单生成付款单
     *
     * @param financeAccountStatement
     */
    @Override
    public void generatePaymentDocument(FinanceAccountStatement financeAccountStatement) {

        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setAdditionalId(financeAccountStatement.getId());
        financePaymentOrder.setType(ACCOUNT_STATEMENT);
        financePaymentOrder.setAmount(financeAccountStatement.getTotalBillAmount().subtract(financeAccountStatement.getWriteOffAmount()));
        //付款审核中状态
        financePaymentOrder.setStatus(FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        financePaymentOrder.setCreator(ObjectUtils.isEmpty(financeAccountStatement.getCreator()) ? " " : financeAccountStatement.getCreator());
        financePaymentOrder.setUpdater(getAdminName());
        financePaymentOrder.setApplicationTime(LocalDateTime.now());
        financePaymentOrder.setSupplierId(financeAccountStatement.getSupplierId());
        financePaymentOrder.setSupplierName(financeAccountStatement.getSupplierName());
        financePaymentOrderMapper.insertData(financePaymentOrder);
        logger.info("对账单：{}，生成付款单", financeAccountStatement.getId());

        //发送钉钉审批
        createProcessInstance(financeAccountStatement, financePaymentOrder);

    }

    @Override
    public void generatePaymentBill(BmsPaymentDocumentVO bmsPaymentDocumentVO) {
        //BMS打款单生成付款单
        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setAdditionalId(Long.valueOf(bmsPaymentDocumentVO.getId()));
        financePaymentOrder.setType(BMS_BILL);
        financePaymentOrder.setAmount(bmsPaymentDocumentVO.getPaymentAmount());
        //付款审核中状态
        financePaymentOrder.setStatus(FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());
        Admin admin = adminMapper.selectByPrimaryKey(bmsPaymentDocumentVO.getCreator());
        financePaymentOrder.setCreator(admin.getRealname());
        financePaymentOrder.setUpdater(getAdminName());
        financePaymentOrder.setApplicationTime(LocalDateTime.now());
        financePaymentOrder.setSupplierId(bmsPaymentDocumentVO.getCarrierId());
        financePaymentOrder.setSupplierName(bmsPaymentDocumentVO.getCarrierName());
        financePaymentOrderMapper.insertData(financePaymentOrder);
        logger.info("BMS打款单：{}，生成付款单", bmsPaymentDocumentVO.getPaymentNo());

        //发送钉钉审批
        createProcessInstanceBill(bmsPaymentDocumentVO,financePaymentOrder);
    }

    /**
     * 对账单付款钉钉审批发起
     *
     * @param financeAccountStatement
     * @param financePaymentOrder
     */
    private void createProcessInstance(FinanceAccountStatement financeAccountStatement, FinancePaymentOrder financePaymentOrder) {

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(financeAccountStatement.getCreatorAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(financePaymentOrder.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(2);

        DingdingFormBO paymentObject = new DingdingFormBO();
        paymentObject.setFormName("打款对象");
        paymentObject.setFormValue(financeAccountStatement.getSupplierName());
        dingForms.add(paymentObject);

        DingdingFormBO originator = new DingdingFormBO();
        originator.setFormName("发起人");
        originator.setFormValue(financeAccountStatement.getCreator());
        dingForms.add(originator);

        DingdingFormBO sourceType = new DingdingFormBO();
        sourceType.setFormName("来源类型");
        sourceType.setFormValue("对账单");
        dingForms.add(sourceType);

        SupplierReq supplierReq = supplierMapper.selectBill(financeAccountStatement.getSupplierId());
        DingdingFormBO settlementMethod = new DingdingFormBO();
        settlementMethod.setFormName("结算方式");
        StringBuilder stringBuilder = new StringBuilder();
        if (!ObjectUtils.isEmpty(supplierReq.getSettleType())) {
            if (Objects.equals(supplierReq.getSettleType(), 0)) {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 30天账期")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            } else {
                stringBuilder.append(SettleFormEnum.PERIOD.getName())
                        .append("结算 ")
                        .append(supplierReq.getCustomCycle())
                        .append("天账期 ")
                        .append(supplierReq.getCreditDays())
                        .append("天打款");
            }
        } else {
            stringBuilder.append("暂无合同");
        }
        DingdingFormBO totalAmountPayable = new DingdingFormBO();
        totalAmountPayable.setFormName("应付总额");
        totalAmountPayable.setFormValue(String.valueOf(financeAccountStatement.getTotalBillAmount().subtract(financeAccountStatement.getWriteOffAmount())));
        dingForms.add(totalAmountPayable);

        DingdingFormBO financeAccountId = new DingdingFormBO();
        financeAccountId.setFormName("来源单号");
        financeAccountId.setFormValue(String.valueOf(financeAccountStatement.getId()));
        dingForms.add(financeAccountId);

        DingdingFormBO detail = new DingdingFormBO();
        detail.setFormName("单据详情");
        if (SpringContextUtil.isProduct()) {
            detail.setFormValue(Global.FINANCE_ACCOUNT_ADDRESS + financeAccountStatement.getId());
        } else if (SpringContextUtil.isQa()) {
            detail.setFormValue(Global.FINANCE_ACCOUNT_ADDRESS_QA + financeAccountStatement.getId());
        } else if (SpringContextUtil.isDev()) {
            detail.setFormValue(Global.FINANCE_ACCOUNT_ADDRESS_DEV + financeAccountStatement.getId());
        } else {
            detail.setFormValue(Global.FINANCE_ACCOUNT_ADDRESS_DEV2 + financeAccountStatement.getId());
        }
        dingForms.add(detail);

        DingdingFormBO purchaseDetailEntity = new DingdingFormBO();
        purchaseDetailEntity.setFormName("金额明细");

        FinanceAccountDetailInput input = new FinanceAccountDetailInput();
        input.setFinanceAccountStatementId(financeAccountStatement.getId());
        List<FinanceAccountSkuVO> financeAccountSkuVOS = financeAccountStatementService.selectDetails(input);

        List<List<FeiShuTableFormEntity>> tableList = Lists.newArrayList();
        for (FinanceAccountSkuVO skuDetail : financeAccountSkuVOS) {
            if(tableList.size() >= 200){
                break;
            }
            List<FeiShuTableFormEntity> rowList = Lists.newArrayList();

            FeiShuTableFormEntity titleForm = new FeiShuTableFormEntity();
            titleForm.setName("商品名称");
            titleForm.setValue(skuDetail.getPdName());
            rowList.add(titleForm);

            FeiShuTableFormEntity specForm = new FeiShuTableFormEntity();
            specForm.setName("规格");
            specForm.setValue(skuDetail.getSkuSpec());
            rowList.add(specForm);

            FeiShuTableFormEntity inForm = new FeiShuTableFormEntity();
            inForm.setName("收货数量");
            inForm.setValue(skuDetail.getReceivedQuantity().toString());
            rowList.add(inForm);

            FeiShuTableFormEntity outForm = new FeiShuTableFormEntity();
            outForm.setName("退货数量");
            outForm.setValue(skuDetail.getReturnQuantity().toString());
            rowList.add(outForm);

            FeiShuTableFormEntity amountForm = new FeiShuTableFormEntity();
            amountForm.setName("总金额");
            amountForm.setValue(skuDetail.getTotalAmount().toString());
            rowList.add(amountForm);
            tableList.add(rowList);
        }
        purchaseDetailEntity.setFormValue(JSON.toJSONString(tableList));
        dingForms.add(purchaseDetailEntity);

        processInstanceCreateBO.setDingdingForms(dingForms);
        try {
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        } catch (DingdingProcessException e) {
            logger.info("钉钉调用失败:{}", e.getMessage(), e);
            throw new DefaultServiceException(e.getMessage());
        }
    }

    /**
     * BMS付款单审批流
     * @param bmsPaymentDocumentVO
     * @param financePaymentOrder
     */
    private void createProcessInstanceBill(BmsPaymentDocumentVO bmsPaymentDocumentVO, FinancePaymentOrder financePaymentOrder) {

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        //审批code config
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.BMS_PAYMENT_ORDER_APPROVAL_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(bmsPaymentDocumentVO.getCreator());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(financePaymentOrder.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(2);

        DingdingFormBO paymentObject = new DingdingFormBO();
        paymentObject.setFormName("打款对象");
        paymentObject.setFormValue(bmsPaymentDocumentVO.getCarrierName());
        dingForms.add(paymentObject);

        DingdingFormBO originator = new DingdingFormBO();
        originator.setFormName("发起人");
        Admin admin = adminMapper.selectByPrimaryKey(bmsPaymentDocumentVO.getCreator());
        originator.setFormValue(admin.getRealname());
        dingForms.add(originator);

        DingdingFormBO sourceType = new DingdingFormBO();
        sourceType.setFormName("来源类型");
        sourceType.setFormValue("打款单");
        dingForms.add(sourceType);

        DingdingFormBO settlementMethod = new DingdingFormBO();
        settlementMethod.setFormName("结算方式");
        settlementMethod.setFormValue("逐笔结算");
        dingForms.add(settlementMethod);

        DingdingFormBO totalAmountPayable = new DingdingFormBO();
        totalAmountPayable.setFormName("应付总额");
        totalAmountPayable.setFormValue(String.valueOf(bmsPaymentDocumentVO.getPaymentAmount()));
        dingForms.add(totalAmountPayable);

        DingdingFormBO financeAccountId = new DingdingFormBO();
        financeAccountId.setFormName("来源单号");
        financeAccountId.setFormValue(bmsPaymentDocumentVO.getPaymentNo());
        dingForms.add(financeAccountId);

        DingdingFormBO detail = new DingdingFormBO();
        detail.setFormName("单据详情");
        if (SpringContextUtil.isProduct()) {
            detail.setFormValue(Global.BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS + bmsPaymentDocumentVO.getId());
        } else if (SpringContextUtil.isQa()) {
            detail.setFormValue(Global.BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_QA + bmsPaymentDocumentVO.getId());
        } else if (SpringContextUtil.isDev()) {
            detail.setFormValue(Global.BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV + bmsPaymentDocumentVO.getId());
        } else {
            detail.setFormValue(Global.BMS_PAYMENT_DOCUMENT_ORDER_ADDRESS_DEV2 + bmsPaymentDocumentVO.getId());
        }
        dingForms.add(detail);

        processInstanceCreateBO.setDingdingForms(dingForms);
        try {
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        } catch (DingdingProcessException e) {
            logger.info("钉钉调用失败:{}", e.getMessage(), e);
            throw new DefaultServiceException(e.getMessage());
        }
    }

    @Override
    public void approvedFinancePaymentOrderAccount(Long bizId, String handlerUserId) {
        logger.info("对账单付款单付款通过" + bizId + handlerUserId);
        updateStatus(bizId, FinancePaymentOrderStatusEnum.WAIT_PAY.ordinal(), FinanceAccountStatementStatusEnum.WAIT_PAY.ordinal(), handlerUserId);
        

    }

    @Override
    public void refuseFinancePaymentOrderAccount(Long bizId, String handlerUserId) {
        logger.info("对账单付款单付款拒绝" + bizId + handlerUserId);
        updateStatus(bizId, FinancePaymentOrderStatusEnum.CANCEL.ordinal(), FinanceAccountStatementStatusEnum.INVALID.ordinal(), handlerUserId);
        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.STATEMENTS.ordinal(), handlerUserId, CommonNumbersEnum.ONE.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());

    }

    @Override
    public void approvedFinancePaymentOrderAdvance(Long bizId, String handlerUserId) {

        logger.info("钉钉付款单通过审核" + bizId);
        //查询付款单数据
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);

        //非付款审核中的付款单 不做处理
        if (!Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal())) {
            return;
        }
        //更改付款单状态
        updatePaymentOrder(bizId, FinancePaymentOrderStatusEnum.WAIT_PAY.ordinal());

        PurchaseAdvancedOrderVO purchaseAdvancedOrderVO = purchaseAdvancedOrderMapper.selectDetail(paymentOrder.getAdditionalId());
        PurchaseAdvancedOrder purchaseAdvancedOrder = new PurchaseAdvancedOrder();
        purchaseAdvancedOrder.setId(purchaseAdvancedOrderVO.getId());
        purchaseAdvancedOrder.setStatus(PurchaseAdvancedOrderStatusEnum.WAIT_PAY.ordinal());
        //查询当前付款人
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin select = adminMapper.select(settlementConfig.getPayer());
        purchaseAdvancedOrder.setCurrentProcessor(select.getRealname());
        purchaseAdvancedOrderMapper.update(purchaseAdvancedOrder);

    }

    @Override
    public void refuseFinancePaymentOrderAdvance(Long bizId, String handlerUserId) {

        //查询付款单数据
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);

        //非付款审核中的付款单 不做处理
        if (!Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal())) {
            return;
        }
        //更改付款单状态
        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setId(bizId);
        financePaymentOrder.setStatus(FinancePaymentOrderStatusEnum.CANCEL.ordinal());
        financePaymentOrder.setDeleteReason(FinancePaymentOrderReasonEnum.PAYMENT_APPROVAL_FAILED.ordinal());
        financePaymentOrder.setApprovalTime(LocalDateTime.now());
        financePaymentOrderMapper.update(financePaymentOrder);

        PurchaseAdvancedOrderVO purchaseAdvancedOrderVO = purchaseAdvancedOrderMapper.selectDetail(paymentOrder.getAdditionalId());
        PurchaseAdvancedOrder purchaseAdvancedOrder = new PurchaseAdvancedOrder();
        purchaseAdvancedOrder.setId(purchaseAdvancedOrderVO.getId());
        purchaseAdvancedOrder.setStatus(PurchaseAdvancedOrderStatusEnum.INVALID.ordinal());
        purchaseAdvancedOrder.setDeleteReason(PurchaseAdvancedOrderReasonEnum.PAYMENT_APPROVAL_FAILED.ordinal());
        purchaseAdvancedOrder.setSupplierId(purchaseAdvancedOrderVO.getSupplierId());
        purchaseAdvancedOrder.setSupplierName(purchaseAdvancedOrderVO.getSupplierName());
        purchaseAdvancedOrder.setType(purchaseAdvancedOrderVO.getType());
        purchaseAdvancedOrderMapper.update(purchaseAdvancedOrder);
        PurchaseAdvancedOrder order = purchaseAdvancedOrderMapper.queryById(paymentOrder.getAdditionalId());
        //预付单中的采购单使其可以被重新使用
        settleFlagReset(purchaseAdvancedOrderVO.getId(), purchaseAdvancedOrderVO.getSupplierId());
        //供应商金额回退
//        supplierPaymentBack(order);

        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.ADVANCE.ordinal(), handlerUserId, CommonNumbersEnum.ONE.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());

    }

    /**
     * 发起预付标识更新
     *
     * @param id
     */
    private void settleFlagReset(Long id, Integer supplierId) {
        PurchaseBindingPrepayment query = new PurchaseBindingPrepayment();
        query.setPurchaseAdvancedOrderId(id);
        List<PurchaseBindingPrepayment> purchaseBindingPrepayments = purchaseBindingPrepaymentMapper.queryAll(query);
        PurchasesPlan updatePlan = new PurchasesPlan();
        for (PurchaseBindingPrepayment prepayment : purchaseBindingPrepayments) {
            List<PurchasesPlanVO> purchasesPlanVOS = purchasesPlanMapper.queryAdvanceAble(prepayment.getPurchaseNo(), supplierId, NumberUtils.INTEGER_ONE);
            for (PurchasesPlanVO plan : purchasesPlanVOS) {
                if (Objects.equals(plan.getSettleFlag(), IN_SETTLE)) {
                    updatePlan.setId(plan.getId());
                    updatePlan.setSettleFlag(UN_SETTLE);
                    purchasesPlanMapper.update(updatePlan);
                }
            }
        }
    }

    /**
     * 关联了采购单的需要回退金额
     *
     * @param purchaseAdvancedOrder
     */
    public void supplierPaymentBack(PurchaseAdvancedOrder purchaseAdvancedOrder) {
        if (!Objects.equals(purchaseAdvancedOrder.getType(), ASSOCIATED)) {
            return;
        }
        PurchaseBindingPrepayment queryBind = new PurchaseBindingPrepayment();
        queryBind.setPurchaseAdvancedOrderId(purchaseAdvancedOrder.getId());
        List<PurchaseBindingPrepayment> purchaseBindingPrepayments = purchaseBindingPrepaymentMapper.queryAll(queryBind);
        if (CollectionUtils.isEmpty(purchaseBindingPrepayments)) {
            return;
        }

        PurchaseSupplierPayment queryPayment = new PurchaseSupplierPayment();
        for (PurchaseBindingPrepayment purchaseBindingPrepayment : purchaseBindingPrepayments) {
            String purchaseNo = purchaseBindingPrepayment.getPurchaseNo();
            Integer supplierId = purchaseAdvancedOrder.getSupplierId();
            BigDecimal advanceAmount = purchaseBindingPrepayment.getAdvanceAmount();
            queryPayment.setPurchaseNo(purchaseNo);
            queryPayment.setSupplierId(supplierId);
            PurchaseSupplierPayment purchaseSupplierPayment = purchaseSupplierPaymentMapper.selectOne(queryPayment);
            if (Objects.isNull(purchaseSupplierPayment)) {
                continue;
            }

            PurchaseSupplierPayment update = new PurchaseSupplierPayment();
            update.setId(purchaseSupplierPayment.getId());
            update.setAdvanceAmount(advanceAmount.negate());
            logger.info(purchaseSupplierPayment.getId() + "预付单付款单拒绝金额" + advanceAmount.negate().toString());
            purchaseSupplierPaymentMapper.updateAmount(update);
            logger.info("预付单：{}审核不通过，供应商：{}，采购单号：{}，回退了预付金额：{}元", purchaseAdvancedOrder.getId(), purchaseAdvancedOrder.getSupplierName(), purchaseNo, advanceAmount);
        }
    }


    /**
     * 采购对账单钉钉付款审批
     *
     * @param bizId
     * @param status
     */
    private void updateStatus(Long bizId, Integer status, Integer state, String handlerUserId) {
        //查询付款单数据
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);

        //非付款审核中的付款单 不做处理
        if (!Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal())) {
            return;
        }

        //更改付款单状态
        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setId(bizId);
        financePaymentOrder.setStatus(status);
        if (Objects.equals(status, FinancePaymentOrderStatusEnum.CANCEL.ordinal())) {
            financePaymentOrder.setDeleteReason(FinancePaymentOrderReasonEnum.PAYMENT_APPROVAL_FAILED.ordinal());
        }
        financePaymentOrder.setApprovalTime(LocalDateTime.now());
        financePaymentOrderMapper.update(financePaymentOrder);

        //更改对账单状态
        FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.select(paymentOrder.getAdditionalId());
        FinanceAccountStatement financeAccount = new FinanceAccountStatementInput();
        financeAccount.setId(paymentOrder.getAdditionalId());
        financeAccount.setStatus(state);
        //查询当前付款人
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin select = adminMapper.select(settlementConfig.getPayer());
        if (Objects.equals(state, FinanceAccountStatementStatusEnum.INVALID.ordinal())) {
            //作废并说明原因
            financeAccount.setDeleteReason(FinanceAccountStatementReasonEnum.PAYMENT_APPROVAL_FAILED.ordinal());
            financeAccount.setCurrentProcessor("--");
        } else {
            financeAccount.setCurrentProcessor(select.getRealname());
        }
        //修改对账单信息
        financeAccountStatementMapper.updateBack(financeAccount);
        Admin admin = null;
        if (!ObjectUtils.isEmpty(handlerUserId)) {
            admin = adminMapper.selectByPrimaryKey(Integer.valueOf(handlerUserId));
        }
        //审批失败的对账单还需要修改数据
        if (Objects.equals(status, FinancePaymentOrderStatusEnum.CANCEL.ordinal())) {
            //对账单审核失败，预付池数据加回(如有）,出库详情关联发票状态失效
            if (financeAccountStatement.getWriteOffAmount().compareTo(BigDecimal.ZERO) > 0) {
                supplierPaymentReset(financeAccountStatement, financeAccountStatement.getWriteOffAmount());
            }
            if (!ObjectUtils.isEmpty(financeAccountStatement.getAdvancedOrderId())) {
                update(financeAccountStatement);
            }
            disassociate(paymentOrder.getAdditionalId());

            //票夹解散
            FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
            financePurchaseInvoiceWallets.setUpdater(ObjectUtils.isEmpty(admin) ? "" : admin.getRealname());
            financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
            financePurchaseInvoiceWallets.setId(financeAccountStatement.getWalletsId());
            financePurchaseInvoiceWalletsMapper.updateInvoiceBack(financePurchaseInvoiceWallets);
            //发票返回可以匹配状态
            purchaseInvoiceMapper.updateByWalletsId(financeAccountStatement.getWalletsId());

            //srm对账单作废
            sendMessage(financeAccountStatement.getId(), MType.SRM_CANCEL_ACCOUNT.name());

            //发送付款审核失败的微信通知给供应商
            sendMessage(financeAccountStatement.getId(), MType.SRM_WECHAT_PAYMENT_FAIL_MESSAGE.name());
            logger.info("发送付款审核失败的微信通知给供应商" + financeAccountStatement.getId());

        } else {
            //审批通过的付款单 需要将票夹已归档
            FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
            financePurchaseInvoiceWallets.setUpdater(ObjectUtils.isEmpty(admin) ? "" : admin.getRealname());
            financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
            financePurchaseInvoiceWallets.setId(financeAccountStatement.getWalletsId());
            financePurchaseInvoiceWallets.setStatus(PurchaseInvoiceEnum.ARCHIVED_STATUS.getId());
            financePurchaseInvoiceWalletsMapper.updateByPrimaryKeySelective(financePurchaseInvoiceWallets);

            //srm对账单待付款
            sendMessage(financeAccountStatement.getId(), MType.SRM_WAIT_MONEY.name());

        }
    }

    /**
     * 取消对账单对应出入库详情中的关联信息
     *
     * @param id
     */
    private void disassociate(Long id) {
        //查询对账单详情列表
        List<FinanceAccountStatementDetail> financeAccountStatementDetails = financeAccountStatementDetailMapper.selectByPrimaryKey(id);
        for (FinanceAccountStatementDetail financeAccountStatementDetail : financeAccountStatementDetails) {
            //取消对账单对应出入库详情中的关联信息
            stockTaskProcessDetailMapper.updateState(financeAccountStatementDetail.getStockTaskProcessDetailId(), CommonNumbersEnum.ZERO.getNumber());
        }
    }


    /**
     * 修改预付单的状态
     *
     * @param financeAccountStatement
     */
    private void update(FinanceAccountStatement financeAccountStatement) {
        //如果对账单关联预付单，修改预付单的状态
        List<String> ids = Arrays.asList(financeAccountStatement.getAdvancedOrderId().split(Global.SEPARATING_SYMBOL));
        PurchaseAdvancedOrder purchaseAdvancedOrder = new PurchaseAdvancedOrder();
        purchaseAdvancedOrder.setState(CommonNumbersEnum.ONE.getNumber());
        for (String i : ids) {
            purchaseAdvancedOrder.setId(Long.valueOf(i));
            purchaseAdvancedOrderMapper.update(purchaseAdvancedOrder);
        }
    }

    @Override
    public void approvedFinancePaymentOrderAccountTask(Long bizId, String handlerUserId) {
        logger.info("对账单付款单审批任务回调通过" + bizId);
        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.STATEMENTS.ordinal(), handlerUserId, CommonNumbersEnum.ZERO.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());

    }

    @Override
    public void approvedFinancePaymentOrderAdvanceTask(Long bizId, String handlerUserId) {
        logger.info("预付单付款单审批任务回调通过" + bizId);
        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.ADVANCE.ordinal(), handlerUserId, CommonNumbersEnum.ZERO.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());

    }

    @Override
    public void refuseFinancePaymentOrderAccountTask(Long bizId, String handlerUserId) {
        logger.info("对账单付款单审批任务回调拒绝" + bizId);
        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.STATEMENTS.ordinal(), handlerUserId, CommonNumbersEnum.ONE.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());
    }

    @Override
    public void refuseFinancePaymentOrderAdvanceTask(Long bizId, String handlerUserId) {
        logger.info("预付单付款单审批任务回调拒绝" + bizId);
        addFinanceOperatorLog(bizId, FinancePurchaseTypeEnum.ADVANCE.ordinal(), handlerUserId, CommonNumbersEnum.ONE.getNumber(), FinancePersonnelType.PAYMENT_REVIEWER.ordinal());
    }

    /**
     * 插入操作记录
     *
     * @param bizId
     * @param type
     * @param handlerUserId
     * @param operationResults
     * @param personnelType
     */
    private void addFinanceOperatorLog(Long bizId, Integer type, String handlerUserId, Integer operationResults, Integer personnelType) {
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setAdditionalId(paymentOrder.getAdditionalId());
        financeOperatorLog.setType(type);
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(personnelType);
        financeOperatorLog.setOperatorTime(LocalDateTime.now());
        //查询创建人
        Admin admin = adminMapper.selectByAid(Integer.valueOf(handlerUserId));
        if (!ObjectUtils.isEmpty(admin)) {
            financeOperatorLog.setOperationResults(operationResults);
            financeOperatorLog.setOperatorId(admin.getAdminId());
            financeOperatorLog.setOperator(admin.getRealname());
            financeOperatorLogMapper.insertSelective(financeOperatorLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvedFinancePaymentOrderBms(Long bizId, String handlerUserId) {

        logger.info("钉钉BMS打款单付款单通过审核" + bizId);

        //查询付款单数据
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);

        //非付款审核中的付款单 不做处理
        if (!Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal())) {
            return;
        }

        //付款单通过人
        Admin admin = adminMapper.selectByPrimaryKey(Integer.valueOf(handlerUserId));

        //更改付款单状态
        updatePaymentOrder(bizId, FinancePaymentOrderStatusEnum.WAIT_PAY.ordinal());

        PaymentDocQueryRequest paymentDocQueryRequest = new PaymentDocQueryRequest();
        paymentDocQueryRequest.setId(paymentOrder.getAdditionalId());
        paymentDocQueryRequest.setHasQueryInvoice(true);
        PaymentDocDetailResponse response = bmsServiceClientFacade.queryPaymentDocDetail(paymentDocQueryRequest);
        if(CollectionUtils.isEmpty(response.getPaymentDocInvoiceRelList())){
            throw new ProviderException("根据打款单找不到对应发票:打款单id:" + response.getId());
        }
        Long walletsId = response.getPaymentDocInvoiceRelList().get(0).getWalletsId();
        if(Objects.isNull(walletsId)){
            throw new ProviderException("对应发票:没有票夹信息" + response.getId());
        }

//        //查询打款单信息
//        BmsPaymentDocumentVO bmsPaymentDocumentVO = bmsPaymentDocumentService.queryBmsPaymentDocumentById(paymentOrder.getAdditionalId().intValue());
//        //查询发票信息(2022-11-24改为一对多)
//        List<Integer> invoiceIds = bmsPaymentInvoiceRelMapper.selectByPaymentId(bmsPaymentDocumentVO.getId());
//        List<PurchaseInvoiceVO> purchaseInvoiceVOList = purchaseInvoiceMapper.selectByIdList(invoiceIds);
//        if (CollectionUtils.isEmpty(purchaseInvoiceVOList)){
//            throw new DefaultServiceException("根据打款单找不到对应发票:打款单id:" + bmsPaymentDocumentVO.getId());
//        }
        //将票夹归档
        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
        financePurchaseInvoiceWallets.setUpdater(admin.getRealname());
        financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
        financePurchaseInvoiceWallets.setStatus(PurchaseInvoiceEnum.ARCHIVED_STATUS.getId());
        financePurchaseInvoiceWallets.setId(walletsId);
        financePurchaseInvoiceWalletsMapper.updateByPrimaryKeySelective(financePurchaseInvoiceWallets);
        logger.info("钉钉BMS打款单付款单票夹通过变为已归档,票夹id：" + walletsId);

        //发mq表示BMS打款单审批流通过
        messageTemplate(paymentOrder.getAdditionalId(), MType.BMS_PAYMENT_ORDER_APPROVAL_SUCCESS.name());

    }

    @Override
    public void refuseFinancePaymentOrderBms(Long bizId, String handlerUserId) {

        logger.info("钉钉BMS打款单付款单审核拒绝，财务付款单FinancePaymentOrderId:{}",bizId);

        //查询付款单数据
        FinancePaymentOrder paymentOrder = financePaymentOrderMapper.queryById(bizId);

        //非付款审核中的付款单 不做处理
        if (!Objects.equals(paymentOrder.getStatus(), FinancePaymentOrderStatusEnum.PAYMENT_UNDER_REVIEW.ordinal())) {
            return;
        }

        //更改付款单状态
        updatePaymentOrder(bizId, FinancePaymentOrderStatusEnum.CANCEL.ordinal());

        PaymentDocQueryRequest paymentDocQueryRequest = new PaymentDocQueryRequest();
        paymentDocQueryRequest.setId(paymentOrder.getAdditionalId());
        paymentDocQueryRequest.setHasQueryInvoice(true);
        PaymentDocDetailResponse response = bmsServiceClientFacade.queryPaymentDocDetail(paymentDocQueryRequest);
        List<Integer> invoiceIds = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(response.getPaymentDocInvoiceRelList())){
            invoiceIds =  response.getPaymentDocInvoiceRelList().stream().map(PaymentDocInvoiceResponse::getId).collect(Collectors.toList());
        }
//        //查询打款单信息
//        BmsPaymentDocumentVO bmsPaymentDocumentVO = bmsPaymentDocumentService.queryBmsPaymentDocumentById(paymentOrder.getAdditionalId().intValue());
//        logger.info("钉钉BMS打款单付款单审核拒绝,BMS结算打款单id" + paymentOrder.getAdditionalId());
//        //查询发票信息(2022-11-24改为一对多)
//        List<Integer> invoiceIds = bmsPaymentInvoiceRelMapper.selectByPaymentId(bmsPaymentDocumentVO.getId());

        //BMS打款单信息接口
        if (!CollectionUtils.isEmpty(invoiceIds)){
            invoiceIds.forEach(i->{
                PurchaseInvoiceVO purchaseInvoiceVO = purchaseInvoiceMapper.select(i);
                //将票夹打散 发票退回可匹配提
                FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
                financePurchaseInvoiceWallets.setUpdater(getAdminName());
                financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
                financePurchaseInvoiceWallets.setId(purchaseInvoiceVO.getWalletsId());
                financePurchaseInvoiceWalletsMapper.updateInvoiceBack(financePurchaseInvoiceWallets);
                logger.info("钉钉BMS打款单付款单票夹驳回,票夹id：" + purchaseInvoiceVO.getWalletsId());
                //发票返回可以匹配状态
                purchaseInvoiceMapper.updateByWalletsId(purchaseInvoiceVO.getWalletsId());
            });
        }

        //发mq表示BMS打款单审批流拒绝
        messageTemplate(paymentOrder.getAdditionalId(), MType.BMS_PAYMENT_ORDER_APPROVAL_FAIL.name());
    }

    /**
     * 改变付款单状态
     * @param bizId
     * @param status
     */
    private void updatePaymentOrder(Long bizId, Integer status) {
        //更改付款单状态
        FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
        financePaymentOrder.setId(bizId);
        financePaymentOrder.setStatus(status);
        financePaymentOrder.setApprovalTime(LocalDateTime.now());
        financePaymentOrderMapper.update(financePaymentOrder);
    }

    /**
     * 审批流结果传消息给BMS
     * @param id
     * @param key
     */
    private void messageTemplate(Long id, String key) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("id", id);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    @Override
    public void initializationSupplierMessage() {
        //查询付款单信息，根据对账单还是付款单同步信息
        //预付单的付款单
        List<FinancePaymentOrderVO> advanceList = financePaymentOrderMapper.queryData(ADVANCE_TYPE);
        for (FinancePaymentOrderVO financePaymentOrderVO : advanceList) {
            if (ObjectUtils.isEmpty(financePaymentOrderVO.getSupplierId()) || ObjectUtils.isEmpty(financePaymentOrderVO.getSupplierName())) {
                continue;
            }
            //将预付单的发票销售方数据插入付款单表
            FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
            financePaymentOrder.setId(financePaymentOrderVO.getId());
            financePaymentOrder.setSupplierName(financePaymentOrderVO.getSupplierName());
            financePaymentOrder.setSupplierId(financePaymentOrderVO.getSupplierId());
            financePaymentOrderMapper.update(financePaymentOrder);
        }
        List<FinancePaymentOrderVO> accountList = financePaymentOrderMapper.queryDataOne(ACCOUNT_STATEMENT);
        for (FinancePaymentOrderVO financePaymentOrderVO : accountList) {
            if (ObjectUtils.isEmpty(financePaymentOrderVO.getSupplierId()) || ObjectUtils.isEmpty(financePaymentOrderVO.getSupplierName())) {
                continue;
            }
            //将对账单的发票销售方数据插入付款单表
            FinancePaymentOrder financePaymentOrder = new FinancePaymentOrder();
            financePaymentOrder.setId(financePaymentOrderVO.getId());
            financePaymentOrder.setSupplierName(financePaymentOrderVO.getSupplierName());
            financePaymentOrder.setSupplierId(financePaymentOrderVO.getSupplierId());
            financePaymentOrderMapper.update(financePaymentOrder);
        }
    }
}
