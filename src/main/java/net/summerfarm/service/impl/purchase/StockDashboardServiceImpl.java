package net.summerfarm.service.impl.purchase;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.CurrencyStatusEnum;
import net.summerfarm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.mapper.offline.StockDashboardFutureMapper;
import net.summerfarm.mapper.offline.StockDashboardHistoryMapper;
import net.summerfarm.mapper.offline.WarehouseEstimatedConsumptionMapper;
import net.summerfarm.model.DTO.plan.SkuImportDTO;
import net.summerfarm.model.DTO.purchase.DashboardCalculateDTO;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.offline.StockDashboardFuture;
import net.summerfarm.model.domain.offline.StockDashboardHistory;
import net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import net.summerfarm.model.vo.StockHistorySalesVO;
import net.summerfarm.model.vo.purchase.StockDashboardFutureVO;
import net.summerfarm.model.vo.purchase.StockDashboardHistoryVO;
import net.summerfarm.module.scp.common.enums.StockDashboardEnums;
import net.summerfarm.module.scp.common.util.StockDashboardUtil;
import net.summerfarm.service.ProductLabelValueService;
import net.summerfarm.service.ProductStockService;
import net.summerfarm.service.purchase.StockDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StockDashboardServiceImpl extends BaseService implements StockDashboardService {

    @Autowired
    private InventoryMapper inventoryMapper;
    @Autowired
    private StockDashboardFutureMapper dashboardFutureMapper;
    @Autowired
    private StockDashboardHistoryMapper dashboardHistoryMapper;
    @Autowired
    private WarehouseEstimatedConsumptionMapper estimatedConsumptionMapper;
    @Autowired
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Autowired
    ProductStockService productStockService;

    @Autowired
    ConversionSkuConfigMapper conversionSkuConfigMapper;

    @Autowired
    ProductLabelValueService productLabelValueService;

    @Autowired
    PurchasesPlanMapper purchasesPlanMapper;

    @Autowired
    RedisTemplate redisTemplate;


    @Override
    public AjaxResult<List<StockDashboardHistoryVO>> queryDashboardHistory(StockDashboardQueryInput queryInput) {
        List<StockDashboardHistoryVO> dashboardHistoryVOS = Lists.newArrayList();
        List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(Lists.newArrayList(queryInput.getSkuId()));
        if (CollectionUtil.isEmpty(skuBaseInfoDTOS)) {
            return AjaxResult.getErrorWithMsg("未查到该SKU信息");
        }

        List<StockDashboardHistory> historyList = dashboardHistoryMapper.selectList(queryInput);
        if (CollectionUtil.isNotEmpty(historyList)) {
            dashboardHistoryVOS = historyList.stream().map(
                    his -> {
                        StockDashboardHistoryVO historyVO = new StockDashboardHistoryVO();
                        historyVO.setId(his.getId());
                        historyVO.setPdId(his.getPdId());
                        historyVO.setSkuId(his.getSkuId());
                        historyVO.setWarehouseNo(his.getWarehouseNo());
                        historyVO.setViewDate(his.getViewDate());
                        historyVO.setConsumption(his.getConsumption());
                        historyVO.setSalesQuantity(his.getSalesQuantity());
                        historyVO.setTransferOutQuantity(his.getTransferOutQuantity());
                        historyVO.setInitQuantity(his.getInitQuantity());
                        historyVO.setEnabledQuantity(his.getEnabledQuantity());
                        historyVO.setOnWayQuantity(his.getOnWayQuantity());
                        historyVO.setTransferInQuantity(his.getTransferInQuantity());
                        historyVO.setOnWayOrderQuantity(his.getOnWayOrderQuantity() == null ? 0 : his.getOnWayOrderQuantity());
                        historyVO.setTerminalEnabledQuantity(his.getTerminalEnabledQuantity() == null ? 0 : his.getTerminalEnabledQuantity());
                        historyVO.setTimingDeliveryOutQuantity(his.getTimingDeliveryOutQuantity() == null ? 0 : his.getTimingDeliveryOutQuantity().intValue());
                        return historyVO;
                    }
            ).collect(Collectors.toList());
        } else {
            SkuBaseInfoDTO skuBaseInfoDTO = skuBaseInfoDTOS.get(0);
            for (LocalDateTime startTime = queryInput.getStartDate().with(LocalTime.MIN); startTime.isBefore(queryInput.getEndDate()) || startTime.isEqual(queryInput.getEndDate()); startTime = startTime.plusDays(1)) {
                StockDashboardHistoryVO historyVO = new StockDashboardHistoryVO();
//            historyVO.setId(his.getId());
                historyVO.setPdId(skuBaseInfoDTO.getPdId().intValue());
                historyVO.setSkuId(skuBaseInfoDTO.getSku());
                historyVO.setWarehouseNo(queryInput.getWarehouseNo());
                historyVO.setViewDate(startTime);
                historyVO.setConsumption(0);
                historyVO.setSalesQuantity(0);
                historyVO.setTransferOutQuantity(0);
                historyVO.setInitQuantity(0);
                historyVO.setEnabledQuantity(0);
                historyVO.setOnWayQuantity(0);
                historyVO.setTransferInQuantity(0);
                historyVO.setOnWayOrderQuantity(0);
                historyVO.setTerminalEnabledQuantity(0);
                historyVO.setTimingDeliveryOutQuantity(0);
                dashboardHistoryVOS.add(historyVO);
            }
        }
        return AjaxResult.getOK(dashboardHistoryVOS);
    }

    @Override
    public List<StockHistorySalesVO> queryStockHistorySalesHistory(StockDashboardQueryInput queryInput) {
        List<StockHistorySalesVO> result = Lists.newArrayList();
        List<StockDashboardHistory> historyList = dashboardHistoryMapper.selectList(queryInput);
        if (!CollectionUtils.isEmpty(historyList)) {
            Map<String, List<StockDashboardHistory>> dataMap = historyList.stream().collect(Collectors.groupingBy(StockDashboardHistory::getSkuId));
            dataMap.forEach((sku, dataList) -> {
                StockHistorySalesVO vo = new StockHistorySalesVO();
                vo.setSku(sku);
                vo.setWarehouseNo(queryInput.getWarehouseNo());
                vo.setHistorySalesSeven(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(1)));
                vo.setHistorySalesSix(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(2)));
                vo.setHistorySalesFive(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(3)));
                vo.setHistorySalesFour(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(4)));
                vo.setHistorySalesThree(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(5)));
                vo.setHistorySalesTwo(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(6)));
                vo.setHistorySalesOne(getSalesByDataList(dataList, LocalDateTime.now().with(LocalTime.MIN).minusDays(7)));
                result.add(vo);

            });
        }
        return result;

    }

    private BigDecimal getSalesByDataList(List<StockDashboardHistory> dataList, LocalDateTime historyDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String paramTime = historyDate.format(formatter);
        for (StockDashboardHistory data : dataList) {
            String dataTime = data.getViewDate().format(formatter);
            if (paramTime.equals(dataTime)) {
                return data.getOrderSalesQuantity();
            }
        }
        return null;
    }


    @Override
    public AjaxResult<List<StockDashboardFutureVO>> queryStockDashboardFuture(StockDashboardQueryInput queryInput) {
        List<StockDashboardFutureVO> futureVOS = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(Lists.newArrayList(queryInput.getSkuId()));
        if (CollectionUtil.isEmpty(skuBaseInfoDTOS)) {
            return AjaxResult.getErrorWithMsg("未查到该SKU信息");
        }
        if (!Global.FALSE_FLAG.equals(queryInput.getCheckDataFlag())) {
            List<String> tableNames = Lists.newArrayList(DataSynchronizationInformationEnum.STOCK_DASHBOARD_HISTORY.getTableName(),
                    DataSynchronizationInformationEnum.STOCK_DASHBOARD_FUTURE.getTableName(), DataSynchronizationInformationEnum.WAREHOUSE_ESTIMATED_CONSUMPTION.getTableName());
            List<DataSynchronizationInformation> informations = dataSynchronizationInformationMapper.selectByTableNames(tableNames, getDateKey(now.minusDays(1)));
            if (informations.size() != tableNames.size()) {
                return AjaxResult.getErrorWithMsg("数据未生成,请联系管理员");
            }
        }
        LocalDateTime startDateTime = LocalDateTime.now().minusDays(1).with(LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
        StockDashboardQueryInput historyQueryInput = new StockDashboardQueryInput();
        historyQueryInput.setSkuId(queryInput.getSkuId());
        historyQueryInput.setWarehouseNo(queryInput.getWarehouseNo());
        historyQueryInput.setStartDate(startDateTime);
        historyQueryInput.setEndDate(endDateTime);
        List<StockDashboardHistory> historyList = dashboardHistoryMapper.selectList(historyQueryInput);
        BigDecimal terminalEnabledQuantity = CollectionUtil.isEmpty(historyList) ? BigDecimal.ZERO : new BigDecimal(historyList.get(0).getTerminalEnabledQuantity());
        // 2.查出未来数据
        List<StockDashboardFuture> stockDashboardFutures = dashboardFutureMapper.selectList(queryInput);
        if (CollectionUtil.isEmpty(stockDashboardFutures)) {
            initEmptyData(futureVOS, skuBaseInfoDTOS, startDateTime, endDateTime, historyQueryInput);
            return AjaxResult.getOK(futureVOS);
        }
        // 3.查出未来消耗量数据
        List<WarehouseEstimatedConsumption> estimatedConsumptions = estimatedConsumptionMapper.selectList(queryInput);
        if (CollectionUtil.isEmpty(estimatedConsumptions)) {
            initEmptyData(futureVOS, skuBaseInfoDTOS, startDateTime, endDateTime, historyQueryInput);
            return AjaxResult.getOK(futureVOS);
        }
        Map<Integer, WarehouseEstimatedConsumption> estimatedMap = estimatedConsumptions.stream().collect(Collectors.toMap(estimated -> getDateKey(estimated.getViewDate()), Function.identity(), (o, n) -> o));
        List<BigDecimal> estimatedDemandQuantity = null;
        if (queryInput.getLabelSku() == null) {
            //校验商品是否打标
            boolean replenishmentLabel = productLabelValueService.isReplenishmentLabel(queryInput.getSkuId());
            if (replenishmentLabel) {
                //查询所有小规格的预估需求量
                estimatedDemandQuantity = getEstimatedDemandQuantity(queryInput);
            }
        }
        // 4.计算期初库存及状态
        List<DashboardCalculateDTO> calculateDTOS = Lists.newArrayList();
        for (int i = 0; i < stockDashboardFutures.size(); i++) {
            StockDashboardFuture future = stockDashboardFutures.get(i);
            WarehouseEstimatedConsumption estimated = estimatedMap.containsKey(getDateKey(future.getViewDate()))
                    ? estimatedMap.get(getDateKey(future.getViewDate())) : null;
            DashboardCalculateDTO dto = new DashboardCalculateDTO();
            dto.setId(future.getId());
            dto.setPdId(future.getPdId());
            dto.setSkuId(future.getSkuId());
            dto.setWarehouseNo(future.getWarehouseNo());
            dto.setViewDate(future.getViewDate());
            dto.setOnWayQuantity(future.getOnWayQuantity());
            dto.setTransferInQuantity(future.getTransferInQuantity());
            dto.setOnWayOrderQuantity(future.getOnWayOrderQuantity() == null ? 0 : future.getOnWayOrderQuantity());
            if (i == 0) {
                dto.setQuantity(terminalEnabledQuantity);
            }
            dto.setEstimatedDemandQuantity(CollectionUtils.isEmpty(estimatedDemandQuantity) ? BigDecimal.ZERO : estimatedDemandQuantity.get(i));
            dto.setForecastSales(estimated == null ? BigDecimal.ZERO : estimated.getForecastSales());
            dto.setForecastTransferOut(estimated == null ? BigDecimal.ZERO : estimated.getForecastTransferOut());
            dto.setForecastConsumption(estimated == null || estimated.getForecastConsumption() == null ? BigDecimal.ZERO : estimated.getForecastConsumption().add(dto.getEstimatedDemandQuantity()));
            dto.setPoOnWayQuantity(future.getPoOnWayQuantity());
            dto.setTransferOrderInQuantity(future.getTransferOrderInQuantity());
            calculateDTOS.add(dto);
        }
        StockDashboardUtil.completeStockViewDataList(calculateDTOS);
        // 5.封装返回结果
        for (DashboardCalculateDTO calculateDTO : calculateDTOS) {
            StockDashboardFutureVO futureVO = new StockDashboardFutureVO();
            futureVO.setId(calculateDTO.getId());
            futureVO.setPdId(calculateDTO.getPdId());
            futureVO.setSkuId(calculateDTO.getSkuId());
            futureVO.setWarehouseNo(calculateDTO.getWarehouseNo());
            futureVO.setViewDate(calculateDTO.getViewDate());
            futureVO.setInitQuantity(calculateDTO.getInitQuantity());
            futureVO.setOnWayOrderQuantity(calculateDTO.getOnWayOrderQuantity());
            futureVO.setStockViewStatus(calculateDTO.getStatus());
            futureVO.setEnabledQuantity(calculateDTO.getEnabledQuantity());
            futureVO.setOnWayQuantity(calculateDTO.getOnWayQuantity());
            futureVO.setTransferInQuantity(calculateDTO.getTransferInQuantity());
            futureVO.setForecastSales(calculateDTO.getForecastSales());
            futureVO.setForecastTransferOut(calculateDTO.getForecastTransferOut());
            futureVO.setForecastConsumption(calculateDTO.getForecastConsumption());
            futureVO.setEstimatedDemandQuantity(calculateDTO.getEstimatedDemandQuantity());
            futureVO.setPoOnWayQuantity(calculateDTO.getPoOnWayQuantity());
            futureVO.setTransferOrderInQuantity(calculateDTO.getTransferOrderInQuantity());
            futureVOS.add(futureVO);
        }
        return AjaxResult.getOK(futureVOS);
    }


    private Integer getDateKey(LocalDateTime viewDate) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT);
        return Integer.valueOf(viewDate.format(dateTimeFormatter));
    }

    /**
     * 获取每天的预估需求量
     *
     * @return
     */
    @Override
    public List<BigDecimal> getEstimatedDemandQuantity(StockDashboardQueryInput input) {
        ConversionSkuConfig conversionSkuConfig = new ConversionSkuConfig();
        conversionSkuConfig.setOutSku(input.getSkuId());
        conversionSkuConfig.setStatus(CurrencyStatusEnum.EFFECTIVE.ordinal());
        conversionSkuConfig.setWarehouseNo(1);
        List<ConversionSkuConfig> conversionSkuConfigs = conversionSkuConfigMapper.selectConfig(conversionSkuConfig);
        if (CollectionUtils.isEmpty(conversionSkuConfigs)) {
            return null;
        }
        List<BigDecimal> estimatedDemandQuantityList = Lists.newArrayList();
        List<BigDecimal> estimatedDemandExactQuantityList = Lists.newArrayList();
        //查找转入小规则sku的预估需求
        for (ConversionSkuConfig o : conversionSkuConfigs) {
            if (StringUtils.isBlank(o.getRates())) {
                continue;
            }
            StockDashboardQueryInput stockDashboardQueryInput = new StockDashboardQueryInput();
            stockDashboardQueryInput.setStartDate(input.getStartDate());
            stockDashboardQueryInput.setEndDate(input.getEndDate());
            stockDashboardQueryInput.setSkuId(o.getInSku());
            stockDashboardQueryInput.setWarehouseNo(input.getWarehouseNo());
            stockDashboardQueryInput.setLabelSku(true);
            AjaxResult<List<StockDashboardFutureVO>> ajaxResult = queryStockDashboardFuture(stockDashboardQueryInput);
            List<StockDashboardFutureVO> stockDashboardFutureVOList = ajaxResult.getData();
            if (CollectionUtils.isEmpty(stockDashboardFutureVOList)) {
                continue;
            }
            for (int i = 0; i < stockDashboardFutureVOList.size(); i++) {
                StockDashboardFutureVO stockDashboardFutureVO = stockDashboardFutureVOList.get(i);
                String[] split = o.getRates().split(":");
                BigDecimal divide = new BigDecimal(split[1]).divide(new BigDecimal(split[0]));
                BigDecimal requirement = (stockDashboardFutureVO.getForecastConsumption().subtract(stockDashboardFutureVO.getInitQuantity())).divide(divide, 8, RoundingMode.HALF_UP);
                if (requirement.compareTo(BigDecimal.ZERO) < 0) {
                    requirement = BigDecimal.valueOf(0);
                }
                if (i >= estimatedDemandExactQuantityList.size()) {
                    estimatedDemandExactQuantityList.add(i, requirement);
                } else {
                    BigDecimal now = estimatedDemandExactQuantityList.get(i);
                    now = now.add(requirement);
                    estimatedDemandExactQuantityList.set(i, now);
                }
            }
        }
        for (BigDecimal num : estimatedDemandExactQuantityList) {
            estimatedDemandQuantityList.add(num.setScale(2, RoundingMode.HALF_UP));
        }
        return estimatedDemandQuantityList;
    }

    @Override
    public Boolean isViewDayExists(SkuImportDTO skuImportDTO) {
        StockDashboardQueryInput stockDashboardQueryInput = new StockDashboardQueryInput();
        stockDashboardQueryInput.setStartDate(skuImportDTO.getViewDay().atStartOfDay());
        stockDashboardQueryInput.setWarehouseNo(skuImportDTO.getWarehouseNo());
        stockDashboardQueryInput.setSkuId(skuImportDTO.getSku());
        stockDashboardQueryInput.setEndDate(skuImportDTO.getViewDay().plusDays(1).atStartOfDay());
        if (skuImportDTO.getViewDay().isBefore(LocalDate.now())) {
            List<StockDashboardHistory> stockDashboardHistories = dashboardHistoryMapper.selectList(stockDashboardQueryInput);
            if (CollectionUtils.isEmpty(stockDashboardHistories)) {
                return false;
            }
        } else {
            List<WarehouseEstimatedConsumption> warehouseEstimatedConsumptions = estimatedConsumptionMapper.selectList(stockDashboardQueryInput);
            List<StockDashboardFuture> stockDashboardFutures = dashboardFutureMapper.selectList(stockDashboardQueryInput);
            if (CollectionUtils.isEmpty(stockDashboardFutures) || CollectionUtils.isEmpty(warehouseEstimatedConsumptions)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 伪造空结果集，兼容数无数据情况
     *
     * @param futureVOS
     * @param skuBaseInfoDTOS
     * @param startDateTime
     * @param endDateTime
     * @param historyQueryInput
     */
    private void initEmptyData(List<StockDashboardFutureVO> futureVOS, List<SkuBaseInfoDTO> skuBaseInfoDTOS, LocalDateTime startDateTime, LocalDateTime endDateTime, StockDashboardQueryInput historyQueryInput) {
        for (LocalDate startDate = startDateTime.toLocalDate(); startDate.isBefore(endDateTime.toLocalDate()) || startDate.isEqual(endDateTime.toLocalDate()); startDate = startDate.plusDays(1)) {
            StockDashboardFutureVO futureVO = new StockDashboardFutureVO();
            futureVO.setId(Long.valueOf(startDate.getDayOfMonth()));
            futureVO.setPdId(skuBaseInfoDTOS.get(0).getPdId().intValue());
            futureVO.setSkuId(historyQueryInput.getSkuId());
            futureVO.setWarehouseNo(historyQueryInput.getWarehouseNo());
            futureVO.setViewDate(startDate.atStartOfDay());
            futureVO.setInitQuantity(BigDecimal.ZERO);
            futureVO.setOnWayOrderQuantity(0);
            futureVO.setStockViewStatus(StockDashboardEnums.StockViewStatus.SELL_OUT.getValue());
            futureVO.setEnabledQuantity(BigDecimal.ZERO);
            futureVO.setOnWayQuantity(0);
            futureVO.setTransferInQuantity(0);
            futureVO.setForecastSales(BigDecimal.ZERO);
            futureVO.setForecastTransferOut(BigDecimal.ZERO);
            futureVO.setForecastConsumption(BigDecimal.ZERO);
            futureVO.setEstimatedDemandQuantity(BigDecimal.ZERO);
            futureVO.setPoOnWayQuantity(BigDecimal.ZERO);
            futureVO.setTransferOrderInQuantity(BigDecimal.ZERO);
            futureVOS.add(futureVO);
        }
    }
}
