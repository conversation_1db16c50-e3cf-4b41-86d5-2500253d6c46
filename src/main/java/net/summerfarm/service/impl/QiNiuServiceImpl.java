package net.summerfarm.service.impl;

import static net.summerfarm.common.exceptions.ErrorCode.SYSTEM_ERROR;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.DownloadUrl;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.util.qiNiu.Auth;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.easyexcel.*;
import net.summerfarm.service.QiNiuService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.TempFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @title: QiNiuServiceImpl
 * @date 2021/12/15 13:48
 */
@Service
public class QiNiuServiceImpl implements QiNiuService {


    private static final Logger logger = LoggerFactory.getLogger(QiNiuService.class);

    @Autowired
    private ConfigMapper configMapper;

    @Override
    public AjaxResult uploadFile(String fileName, Workbook workbook) {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;
        DefaultPutRet putRet = null;
        String suffix = ".xls";
        String[] suffixArr= fileName.split(".");
        if (suffixArr.length > 1) {
            suffix = suffixArr[1];
        }
        File temFile = null;
        try {
            temFile = TempFile.createTempFile(fileName, suffix);
            workbook.write(new FileOutputStream(temFile));
            Auth auth = Auth.create(accessKey, secretKey);
            String upToken = auth.uploadToken(bucket);
            Response response = uploadManager.put(new FileInputStream(temFile), key, upToken, null, null);
            //解析上传成功的结果
            putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            logger.info("文件上传完成:{}, {}", fileName, putRet);
        } catch (Exception e) {
            logger.error("文件上传失败:{}", fileName, e);
            return AjaxResult.getError(e.getMessage() + ".异常导致文件上传不成功:" + fileName);
        } finally {
            if (temFile != null && temFile.exists() && temFile.isFile()) {
                temFile.delete();
            }
        }
        return AjaxResult.getOK(putRet);
    }

    @Override
    public AjaxResult uploadZip(Map<String, Workbook> workbooks, String fileName) throws IOException{

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        //压缩文件
        try {
            for (String name : workbooks.keySet()) {
                Workbook workbook = workbooks.get(name);
                ZipEntry entry = new ZipEntry(name);
                zos.putNextEntry(entry);
                workbook.write(zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return AjaxResult.getErrorWithMsg("无文件信息");
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, key, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
        }
        //解析上传成功的结果
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult delete(String fileName) {
        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;
        com.qiniu.util.Auth auth = com.qiniu.util.Auth.create(accessKey, secretKey);
        BucketManager bucketManager = new BucketManager(auth, configuration);
        try {
            bucketManager.delete(bucket, fileName);
        } catch (QiniuException ex) {
            //如果遇到异常，说明删除失败
            logger.info("七牛云删除失败");
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult uploadZipCashSettlementDocumentExcel(Map<String, CashSettlementDocumentExcelInput> map, String fileName) throws IOException {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        //压缩文件
        try {
            for (Map.Entry<String, CashSettlementDocumentExcelInput> entry : map.entrySet()) {
                //文件名
                String name = entry.getKey();
                //值
                CashSettlementDocumentExcelInput value = entry.getValue();
                //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
                ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
                //构建一个sheet页
                WriteSheet firstSheet = EasyExcel.writerSheet("销售订单主表").build();
                //构建excel表头信息
                WriteTable writeTableOne = EasyExcel.writerTable(0).head(CashSettlementDocumentExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDocumentExcels(), firstSheet, writeTableOne);
                //构建一个sheet页
                WriteSheet twoSheet = EasyExcel.writerSheet("销售订单子表").build();
                WriteTable writeTableTwo = EasyExcel.writerTable(1).head(CashSettlementDocumentDetailExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDocumentDetailExcels(), twoSheet, writeTableTwo);

                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                ZipEntry zipEntry = new ZipEntry(name);
                zos.putNextEntry(zipEntry);
                workbook.write(zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return AjaxResult.getErrorWithMsg("无文件信息");
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, key, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
        }
        //解析上传成功的结果
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult uploadZipCashSettlementExcel(Map<String, CashSettlementExcelInput> map, String fileName) throws IOException {
        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        //压缩文件
        try {
            for (Map.Entry<String, CashSettlementExcelInput> entry : map.entrySet()) {
                //文件名
                String name = entry.getKey();
                //值
                CashSettlementExcelInput value = entry.getValue();
                //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
                ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
                //构建一个sheet页
                WriteSheet firstSheet = EasyExcel.writerSheet("应收事项主表").build();
                WriteTable writeTableOne = EasyExcel.writerTable(0).head(CashSettlementDocumentExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementMainTableExcels(), firstSheet, writeTableOne);
                //构建一个sheet页
                WriteSheet twoSheet = EasyExcel.writerSheet("应收事项子表").build();
                WriteTable writeTableTwo = EasyExcel.writerTable(0).head(CashSettlementDocumentExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementMainSubTableExcels(), twoSheet, writeTableTwo);

                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                ZipEntry zipEntry = new ZipEntry(name);
                zos.putNextEntry(zipEntry);
                workbook.write(zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return AjaxResult.getErrorWithMsg("无文件信息");
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, key, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
        }
        //解析上传成功的结果
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult uploadZipCashSettlementDetails(Map<String, CashSettlementDetailsExcelInput> map, String fileName) throws IOException {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        //压缩文件
        try {
            for (Map.Entry<String, CashSettlementDetailsExcelInput> entry : map.entrySet()) {
                //文件名
                String name = entry.getKey();
                //值
                CashSettlementDetailsExcelInput value = entry.getValue();
                //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
                ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
                //构建一个sheet页
                WriteSheet firstSheet = EasyExcel.writerSheet("概况").build();
                WriteTable writeTableOne = EasyExcel.writerTable(0).head(CashSettlementDetailsSurveyExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDetailsSurveyExcels(), firstSheet, writeTableOne);
                //构建一个sheet页
                WriteSheet twoSheet = EasyExcel.writerSheet("本期确认收入的订单明细").build();
                WriteTable writeTableTwo = EasyExcel.writerTable(0).head(CashSettlementDetailsConfirmedExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDetailsConfirmedExcels(), twoSheet, writeTableTwo);

                WriteSheet threeSheet = EasyExcel.writerSheet("本期已到货售后完成的订单明细").build();
                WriteTable writeTableThree = EasyExcel.writerTable(0).head(CashSettlementDetailsAfterSaleOrderExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDetailsAfterSaleOrderExcels(), threeSheet, writeTableThree);

                WriteSheet fourSheet = EasyExcel.writerSheet("截至本期末已收款未退款未送达订单明细").build();
                WriteTable writeTableFour = EasyExcel.writerTable(0).head(CashSettlementDetailsConUnfirmedExcel.class).needHead(Boolean.TRUE).build();
                excelWriter.write(value.getCashSettlementDetailsConUnfirmedExcels(), fourSheet, writeTableFour);

                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                ZipEntry zipEntry = new ZipEntry(name);
                zos.putNextEntry(zipEntry);
                workbook.write(zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return AjaxResult.getErrorWithMsg("无文件信息");
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, key, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
        }
        //解析上传成功的结果
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult download(String key) {
        if (StringUtils.isBlank(key)) {
            return AjaxResult.getErrorWithMsg("下载文件所需的key缺失");
        }
        DownloadUrl url = new DownloadUrl(BaseConstant.DOWNLOAD_DOMAIN, true, key);
        com.qiniu.util.Auth auth = com.qiniu.util.Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        try {
            //自定义链接过期时间
            String urlStr = url.buildURL(auth, 3600L);
            return AjaxResult.getOK(urlStr);
        } catch (Exception e) {
            throw new BizException(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage(), e);
        }
    }

    @Override
    public AjaxResult uploadFile(String configKey, String fileName, InputStream inputStream) {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;
        byte[] uploadBytes = readInputStream(inputStream);
        ByteArrayInputStream byteInputStream = new ByteArrayInputStream(uploadBytes);
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        String fileUrl = null;
        try {
            Response response = uploadManager.put(byteInputStream, key, upToken, null, null);
            //解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            fileUrl = BaseConstant.DOWNLOAD_DOMAIN + putRet.key;
            configMapper.updateValue(configKey, fileUrl);
        } catch (QiniuException ex) {
            logger.info(ex.getMessage());
        }
        return AjaxResult.getOK(fileUrl);
    }

    private static byte[] readInputStream(InputStream is) {
        ByteArrayOutputStream writer = new ByteArrayOutputStream();
        byte[] buff = new byte[1024 * 2];
        int len = 0;
        try {
            while ((len = is.read(buff)) != -1) {
                writer.write(buff, 0, len);
            }
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return writer.toByteArray();
    }

    public AjaxResult downloadFile(String configKey) {
        Config config = configMapper.selectOne(configKey);
        if (config == null) {
            return AjaxResult.getErrorWithMsg("文件缺失");
        }
        return AjaxResult.getOK(config.getValue());
    }

}
