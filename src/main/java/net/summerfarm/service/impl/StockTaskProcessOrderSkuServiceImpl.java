package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.common.util.FunctionalUtil;
import net.summerfarm.enums.StockTaskType;
import net.summerfarm.mapper.StockTaskOrderSkuMapper;
import net.summerfarm.mapper.StockTaskProcessOrderSkuMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.StockTaskMapper;
import net.summerfarm.mapper.manage.StockTaskProcessMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.StockTaskProcessVO;
import net.summerfarm.service.StockTaskConfigService;
import net.summerfarm.service.StockTaskProcessOrderSkuService;
import net.summerfarm.service.StockTaskService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2023/2/8 17:00
 * @<AUTHOR>
 */
@Slf4j
@Service
public class StockTaskProcessOrderSkuServiceImpl implements StockTaskProcessOrderSkuService {

    @Resource
    private StockTaskOrderSkuMapper stockTaskOrderSkuMapper;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private StockTaskProcessOrderSkuMapper stockTaskProcessOrderSkuMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskConfigService stockTaskConfigService;

    @Override
    public void saveStockTaskProcessOrderSku(ProcessOrderSkuSaveCmd processDetail) {
        Integer processId = processDetail.getStockTaskProcessId();
        String sku = processDetail.getSku();
        StockTaskProcessVO stockTaskProcessVO = stockTaskProcessMapper.selectByPrimaryKey(processId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskProcessVO), String.format("未查询到对应出库单信息 processId:%s", processId));
        Integer stockTaskId = stockTaskProcessVO.getStockTaskId();
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTask), String.format("未查询到对应出库任务信息 stockTaskId:%s", stockTaskId));
        if (!stockTaskConfigService.supportHandleStockTaskMsg(stockTask.getType())) {
            return;
        }
        // 根据任务ID+sku查询是否存在多个订单中
        List<StockTaskOrderSkuDO> taskOrderSkuDOList = stockTaskOrderSkuMapper.selectListByTaskIdAndSku(stockTaskId, sku);
        // ExceptionUtil.checkAndThrow(CollectionUtils.isNotEmpty(taskOrderSkuDOList), String.format("未查询到出库任务&sku信息 stockTaskId:%s, sku:%s", stockTaskId, sku));
        if (CollectionUtils.isEmpty(taskOrderSkuDOList)) {
            log.warn("未查询到出库任务订单明细信息，stockTaskId:{}, sku:{}", stockTaskId, sku);
            return;
        }
        // 包含重复订单号，数据告警
        List<String> orderNoList = taskOrderSkuDOList.stream().map(StockTaskOrderSkuDO::getOutOrderNo).distinct().collect(Collectors.toList());
        if (taskOrderSkuDOList.size() != orderNoList.size()) {
            log.error("根据任务编码&sku查询到重复的订单号，stockTaskId:{}, sku:{}, taskOrderSkuDOList:{}", stockTaskId, sku, JSONUtil.toJsonStr(taskOrderSkuDOList));
            return;
        }

        // 出库数量分配逻辑
        Map<String, Integer> orderQuantityMap = this.matchOrderQuantity(processDetail, taskOrderSkuDOList);
        taskOrderSkuDOList.forEach(stockTaskOrderSkuDO -> {
            String orderNo = stockTaskOrderSkuDO.getOutOrderNo();
            Integer matchQuantity = orderQuantityMap.get(orderNo);
            if (Objects.isNull(matchQuantity) || matchQuantity <= 0) {
                return;
            }
            // 构建出库单订单明细对象
            StockTaskProcessOrderSkuDO processOrderSkuDO = this.buildProcessOrderSkuDO(processDetail, stockTaskOrderSkuDO, stockTaskProcessVO, matchQuantity);
            stockTaskProcessOrderSkuMapper.insert(processOrderSkuDO);
            // 更新出库任务订单明细实出数量
            int rows = stockTaskOrderSkuMapper.updateActualQuantityById(stockTaskOrderSkuDO.getId(), stockTaskOrderSkuDO.getActualQuantity() + matchQuantity);
            if (rows <= 0) {
                // retry or throw or warn-notice
                ExceptionUtil.wrapAndThrow("更新出库任务订单明细并发竞争，请稍后重试");
                log.error("更新出库任务订单明细实际出库数量异常，stockTaskOrderSkuId:{} actualQuantity:{}", stockTaskOrderSkuDO.getId(), stockTaskOrderSkuDO.getActualQuantity() + matchQuantity);
            }
        });

    }

    private StockTaskProcessOrderSkuDO buildProcessOrderSkuDO(ProcessOrderSkuSaveCmd processDetail, StockTaskOrderSkuDO stockTaskOrderSkuDO, StockTaskProcessVO stockTaskProcessVO, Integer matchQuantity) {
        return StockTaskProcessOrderSkuDO.builder()
                .stockTaskProcessId(processDetail.getStockTaskProcessId().longValue())
                .stockTaskId(stockTaskOrderSkuDO.getStockTaskId())
                .outOrderNo(stockTaskOrderSkuDO.getOutOrderNo())
                .sku(processDetail.getSku())
                .quantity(matchQuantity)
                // .purchaseBatch(processDetail.getListNo())
                // .qualityDate(processDetail.getQualityDate())
                .creator(stockTaskProcessVO.getRecorder())
                .operator(stockTaskProcessVO.getRecorder())
                .gmtCreated(System.currentTimeMillis())
                .gmtModified(System.currentTimeMillis())
                .isDeleted(0)
                .lastVer(1).build();
    }

    private Map<String, Integer> matchOrderQuantity(ProcessOrderSkuSaveCmd processDetail, List<StockTaskOrderSkuDO> taskOrderSkuDOList) {
        Map<String, Integer> result = Maps.newHashMap();
        // 排序
        taskOrderSkuDOList = taskOrderSkuDOList.stream().sorted(Comparator.comparing(StockTaskOrderSkuDO::getId)).collect(Collectors.toList());
        Integer processQuantity = processDetail.getQuantity();
        for (StockTaskOrderSkuDO taskOrderSkuDO : taskOrderSkuDOList){
            if (processQuantity <= 0) {
                break;
            }
            String orderNo = taskOrderSkuDO.getOutOrderNo();
            // 总数量
            Integer quantity = taskOrderSkuDO.getQuantity();
            // 已实出数量
            Integer actualQuantity = taskOrderSkuDO.getActualQuantity();
            Integer matchQuantity = Math.min(processQuantity, Math.max(quantity - actualQuantity, 0));
            result.put(orderNo, matchQuantity);
            processQuantity = processQuantity - matchQuantity;
        };
        return result;

    }
}
