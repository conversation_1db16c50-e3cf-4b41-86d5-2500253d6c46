package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.StockChangeType;
import net.summerfarm.model.DTO.BlockCheckDTO;
import net.summerfarm.model.DTO.BlockCheckResultDTO;
import net.summerfarm.model.DTO.DeliveryPointDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.mq.ofc.input.DeliveryOrderMessageInput;
import net.summerfarm.tms.message.BatchMessage;
import net.summerfarm.tms.message.DeliveryOrderMessage;
import net.summerfarm.tms.message.DeliverySiteMessage;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.service
 * @Description: 订单业务接口
 * @author: <EMAIL>
 * @Date: 2016/8/29
 */
public interface OrderService {

    /**
     * 分页查询订单数据
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param selectKeys 查询条件：mphone手机号、afterSaleFlag:1有售后、0无售后
     *                   、type:0普通、1省心送、3代下单、4预售、11直发、12秒杀
     * @param orderBy 排序依据
     * @return 订单数据列表
     */
    AjaxResult selectOrderList(int pageIndex, int pageSize, OrderReq selectKeys, String orderBy);

    AjaxResult selectOrderItem(int pageIndex, int pageSize, OrderItemVO selectKeys);

    /**
     * 修改订单信息
     * @param record
     * @return
     */
    AjaxResult update(String orderNo, OrderVO record);

    /**
     * 删除配送计划
     * @param id 配送计划id
     * @return ok
     */
    AjaxResult delete(Integer id);

    /**
     * 关闭订单
     * @param orderNo
     * @return
     */
    AjaxResult closeOrder(String orderNo);

    /**
     * 根据订单号查询
     * @param orderNo 订单编号
     * @return 订单详情
     */
    AjaxResult selectOrderDetails(String orderNo);

    AjaxResult deleteHelpOrder(String orderNo);


    void updateStock(Boolean share, String sku, Integer quantity, Integer areaNo, StockChangeType changeType, String recordNo, Map<String, QuantityChangeRecord> recordMap, Long contactId);

    AjaxResult outTimes(String orderNo, int type);

    /**
     * 截单数据下载
     *
     * @param startDate
     * @param endDate
     * @param response
     * @return
     */
    void closingOrder(Integer parentNo, Date startDate, Date endDate, HttpServletResponse response) throws IOException;


    void closingOrderProducts(Integer storeNo, LocalDate deliveryTime, HttpServletResponse response) throws IOException;

    /**
     * 所有截单用户的地图标记
     *
     * @param startDate
     * @param endDate
     * @return
     */
    AjaxResult showPoiNotes(Date startDate, Date endDate);

    /**
     * 修改备注接口
     * @param orderNo 订单编号
     * @param remark 备注内容
     * @return ok
     */
    AjaxResult updateRemark(String orderNo, String remark);

    /**
     * 生成配送单
     *
     * @param startDate
     * @param endDate
     * @param response
     * @return
     */
    void distributionList(Integer parentNo, Date startDate, Date endDate, String orderNo, HttpServletResponse response, Boolean deliverytype,Integer warehouseNo) throws IOException;

    AjaxResult contactList(Integer storeNo, HttpServletResponse response) throws IOException;

    AjaxResult deliveryPathUpload(MultipartFile file);

    AjaxResult deliveryPathDownload(Integer storeNo, LocalDate deliveryTime, HttpServletResponse response);

    AjaxResult deliveryPathStart(String shopName, Integer storeNo, LocalDate startDate, LocalDate endDate);

    AjaxResult deliveryPathDelete(DeliveryPath deliveryPath);

    AjaxResult deliveryPathList(DeliveryPathVO deliveryPathVO, Boolean isExist);

    AjaxResult getPeliveryPathListByName(DeliveryPathVO deliveryPathVO, Boolean isExist, List<Long> contactIds);

    AjaxResult deliveryPathSelect(DeliveryPathVO deliveryPathVO);

    AjaxResult deliveryPathUpdate(DeliveryPath deliveryPath);

    AjaxResult deliveryPathRemove(int id);

    AjaxResult deliveryPathSave(DeliveryCarPath deliveryCarPath);

    AjaxResult deliveryCarPathSave(DeliveryCarPath deliveryCarPath);

    AjaxResult deliveryCarPathRemove(Integer id);

    AjaxResult deliveryCarPathUpdate(DeliveryCarPath deliveryCarPath);

    /**
     * 查询有效订单数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    AjaxResult count(LocalDate startTime, LocalDate endTime);

    /**
     * 大客户订单下载
     * @param selectKeys
     * @param adminId
     * @param filename 文件名
     * @return
     */
    void downloadOrder(OrderVO selectKeys, Integer adminId,String filename,Long resId);

    /**
     * 大客户对账单导出请求
     * @param selectKeys
     * @return
     */
    AjaxResult receiveDownloadRequest(OrderVO selectKeys);

    AjaxResult orderData(String phone, LocalDate startDate, LocalDate endDate);

    AjaxResult notDeliveryOrders(int pageIndex, int pageSize, DeliveryOrdersVO deliveryOrdersVO);

    AjaxResult notDeliveryOrdersDetail(DeliveryOrdersVO deliveryOrdersVO);

    AjaxResult skuSales(DeliveryOrdersVO deliveryOrdersVO);

    AjaxResult downloadOrderList(OrderVO selectKeys);

    AjaxResult updatePathName(DeliveryCarPath deliveryCarPath);

     AjaxResult selectTimingOrder(String sku, Integer storeNo);


     List<ClosingOrder> getClosingOrderData(Date startDate, Date endDate, Integer areaNo, Integer parentNo, String orderNo, Boolean deliverytype);

    /**
     * 近期下单sku
     * @param orderItemInput 查询条件
     * @return
     */
    AjaxResult recentOrderSku(int pageIndex, int pageSize, OrderItemInput orderItemInput);

    AjaxResult getDeliveryCarPathPoi(DeliveryCarPath deliveryCarPath);

    /**
     * 查询直发采购订单列表
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    AjaxResult directPurchaseOrderList(int pageIndex, int pageSize, OrderVO selectKeys);

    /**
     * 导出直发采购订根据查询条件的订单列表内容
     * @param selectKeys
     * @return
     */
    void directPurchaseOrderExport(OrderVO selectKeys);

    /**
     * 直发采购订单下单: 生成采购单， 生成订单， 保存下单信息， 匹配扣款
     * @param input
     * @return
     */
    AjaxResult placeDirectPurchaseOrder(DirectPurchasePlaceOrderInput input);

    /**
     * 根据直发次采购单号，删除对应的订单后需要的一系列操作
     * @param orderNo
     * @return
     */
    AjaxResult deleteDirectPurchaseOrder(String orderNo);

    /**
     * 一键发货后第二天中午商城订单自动完结 每天12点
     */
    void autoConfirm();

    /**
     * CRM小程序：分页查询订单数据
     * @param crmOrderListQuery 查询条件:areaNo、mname(商户名称)、orderNo（订单号）、mSize(客户类型)、startTime、endTime
     *                   type:0普通、1省心送、4预售
     *                   status:1待支付、2待配送、3待收货、6已完结、11已撤销
     * @return 订单数据列表
     */
    CommonResult<PageInfo<OrderVO>> selectForCrmOrderList(CrmOrderListQuery crmOrderListQuery);

    /**
     * 验证手机号格式
     * @param mPhone 手机号
     * @return true是符合格式的号码
     */
    boolean verifyPhone(String mPhone);

    /**
     * 客户有大单购买趋势时,发送提醒,已购买触发
     * @param orderNo 订单编号
     */
    void sendOrderReminder(String orderNo);

    /**
     * 客户有大单购买趋势时,发送提醒,已加购触发
     * @param mId 商户Id
     */
    void sendOrderReminder(Long mId);

    AjaxResult deliveryPathBatchUpdate(List<DeliveryPath> recordList,String deliveryPath);

    /**
     * 根据订单创建整单售后单
     * @param orderNos
     * @param sampleIdList
     * @param afterSaleOrderNoList
     * @param afterSalesInstructions
     * @param afterSalesReason
     * @return
     */
    AjaxResult createAfterSaleOrder(List<String> orderNos, List<Integer> sampleIdList,
                              ArrayList<String> afterSaleOrderNoList, String afterSalesInstructions,
                             Integer afterSalesReason,Integer deliveryPlanId);

    /**
     * 根据订单创建整单售后单
     * @param orderNos
     * @param sampleIdList
     * @param afterSaleOrderNoList
     * @param afterSalesInstructions
     * @param afterSalesReason
     * @return
     */
    AjaxResult createAfterSaleOrderNew(List<String> orderNos, List<Integer> sampleIdList,
                                    ArrayList<String> afterSaleOrderNoList, String afterSalesInstructions,
                                    Integer afterSalesReason,Integer deliveryPlanId,String operator);

    /**
     * 校验商户是否可以被拉入私海
     * @param mIdList midList
     * @return success
     */
    AjaxResult checkPurchased(List<Long> mIdList);

    /**
     * crm客户基础数据(订单部分)
     * @param mId 商户id
     * @return crm客户基础数据(订单部分)
     */
    AjaxResult crmMerchantBaseDetail(Long mId);


    /**
     * 显示客户订单数据详情
     *
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param teamDataInput 查询条件，其中mid为必传，开始、结束时间非必须
     * @return 客户数据详情
     */
    AjaxResult merchantOrderDetails(int pageIndex, int pageSize, TeamDataInput teamDataInput);

    /**
     * 导出店铺配送单
     * @param mId
     * @param startDate
     * @param endDate
     * @return
     */
    AjaxResult distributionListByMid(Integer mId, Date startDate, Date endDate,HttpServletResponse response) throws IOException;

    /**
     * 查询配送详情
     * @param deliveryPlan
     * @return
     */
    AjaxResult shippingDetails(DeliveryPlan deliveryPlan);

    /**
     * 获得司机配送实时点位
     * @param deliveryPointDTO 订单参数
     * @return 返回点位结果
     */
    AjaxResult getDeliveryPoint(DeliveryPointDTO deliveryPointDTO);


    /**
     * 订单类型
     */
    CommonResult<Orders> getOrderMsg(String orderNo);

    /**
     * 获取订单关联的发票信息
     * @param orderNo 订单号
     * @return 发票信息
     */
    AjaxResult selectOrderFinancialInfo(String orderNo);

    /**
     * 拦截订单创建售后单
     * @param deliveryOrderMessages 订单号
     */
    void createAfterSaleOrderByMq(List<DeliveryOrderMessage> deliveryOrderMessages);

    /**
     * 拦截订单创建售后单
     * @param deliveryOrderMessages 订单号
     */
    void createAfterSaleOrderByOfc(List<DeliveryOrderMessageInput> deliveryOrderMessages);

    /**
     * 完成排线
     * @param deliveryOrderMessages 订单号
     */
    void completeWiring(List<DeliveryOrderMessage> deliveryOrderMessages);

    /**
     * 完成配送
     * @param deliveryOrderMessages 订单号
     * @param deliverySiteMessage 点位
     * @param batchMessage 司机
     */
    void completeDelivery(List<DeliveryOrderMessage> deliveryOrderMessages, DeliverySiteMessage deliverySiteMessage, BatchMessage batchMessage);

    /**
     * 查询订单配送说评价
     * @param deliveryPlanId
     * @return
     */
    CommonResult<DeliveryEvaluationVO> deliveryEvaluationDetail(Integer deliveryPlanId);

    /**
     * @description: 订单项插入的时候新增sku有效期快照
     * @author: lzh
     * @date: 2023/4/13 14:48
     * @param: [dtsModel]
     * @return: void
     **/
    void saveValiditySnapshot(DtsModel dtsModel);

    /**
     * tms新打印配送单接口
     * @param storeNo 城配仓编号
     * @param endDate 结束日期
     * @param response 打印
     * @param printDirection 打印方向
     */
    void tmsDistributionList(Integer storeNo, Date endDate, HttpServletResponse response, Boolean printDirection) throws IOException;

    void addDeliveryPlanExtend(List<DeliveryPlan> deliveryPlans,Integer Type);

    /**
     * 修改订单运营服务区
     * @param orderNo 订单号
     * @param oldAreaNo 原运营服务区编号
     * @param newAreaNo 新运营服务区编号
     */
    void updateOrderAreaNo(String orderNo, Integer oldAreaNo, Integer newAreaNo);
}
