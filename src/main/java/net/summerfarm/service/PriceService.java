package net.summerfarm.service;

import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.domain.ActivitySku;
import net.summerfarm.model.domain.AreaSku;

/**
 * sku价格逻辑service
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-02-24
 */
public interface PriceService {

    /**
     * 获取sku定价
     * @param areaSku areaSku信息
     * @return 价格信息
     */
    PriceInfoBO getNormalPrice(AreaSku areaSku);

    /**
     * 获取sku定价
     * @param areaSku areaSku信息
     * @param isActivitySku 是否正在活动中
     * @return 价格信息
     */
    PriceInfoBO getNormalPrice(AreaSku areaSku, boolean isActivitySku);

    /**
     * 获取sku活动价
     * @param areaSku areaSku信息
     * @param activitySku 活动sku信息
     * @return 价格信息
     */
    PriceInfoBO getActivityPrice(AreaSku areaSku, ActivitySku activitySku);

    /**
     * 获取sku活动价
     * @param areaNo 城市编号
     * @param activitySku 活动sku信息
     * @return 价格信息
     */
    PriceInfoBO getActivityPrice(Integer areaNo, ActivitySku activitySku);
}
