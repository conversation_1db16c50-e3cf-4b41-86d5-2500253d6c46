package net.summerfarm.service.stockTransfer.dto.req;

import lombok.Data;
import net.summerfarm.service.stockTransfer.dto.TransferOutInfo;
import net.summerfarm.service.stockTransfer.enums.TransferOpEnum;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/19
 */
@Data
public class StockTransferCostQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "转换编号不可为空")
    Long stockTransferId;

    /**
     * 转换实例编号
     */
    @NotNull(message = "转换实例编号不可为空")
    Long stockTransferItemId;

    /**
     * 转换操作类型
     *
     * @see TransferOpEnum
     */
    Integer type;

    /**
     * 生产日期
     */
    Long produceDate;

    /**
     * 转换比例
     */
    @NotNull(message = "转换比例不可为空")
    @NotEmpty(message = "转换比例不可为空")
    String transferRatio;

    /**
     * 转出sku
     */
    @NotNull(message = "转出sku不可为空")
    @NotEmpty(message = "转出sku不可为空")
    String transferOutSku;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 库存转出信息
     */
    @NotEmpty(message = "库存转出信息不可为空")
    List<TransferOutInfo> transferOutInfos;

}
