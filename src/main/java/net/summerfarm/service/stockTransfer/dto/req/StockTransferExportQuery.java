package net.summerfarm.service.stockTransfer.dto.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 转换任务列表查询
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferExportQuery implements Serializable {
    private static final long serialVersionUID = 6085975313082233851L;

    /**
     * 任务id
     */
    Long stockTransferId;
}
