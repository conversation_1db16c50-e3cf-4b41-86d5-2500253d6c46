package net.summerfarm.service.stockTransfer.dto.res;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * 转换任务详情出参对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemRes implements Serializable {
    private static final long serialVersionUID = 613952657721538021L;

    /**
     * 转换任务实例id
     */
    Long stockTransferItemId;

    /**
     * 转入商品id
     */
    Long transferInGoodsId;

    /**
     * 转入商品名称
     */
    String transferInGoodsName;

    /**
     * 转入商品sku
     */
    String transferInGoodsSku;

    /**
     * 转入商品规格
     */
    String transferInGoodsSpec;

    /**
     * 转入商品归属
     */
    String pdAttribute;

    /**
     * 商品类目
     */
    String goodsCategory;

    /**
     * 类目名称
     */
    String categoryName;

    /**
     * 存储区域
     */
    String storageArea;

    /**
     * 包装
     */
    String packaging;

    /**
     * 应转入数量
     */
    Long preTransferInNum;

    /**
     * 实际转入数量
     */
    Long actualTransferInNum;

    /**
     * 转换操作详情
     */
    List<StockTransferItemOpRes> stockTransferItemOps;

    /**
     * 进口/国产
     */
    Integer isDomestic;
}
