package net.summerfarm.service.stockTransfer.dto.req;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.service.stockTransfer.dto.GoodsLocationInfo;
import net.summerfarm.service.stockTransfer.dto.TransferOutInfo;
import net.summerfarm.service.stockTransfer.enums.TransferOpEnum;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 转换任务创建req
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOpCommand implements Serializable {
    private static final long serialVersionUID = 8176126288339628897L;
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 转换任务id
     */
    Long stockTransferId;

    /**
     * 转换实例编号
     */
    @NotNull(message = "转换实例编号不可为空")
    Long stockTransferItemId;

    /**
     * 转换操作类型
     *
     * @see TransferOpEnum
     */
    Integer type;

    /**
     * 生产日期
     */
    Long produceDate;

    /**
     * 转换比例
     */
    @NotNull(message = "转换比例不可为空")
    @NotEmpty(message = "转换比例不可为空")
    String transferRatio;

    /**
     * 转出sku
     */
    @NotNull(message = "转出sku不可为空")
    @NotEmpty(message = "转出sku不可为空")
    String transferOutSku;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 库存转出信息
     */
    List<TransferOutInfo> transferOutInfos;

    /**
     * 货位信息，后续拿掉
     */
    List<GoodsLocationInfo> glInfos;

}
