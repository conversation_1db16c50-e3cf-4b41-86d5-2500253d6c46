package net.summerfarm.service.stockTransfer.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum TransferOpEnum {
    /**
     * 单批次转换
     */
    ONE_WAY(0, "单批次转换"),
    MIX(1, "混合批次转换"),
    ;

    public static TransferOpEnum convert(Integer code) {
        return Arrays.stream(TransferOpEnum.values())
                .filter(o -> o.getCode().equals(code))
                .findFirst()
                .orElse(TransferOpEnum.ONE_WAY);
    }

    Integer code;
    String desc;
}
