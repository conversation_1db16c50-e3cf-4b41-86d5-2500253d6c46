package net.summerfarm.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description 绑定预付service
 * <AUTHOR>
 * @date 2022/2/17 11:47
 */
public interface PurchaseBindingPrepaymentService {

    /**
     * 校验供应商维度下的采购项是否发起预付
     * @param purchaseNo
     * @param supplierId
     * @return
     */
    BigDecimal checkBind(String purchaseNo, Integer supplierId);

    BigDecimal queryAdvanceAmount(String purchaseNo, Integer supplierId);
    Map<String,BigDecimal> advanceAmountPurchasesMap(List<String> purchaseNoList, Integer supplierId, String advanceEndTime);
}
