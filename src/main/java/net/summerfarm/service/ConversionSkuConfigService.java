package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.ConversionSkuConfig;
import net.summerfarm.model.vo.ConversionSkuConfigVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/18  14:29
 */
public interface ConversionSkuConfigService {

    /**
     * 列表查询
     * @Author: ct
     * @param pageSize  size
     * @param pageIndex   index
     * @param vo 查询
     * @return
     **/
    AjaxResult selectConfigVO(Integer pageIndex, Integer pageSize, ConversionSkuConfigVO vo);


    /**
     * 新增
     * @Author: ct
     * @param conversionSkuConfig 配置
     * @return
     **/
    AjaxResult saveConfigVO(ConversionSkuConfig conversionSkuConfig);

    /**
     * 批量导入
     * @Author: ct
     * @param file 文件
     * @return
     **/
    AjaxResult importExcel(MultipartFile file);

    /**
     * 更新
     * @Author: ct
     * @param conversionSkuConfig 配置信息
     * @return
     **/
    AjaxResult updateConfig(ConversionSkuConfig conversionSkuConfig);


    /**
     * 模版下载
     * @Author: ct
     * @param  response
     * @return
     **/
    void templateDown(HttpServletResponse response);

    /**
     * 数据生成
     */
    void dataSkuQuantity();

    /**
     * 同步sku销量信息
     *
     * <AUTHOR>
     * @Date 2022/12/5 11:48
     **/
    void syncSkuQuantity();
}
