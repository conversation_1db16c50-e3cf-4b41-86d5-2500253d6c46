package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.MerchantLeads;
import net.summerfarm.model.vo.MerchantLeadsVO;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-09
 */
public interface MerchantLeadsService {
    AjaxResult select(int pageIndex, int pageSize, MerchantLeads selectKeys);

    AjaxResult save(MerchantLeadsVO merchantLeads);
}
