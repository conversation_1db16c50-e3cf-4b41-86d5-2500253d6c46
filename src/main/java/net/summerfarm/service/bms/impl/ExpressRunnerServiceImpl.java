package net.summerfarm.service.bms.impl;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.bms.ExpressRunnerService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/8/26
 */
@Service
@Slf4j
public class ExpressRunnerServiceImpl implements ExpressRunnerService {
    @Override
    public BigDecimal calculation(String formula, DefaultContext<String, Object> context) {
        ExpressRunner runner = new ExpressRunner(true,false);
        Object result;
        try {
            result = runner.execute(formula, context, null, true, false);
            if(Objects.isNull(result)){
                return BigDecimal.ZERO;
            }
            return new BigDecimal(String.valueOf(result));
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
        }
        return BigDecimal.ZERO;
    }

}
