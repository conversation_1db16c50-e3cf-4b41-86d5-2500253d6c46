package net.summerfarm.service.bms;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.bms.BmsSettleAccountQuery;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/8/23
 */
public interface BmsSettleAccountService {

    /**
     * 查询结算明细单
     * @param param
     * @return
     */
    AjaxResult selectSettleAccount(BmsSettleAccountQuery param);

    /**
     * 批量完成核对
     * @param settleAccountIds
     * @return
     */
    AjaxResult checkSettleAccount(List<Integer> settleAccountIds);

    /**
     * 发起结算对账单
     * @param param
     * @return
     */
    AjaxResult reconciliationSettleAccount(BmsSettleAccountQuery param);

    /**
     * 查询明细单费用详情
     * @param param
     * @return
     */
    AjaxResult selectAccountFeeDetails(BmsSettleAccountQuery param);

    /**
     * 结算明细单费用详情账单详情
     * @param id
     * @return
     */
    AjaxResult selectCalculationDetails(Integer id);

    /**
     * 生成today-1天配送日期城配明细单
     */
    void deliverySettleAccount();

    /**
     * 结算明细单导出
     * @param param
     * @return
     */
    void exportSettleAccountList(BmsSettleAccountQuery param)  throws IOException;

    /**
     * 结算明细单详情导出
     * @param param
     * @return
     * @throws IOException
     */
    void exportSettleAccountDetail(BmsSettleAccountQuery param) throws IOException;

    /**
     * 结算明细单基础信息
     * @param param
     * @return
     */
    AjaxResult selectSettleAccountBase(BmsSettleAccountQuery param);

    /**
     * 当前仓生成结算明细单
     * @param storeNo
     * @param deliveryDate
     */
    void settleAccountSingle(Integer storeNo, LocalDate deliveryDate);

    void manualSettleAccountSingle(Integer storeNo, LocalDate date);

    /**
     * 调整单批量调整模板
     * @param param
     * @return
     */
    void exportCostAdjustmentTemplate(BmsSettleAccountQuery param) throws IOException;

    /**
     * 批量调整上传
     * @param id
     * @return
     */
    AjaxResult importCostAdjustmentTemplate(Integer id, MultipartFile file);

    String createImportKey(Integer id);
}
