package net.summerfarm.service;

import net.summerfarm.enums.PriceStrategyTypeEnum;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.domain.PriceStrategy;
import net.summerfarm.model.vo.PriceStrategyAuditRecordVO;
import net.summerfarm.model.vo.PriceStrategyVO;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

public interface PriceStrategyService {
    /**
     * 计算价格并触发审批
     *
     * @param typeEnum      价格类型
     * @param businessId    业务id
     * @param costPrice     成本价
     * @param originalPrice 原价
     * @return 计算后的价格
     */
    PriceStrategyAuditRecordVO calcStrategyPrice(PriceStrategyTypeEnum typeEnum, Long businessId, BigDecimal costPrice, BigDecimal originalPrice);

    /**
     * 计算价格不触发审批
     *
     * @param priceStrategy 价格策略
     * @param costPrice     成本价
     * @param originalPrice 原价
     * @return 计算后的价格
     */
    PriceStrategyAuditRecordVO calcStrategyPrice(PriceStrategy priceStrategy, BigDecimal costPrice, BigDecimal originalPrice);

    /**
     * 新增、修改价格策略
     *
     * @param typeEnum     类型
     * @param strategyList 策略list
     * @param areaNo       城市编号
     * @param sku          sku
     * @param bizId        业务id
     */
    void insertOrUpdate(PriceStrategyTypeEnum typeEnum, List<PriceStrategy> strategyList, Integer areaNo, String sku, Integer bizId);

    /**
     * 新增、修改价格策略
     *
     * @param typeEnum     类型
     * @param strategy     策略
     * @param areaNo       城市编号
     * @param sku          sku
     * @param bizId        业务id
     */
    void insertOrUpdate(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer areaNo, String sku, Integer bizId);

    /**
     * 新增、修改价格策略&不发起钉钉审批
     *
     * @param typeEnum     类型
     * @param areaNo       城市编号
     * @param sku          sku
     * @param bizId        业务id
     * @return 原价
     */
    BigDecimal insertOrUpdateWithoutDing(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer areaNo, String sku, Integer bizId);

    /**
     * 新增、修改价格策略&不发起钉钉审批&拓展购买使用
     *
     * @param typeEnum     类型
     * @param bizId        业务id
     * @return 原价
     */
    BigDecimal insertOrUpdateWithoutDingByExpandActivity(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer bizId);

    /**
     * 新增、修改价格策略&不发起钉钉审批&换购使用
     * @param typeEnum 类型
     * @param strategy
     * @param bizId    业务id
     * @return 原价
     */
    BigDecimal insertOrUpdateWithoutDingByExchangeActivity(PriceStrategyTypeEnum typeEnum, PriceStrategy strategy, Integer bizId);

    /**
     * @param typeEnum 类型
     * @param businessId 业务id
     * @return 查询对应的价格策略及价格信息
     */
    PriceStrategyVO selectPriceStrategyVO(PriceStrategyTypeEnum typeEnum, Long businessId);


    /**
     * 查看阶梯价最后落在哪个阶梯
     * @param priceList 阶梯价
     * @param unit 为空时 默认为1
     * @param salePrice 商城价
     * @return
     */
    BigDecimal selectLadderPriceByUnit(List<ActivityLadderPriceDTO> priceList, Integer unit, BigDecimal salePrice);
}
