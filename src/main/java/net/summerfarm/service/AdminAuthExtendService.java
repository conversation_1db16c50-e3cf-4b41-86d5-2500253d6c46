package net.summerfarm.service;

import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.model.domain.AdminAuthExtend;

import java.util.List;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-28
 */
public interface AdminAuthExtendService {

    /**
     * 根据系统用户的id查询钉钉用户信息
     * @param adminId 系统用户id
     * @return 钉钉用户信息
     */
    AdminAuthExtend findDingdingUserId(Integer adminId) throws DingdingProcessException;

    /**
     * @description: 批量获取钉钉用户信息
     * @author: lzh
     * @date: 2023/4/26 16:59
     * @param: [approverAdmin]
     * @return: java.util.List<net.summerfarm.model.domain.AdminAuthExtend>
     **/
    List<AdminAuthExtend> getListByAdminId(List<Integer> approverAdmin);
}
