package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.SaleOutTypeEnum;
import net.summerfarm.model.DTO.BatchStorageSkuDetailDTO;
import net.summerfarm.model.DTO.CapacityUsedDTO;
import net.summerfarm.model.DTO.SkuCapacityDTO;
import net.summerfarm.model.DTO.StockTaskDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.*;
import net.summerfarm.model.input.stock.StockTaskInStoreInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface StockTaskService {

    /**
     * 当前仓出库任务（本仓任务、城配仓任务）
     *
     * @param taskStoreNo 生成任务仓库编号
     * @param execDate    执行日期
     */
    List<Integer> saleOutTask(Integer taskStoreNo, LocalDate execDate);

    List<Integer> saleOutTask(Integer taskStoreNo, LocalDate execDate, Boolean skipRepeat);

    List<Integer> saleAndAfterOutTaskForCross(Integer taskStoreNo, LocalDate execDate, Boolean skipRepeat);

    Integer cabinetManagementSaleOutTask(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate, Boolean isWaveStockTask);

    Integer cabinetManagementAfterSaleTask(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate, Boolean isWaveStockTask);

    Integer cabinetManagementSaleAndAfterOutTaskForCross(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate, Boolean isWaveStockTask);

    /**
     * 特殊城市截单处理
     */
    void saleOutStockTask(String key);

    AjaxResult inOutStore(Integer type, String json);

    AjaxResult finishStockTask(Integer id);

    void stockTaskDetailDownload(Integer id);

    void distributionList(Integer id, HttpServletResponse response) throws IOException;

    AjaxResult storeRecordData(int pageIndex, int pageSize, StockTaskReq stockTaskReq);

    void storeRecordDataDownload(StockTaskReq stockTaskReq, HttpServletResponse response);

    AjaxResult stockTaskCreate(StockTaskVO stockTaskVO, Integer status);

    AjaxResult newTaskList(int pageIndex, int pageSize, StockTaskReq stockTaskReq);

    AjaxResult validSku(Integer areaNo, String skus);

    AjaxResult autoTransferList(int pageIndex, int pageSize, AutoTransferVO autoTransferVO);

    AjaxResult autoTransferDel(Integer id);

    AjaxResult autoTransferSave(AreaStore areaStore);

    /**
     * 自动转换模板、报告下载
     *
     * @param reportFile
     * @return
     */
    void autoTransferBatchTemplate(String reportFile);

    AjaxResult autoTransferBatchImport(MultipartFile file);

    AjaxResult taskInfo(Integer areaNo);

    AjaxResult taskValue(Integer areaNo);

    AjaxResult unfinishTaskList(int pageIndex, int pageSize, StockTaskReq req);

    AjaxResult warningDataList(int pageIndex, int pageSize, WarningDataVO warningDataVO);

    AjaxResult warningDataDetail(String sku, Integer areaNo);

    AjaxResult overQualityDataList(int pageIndex, int pageSize, OverQualityDataVO overQualityDataVO);

    AjaxResult overQualityDataDetail(String sku, Integer areaNo);

    AjaxResult centerToStoreData(int pageIndex, int pageSize, StockTaskItemVO stockTaskItemVO);

    AjaxResult centerToStoreDownload(StockTaskItemVO stockTaskItemVO);

    void inStockSubscribe();

    /**
     * 一键录入
     *
     * @param sku
     * @param
     * @return
     */
    AjaxResult stockTaskService(String sku, Integer areaNo, Integer stockTaskId);

    /**
     * 调拨出库一键录入
     */
    AjaxResult oneEntryAllot(String skus, Integer areaNo, Integer stockTaskId, String listNo);

    AjaxResult selectUnStoreList(StoreRecordVO storeRecord);

    /**
     * 自动货损任务
     *
     * @param runDate 任务计算日期
     */
    void autoDamageTask(LocalDate runDate);

    void createDamageTask(Integer areaNo, Map<String, List<StockTaskItemDetail>> damageMap, Long tenantId);

    void createDamageTaskForCabinet(Integer warehouseNo, Map<String, List<StockTaskItemDetail>> damageMap, Long tenantId, LocalDate handleDate);

    /**
     * 临保自动转换任务
     *
     * @param runDate
     */
    void autoTemporaryTransfer(LocalDate runDate);

    /**
     * 补货出库任务
     */
    void createAfterSaleTask();

    /**
     * 补货出库任务（指定配送日期）
     *
     * <AUTHOR>
     * @Date 2023/1/5 12:17
     * @param deliveryDate 配送日期
     **/
    void createAfterSaleTask(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate);


    /**
     * 退货入库任务 0 非南京仓 1 南京仓
     */
    void autoAfterSaleIn();


    /**
     * 拒收入库
     */
    void autoRejectTask();


    /**
     * 根据入库单编号查询采购详情
     *
     * @param id
     * @return
     */
    AjaxResult selectPurchaseDetail(Integer id);


    /**
     * 批量下载
     *
     * @param ids
     */
    AjaxResult stockTaskBatchDownload(String ids);

    /**
     * 转换任务下载
     */
    void transferDown(String fileName);

    /**
     * 巡检任务
     */
    void autoPatrolCheck();

    /**
     * 处理binlog日志监听
     */
    void handleBinLogToCloseTask(DtsModel dtsModel);

    /**
     * 关闭出库任务
     *
     * @param stockTask
     * @param items
     */
    void closeStockTask(StockTask stockTask, List<StockTaskItem> items);

    /**
     * 退货拒收列表数据查询
     *
     * @param pageIndex
     * @param pageSize
     * @param returnRejectListReq
     * @return
     */
    AjaxResult returnRejectList(int pageIndex, int pageSize, ReturnRejectListReq returnRejectListReq);

    /**
     * 入库数量与应入数量不符原因
     *
     * @param id
     * @param reason
     * @return
     */
    AjaxResult notMatchReason(Integer id, String reason);

    /**
     * 缺货入库查询
     * @param pageIndex
     * @param pageSize
     * @param lackGoodsListReq
     * @return
     */
    AjaxResult lackGoodsList(int pageIndex, int pageSize, LackGoodsListReq lackGoodsListReq);

    List<OrderItem> cabinetManagementAfterSaleTaskForCross(Integer warehouseNo, Integer taskStoreNo, LocalDate deliveryDate, Boolean isWaveStockTask);

    /**
     * 拦截任务的生成
     *
     * @param orderNo
     * @param orderItems
     * @param storeNo
     * @param sampleSkus
     * @param reissueList
     */
    void stockTaskInterceptCreate(String orderNo, List<OrderItem> orderItems, Integer storeNo, List<SampleSkuVO> sampleSkus, List<GetAfterDataVO> reissueList);

    /**
     * 批量查询入库信息
     *
     * @param batchStorageSkuDetailDTO
     * @return
     */
    AjaxResult getBatchStorageSkuDetail(BatchStorageSkuDetailDTO batchStorageSkuDetailDTO);


    CapacityUsedDTO getOwnCapacity(Integer areaNo, Date expectTime);

    List<StockTaskDTO> selectWarehouseAndExpectTime(Date startTime, Date endTime, List<Integer> warehouseNo);

    /**
     * 获取产能信息
     *
     * @param warehouseNo
     * @param expectTime
     * @param skuCapacityDTOList
     * @return
     */
    CapacityVO getCapacity(Integer warehouseNo, Date expectTime, List<SkuCapacityDTO> skuCapacityDTOList);


    AjaxResult getCapacityList(Integer pageIndex, Integer pageSize, String warehouseNo, Date startTime, Date endTime, Integer status);


    List<CapacityListExportVO> exportCapacity(String warehouseNo, Date startTime, Date endTime, Integer status);

    /**
     * 手动关闭任务
     * @param stockTaskId
     * @param closeReason
     * @param type
     * @return
     */
    AjaxResult manualCloseStockTask(Integer stockTaskId,String closeReason, Integer type);


    /**
     * saa手动入库
     */
    void saasInStockTask();

    /**
     *
     * @param stockTaskId
     * @return
     */
    AjaxResult queryStockTaskStatusById(Integer stockTaskId);


    void sendCreateTrunk(List<Integer> taskId);


    void createInStoreTask(StockTaskInStoreInput storeInput);


    Long queryStockTaskIdBySourceId(StockTaskInStoreInput storeInput);


    /**
     * 新版本入库任务生成
     */
    void sendCrateInStore(StockStorageCreateReqDTO storageCreateReqDTO);

    /**
     * 新版异常完成
     * @param stockTaskId
     */
    void finishNewInStore(Integer stockTaskId);

    /**
     * 邮件推送选中的出库任务
     * @param stockTaskIdList 出库任务列表
     * @return 接口请求结果
     */
    CommonResult<Void> sendMail(List<Integer> stockTaskIdList);

    /**
     * 根据配送计划生成出库任务
     *
     * <AUTHOR>
     * @date 2023/2/23 11:03
     * @param planId 配送计划ID
     * @return java.lang.Integer 任务ID
     */
    Integer createStockTaskByPlanId(Integer planId);

    /**
     * 查询出库任务列表
     *
     * <AUTHOR>
     * @date 2023/2/16 17:24
     * @param stockTaskQuery 任务查询条件
     * @return List<StockTask> 出库任务列表
     */
    List<StockTask> queryStockTaskList(StockTaskQuery stockTaskQuery);

    /**
     * 根据外部订单号查询出库任务
     *
     * <AUTHOR>
     * @date 2023/4/28 14:00
     * @param outOrderNo 外部订单号
     * @return java.util.List<net.summerfarm.model.domain.StockTask>
     */
    List<StockTask> batchQueryStockTaskByOutOrderNo(String outOrderNo);

    void saleAndAfterOutStockTaskForCross(String key);

    Integer saleOutTaskCreate(List<OrderItem> orderItems, Integer storeNo, Integer outStoreNo, SaleOutTypeEnum outType, List<String> orderNos, List<StockTaskItem> stockTaskItems, Integer type
        , Boolean isWaveTask);
}
