package net.summerfarm.service.helper.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.DTO.market.malltag.SimpleSkuBatchDTO;
import net.summerfarm.model.DTO.market.malltag.SimpleSkuImportDTO;
import net.summerfarm.model.DTO.market.malltag.SimpleSkuInfoDTO;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
public class SimpleSkuDataListener implements ReadListener<SimpleSkuImportDTO> {

    private InventoryMapper inventoryMapper = (InventoryMapper) SpringContextUtil.getBean(
            "inventoryMapper");

    private List<SimpleSkuImportDTO> dataList = Lists.newArrayList();

    private SimpleSkuBatchDTO simpleSkuBatchDTO;

    public SimpleSkuDataListener(SimpleSkuBatchDTO simpleSkuBatchDTO) {
        this.simpleSkuBatchDTO = simpleSkuBatchDTO;
    }

    @Override
    public void invoke(SimpleSkuImportDTO simpleSkuImportDTO, AnalysisContext analysisContext) {
        if (simpleSkuImportDTO == null || StringUtils.isEmpty(simpleSkuImportDTO.getSku())) {
            log.error("【前台标签】数据异常,data:{}", simpleSkuImportDTO);
            return;
        }
        dataList.add(simpleSkuImportDTO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        try {
            List<SimpleSkuInfoDTO> skuInfoDTOList = Lists.newArrayList();
            List<String> failedSkus = Lists.newArrayList();
            List<String> skus = dataList.stream().filter(x -> StringUtils.isNotEmpty(x.getSku()))
                    .map(x -> x.getSku()).distinct().collect(Collectors.toList());

            List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(
                    skus);
            Map<String, SkuBaseInfoDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                    .collect(Collectors.toMap(x -> x.getSku(), Function.identity()));
            for (String sku : skus) {
                SkuBaseInfoDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(sku);
                if (skuBaseInfoDTO == null) {
                    log.warn("【前台标签】未获取到sku信息,sku:{}", sku);
                    failedSkus.add(sku);
                    continue;
                }
                SimpleSkuInfoDTO skuInfoDTO = new SimpleSkuInfoDTO();
                skuInfoDTO.setSku(skuBaseInfoDTO.getSku());
                skuInfoDTO.setPdName(skuBaseInfoDTO.getPdName());
                skuInfoDTO.setWeight(skuBaseInfoDTO.getWeight());
                skuInfoDTO.setLogo(skuBaseInfoDTO.getPicturePath());
                skuInfoDTO.setExtType(skuBaseInfoDTO.getExtType());
                skuInfoDTOList.add(skuInfoDTO);
            }
            simpleSkuBatchDTO.setFailedSkus(failedSkus);
            simpleSkuBatchDTO.setSkuInfoDTOList(skuInfoDTOList);
            log.info("【前台标签】所有数据解析完成！");
        } catch (Exception e) {
            log.error("【前台标签】数据处理异常,cause:{}", Throwables.getStackTraceAsString(e));
        }


    }
}
