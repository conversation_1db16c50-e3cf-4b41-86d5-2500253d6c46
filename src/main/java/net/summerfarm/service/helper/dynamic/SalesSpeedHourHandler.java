package net.summerfarm.service.helper.dynamic;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.IntSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.math.MathUtil;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.facade.order.DistributionSkuSalesFacade;
import net.summerfarm.mapper.manage.DynamicPriceTaskMapper;
import net.summerfarm.model.DTO.inventory.DynamicPriceAdjustDTO;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import net.summerfarm.model.domain.DynamicPriceTask;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import net.summerfarm.ofc.client.req.SkuSalesReq;
import net.summerfarm.ofc.client.resp.SkuSalesResp;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.springframework.stereotype.Component;

/**
 * 小时销售速度 处理器
 *
 * @author: <EMAIL>
 * @create: 2023/2/3
 */
@Slf4j
@Component
public class SalesSpeedHourHandler extends DynamicPriceFieldHandler {

    @Resource
    private DistributionSkuSalesFacade distributionSkuSalesFacade;

    @Resource
    private DynamicPriceTaskMapper dynamicPriceTaskMapper;


    @Override
    public BigDecimal handlerAfter(List<DynamicPriceStatistics> priceStatistics,
            List<DynamicPriceFieldConfig> fieldConfigs,
            DynamicPriceAdjustDTO dynamicPriceAdjustDTO) {
        BigDecimal salesSpeedHour = BigDecimal.ONE;
        Integer limit = dynamicPriceAdjustDTO.getLimit();
        DynamicPriceStatistics lastDayStat = priceStatistics.get(0);
        //昨日出现过售罄或者数据不存在，则昨日数据不统计，则返回1
        if (Objects.equals(lastDayStat.getSellOut(), 1)) {
            log.warn("【小时销售速度概率】昨日出现过售罄,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }

        //获取当前时间段
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH00");
        String hourStr = sdf.format(now);

        DynamicPriceTask todayLast = dynamicPriceTaskMapper.getTodayLast(
                dynamicPriceAdjustDTO.getCategoryType());
        if (todayLast == null) {
            log.warn("【小时销售速度概率】当天没有最近的一次动态调价,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }
        //当天上一次调价的时间点
        String lastHour = sdf.format(todayLast.getTaskExeTime());
        //计算时间差
        int hourInterval = (Integer.valueOf(hourStr) - Integer.valueOf(lastHour)) / 100;
        //前limit天的同时段的小时销售速度
        Map<Integer, BigDecimal> salesSpeedHourFilterMap = Maps.newHashMap();
        for (DynamicPriceStatistics priceStatistic : priceStatistics) {
            String salesSpeedHourStr = priceStatistic.getSalesSpeedHour();
            if (StringUtils.isBlank(salesSpeedHourStr)) {
                continue;
            }
            Map<String, String> salesSpeedHourMap = parseToMap(salesSpeedHourStr,
                    24);
            //获取当天上一次调价时间点A至当前时间点B之间的同时段的小时销售量
            IntSummaryStatistics sumSalesVolumeHour = salesSpeedHourMap.entrySet().stream()
                    .filter(x -> x.getKey().compareTo(lastHour) >= 0
                            && x.getKey().compareTo(hourStr) < 0)
                    .collect(Collectors.summarizingInt(x -> Integer.parseInt(
                            x.getValue())));
            Map<String, String> sellOutHourMap = parseToMap(priceStatistic.getSellOutHour(), 24);

            //过滤掉同时间段内出现过售罄的数据
            boolean present = sellOutHourMap.entrySet().stream()
                    .filter(x -> x.getKey().compareTo(lastHour) >= 0
                            && x.getKey().compareTo(hourStr) < 0 && Objects.equals(x.getValue(),
                            "1")).findAny().isPresent();
            if (present) {
                continue;
            }
            BigDecimal divide = BigDecimal.valueOf(sumSalesVolumeHour.getSum())
                    .divide(new BigDecimal(hourInterval), 2, RoundingMode.HALF_UP);
            salesSpeedHourFilterMap.put(priceStatistic.getDateFlag(), divide);
        }

        List<Entry<Integer, BigDecimal>> filterData = salesSpeedHourFilterMap.entrySet().stream()
                .collect(Collectors.toList());

        //剩下的数据判断是否符合
        boolean satisfyCondition = satisfyCondition(filterData, limit, false);
        if (!satisfyCondition) {
            log.warn("【小时销售速度概率】剩余数据量不满足,因子为1,sku:{},warehouseNo:{}",
                    dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }

        //满足最少数据量，则开始继续清理，现计算出上下四分位数
        List<Double> toCalData = filterData.stream().map(x -> x.getValue().doubleValue())
                .collect(Collectors.toList());
        List<Double> qList = MathUtil.calculate(toCalData);
        if (CollectionUtil.isEmpty(qList)) {
            log.warn("【小时销售速度概率】剩余数据量不满足,因子为1,sku:{},warehouseNo:{}",
                    dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }

        //下四分位数
        double Q1 = qList.get(0);
        //上四分位数
        double Q2 = qList.get(2);
        List<Double> targetData = MathUtil.filterData(Q1, Q2, toCalData);
        log.info("【小时销售速度概率】下四分位:{},上四分位:{},sku:{},warehouseNo:{},清洗后数据:{}", Q1, Q2,
                dynamicPriceAdjustDTO.getSku(), dynamicPriceAdjustDTO.getWarehouseNo(), targetData);

        satisfyCondition = satisfyCondition(targetData, limit, false);
        if (!satisfyCondition) {
            log.warn("【小时销售速度概率】剩余数据量不满足,因子为1,sku:{},warehouseNo:{}",
                    dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }
        double[] targetArr = targetData.stream().mapToDouble(Double::doubleValue).toArray();

        double avg = MathUtil.avg(targetArr);
        //标准差
        double standardDeviation = MathUtil.standardDeviation(targetArr);
        if (standardDeviation <= 0) {
            log.warn("【小时销售速度概率】标准差为0,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }

        //标准正态分布
        NormalDistribution normalDis = new NormalDistribution(avg, standardDeviation);

        //查询当前时间段内的销售量
        SkuSalesReq salesReq = new SkuSalesReq();
        salesReq.setWarehouseNo(lastDayStat.getWarehouseNo());
        salesReq.setOutItemIds(Lists.newArrayList(lastDayStat.getSku()));
        salesReq.setBeginTime(DateUtil.parseLocalDateTime(
                DateUtil.formatYmdDate(LocalDate.now()) + " " + lastHour.substring(0, 2)
                        + ":00:00"));
        salesReq.setEndTime(DateUtil.parseLocalDateTime(
                DateUtil.formatYmdDate(LocalDate.now()) + " " + hourStr.substring(0, 2)
                        + ":00:00"));
        List<SkuSalesResp> list = distributionSkuSalesFacade.listSkuSales(salesReq);
        Long salesVolume = null;
        if (CollectionUtil.isNotEmpty(list)) {
            salesVolume = list.stream()
                    .filter(x -> Objects.equals(x.getOutItemId(), lastDayStat.getSku())).findFirst()
                    .map(x -> x.getSalesVolume().longValue()).orElse(null);
        }
        if (salesVolume == null) {
            log.warn("【小时销售速度概率】当前时间段销量获取失败,因子为1,sku:{},warehouseNo:{}",
                    dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesSpeedHour;
        }

        Double todaySalesSpeedHour = BigDecimal.valueOf(salesVolume)
                .divide(new BigDecimal(hourInterval), 2, RoundingMode.HALF_UP).doubleValue();
        //距当天上次调价的销售速度在近x天同时段销售速度 在正态分布中出现的概率
        double probability = normalDis.cumulativeProbability(todaySalesSpeedHour);
        log.info("【小时销售速度概率】sku:{},warehouseNo:{},均值:{},标准差:{},概率:{}",
                dynamicPriceAdjustDTO.getSku(),
                dynamicPriceAdjustDTO.getWarehouseNo(), avg, standardDeviation, probability);

        salesSpeedHour = calFactor(fieldConfigs, probability);
        if (BigDecimal.ONE.compareTo(salesSpeedHour) == 0) {
            log.warn("【小时销售速度概率】因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
        }
        return salesSpeedHour;
    }
}
