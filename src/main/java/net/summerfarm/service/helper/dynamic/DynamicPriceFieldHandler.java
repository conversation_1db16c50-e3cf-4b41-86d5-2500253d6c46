package net.summerfarm.service.helper.dynamic;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.DTO.inventory.DynamicPriceAdjustDTO;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import net.summerfarm.service.bms.ExpressRunnerService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

/**
 * 字段处理器抽象类
 *
 * @author: <EMAIL>
 * @create: 2023/2/2
 */
@Slf4j
@Service
public abstract class DynamicPriceFieldHandler {

    @Resource
    private ExpressRunnerService expressRunnerService;

    /**
     * 处理公式中的因子
     *
     * @param priceStatistics
     * @param fieldConfigs
     * @param dynamicPriceAdjustDTO
     * @return
     */
    protected abstract BigDecimal handlerAfter(List<DynamicPriceStatistics> priceStatistics,
            List<DynamicPriceFieldConfig> fieldConfigs,
            DynamicPriceAdjustDTO dynamicPriceAdjustDTO);

    /**
     * 处理公式中的因子
     *
     * @param priceStatistics
     * @param fieldConfigs
     * @param dynamicPriceAdjustDTO
     * @return
     */
    public BigDecimal handler(List<DynamicPriceStatistics> priceStatistics,
            List<DynamicPriceFieldConfig> fieldConfigs,
            DynamicPriceAdjustDTO dynamicPriceAdjustDTO) {
        String sku = dynamicPriceAdjustDTO.getSku();
        Integer warehouseNo = dynamicPriceAdjustDTO.getWarehouseNo();
        Integer limit = dynamicPriceAdjustDTO.getLimit();
        //离线数据会把当前没有指标的数据排除掉，默认先把这部分数据当做当天出现有售罄来解决
        List<Integer> dateFlags = listDateByLimit(limit);
        Map<Integer, DynamicPriceStatistics> statisticsMap = priceStatistics.stream()
                .collect(Collectors.toMap(x -> x.getDateFlag(), Function.identity()));
        //先把缺失的天数补全
        List<DynamicPriceStatistics> fillData = dateFlags.stream().map(x -> {
            if (statisticsMap.get(x) == null) {
                DynamicPriceStatistics statistics = new DynamicPriceStatistics();
                statistics.setSku(sku);
                statistics.setWarehouseNo(warehouseNo);
                statistics.setDateFlag(x);
                statistics.setSellOut(1);
                return statistics;
            }
            return statisticsMap.get(x);
        }).collect(Collectors.toList());

        //按时间倒序
        List<DynamicPriceStatistics> limitFilterData = fillData.stream()
                .sorted(Comparator.comparing(DynamicPriceStatistics::getDateFlag).reversed())
                .limit(limit).collect(Collectors.toList());
        DynamicPriceStatistics lastDayStat = limitFilterData.get(0);
        //昨日出现过售罄，则昨日数据不统计，则返回1
        if (Objects.equals(lastDayStat.getSellOut(), 1)) {
            return BigDecimal.ONE;
        }
        //清洗前数据量判断是否满足条件，不满足则返回1
        boolean satisfyCondition = satisfyCondition(limitFilterData, limit, true);
        if (!satisfyCondition) {
            return BigDecimal.ONE;
        }
        return handlerAfter(limitFilterData, fieldConfigs, dynamicPriceAdjustDTO);
    }

    /**
     * 计算因子
     *
     * @param fieldConfigs
     * @param x
     * @return
     */
    public BigDecimal calFactor(List<DynamicPriceFieldConfig> fieldConfigs, double x) {
        BigDecimal result = BigDecimal.ONE;
        DynamicPriceFieldConfig fieldConfigMatch = null;
        //判断x值所在的区间，获取对应的配置算法
        for (DynamicPriceFieldConfig fieldConfig : fieldConfigs) {
            if (BigDecimal.valueOf(x).compareTo(fieldConfig.getLowerLimit()) >= 0
                    && BigDecimal.valueOf(x).compareTo(fieldConfig.getUpperLimit()) < 0) {
                fieldConfigMatch = fieldConfig;
                break;
            }
        }

        if (fieldConfigMatch == null) {
            log.warn("【动态定价】无法匹配x={}", x);
            //因子直接为1
            return result;
        }

        DefaultContext<String, Object> fieldValueMap = new DefaultContext<>();
        fieldValueMap.put("x", x);
        //如果有匹配的算法，则执行算法计算
        String formulaStr = Arrays.stream(fieldConfigMatch.getFormula().split(","))
                .collect(Collectors.joining());
        result = expressRunnerService.calculation(formulaStr, fieldValueMap);
        log.warn("【动态定价】因子计算结果x={}", x);
        return result;
    }

    /**
     * 将英文逗号,分割的key:value 字符串 转换成Map
     *
     * @param value
     * @param limit 截取前limit个值（key按自然数从小到大排序）
     * @return
     */
    public Map<String, String> parseToMap(String value, Integer limit) {
        Map<String, String> map = Maps.newHashMap();
        String[] split = value.split(",");
        for (String s : split) {
            String[] keyValue = s.split(":");
            map.put(keyValue[0], keyValue[1]);
        }
        List<Entry<String, String>> collect = map.entrySet().stream()
                .sorted(Comparator.comparing(x -> x.getKey())).limit(limit).collect(
                        Collectors.toList());
        Map<String, String> result = collect.stream()
                .collect(Collectors.toMap(x -> x.getKey(), x -> x.getValue()));
        return result;
    }

    /**
     * 判断数据量是否满足条件
     *
     * @param list
     * @param limit
     * @param cleanBefore
     * @param <T>
     * @return
     */
    public <T> boolean satisfyCondition(List<T> list, Integer limit, boolean cleanBefore) {
        //清洗前数据规则
        if (cleanBefore) {
            switch (limit) {
                case 7:
                    return list.size() < 4 ? false : true;
                case 14:
                    return list.size() < 7 ? false : true;
                case 30:
                    return list.size() < 15 ? false : true;
                default:
                    return false;
            }
        }

        switch (limit) {
            case 7:
            case 14:
                return list.size() < 4 ? false : true;
            case 30:
                return list.size() < 7 ? false : true;
            default:
                return false;
        }
    }

    /**
     * 获取当前日期的前limit天的日期
     *
     * @param limit
     * @return
     */
    public List<Integer> listDateByLimit(Integer limit) {
        List<Integer> list = Lists.newArrayList();
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        for (int i = 1; i <= limit; i++) {
            Date startDate = DateUtils.addDays(now, -i);
            Integer dateFlag = Integer.valueOf(sdf.format(startDate));
            list.add(dateFlag);
        }
        return list;
    }
}
