package net.summerfarm.service.helper.dynamic;

import cn.hutool.core.collection.CollectionUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.math.MathUtil;
import net.summerfarm.model.DTO.inventory.DynamicPriceAdjustDTO;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.springframework.stereotype.Component;

/**
 * 销量 处理器
 *
 * @author: <EMAIL>
 * @create: 2023/2/3
 */
@Slf4j
@Component
public class SalesVolumeHandler extends DynamicPriceFieldHandler {

    @Override
    public BigDecimal handlerAfter(List<DynamicPriceStatistics> priceStatistics,
            List<DynamicPriceFieldConfig> fieldConfigs,
            DynamicPriceAdjustDTO dynamicPriceAdjustDTO) {
        DynamicPriceStatistics lastDayStat = priceStatistics.get(0);
        BigDecimal salesVolume = BigDecimal.ONE;
        Integer limit = dynamicPriceAdjustDTO.getLimit();
        //昨日出现过售罄，则昨日数据不统计，则返回1
        if (Objects.equals(lastDayStat.getSellOut(), 1)) {
            log.warn("【销量概率】昨日点击量<昨日最小点击量,因子为1,sku:{},warehouseNo:{}",
                    dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesVolume;
        }

        //排除掉售罄的数据
        List<DynamicPriceStatistics> filterData = priceStatistics.stream()
                .filter(x -> x.getSellOut() == 0).collect(Collectors.toList());

        //剩下的数据判断是否符合
        boolean satisfyCondition = satisfyCondition(filterData, limit, false);
        if (!satisfyCondition) {
            log.warn("【销量概率】剩余数据量不满足,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesVolume;
        }

        //满足最少数据量，则开始继续清理，现计算出上下四分位数
        List<Double> toCalData = filterData.stream().map(x -> x.getSalesVolume().doubleValue())
                .collect(Collectors.toList());
        List<Double> qList = MathUtil.calculate(toCalData);
        if (CollectionUtil.isEmpty(qList)) {
            return salesVolume;
        }

        //下四分位数
        double Q1 = qList.get(0);
        //上四分位数
        double Q2 = qList.get(2);
        List<Double> targetData = MathUtil.filterData(Q1, Q2, toCalData);
        log.info("【销量概率】下四分位:{},上四分位:{},sku:{},warehouseNo:{},清洗后数据:{}", Q1, Q2,
                dynamicPriceAdjustDTO.getSku(), dynamicPriceAdjustDTO.getWarehouseNo(), targetData);

        satisfyCondition = satisfyCondition(targetData, limit, false);
        if (!satisfyCondition) {
            log.warn("【销量概率】剩余数据量不满足,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesVolume;
        }
        double[] targetArr = targetData.stream().mapToDouble(Double::doubleValue).toArray();

        double avg = MathUtil.avg(targetArr);
        //标准差
        double standardDeviation = MathUtil.standardDeviation(targetArr);
        if (standardDeviation <= 0) {
            log.warn("【销量概率】标准差为0,因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
            return salesVolume;
        }

        //标准正态分布
        NormalDistribution normalDis = new NormalDistribution(avg, standardDeviation);
        Double lastDaySalesVolume = lastDayStat.getSalesVolume().doubleValue();
        //昨日销售量在正态分布中出现的概率
        double probability = normalDis.cumulativeProbability(lastDaySalesVolume);
        log.info("【销量概率】sku:{},warehouseNo:{},均值:{},标准差:{},概率:{}", dynamicPriceAdjustDTO.getSku(),
                dynamicPriceAdjustDTO.getWarehouseNo(), avg, standardDeviation, probability);
        salesVolume = calFactor(fieldConfigs, probability);
        if (BigDecimal.ONE.compareTo(salesVolume) == 0) {
            log.warn("【销量概率】因子为1,sku:{},warehouseNo:{}", dynamicPriceAdjustDTO.getSku(),
                    dynamicPriceAdjustDTO.getWarehouseNo());
        }
        return salesVolume;
    }
}
