package net.summerfarm.service.item.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2025/1/13 16:39
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeafCategoryDetailResp {

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 三级类目名称
     */
    private String categoryName;

    /**
     * 类目id
     */
    private Long secondCategoryId;

    /**
     * 二级类目名称
     */
    private String secondCategoryName;

    /**
     * 类目id
     */
    private Long firstCategoryId;

    /**
     * 一级类目名称
     */
    private String firstCategoryName;

    /**
     * 类目类型
     */
    private Integer categoryType;

}
