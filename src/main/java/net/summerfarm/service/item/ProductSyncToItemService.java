package net.summerfarm.service.item;


import net.summerfarm.model.input.item.ProductSyncToItemInput;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-05-06
 * @description  同步商品数据到item-center
 */
public interface ProductSyncToItemService {

    /**
     * 处理sku新增
     * @param skuId  inv_id
     */
    void skuInsertSyncToItem(Long skuId);

    /**
     * t同步sku修改到商品中心
     * @param skuId
     */
    void skuUpdateSyncToItem(Long skuId);

    /**
     * 同步product 修改到商品中心
     * @param pdId
     */
    void productsUpdateSyncToItem(Long pdId);

    /**
     * 同步area_sku 数据到item-center
     * @param areaNo
     * @param sku
     */
    void areaSkuInsertSyncToItem(Integer areaNo,String sku);

    /**
     * 同步sku修改到货品
     * @param skuId
     */
    void skuUpdateSyncToSku(Long skuId);

    /**
     * 同步product修改到货品
     * @param pdId
     */
    void productsUpdateSyncToSpu(Long pdId);

    /**
     * 同步属性变化到货品
     * @param pdId
     */
    void propertyUpdateSyncToSpu(Long pdId);

    /**
     * 初始化sku 到货品 和商品
     * @param skuId
     */
    void skuToItemTask(String startTime,String endTime,Long skuId,Long maxSkuId);

    void upsertAreaSkuToItemTask(Long skuId,Long maxSkuId);

    void updateAreaSkuToItemTask(String startTime,String endTime,Integer id,Integer maxId);

    void updateAreaSkuToItemOffsetsTask(ProductSyncToItemInput input);

    void skuAndAreaSkuToItemTask(Long skuId,Long maxSkuId);

    void upsertSkuAndAreaToItem(List<Long> skuIds);

    void upsertSkuToItem(List<Long> skuIds);
}
