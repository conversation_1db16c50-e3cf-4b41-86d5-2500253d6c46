package net.summerfarm.service.srm.impl;

import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.srm.SrmSupplierInventoryMapper;
import net.summerfarm.model.param.srm.SrmSupplierInventoryParam;
import net.summerfarm.model.vo.srm.SrmSupplierInventoryVo;
import net.summerfarm.service.srm.SrmSupplierInventoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class SrmSupplierInventoryServiceImpl implements SrmSupplierInventoryService {

    @Resource
    SrmSupplierInventoryMapper srmSupplierInventoryMapper;
    @Override
    public AjaxResult select(int pageIndex, int pageSize, SrmSupplierInventoryParam srmSupplierInventoryParam) {
        PageHelper.startPage(pageIndex, pageSize);
        List<SrmSupplierInventoryVo> inventoryDetailVOList = srmSupplierInventoryMapper.select(srmSupplierInventoryParam);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(inventoryDetailVOList));
    }
}
