package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.ScheduleTask;

@Deprecated
public interface ScheduleTaskService {
    /**
     * 任务列表页查询
     *
     * @param name      任务名称
     * @param service
     * @param status    任务状态
     * @param pageIndex 页数
     * @param pageSize  分页大小
     * @return
     */
    AjaxResult listPage(String name, String service, Integer status, int pageIndex, int pageSize);

    /**
     * 添加任务
     *
     * @param instance 任务
     * @return
     */
    AjaxResult add(ScheduleTask instance);

    /**
     * 更新任务
     *
     * @param instance 任务数据
     * @return
     */
    AjaxResult update(ScheduleTask instance);

    /**
     * 删除任务
     *
     * @param id 任务id
     * @return
     */
    AjaxResult delete(Integer id);

    /**
     * 立即执行
     *
     * @param id 任务id
     * @return
     */
    AjaxResult execImmediately(Integer id);

    /**
     * 通过商城执行
     * @param id 任务id
     * @return
     */
    AjaxResult executeByMall(Integer id);

    /**
     * 任务初始化
     */
    void init();

    /**
     * 执行任务
     *
     * @param id 任务id
     */
    void execute(Integer id);

    /**
     * 任务执行日志列表查询
     *
     * @param taskId    任务id
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @return
     */
    AjaxResult listLogPage(Integer taskId, int pageIndex, int pageSize);

    /**
     * 任务执行日报
     */
    void dailyReport();


    /**
    * 查询任务信息
    */
    ScheduleTask selectScheduleTask(ScheduleTask scheduleTask);


    /**
     * 重新启用任务
     *
     * @param id 任务id
     * @return
     */
    void resetScheduleTask(Integer id);

    /**
     * 任务执行失败后发送钉钉消息提示
     * @param id
     */
    void sendFailTaskMsg(Integer id);
}
