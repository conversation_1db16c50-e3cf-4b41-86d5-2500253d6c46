package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.StockTask;
import net.summerfarm.model.domain.StockTaskItem;
import net.summerfarm.model.domain.StockTaskItemDetail;
import net.summerfarm.model.input.StockTaskSaleOutReq;
import net.summerfarm.model.vo.StockTaskVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/3/24  19:08
 */
public interface NormalTaskService {

    /**
     *  单条数据入库
     * @param req
     * @param task
     * @param amountMap
     * @param processId
     */
    void skuInOutStore(StockTaskSaleOutReq req , StockTask task, HashMap<String,String> amountMap, Integer processId);

    /**
     * item处理
     * @param req
     * @param stockTaskItemDetail
     * @return
     */
    StockTaskItemDetail handItemDetail(StockTaskSaleOutReq req, StockTaskItemDetail stockTaskItemDetail);

    /**
    * 处理批次信息
    */
    void handleStoreRecord(StockTask stockTask, StockTaskItemDetail stockTaskItemDetail, BigDecimal totalAmount, String sku);

    /**
     * 销售任务关闭更新货位锁定库存信息
     * @param stockTaskItems
     */
    void updateSaleLockGoodsDetail(List<StockTaskItem> stockTaskItems);

    /**
     * 修改订单状态
     * @param task
     */
    void updateOrdersStatus(StockTask task);

    /**
     * 查询条目详情信息
     * @param id
     * @return
     */
    AjaxResult<StockTaskVO> queryItemByTaskId(Integer id);

    /**
     * 根据查询销售出库任务
     * @param storeNo
     * @param deliveryDate
     * @return
     */
    List<StockTask> queryTaskByStoreNo(Integer storeNo, LocalDate deliveryDate);

}
