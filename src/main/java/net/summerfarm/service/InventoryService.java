package net.summerfarm.service;

import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.summerfarm.model.dto.SummerfarmSynchronizedSkuDTO;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.goods.client.req.XmSyncSkuReq;
import net.summerfarm.model.DTO.SkuCapacityDTO;
import net.summerfarm.model.DTO.inventory.SamePropertyInventoryQueryDTO;
import net.summerfarm.model.DTO.inventory.SkuPropertyValueDTO;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.InventoryWMSInfo;
import net.summerfarm.model.input.AdminSkuMappingInput;
import net.summerfarm.model.input.InventoryReq;
import net.summerfarm.model.param.ProductSearchParam;
import net.summerfarm.model.vo.InventorySkuVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.StockVO;
import net.summerfarm.mq.DtsModel;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Package net.summerfarm.controller
 * @Description: 库存管理业务接口
 * @author: <EMAIL>
 * @Date: 2016/7/20
 */
public interface InventoryService {


    /**
     *删除商品
     * @param ids
     * @return
     */
    AjaxResult delete(String[] ids);

    /**
     * 新增库存
     * @param inventoryReq
     * @return
     */
    AjaxResult save(InventoryReq inventoryReq);

    /**
     * 复制sku
     * @param inventoryReq sku入参
     * @return sku及提示信息
     */
    AjaxResult copy(InventoryReq inventoryReq);

    AjaxResult uploadIntroduction(String sku, int picName);

    /**
     *
     * @param id
     * @param inventoryReq
     * @return
     */
    AjaxResult update(String id, InventoryReq inventoryReq);

    /**
     * 审核saas代仓商品
     * @param sku
     * @return
     */
    AjaxResult approvedSaasAgent(String sku,String volume);

    /**
     * 修改数据前 校验sku是否在其他区域是否有售卖信息
     * @param sku
     * @param pdId
     * @return
     */
    AjaxResult vaild(String sku, Integer pdId);

    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @param orderBy
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, InventoryReq selectKeys, String orderBy);

    AjaxResult selectSkuList(InventoryReq selectKeys);


    AjaxResult selectBySpu(InventoryReq selectKeys);

    /**
     * 查询查库库存
     *
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @param tenantId
     * @return
     */
    AjaxResult selectStoreStock(int pageIndex, int pageSize, StockVO selectKeys, Long tenantId);

    Long storeStockDownload(StockVO selectKeys, Integer hasInventory, Long tenantId, HttpServletResponse response);

    void exportStoreStock(StockVO selectKeys);

    /**
     * 查询所有sku及名称
     * @param noWarehouseFlag 过滤代销不入仓标识 0:否;1:是
     * @return
     */
    AjaxResult selectSkus(Integer storeNo, Integer characters, Integer categoryId,Integer noWarehouseFlag);

    /**
     * 商品名称匹配
     * @return
     */
    AjaxResult pdNamesMatch(String pdName);


    /**
     * 查询出库存不为0的
     * @return
     */
    AjaxResult selectQuantityNotEmpty(InventoryReq selectKeys);

    /**
     * 列表
     * @param selectKeys
     * @return
     */
    AjaxResult selectList(InventoryReq selectKeys);

    /**
     * 恢复sku
     * @param sku
     * @return
     */
    AjaxResult recovery(String sku);

    AjaxResult selectSkusGroup(Integer storeNo, Integer characters, Integer categoryId);

    /**
     * 查询单个sku信息
     * @param areaNo
     * @param sku
     * @return
     */
    AjaxResult selectAreaSku(Integer areaNo, String sku);

    /**
     * 查询某个sku的商城价
     * @param sku
     * @param areaNo
     * @return
     */
    AjaxResult selectPriceBySku(String sku, Integer areaNo);


    /**
     * 查询代仓和自营商品分组
     */
    AjaxResult selectByAdminIdSkusGroup(Integer adminId);

    /**
     * 查询代仓和自营商品
     */
    AjaxResult selectByAdminIdSkus(Integer adminId);

    AjaxResult selectSku(String sku);

    /**
     * 查询在某一个仓库在样品池的sku
     */
    AjaxResult selectSampleSku(Integer areaNo, String queryStr);

    /**
     * 查询sku的一些WMS用的外部信息，类目、包装方式、储存区域...
     *
     * @param sku
     * @return
     */
    InventoryWMSInfo queryWMSInfo(String sku);

    /**
     * 批量查询sku的一些WMS用的外部信息
     *
     * @param skuList sku列表
     * @return key->sku value->WMS用的外部信息
     */
    Map<String, InventoryWMSInfo> batchQueryWMSInfo(List<String> skuList);

    /**
     * 查询sku是否为代仓
     * @param sku
     * @return
     */
    AjaxResult isNotDirectStore(String sku, Integer adminId);

    /**
     * 将商品日销数据同步到day_sale_rank表内；每天 4：20同步；
     */
    void syncSkuDaySaleRank();

    /**
     * 查询不在回收站内的sku信息
     * @return
     */
    AjaxResult selectSkuInfo();

    /**
     * 查询不在回收站内的spu信息
     * @return
     */
    AjaxResult selectBySpuInfo();

    AjaxResult skuQuery(InventoryVO query);

    /**
     * 采购创建sku信息
     * @param record sku
     * @return 操作结果
     */
    AjaxResult purchaseCreateSku(InventoryReq record);

    /**
     * sku上新忽略
     * @param sku
     * @param refuseReason
     * @return
     */
    AjaxResult auditFail(String sku, String refuseReason);



    /**
     * 批量sku查询
     * @param input
     * @return
     */
    AjaxResult selectBySkuStr(AdminSkuMappingInput input);

    /**
     * 根据sku查询售卖城市和库存仓
     * @param sku sku编号
     * @return sku售卖城市编号和库存仓
     */
    AjaxResult selectArea(String sku);

    /**
     * 查询sku成本价
     * @param sku sku
     * @param areaNo 城市编号
     * @return 成本价
     */
    BigDecimal selectCostPrice(String sku, Integer areaNo);

    /**
     * 根据条件查询sku
     * @param selectKeys
     * @return
     */
    List<String> selectByCondition(InventoryReq selectKeys);

    /**
     * 调拨单备货状态商品名称模糊匹配
     * @param pageIndex
     * @param pageSize
     * @param queryStr
     * @param warehouseNo
     * @return
     */
    @Deprecated
    AjaxResult match2SkuOrName(int pageIndex, int pageSize, String queryStr, Integer warehouseNo);

    /**
     * 上新发送钉钉消息
     * @param dtsModel
     */
    void handleAuditResult(DtsModel dtsModel);

    /**
     * 查询某个pdId下相同销售属性组sku
     * @param samePropertyInventoryQueryDTO 销售属性信息
     * @return 相同销售属性组sku
     */
    List<String> querySamePropertyInventory(SamePropertyInventoryQueryDTO samePropertyInventoryQueryDTO);

    List<String> getCanBindSkus(Integer pdId, Integer type, String unit,
                                Integer extType,
                                List<SkuPropertyValueDTO> saleValueList,
                                List<Inventory> inventories);

    /**
     * 初始化SKU绑定关系-处理历史数据
     */
    void initBindTask();

    /**
     * 初始化绑定关系
     *
     * @param inventory       破袋/临保商品sku
     * @param noCanBindSku
     * @param multiCanBindSku
     */
    void initBind(Inventory inventory, Set<String> noCanBindSku, Set<String> multiCanBindSku);

    /**
     * 查询invId 查询 sku信息
     */
    Inventory selectById(Long Id);

    /**
     * 批量查询
     * @param invIds
     */
    List<Inventory> selectByIds(List<Long> invIds);

    /**
     * 查询奖励sku信息
     * @param skuName 商品名称
     * @param id 商品id
     * @return
     */
    AjaxResult querySku(String skuName, Integer id);

    /**
     * 查询sku是否存在及是否对供应商可见
     * @param sku sku
     * @return 结果
     */
    Integer selectSkuVisible(String sku);

    /**
     * 查询需要同步的sku信息
     *
     * @return
     */
    List<SummerfarmSynchronizedSkuDTO> queryNeedSynchronizedSkuInfo(String startTime,String endTime);

    /**
     * 查詢需要同步的sku信息
     *
     * @param skuIds
     * @return
     */
    SummerfarmSynchronizedSkuDTO queryNeedSynchronizedSkuInfoBySkuId(Long skuIds);


    /**
     * 获取sku类目
     * @param skus
     * @return
     */
    String getCategory(List<String> skus);

    /**
     * 获取sku重量
     * @param skus
     * @return
     */
    BigDecimal getWeightNums(List<SkuCapacityDTO> skus);


    AjaxResult selectBySkuMsg(String sku);

    /**
     * 根据spuId查询
     *
     * @param spuId
     * @return
     */
    List<Inventory> selectByPdIdAndAdminId(Integer spuId, Integer adminId);

    /**
    * 根据skuList搜索
    * @date 2023/3/1 15:51
    * @param * @Param skuList:
    * @return * @return: java.util.List<net.summerfarm.model.domain.Inventory>
    */
    List<InventorySkuVO> selectSkuInfoBySkuList(List<String> skuList);

    List<Inventory> selectBySkuList(List<String> skuList);

    /**
     * 根据sku编码查询商品信息
     *
     * <AUTHOR>
     * @date 2023/5/4 14:24
     * @param sku sku编码
     * @return net.summerfarm.model.domain.Inventory
     */
    Inventory selectBySku(String sku);

    /**
     * 处理商品变更消息通知
     *
     * <AUTHOR>
     * @date 2023/2/28 11:02
     * @param dtsModel 数据对象
     */
    void handleMsgNotice(DtsModel dtsModel);

    /**
     * 申请代仓
     *
     * <AUTHOR>
     * @date 2023/4/21 18:49
     * @param sku sku编码
     * @param createType 类型
     */
    void editAgentCreateType(String sku, String createType);

    /**
     * saas取消申请代仓，修改sku为纯自营货品
     * @param skuID skuId
     */
    void cancelAgentSku(Long skuID);

    /**
     * 查詢需要同步的sku信息
     * @param skuIds
     * @param pdIds
     * @return
     */
    List<SummerFarmSynchronizedSkuReq> queryNeedSyncSkuList(List<Long> pdIds,List<Long> skuIds);

    List<XmSyncSkuReq> queryNeedSyncSkuListForGoods(List<Long> pdIds, List<Long> skuIds);

    List<Long> querySkuIdsByAdminId(Integer adminId);

    /**
     * @description: 查询全部的sku信息
     * @author: lzh
     * @date: 2023/7/11 18:53
     * @param: [selectKeys]
     * @return: net.summerfarm.common.AjaxResult
     **/
    AjaxResult queryAllSku(InventoryReq selectKeys);

    /** 货品搜索 **/
    PageInfo<InventoryVO> productSearch(ProductSearchParam productSearchParam);

    /**
     * 二级性质校验
     *
     * @param subType 子类型
     * @return {@link AjaxResult}
     */
    @Deprecated
    AjaxResult checkSubType(Integer subType);


    /**
     * 查询sql最小起售量
     * @param sku
     * @return
     */
    Integer selectMinSaleUnit(String sku);
}
