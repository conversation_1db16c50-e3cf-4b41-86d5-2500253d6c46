package net.summerfarm.service.purchase;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.purchase.PurchaselatestArrivalDateDTO;
import net.summerfarm.model.DTO.purchase.SupplierBaseInfoDTO;
import net.summerfarm.model.input.purchase.ProductWarehouseConfigInput;
import net.summerfarm.model.input.purchase.ProductWarehouseConfigUpsert;
import net.summerfarm.model.input.purchase.ProductWarehouseSaveInput;
import net.summerfarm.model.vo.purchase.ProductWarehouseConfigVO;

import java.util.List;

public interface ProductWarehouseConfigService {
    AjaxResult<PageInfo<List<ProductWarehouseConfigVO>>> pageQueryProductConfig(int pageIndex, int pageSize, ProductWarehouseConfigInput input);

    AjaxResult listSpuConfigDetail(Long pdId);

    AjaxResult batchSaveConfig(ProductWarehouseSaveInput productWarehouseSaveInput);

    Boolean dealWarehouseChangeTask();

    Integer manualAddProductConfig(Long pdId, Integer warehouseNo);

    AjaxResult<List<SupplierBaseInfoDTO>> queryEnableSupplier(Long pdId, Integer warehouseNo);

    PurchaselatestArrivalDateDTO getAdvanceDayBySkuWarehouseAndSupplier(String sku, Integer warehouseNo, Integer supplierId);


    void productWarehouseConfigUpdate(ProductWarehouseConfigUpsert productWarehouseConfigUpsert);

    void initWaterLevelAndBacklogDay();
}
