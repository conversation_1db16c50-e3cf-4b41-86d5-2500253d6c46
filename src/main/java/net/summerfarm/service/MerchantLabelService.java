package net.summerfarm.service;

import net.summerfarm.model.input.MerchantLabelReq;
import net.summerfarm.model.vo.MerchantLabelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 商户标签
 * @date 2023/4/11 14:13:52
 */
public interface MerchantLabelService {

    /**
     * @description: 根据type查询所有启用的标签数据
     * @author: lzh
     * @date: 2023/4/11 15:15
     * @param: [merchantLabelReq]
     * @return: net.xianmu.common.result.CommonResult<net.summerfarm.model.vo.MerchantLabelVO>
     **/
    List<MerchantLabelVO> getAll(MerchantLabelReq merchantLabelReq);

    /**
     * @description: 根据用户信息和售后类型获取用户标签
     * @author: lzh
     * @date: 2023/4/11 18:52
     * @param: [merchantLabelReq]
     * @return: net.xianmu.common.result.CommonResult<java.util.List<net.summerfarm.model.vo.MerchantLabelVO>>
     **/
    List<MerchantLabelVO> getMerchantLabel(MerchantLabelReq merchantLabelReq);
}
