package net.summerfarm.service.advancePurchase;

import net.summerfarm.model.domain.AdvancePurchaseAmount;
import net.summerfarm.model.vo.AdvancePurchaseAmountVO;

import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2021/5/7  11:30
 */
public interface AdvancePurchaseAmountService {

    /**
    * 添加金额
    */
    void updateAddAdvanceAmount(Integer supplierId, BigDecimal amount);

    /**
    * 扣减金额 如但前供应商不存在则报错
    */
    void updateSubAdvanceAmount(Integer supplierId, BigDecimal amount);

    /**
    * 查询供应商金额
    */
    AdvancePurchaseAmount selectAmount(Integer supplierId);


    /**
     * 添加金额 如果当前供应商金额不存在,则新增
     */
    void saveAdvanceAmount(Integer supplierId, BigDecimal amount);

    /**
     * 查询供应商金额信息
     */
    AdvancePurchaseAmountVO selectAmountVO(Integer supplierId);

}
