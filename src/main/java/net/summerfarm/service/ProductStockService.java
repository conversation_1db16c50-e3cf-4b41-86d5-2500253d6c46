package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.StockVO;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * @Package: net.summerfarm.service
 * @Description: 库存业务接口(鲜沐)
 * @author: <EMAIL>
 * @Date: 2016/8/30
 */
public interface ProductStockService {

    AjaxResult selectStock(int pageIndex, int pageSize, StockVO selectKeys, String orderBy);



    /**
     * 查询所有
     *
     * @return
     */
    AjaxResult selectBySku(String sku, Integer areaNo);

    /**
     * 用于:价格调整->查看->城市价格信息
     * 查询使用同一sku且使用同一个仓的城市信息
     * @param sku
     * @param areaNo
     * @return
     */
    AjaxResult selectSkuStoreCityInfo(String sku, Integer areaNo);


    /**
     * 修改虚拟库存
     * @return
     */
    AjaxResult updateQuantity(String sku, Integer areaNo, int quantity);

    /**
     * 修改二级城市库存
     * @return
     */
    AjaxResult updateTwo(AreaStoreVO areaStoreVO);

    @Transactional(propagation = Propagation.REQUIRED)
    void reversalPdPriority(String sku, Integer storeNo, Integer onlineQuantityBefore, Integer onlineQuantity);

    AjaxResult  queryAreaStore(String sku, Integer areaNo);

    Boolean checkTrust(String sku, Integer storeNo, Boolean store);


    AjaxResult updateReserveQuantity(Integer storeNo, String sku, Integer reserveQuantity, Integer type);

    AjaxResult trustReserveQuantity(AreaStore areaStore);

    List<String> sendDingTalkAtList(String sku, Integer areaNo, Integer adminId);

    /**
    * 查询配送仓信息
    */
    AjaxResult  selectStoreMsg(int pageIndex, int pageSize, WarehouseInventoryMappingVO inventoryMappingVO);

    Integer calculateTimingQuantity(Integer storeNo, Integer areaNo, String sku);

    /**
    * 切换库存使用
    */
    AjaxResult trustWarehouseNoChange(Integer warehouseNo, Integer storeNo, String sku);

    /**
    * 新 查询库存详细信息
    */
    AjaxResult  queryAreaStoreNew(String sku, Integer warehouseNo);

    AjaxResult selectProductsWarehouse(int pageIndex, int pageSize, String warehouseName);

    /**
     * 批量切换库存使用仓
     * @param storeNos 配送仓编号
     * @param fromStoreNo 复制配送仓编号
     */
    AjaxResult batchTrustChange(String storeNos, Integer fromStoreNo,Integer type);

    void batchHandleChange(List<WarehouseInventoryMapping> wimList, Integer storeNo, Integer fromStoreNo);

    /**
     * 修改库存使用仓
     * @param taskId
     * @param fromStoreNo
     * @param storeNo
     */
    void trustChange(Integer taskId,Integer fromStoreNo, Integer storeNo);

    /**
     * 修改执行任务状态
     * @param taskId
     * @param state
     */
    Boolean updateState(Integer taskId,Integer state);

    /**
     * 查询切换任务状态
     * @param cutTaskId
     * @return
     */
    AjaxResult cutTaskState(String cutTaskId);

    /**
     * 根据类型查询复制城配仓
     * @param type
     * @return
     */
    AjaxResult selectLogisticsCenter(Integer type);

    /**
     * 终止修改库存使用仓切换任务
     * @param taskId
     * @return
     */
    AjaxResult stopCutTask(Integer taskId);

    /**
     * 批量修改单个sku库存使用仓
     * @param warehouseNo
     * @param storeNos
     * @param sku
     * @return
     */
    AjaxResult batchTrustWarehouseNoChange(Integer warehouseNo, String storeNos, String sku);

    /**
     * SKU切换至新库存使用仓
     * @param warehouseNo 新库存仓编号
     * @param sku 品
     * @param storeNo 城配仓编号
     * @param oldWarehouseNo 原库存仓编号
     */
    void changeToNewWarehouse(Integer warehouseNo, String sku, Integer storeNo, Integer oldWarehouseNo);

    /**
     * 查询配送仓信息
     */
    AjaxResult selectAllStoreMsg(WarehouseInventoryMappingVO mappingVO);

    /**
     * 批量更新sku库存使用仓
     * @param excelObjectOssKey 对象OssKey
     */
    void batchUpdateSkuWarehouse(String excelObjectOssKey);

    /**
     * 真正处理批量更新sku库存使用仓
     * @param excelObjectOssKey 对象OssKey
     * @return 字节流
     */
    ByteArrayOutputStream doBatchUpdateSkuWarehouse(String excelObjectOssKey);

    /**
     * 自动开售
     * @param warehouseNo 仓库编号
     * @param sku 商品编号
     */
    void autoOnSale(Integer warehouseNo, String sku);

    /**
     * 批量更新sku库存使用仓 时间限制开关
     */
    void batchUpdateSkuWarehouseSwitch();
}

