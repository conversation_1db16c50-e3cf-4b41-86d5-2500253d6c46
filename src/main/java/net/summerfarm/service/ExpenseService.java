package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Expense;
import net.summerfarm.model.vo.ExpenseVO;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-12
 */
public interface ExpenseService {

    /**
     * 查询报销单
     * @param pageIndex
     * @param pageSize
     * @param expense
     * @return
     */
    AjaxResult selectExpense(int pageIndex, int pageSize, ExpenseVO expense);
    /**
     * 查询报销明细
     * @param id
     * @return
     */
    AjaxResult selectExpenseDetail(int id);
    /**
     * 报销查询导出
     * @param selectKeys
     * @return
     */
    AjaxResult selectExpenseExport(ExpenseVO selectKeys);

    /**
     * 报销单审核.
     * @param expense
     * @return
     */
    AjaxResult auditExpense(Expense expense);
}
