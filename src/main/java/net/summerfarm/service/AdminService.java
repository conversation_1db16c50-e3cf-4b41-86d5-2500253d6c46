package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.AdminMerchantDTO;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.AdminDataPermission;
import net.summerfarm.model.vo.AdminVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/7/23
 */
public interface AdminService {
    /**
     * 根据用户名称查找管理员
     * @param username
     * @return
     */
    Admin select(String username);

    /**
     * 根据id查找管理员
     * @param id
     * @return
     */
    Admin select(Integer id);

    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, AdminVO selectKeys);

    /**
     * 新增
     * @param record
     * @return
     */
    AjaxResult save(AdminVO record);



    /**
     * 锁定或解锁
     * @param id
     * @return
     */
    AjaxResult lock(int id, Admin admin);

    /**
     * 分配角色
     * @param id
     * @param roleIds
     * @param dataPermissions
     * @return
     */
    AjaxResult specifiedRoles(int id, List<Integer> roleIds, List<AdminDataPermission> dataPermissions);

    /**
     * 修改密码
     * @param username
     * @param old
     * @param target
     * @return
     */
    AjaxResult resetPassword(String username, String old, String target);

    /**
     * 查询管理员详细信息
     * @param id
     * @return
     */
    AjaxResult selectWithRole(Integer id);

    AjaxResult selectAll(Integer roleType);

    AjaxResult selectPurchase();

    AjaxResult selectByRoleTypes(String roleTypes);

    AjaxResult update(AdminVO adminVO);

    AjaxResult selectAdminByMerchantId(Long id);

    /**
     * 根据入参的salerId, adminId,查询某个销售下的大客户月统计数据（包括月环比合作门店，交易品种数，交易额GMV)
     * @param selectKeys
     * @return
     */
    AjaxResult queryBigMerchantDataMonthly(Admin selectKeys);

    /**
    * 发送验证码 邮箱 或者 手机号发送验证码
    */
    AjaxResult sendVerificationCode(String phoneOrEmail, String type);

    /**
    * 更改密码
    */
    AjaxResult  updatePassword(String phoneOrEmail, String code, String password);

    /**
    * 自动更新截单时间
    */
    void  autoUpdateCloseTime();

    List<Admin> selectAdministration();

    /**
     * 根据用户名查询
     * @param username
     * @return
     */
    AjaxResult selectByUsername(String username);

    /**
     * 查询大客户信息
     * @return
     */
    AjaxResult selectMajor();

    /**
     * 根据品牌名称模糊搜索品牌
     * @param nameRemakes
     * @return
     */
    AjaxResult selectByNameRemakes(String nameRemakes);

    /**
     * 创建dms账号
     * @param reason 理由
     */
    void createDmsAccount(String reason, String department);

    /**
     * 查询非大客户的管理员账号
     * @return
     */
    AjaxResult selectNotMajorAdmin();

    /**
     * DMS审批回调
     * @param adminId 后台登录人id
     */
    void createDmsCallBack(Integer adminId);

    /**
     * 根据名称备注查询对应的地址id
     * @param nameRemakes 名称备注
     * @return 地址id集合
     */
    CommonResult<List<AdminMerchantDTO>> queryContactByNameRemakes(String nameRemakes);

    /**
     * 默认密码统一初始化
     * @param adminIds 管理员ID集合
     * @return 处理结果
     */
    CommonResult<Void> defaultPwdInit(List<Integer> adminIds);

    /**
     * SRM默认密码统一初始化
     * @return 处理结果
     */
    CommonResult<Void> srmDefaultPwdInit();

    /**
     * 模糊查询管理员名称
     * @param name
     * @return
     */
    CommonResult<List<Admin>> listByName(String name);

    CommonResult<AdminVO> personalInfo();

    /**
     * 获取POP白名单客户
     * @return
     */
    List<AdminVO> listPopWhite();
}

