package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.market.*;
import net.summerfarm.model.vo.ExpandActivityVO;

/**
 * 营销-拓展购买配置业务逻辑类
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface ExpandActivityService {

    /**
     * 保存拓展购买活动配置
     * @param configDto 拓展购买活动配置参数
     * @return 保存结果
     */
    AjaxResult save(ExpandActivityConfigDTO configDto);

    /**
     * 营销-拓展购买活动配置列表查询
     * @param queryDTO 查询参数
     * @return 拓展购买活动配置列表
     */
    PageInfo<ExpandActivityVO> list(ExpandActivityListQueryDTO queryDTO);

    /**
     * 拓展购买名称弹窗列表
     * @param queryDTO 查询参数
     * @return 拓展购买名称弹窗列表
     */
    AjaxResult names(ExpandActivityListQueryDTO queryDTO);

    /**
     * 营销-拓展购买活动配置详情
     * @param id 拓展购买活动配置id
     * @return 详情数据
     */
    AjaxResult detail(Long id);

    /**
     * 停用拓展购买活动
     * @param id 拓展购买活动配置id
     * @return 关闭结果
     */
    AjaxResult close(Long id);

    /**
     * 启用拓展购买活动
     * @param id 拓展购买活动配置id
     * @return 关闭结果
     */
    AjaxResult enable(Long id);

    /**
     * 删除拓展购买活动
     * @param id 拓展购买活动配置id
     * @return 关闭结果
     */
    AjaxResult delete(Long id);

    /**
     * 修改拓展购买活动商品配置
     * @param configId 拓展购买活动配置id
     * @param expandActivityConfigDTO 拓展购买活动商品配置信息
     */
    AjaxResult edit(Long configId, ExpandActivityConfigDTO expandActivityConfigDTO);
}
