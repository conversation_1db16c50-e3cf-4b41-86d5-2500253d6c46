package net.summerfarm.service;


import com.github.pagehelper.PageInfo;
import net.summerfarm.model.ProductsSaleRule;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.param.ProductsSaleRuleQueryParam;
import net.summerfarm.model.vo.ProductsSaleRuleVO;

import java.util.List;

/**
 *
 * @date 2024-11-21 15:54:58
 * @version 1.0
 *
 */
public interface ProductsSaleRuleService {

    /**
     * @description: 列表页
     * @return ProductsSaleRuleEntity
     **/
    PageInfo<ProductsSaleRuleVO> getPage(ProductsSaleRuleQueryParam param);

    /**
     * @description: 新增
     * @return ProductsSaleRuleEntity
     **/
    int insert(ProductsSaleRule entity);


    /**
     * @description: 更新
     * @return:
     **/
    int update(ProductsSaleRule entity);



    /**
     * @description: 删除
     * @return:
     **/
    int delete(Long id);


    PageInfo<Products> selectPageForCreatePurchaseRule(int pageIndex, int pageSize, Products products);

}