package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.PurchaseAdvancedOrder;
import net.summerfarm.model.input.FinanceAccountStatementInput;
import net.summerfarm.model.vo.PurchaseAdvancedOrderVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description 预付单业务层
 * <AUTHOR>
 * @date 2022/1/13 17:35
 */
public interface PurchaseAdvancedOrderService {

    /**
     * 新增预付单
     * 1、预付、审批流新增，如果待打款新增付款单数据
     * 2、预付类型为绑定采购单的话新增绑定关系
     * 3、供应商付款金额充值
     * 4、预付满了则更新标识为不可预付
     *
     * @param purchaseAdvancedOrderVO
     * @return
     */
    AjaxResult savePurchaseAdvancedOrder(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO);

    /**
     * 采购员撤回申请（付款审核中）
     * @param purchaseAdvancedOrder
     * @return
     */
    AjaxResult recall(PurchaseAdvancedOrder purchaseAdvancedOrder);

    /**
     * 预付单详情接口
     * @param purchaseInAdvanceId
     * @return
     */
    AjaxResult selectDetail(Long purchaseInAdvanceId);

    /**
     * 审核接口
     * 通过：更新预付单的状态、如果状态为待打款生成付款单
     * 不通过：更新预付单的状态、预付标识回退、供应商预付金额回退
     *
     * @param id 对账单id
     * @param flag 1.通过 2.不通过 3.撤回
     * @return
     */
    AjaxResult audit(Long id, Integer flag);

    /**
     * 审批接口
     * 通过：更新预付单的状态、生成付款单
     * 不通过：更新预付单的状态、预付标识回退、供应商预付金额回退
     *
     * @param id 对账单id
     * @param flag 1.通过 2.不通过
     * @return
     */
    AjaxResult approve(Long id, Integer flag);

    /**
     * 撤销预付单
     * 更新预付单的状态、预付标识回退、供应商预付金额回退
     *
     * @param id
     * @return
     */
    AjaxResult cancel(Long id);

    /**
     * 预付单列表查询
     * @param pageIndex
     * @param pageSize
     * @param purchaseAdvancedOrderVO
     * @return
     */
    AjaxResult listAll(int pageIndex, int pageSize, PurchaseAdvancedOrderVO purchaseAdvancedOrderVO);

    /**
     * 更新预付单临时备注
     * @param purchaseAdvancedOrderVO
     * @return
     */
    AjaxResult updateTemporaryRemark(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO);

    /**
     * 预付单审核通过
     * @param bizId
     * @param handlerUserId
     */
    void approvedPurchaseAdvancedOrder(Long bizId, String handlerUserId);

    /**
     * 预付单审核拒绝
     * @param bizId
     * @param handlerUserId
     */
    void refusePurchaseAdvancedOrder(Long bizId, String handlerUserId);

    /**
     * 预付单审批任务审核通过
     * @param bizId
     * @param handlerUserId
     */
    void approvedPurchaseAdvancedOrderTask(Long bizId, String handlerUserId);

    /**
     * 预付单审核任务审核拒绝
     * @param bizId
     * @param handlerUserId
     */
    void refusePurchaseAdvancedOrderTask(Long bizId, String handlerUserId);


    /**
     * 预付单审核撤销
     * @param bizId
     */
    void terminatePurchaseAdvancedOrder(Long bizId);

    /**
     * 预付单发起人的审批信息
     * @param purchaseAdvancedOrderVO
     * @return
     */
    AjaxResult auditInformation(PurchaseAdvancedOrderVO purchaseAdvancedOrderVO);

    /**
     * 预付单操作记录
     * @param id
     * @return
     */
    AjaxResult operationRecord(Long id);

}
