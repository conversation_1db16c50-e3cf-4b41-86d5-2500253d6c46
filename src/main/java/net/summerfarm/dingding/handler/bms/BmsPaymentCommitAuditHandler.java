package net.summerfarm.dingding.handler.bms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.common.util.RedissonLockUtil;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.dingding.handler.DingdingConfig;
import net.summerfarm.dingding.handler.DingdingEventHandler;
import net.summerfarm.mapper.bms.BmsPaymentDocumentMapper;
import net.summerfarm.mapper.bms.BmsQuotationProcessMapper;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.DingdingProcessFlowMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.bms.BmsQuotationProcess;
import net.summerfarm.model.domain.dingding.DingdingProcessFlow;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.module.bms.common.model.OperatorBO;
import net.summerfarm.module.bms.domain.PaymentDocAggregate;
import net.summerfarm.module.bms.facade.BmsServiceClientFacade;
import net.summerfarm.service.bms.BmsPaymentDocumentService;
import net.xianmu.bms.client.req.recon.ProcessCallBackRequest;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Classname BmsPaymentCommitAuditHandler
 * @Description 对账单打款审批
 * @Date 2022/09/01 14:02
 * @Created by xrt
 */
@Slf4j
@Component
public class BmsPaymentCommitAuditHandler extends DingdingConfig implements DingdingEventHandler {

    @Resource
    private BmsPaymentDocumentMapper bmsPaymentDocumentMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private BmsQuotationProcessMapper bmsQuotationProcessMapper;
    @Resource
    private DingdingProcessFlowMapper dingdingProcessFlowMapper;
    @Resource
    private BmsPaymentDocumentService bmsPaymentDocumentService;

    @Resource
    private PaymentDocAggregate paymentDocAggregate;


    private static final String  PAYMENT_AUDIT = "PAYMENT_AUDIT_LOCK:%s";

    private static final Integer PAYMENT_AUDIT_TIME = 3;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private BmsServiceClientFacade bmsServiceClientFacade;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.BMS_RECONCILIATION_PAYMENT_AUDIT_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminate(DingdingResultBO result) {
        if (Objects.isNull(result.getBizId())){
            return;
        }
        RLock lock = RedissonLockUtil.tryLock(String.format(PAYMENT_AUDIT,result.getBizId()),PAYMENT_AUDIT_TIME);

        try {
            log.info("提交打款审批撤销!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
            DingdingProcessFlow flow = dingdingProcessFlowMapper.selectByProcessInstanceId(result.getProcessInstanceId());
            Admin adminVO = adminMapper.selectByAid(flow.getCreator());

            //代码迁移
            if(dynamicConfig.bmsCallBackSwitch(DingdingConstantKey.BMS_RECONCILIATION_PAYMENT_AUDIT_CODE)){
                ProcessCallBackRequest request = new ProcessCallBackRequest();
                request.setBizId(result.getBizId());
                request.setRemark(result.getRemark());
                if(Objects.nonNull(adminVO)){
                    request.setOperatorId(adminVO.getAdminId());
                    request.setOperatorName(adminVO.getRealname());
                }

                bmsServiceClientFacade.terminateByCommitProcess(request);
                return;
            }

            BmsQuotationProcess process = new BmsQuotationProcess();
            process.setSourceId(result.getBizId().intValue());
            process.setCreator(Objects.nonNull(adminVO)?adminVO.getRealname():null);
            process.setType(2);
            process.setOperationContent("发起打款审批撤销");
            process.setRemark(result.getRemark());
            bmsQuotationProcessMapper.insert(process);

            bmsPaymentDocumentService.auditPaymentDocumentRefuse(result.getBizId(),result.getHandlerUserId());
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agree(DingdingResultBO result) {
        if (Objects.isNull(result.getBizId())){
            return;
        }
        RLock lock = RedissonLockUtil.tryLock(String.format(PAYMENT_AUDIT,result.getBizId()),PAYMENT_AUDIT_TIME);

        try {
            log.info("提交打款审批通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());

            DingdingProcessFlow flow = dingdingProcessFlowMapper.selectByProcessInstanceId(result.getProcessInstanceId());
            Admin adminVO = adminMapper.selectByAid(flow.getCreator());
            //代码迁移
            if(dynamicConfig.bmsCallBackSwitch(DingdingConstantKey.BMS_RECONCILIATION_PAYMENT_AUDIT_CODE)){
                ProcessCallBackRequest request = new ProcessCallBackRequest();
                request.setBizId(result.getBizId());
                request.setRemark(result.getRemark());
                if(Objects.nonNull(adminVO)){
                    request.setOperatorId(adminVO.getAdminId());
                    request.setOperatorName(adminVO.getRealname());
                }

                bmsServiceClientFacade.agreeByCommitProcess(request);
                return;
            }

            OperatorBO operatorBO = null;
            if(adminVO!=null){
                operatorBO = new OperatorBO();
                operatorBO.setId(adminVO.getAdminId());
                operatorBO.setName(adminVO.getRealname());
            }

            paymentDocAggregate.commitAuditCallback(result.getBizId(),result.getRemark(),operatorBO);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
//        bmsPaymentDocumentService.auditPaymentDocumentSuccess(result.getBizId(),result.getHandlerUserId(),operatorBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refuse(DingdingResultBO result) {
        if (Objects.isNull(result.getBizId())){
            return;
        }
        RLock lock = RedissonLockUtil.tryLock(String.format(PAYMENT_AUDIT,result.getBizId()),PAYMENT_AUDIT_TIME);

        try {
            log.info("提交打款审批不通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
            BmsQuotationProcess process = new BmsQuotationProcess();
            process.setSourceId(result.getBizId().intValue());
            DingdingProcessFlow flow = dingdingProcessFlowMapper.selectByProcessInstanceId(result.getProcessInstanceId());
            Admin adminVO = adminMapper.selectByAid(flow.getCreator());

            //代码迁移
            if(dynamicConfig.bmsCallBackSwitch(DingdingConstantKey.BMS_RECONCILIATION_PAYMENT_AUDIT_CODE)){
                ProcessCallBackRequest request = new ProcessCallBackRequest();
                request.setBizId(result.getBizId());
                request.setRemark(result.getRemark());
                if(Objects.nonNull(adminVO)){
                    request.setOperatorId(adminVO.getAdminId());
                    request.setOperatorName(adminVO.getRealname());
                }

                bmsServiceClientFacade.refuseByCommitProcess(request);
                return;
            }

            process.setCreator(Objects.nonNull(adminVO)?adminVO.getRealname():null);
            process.setType(2);
            process.setOperationContent("发起打款审批失败");
            process.setRemark(result.getRemark());

            bmsQuotationProcessMapper.insert(process);

            bmsPaymentDocumentService.auditPaymentDocumentRefuse(result.getBizId(),result.getHandlerUserId());
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }
}
