package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.service.FinanceAccountStatementService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: FinanceAccountStatementAuditHandler
 * 采购对账单审批回调
 * @date 2022/7/2217:09
 */
@Slf4j
//@Component
public class FinanceAccountStatementAuditHandler extends DingdingConfig implements DingdingEventHandler {

    @Resource
    private FinanceAccountStatementService financeAccountStatementService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.PURCHASE_STATEMENT_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void terminate(DingdingResultBO result) {
        //后台撤销
        log.info("审批撤销或终止了!bizId是 {}", result.getBizId());
        financeAccountStatementService.revokeAccountStatement(result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void agree(DingdingResultBO result) {
        //回调时会去确认对账单的状态 如果已经是作废则不做操作
        log.info("审批通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financeAccountStatementService.approvedAccountStatement(result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void refuse(DingdingResultBO result) {
        //回调时会去确认对账单的状态 如果已经是作废则不做操作
        log.info("审批不通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financeAccountStatementService.rejectAccountStatement(result.getBizId(), result.getHandlerUserId());

    }
}
