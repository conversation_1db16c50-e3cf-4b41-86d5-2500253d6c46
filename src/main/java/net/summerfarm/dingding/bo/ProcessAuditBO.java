package net.summerfarm.dingding.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 同意或拒绝审批任务
 * @date 2023/4/27 14:13:25
 */
@Data
public class ProcessAuditBO {

    /**
     * 审批实例ID。
     */
    private String processInstanceId;

    /**
     * 审批意见，可为空。
     */
    private String remark;

    /**
     * 审批操作，取值。
     *
     * agree：同意
     *
     * refuse：拒绝
     */
    private String result;

    /**
     * 操作人userId。
     */
    private String actionerUserId;

    /**
     * 任务ID。
     */
    private Long taskId;
}
