package net.summerfarm.dao.stockTransfer;

import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockTransferItemDAO {
    /**
     * 批量插入
     *
     * @param items 对象列表
     */
    void batchInsert(@Param("items") List<StockTransferItemDO> items);

    /**
     * 根据主键id查询
     */
    StockTransferItemDO selectById(@Param("id") Long id);

    /**
     * 根据stockTransferId查询
     */
    List<StockTransferItemDO> listByStockTransferId(@Param("transferId") Long transferId);

    /**
     * 根据stockTransferIds和sku查询
     */
    List<Long> listByStockTransferIdsAndSku(@Param("transferIds") List<Long> transferIds,
                                                    @Param("sku") String sku);

    /**
     * 根据主键列表查询
     *
     * @param ids
     * @return
     */
    List<StockTransferItemDO> listByIds(@Param("ids") List<Long> ids);

    /**
     * 根据skus查询实例id列表
     */
    List<Long> listBySku(@Param("skus") List<String> skus);

    /**
     * 根据stockTransferId列表查询
     */
    List<StockTransferItemDO> listByStockTransferIds(@Param("transferIds") List<Long> transferIds);
}
