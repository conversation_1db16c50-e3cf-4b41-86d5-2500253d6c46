package net.summerfarm.dao;

import net.summerfarm.dao.dataobject.StockTaskStorageDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/15  14:02
 */
@Repository
public interface StockTaskStorageDAO {

    /**
     * 新增入库任务
     * @param stockTaskStorageDO
     * @return
     */
    int saveStockTaskStorage(StockTaskStorageDO stockTaskStorageDO);

    /**
     * 查询任务信息列表
     * @param queryDO
     * @return
     */
    //List<StockTaskStorageDO> queryStockTaskStorageDOList(StockTaskStorageQueryDO queryDO);

    /**
     * 查询任务信息
     * @param id
     * @return
     */
    StockTaskStorageDO queryStockTaskStorageDO(Long id);

    /**
     * old task
     * @param taskId
     * @return
     */
    StockTaskStorageDO queryByTaskId(Long taskId);

    List<StockTaskStorageDO> queryMoreInBySourceId(@Param("sourceId") String sourceId);

    List<StockTaskStorageDO> queryByTime(LocalDate time);

    //List<SourceIdQuantityDO> queryBySourceIdsAndSkus(@Param("sourceIds") List<String> sourceIds, @Param("skus") List<String> skus);


    /**
     * 更新任务信息
     * @param stockTaskStorageDO
     * @return
     */
    int  updateStockTaskStorageDO(StockTaskStorageDO stockTaskStorageDO);


    /**
     * 批量查询
     * @param taskIdList
     * @return
     */
    List<StockTaskStorageDO> queryStockTaskBatch(List<Long> taskIdList);

    /**
     * renwu
     * @param stockTaskStorageDO
     * @return
     */
    List<StockTaskStorageDO> queryAllStockTaskStorageDOList(StockTaskStorageDO stockTaskStorageDO);


    /**
     * 根据id chaxun
     * @param stockTaskStorageDO
     * @return
     */
    StockTaskStorageDO queryByStockTaskStorageDO(StockTaskStorageDO stockTaskStorageDO);


    /**
     * 初始化新增入库任务
     * @param stockTaskStorageDO
     * @return
     */
    @Deprecated
    int saveInitStockTaskStorage(StockTaskStorageDO stockTaskStorageDO);


    /**
     * 更新任务信息 初始化
     * @param stockTaskStorageDO
     * @return
     */
    @Deprecated
    int  updateStockTaskNameStorageDO(StockTaskStorageDO stockTaskStorageDO);

    /**
     * 入库中任务数量
     * @param warehouseNo
     * @return
     */
    Long countPartFinishedTask(@Param("warehouseNo") Integer warehouseNo);

}
