package net.summerfarm.provider.impl;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelperManual;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.manage.client.wms.WarehouseProvider;
import net.summerfarm.manage.client.wms.dto.req.WarehouseNameRefreshDTO;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;
import net.summerfarm.model.param.BatchProveParam;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.service.BatchProveService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库信息
 *
 * <AUTHOR> yefeng
 * @create_time: 2023/01/04  17:11
 */
@Slf4j
@Component
@DubboService
public class WarehouseProviderImpl implements WarehouseProvider {
    @Resource
    BatchProveService proveService;

    /**
     * 库存列表查询
     *
     * @param pageIndex         下标
     * @param pageSize          页码
     * @param warehouseQueryReq 查询条件
     * @return
     **/
    @Override
    public DubboResponse<PageInfo<WarehouseStorageDTO>> inventoryList(Integer pageIndex, Integer pageSize, WarehouseQueryReq warehouseQueryReq) {
        BatchProveParam param = new BatchProveParam();
        BeanUtils.copyProperties(warehouseQueryReq, param);
        PageInfo<AreaStoreVO> pageInfo = (PageInfo<AreaStoreVO>) proveService.selectList(pageSize, pageIndex, param).getData();
        PageInfo<WarehouseStorageDTO> dtoPageInfo = PageInfoHelperManual.pageInfoConvert(pageInfo, WarehouseStorageDTO.class);

        return DubboResponse.getOK(dtoPageInfo);
    }

    /**
     * 证明信息
     *
     * @param pageIndex         下标
     * @param pageSize          页码
     * @param warehouseQueryReq 查询条件
     * @return
     **/
    @Override
    public DubboResponse<PageInfo<WarehouseBatchProveRecordDTO>> proveList(Integer pageIndex, Integer pageSize, WarehouseQueryReq warehouseQueryReq) {
        BatchProveParam param = new BatchProveParam();
        BeanUtils.copyProperties(warehouseQueryReq, param);
        PageInfo<WarehouseBatchProveRecordVO> pageInfo = (PageInfo<WarehouseBatchProveRecordVO>) proveService.selectBatchProveList(pageSize, pageIndex, param).getData();
        PageInfo<WarehouseBatchProveRecordDTO> dtoPageInfo = PageInfoHelperManual.pageInfoConvert(pageInfo);

        List<WarehouseBatchProveRecordDTO> dtoList = pageInfo.getList().stream().map(p -> {
            WarehouseBatchProveRecordDTO dto = new WarehouseBatchProveRecordDTO();
            BeanUtils.copyProperties(p, dto);
            if (p.getResidueReport() != null) {
                dto.setPesticideResiduePictures(p.getResidueReport().getPesticideResiduePictures());
            }
            if (p.getHaveRedisReport()==null){
                dto.setHaveRedisReport(NumberUtils.INTEGER_ZERO);
            }
            return dto;
        }).collect(Collectors.toList());

        dtoPageInfo.setList(dtoList);
        return DubboResponse.getOK(dtoPageInfo);
    }

    @Override
    public DubboResponse<Integer>  refreshWarehouseName(WarehouseNameRefreshDTO dto) {
        if (dto == null || dto.getWarehouseNo() == null || StringUtils.isEmpty(dto.getWarehouseName())){
            return DubboResponse.getOK(0);
        }

        Global.warehouseMap.put(dto.getWarehouseNo().intValue(), dto.getWarehouseName());

        return DubboResponse.getOK(1);
    }
}
