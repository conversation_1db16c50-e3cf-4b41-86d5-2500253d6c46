package net.summerfarm.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.inventory.InventoryWriteProvider;
import net.summerfarm.manage.client.inventory.dto.req.InventoryUpdateReqDTO;
import net.summerfarm.manage.client.inventory.dto.req.UpdateSkuOutdatedReqDTO;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: <EMAIL>
 * @create: 2023/3/17
 */
@Slf4j
@DubboService
@Component
public class InventoryWriteProviderImpl implements InventoryWriteProvider {

    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private InventoryMapper inventoryMapper;


    private static final long MAX_LIMIT = 50;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> batchUpdateQualityTime(List<InventoryUpdateReqDTO> list) {
        log.info("batchUpdateQualityTime 入参:{}", list);
        if (CollectionUtil.isEmpty(list) || list.size() > MAX_LIMIT) {
            return DubboResponse.getDefaultError("sku数量限制");
        }
        List<InventoryUpdateReqDTO> filterList = list.stream()
                .filter(x -> x.getQualityTimeType() != null).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterList)) {
            return DubboResponse.getDefaultError("保质期时长类型全部为空");
        }
        productsMapper.batchUpdate(filterList);
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse updateSkuOutdated(List<UpdateSkuOutdatedReqDTO> skuOutdatedReqDTOS) {
        log.info("updateSkuOutdated 入参:{}", skuOutdatedReqDTOS);
        if (CollectionUtil.isEmpty(skuOutdatedReqDTOS) || skuOutdatedReqDTOS.size() > MAX_LIMIT) {
            return DubboResponse.getDefaultError("sku数量限制");
        }
        inventoryMapper.batchUpdateOutdated(skuOutdatedReqDTOS);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
