package net.summerfarm.module.pms.infrastructure.mapper.offline;

import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesPaymentIntervalEntity;
import net.summerfarm.module.pms.infrastructure.param.PurchasesInboundQueryParam;

import java.util.List;

public interface PurchasesInboundDetailAggregateOfflineMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PurchasesInboundDetailAggregateOfflineEntity record);

    int insertSelective(PurchasesInboundDetailAggregateOfflineEntity record);

    PurchasesInboundDetailAggregateOfflineEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchasesInboundDetailAggregateOfflineEntity record);

    int updateByPrimaryKey(PurchasesInboundDetailAggregateOfflineEntity record);

    /**
     * 查询入库单区间信息 最大最小id
     * @param queryParam
     * @return
     */
    PurchasesPaymentIntervalEntity queryInterval(PurchasesInboundQueryParam queryParam);

    /**
     * 筛选条件查询
     * @param queryParam
     * @return
     */
    List<PurchasesInboundDetailAggregateOfflineEntity> selectByParam(PurchasesInboundQueryParam queryParam);
}