package net.summerfarm.module.pms.infrastructure.mapper.offline;

import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesPaymentIntervalEntity;
import net.summerfarm.module.pms.infrastructure.param.PurchasesOutboundQueryParam;

import java.util.List;

public interface PurchasesOutboundDetailAggregateOfflineMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PurchasesOutboundDetailAggregateOfflineEntity record);

    int insertSelective(PurchasesOutboundDetailAggregateOfflineEntity record);

    PurchasesOutboundDetailAggregateOfflineEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchasesOutboundDetailAggregateOfflineEntity record);

    int updateByPrimaryKey(PurchasesOutboundDetailAggregateOfflineEntity record);

    /**
     * 根据查询条件查询区间
     *
     * @param queryParam
     * @return
     */
    PurchasesPaymentIntervalEntity queryInterval(PurchasesOutboundQueryParam queryParam);

    /**
     * 条件查询
     * @param queryParam
     * @return
     */
    List<PurchasesOutboundDetailAggregateOfflineEntity> selectByParam(PurchasesOutboundQueryParam queryParam);


}