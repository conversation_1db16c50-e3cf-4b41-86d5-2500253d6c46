package net.summerfarm.module.pms.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.enums.SupplierTypeEnum;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity;
import net.summerfarm.module.pms.model.excel.purchases.PurchasesInboundExportExcel;
import net.summerfarm.module.pms.model.excel.purchases.PurchasesOutboundExportExcel;
import net.summerfarm.module.pms.model.input.PurchasesPaymentExportInput;

import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/14 10:44
 */
public class PurchasesConvertUtil {

    private final static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm");

    /**
     * 采购入库单导出
     *
     * @param entity
     * @return
     */
    public static PurchasesInboundExportExcel convert(PurchasesInboundDetailAggregateOfflineEntity entity) {
        PurchasesInboundExportExcel inboundExportExcel = new PurchasesInboundExportExcel();
        if (Objects.nonNull(entity.getInboundTime())) {
            inboundExportExcel.setInboundTime(entity.getInboundTime().format(FORMATTER));
        }
        inboundExportExcel.setBatchNo(entity.getBatchNo());
        inboundExportExcel.setPurchasesNo(entity.getBatchNo());
        inboundExportExcel.setSupplierTypeDesc(SupplierTypeEnum.getNameById(entity.getSupplierType()));
        inboundExportExcel.setSupplierName(entity.getSupplierName());
        inboundExportExcel.setInboundCreateUserName(entity.getInboundCreateUserName());
        inboundExportExcel.setInboundCreateUserPhone(entity.getInboundCreateUserPhone());
        inboundExportExcel.setWarehouseName(entity.getWarehouseName());
        inboundExportExcel.setSaasSkuNo(entity.getSaasSkuNo());
        inboundExportExcel.setSaasSkuName(entity.getSaasSkuName());
        inboundExportExcel.setSaasSkuSpecification(entity.getSaasSpecification());
        inboundExportExcel.setSaasSkuPackaging(entity.getSaasPackaging());
        inboundExportExcel.setPurchasesPrice(entity.getPurchasesPrice());
        inboundExportExcel.setInboundPrice(entity.getInboundPrice());
        inboundExportExcel.setPurchasesStock(entity.getPurchasesStock());
        inboundExportExcel.setInboundStock(entity.getInboundStock());

        return inboundExportExcel;
    }


    private static final String OUTBOUND_TYPE = "已入库退货";


    /**
     * 出库单 convert
     *
     * @param entity
     * @return
     */
    public static PurchasesOutboundExportExcel convert(PurchasesOutboundDetailAggregateOfflineEntity entity) {
        PurchasesOutboundExportExcel outboundExportExcel = new PurchasesOutboundExportExcel();
        if (Objects.nonNull(entity.getOutboundTime())) {
            outboundExportExcel.setOutboundTime(entity.getOutboundTime().format(FORMATTER));
        }
        outboundExportExcel.setRefundBatchNo(entity.getRefundBatchNo());
        outboundExportExcel.setOutboundTypeDesc(OUTBOUND_TYPE);
        outboundExportExcel.setOutboundCreateUserName(entity.getOutboundCreateUserName());
        outboundExportExcel.setOutboundCreateUserPhone(entity.getOutboundCreateUserPhone());
        outboundExportExcel.setBatchNo(entity.getBatchNo());
        outboundExportExcel.setInboundCreateUserName(entity.getInboundCreateUserName());
        outboundExportExcel.setInboundCreateUserPhone(entity.getInboundCreateUserPhone());
        outboundExportExcel.setSupplierTypeDesc(SupplierTypeEnum.getNameById(entity.getSupplierType()));
        outboundExportExcel.setSupplierName(entity.getSupplierName());
        outboundExportExcel.setSaasSkuNo(entity.getSaasSkuNo());
        outboundExportExcel.setSaasSkuName(entity.getSaasSkuName());
        outboundExportExcel.setSaasSkuSpecification(entity.getSaasSpecification());
        outboundExportExcel.setSaasSkuPackaging(entity.getSaasPackaging());
        outboundExportExcel.setWarehouseName(entity.getWarehouseName());
        outboundExportExcel.setOutboundStock(entity.getOutboundStock());
        outboundExportExcel.setOutboundPrice(entity.getOutboundPrice());

        return outboundExportExcel;
    }

    /**
     * 导出任务 param
     *
     * @param exportInput
     * @return
     */
    public static String saasTaskParam(PurchasesPaymentExportInput exportInput) {
        JSONObject object = new JSONObject();
        object.put("开始时间", PmsDateUtils.formatYYYY_MM_DD_HH_MM(exportInput.getStartTime()));
        object.put("截止时间", PmsDateUtils.formatYYYY_MM_DD_HH_MM(exportInput.getEndTime()));

        return JSON.toJSONString(object);
    }

}
