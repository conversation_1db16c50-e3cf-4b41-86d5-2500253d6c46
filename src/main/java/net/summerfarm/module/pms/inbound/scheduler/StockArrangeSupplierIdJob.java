package net.summerfarm.module.pms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.PurchasesBackService;
import net.summerfarm.service.StockArrangeService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 *
 * 入库预约单初始化仓库编号
 * 原-入库预约单修复供应商任务
 *
 * **/
@Component
@Slf4j
public class StockArrangeSupplierIdJob extends XianMuJavaProcessor {

    @Resource
    private StockArrangeService stockArrangeService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("入库预约单初始化仓库信息任务同步开始:{}", LocalDate.now());
        stockArrangeService.initHistoryWarehouseData();
        log.info("入库预约单初始化仓库信息任务同步结束:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
