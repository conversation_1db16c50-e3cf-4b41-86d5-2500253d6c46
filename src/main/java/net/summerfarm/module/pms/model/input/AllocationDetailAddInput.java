package net.summerfarm.module.pms.model.input;

import lombok.Data;
import net.summerfarm.model.DTO.ProducePeriodDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2023/2/3 10:53
 */
@Data
public class AllocationDetailAddInput implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 出库数量
     */
    private Integer outQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * sku
     */
    private String sku;

    /**
     * 规格
     */
    private String weight;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 商品名称
     */
    private  String pdName;

    /**
     * 冻结库存
     */
    private Integer allocationLockQuantity;


    /**
     * 保质期
     */
    private List<ProducePeriodDTO> producePeriodDTOList;

}
