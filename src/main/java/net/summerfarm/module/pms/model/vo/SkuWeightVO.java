package net.summerfarm.module.pms.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> chenjie
 * @date : 2023-05-15 10:35
 * @describe :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuWeightVO {
    /** sku **/
    private String sku;
    /** 净重 **/
    private BigDecimal netWeight;
}
