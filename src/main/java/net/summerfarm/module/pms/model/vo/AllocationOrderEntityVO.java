package net.summerfarm.module.pms.model.vo;

import net.summerfarm.module.pms.infrastructure.model.AllocationOrderEntity;
import net.summerfarm.model.domain.StockAllocationProcess;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/6/5
 */
@ApiModel(description = "库存调拨单VO")
public class AllocationOrderEntityVO extends AllocationOrderEntity {

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "步骤")
    private Integer process;

    @ApiModelProperty(value = "库存调拨条目列表")
    private List<AllocationOrderItemEntityVO> stockAllocationItemVOs;

    @ApiModelProperty(value = "库存调拨流程记录列表")
    private List<StockAllocationProcess> stockAllocationProcesses;

    @ApiModelProperty(value = "总货值")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "调拨类型:0每周调拨、1每两周调拨、2次日达")
    private Integer type;

    @ApiModelProperty(value = "销售参与：0.不参与1.参与")
    private Integer salePartake;

    @ApiModelProperty(value = "调拨周期:1每周、2每两周")
    private Integer cycleType;

    @ApiModelProperty(value = "销量日期")
    private LocalDate cycleDate;

    @ApiModelProperty(value = "调出仓销量预估天数")
    private Integer outDays;

    @ApiModelProperty(value = "调入仓销量预估天数")
    private Integer inDays;

    @ApiModelProperty(value = "调出仓历史销量天数")
    private Integer outHistoryDays;

    @ApiModelProperty(value = "调入仓历史销量天数")
    private Integer inHistoryDays;

    @ApiModelProperty(value = "配送时间：星期")
    private Integer logisticsTime;

    @ApiModelProperty(value = "物流信息配置id")
    private Integer configId;

    @ApiModelProperty(value = "销量预估天数")
    private Integer intervalDays;

    /**
     * 采购协作0.不参与1.参与
     */
    private Integer purchasePartake;

    /**
     * 需求销量开始时间
     */
    private LocalDateTime saleQuantityStart;

    /**
     * 需求销量结束时间
     */
    private LocalDateTime saleQuantityEnd;

    /**
     * 0:人工 1:调拨计划
     */
    private Integer originType;


    /**
     * 干线调度 (0:关闭 1:开启)
     */
    private Integer trunkFlag;

    public Integer getTrunkFlag() {
        return trunkFlag;
    }

    public void setTrunkFlag(Integer trunkFlag) {
        this.trunkFlag = trunkFlag;
    }

    public Integer getOriginType() {
        return originType;
    }

    public void setOriginType(Integer originType) {
        this.originType = originType;
    }

    public List<StockAllocationProcess> getStockAllocationProcesses() {
        return stockAllocationProcesses;
    }

    public void setStockAllocationProcesses(List<StockAllocationProcess> stockAllocationProcesses) {
        this.stockAllocationProcesses = stockAllocationProcesses;
    }

    public List<AllocationOrderItemEntityVO> getStockAllocationItemVOs() {
        return stockAllocationItemVOs;
    }

    public void setStockAllocationItemVOs(List<AllocationOrderItemEntityVO> stockAllocationItemVOs) {
        this.stockAllocationItemVOs = stockAllocationItemVOs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getProcess() {
        return process;
    }

    public void setProcess(Integer process) {
        this.process = process;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSalePartake() {
        return salePartake;
    }

    public void setSalePartake(Integer salePartake) {
        this.salePartake = salePartake;
    }

    public Integer getCycleType() {
        return cycleType;
    }

    public void setCycleType(Integer cycleType) {
        this.cycleType = cycleType;
    }

    public LocalDate getCycleDate() {
        return cycleDate;
    }

    public void setCycleDate(LocalDate cycleDate) {
        this.cycleDate = cycleDate;
    }

    public Integer getOutDays() {
        return outDays;
    }

    public void setOutDays(Integer outDays) {
        this.outDays = outDays;
    }

    public Integer getInDays() {
        return inDays;
    }

    public void setInDays(Integer inDays) {
        this.inDays = inDays;
    }

    public Integer getOutHistoryDays() {
        return outHistoryDays;
    }

    public void setOutHistoryDays(Integer outHistoryDays) {
        this.outHistoryDays = outHistoryDays;
    }

    public Integer getInHistoryDays() {
        return inHistoryDays;
    }

    public void setInHistoryDays(Integer inHistoryDays) {
        this.inHistoryDays = inHistoryDays;
    }

    public Integer getLogisticsTime() {
        return logisticsTime;
    }

    public void setLogisticsTime(Integer logisticsTime) {
        this.logisticsTime = logisticsTime;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getIntervalDays() {
        return intervalDays;
    }

    public void setIntervalDays(Integer intervalDays) {
        this.intervalDays = intervalDays;
    }

    public Integer getPurchasePartake() {
        return purchasePartake;
    }

    public void setPurchasePartake(Integer purchasePartake) {
        this.purchasePartake = purchasePartake;
    }

    public LocalDateTime getSaleQuantityStart() {
        return saleQuantityStart;
    }

    public void setSaleQuantityStart(LocalDateTime saleQuantityStart) {
        this.saleQuantityStart = saleQuantityStart;
    }

    public LocalDateTime getSaleQuantityEnd() {
        return saleQuantityEnd;
    }

    public void setSaleQuantityEnd(LocalDateTime saleQuantityEnd) {
        this.saleQuantityEnd = saleQuantityEnd;
    }


}
