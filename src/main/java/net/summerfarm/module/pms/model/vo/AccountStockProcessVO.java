package net.summerfarm.module.pms.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> chenjie
 * @date : 2023-05-05 15:16
 * @describe :
 */
@Data
public class AccountStockProcessVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** sku **/
    private String sku;

    /** 采购单号 **/
    private String listNo;
    /** 入库进度ID **/
    private Integer stockTaskProcessId;
    /**
     * 添加时间
     */
    private LocalDateTime addTime;
    /** 数量 **/
    private Integer quantity;
    /** 单价 **/
    private BigDecimal price;
    /** 供应商ID **/
    private Integer supplierId;
    /** 类型 **/
    private Integer type;
    /**
     * 出入库任务单详情表id
     */
    private Integer stockTaskProcessDetailId;

    /** 规格 **/
    private String weight;
    /** 商品名 **/
    private String pdName;

}
