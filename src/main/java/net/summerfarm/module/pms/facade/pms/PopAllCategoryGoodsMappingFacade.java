package net.summerfarm.module.pms.facade.pms;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.pms.facade.pms.converter.PopAllCategoryGoodsMappingConverter;
import net.summerfarm.module.pms.facade.pms.dto.PopAllCategoryGoodsMappingFacadeDTO;
import net.summerfarm.module.pms.facade.pms.req.PopAllCategoryGoodsMappingSaveFacadeReq;
import net.summerfarm.pms.client.provider.pop.PopAllCategoryGoodsMappingCommandProvider;
import net.summerfarm.pms.client.provider.pop.PopAllCategoryGoodsMappingQueryProvider;
import net.summerfarm.pms.client.req.pop.PopAllCategoryGoodsMappingQueryReq;
import net.summerfarm.pms.client.req.pop.PopAllCategoryGoodsMappingSaveReq;
import net.summerfarm.pms.client.resp.pop.PopAllCategoryGoodsMappingResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ExceptionUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2025/1/10 15:41
 * @<AUTHOR>
 */
@Slf4j
@Component
public class PopAllCategoryGoodsMappingFacade {

    @DubboReference
    private PopAllCategoryGoodsMappingCommandProvider popAllCategoryGoodsMappingCommandProvider;

    @DubboReference
    private PopAllCategoryGoodsMappingQueryProvider popAllCategoryGoodsMappingQueryProvider;

    /**
     * 保存pop&全品类映射关系
     *
     * <AUTHOR>
     * @date 2025/1/13 16:30
     * @param saveFacadeReq
     */
    public void savePopAllCategoryGoodsMapping(PopAllCategoryGoodsMappingSaveFacadeReq saveFacadeReq) {
        ExceptionUtil.Params.checkAndThrow(Objects.nonNull(saveFacadeReq)
                && Objects.nonNull(saveFacadeReq.getAllCategorySku())
                && Objects.nonNull(saveFacadeReq.getPopSku()), "保存pop&全品类映射关系失败，参数缺失");
        PopAllCategoryGoodsMappingSaveReq saveReq = PopAllCategoryGoodsMappingSaveReq.builder()
                .popSku(saveFacadeReq.getPopSku())
                .allCategorySku(saveFacadeReq.getAllCategorySku()).build();
        DubboResponse<Boolean> response = popAllCategoryGoodsMappingCommandProvider.save(saveReq);
        if (!response.isSuccess() || !response.getData()) {
            log.warn("保存pop&全品类映射关系失败:{}", response.getMsg());
            throw new BizException("全品类映射关系失败:" + response.getMsg());
        }

    }


    public List<PopAllCategoryGoodsMappingFacadeDTO> queryPopAllCategoryGoodsMapping(String popSku) {
        if (StringUtils.isBlank(popSku)) {
            return Lists.newArrayList();
        }
        PopAllCategoryGoodsMappingQueryReq queryReq = PopAllCategoryGoodsMappingQueryReq.builder()
                .popSku(popSku).build();
        DubboResponse<PopAllCategoryGoodsMappingResp> response = popAllCategoryGoodsMappingQueryProvider.queryByAllCategorySku(queryReq);
        if (!response.isSuccess()) {
            throw new BizException("查询pop全品类映射关系失败：" + response.getMsg());
        }
        PopAllCategoryGoodsMappingResp mappingResp = response.getData();
        if (Objects.isNull(mappingResp) || CollectionUtils.isEmpty(mappingResp.getMappingDTOList())) {
            return Lists.newArrayList();
        }
        return mappingResp.getMappingDTOList().stream().map(PopAllCategoryGoodsMappingConverter::convert).collect(Collectors.toList());

    }

}
