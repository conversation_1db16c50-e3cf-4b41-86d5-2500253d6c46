package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.TransferService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自动生成转换任务表格信息并推送
 * <AUTHOR>
 * @date 2022/03/10
 */
@Component
public class AutoCreateTransferDateTaskJob extends XianMuJavaProcessor {

    @Resource
    private TransferService transferService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        // 自动生成转换任务表格信息并推送
        transferService.autoCreateTransferDate();
        return new ProcessResult(true);
    }
}
