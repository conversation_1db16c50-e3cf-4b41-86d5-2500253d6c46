package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.StoreGoodsTaskService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 2023/3/8 15:45
 * @<AUTHOR>
 */
@Component
public class CreateGoodsCheckTaskOfNewGoodsJOB extends XianMuJavaProcessor {

    @Resource
    private StoreGoodsTaskService storeGoodsTaskService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        storeGoodsTaskService.createGoodsCheckTaskOfNewGoods();
        return new ProcessResult(true);
    }
}
