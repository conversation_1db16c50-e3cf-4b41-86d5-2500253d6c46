package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.StockTaskService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 补货出库任务
 * @Date 2023/2/23 13:28
 * @<AUTHOR>
 */
@Component
public class CreateAfterSaleTaskJob extends XianMuJavaProcessor {

    @Resource
    private StockTaskService stockTaskService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        stockTaskService.createAfterSaleTask();
        return new ProcessResult(true);
    }
}
