package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.StockTaskService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 自动货损任务
 * <AUTHOR>
 * @date 2023/03/10
 */
@Component
public class AutoDamageTaskJob extends XianMuJavaProcessor {

    @Resource
    private StockTaskService stockTaskService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        //自动货损
        stockTaskService.autoDamageTask(LocalDate.now());
        return new ProcessResult(true);
    }
}
