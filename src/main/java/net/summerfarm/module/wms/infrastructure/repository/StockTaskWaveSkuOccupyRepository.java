package net.summerfarm.module.wms.infrastructure.repository;

import net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO;

import java.util.List;

public interface StockTaskWaveSkuOccupyRepository {

    int insert(StockTaskWaveSkuOccupyDO record);

    void insertList(List<StockTaskWaveSkuOccupyDO> record);

    List<StockTaskWaveSkuOccupyDO> selectListByStockTaskId(Integer stockTaskId,
                                                               List<String> skuList);

    int updatePickChange(Long id,
                         Integer remainOccupyQuantityReduce,
                         Integer remainNotOccupyQuantityReduce);
}
