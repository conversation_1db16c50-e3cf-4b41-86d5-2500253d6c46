package net.summerfarm.module.wms.infrastructure.repository.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.wms.infrastructure.repository.OfcConfirmOrderReceivedRepository;
import net.summerfarm.ofc.client.provider.OfcDataCheckProvider;
import net.summerfarm.ofc.client.req.CheckSaasThirdPartyOrderDataReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

@Repository
@Slf4j
public class OfcConfirmOrderReceivedRepositoryImpl implements OfcConfirmOrderReceivedRepository {


    @DubboReference(check = false)
    private OfcDataCheckProvider ofcDataCheckProvider;

    @Override
    public Boolean checkOfcData(Integer storeNo, LocalDate deliveryDate) {
        try {
            if (ofcDataCheckProvider == null){
                log.error("ofcDataCheckProvider 服务不可用");
                return true;
            }

            CheckSaasThirdPartyOrderDataReq checkSaasThirdPartyOrderDataReq = new CheckSaasThirdPartyOrderDataReq();
            checkSaasThirdPartyOrderDataReq.setDeliveryDate(deliveryDate);
            checkSaasThirdPartyOrderDataReq.setStoreNo(storeNo);
            DubboResponse<Boolean> booleanDubboResponse = ofcDataCheckProvider.checkSaasThirdPartyOrderData(checkSaasThirdPartyOrderDataReq);
            if (booleanDubboResponse == null ||
                    !booleanDubboResponse.isSuccess() ||
                    !booleanDubboResponse.getData()) {
                log.error("履约单比对订单数据数据错误 {} \n {}", storeNo, JSONObject.toJSONString(booleanDubboResponse));
//                throw new BizException("履约单比对订单数据数据错误");
                return true;
            }
        } catch (Throwable throwable) {
            log.error("履约单比对订单数据数据异常 {}", storeNo, throwable);
//            throw new BizException("履约单比对订单数据数据异常");
            return true;
        }
        return true;
    }

}
