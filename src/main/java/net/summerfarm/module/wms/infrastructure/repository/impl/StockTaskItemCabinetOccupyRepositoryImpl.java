package net.summerfarm.module.wms.infrastructure.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import net.summerfarm.mapper.manage.StockTaskItemCabinetOccupyMapper;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyUpdate;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskItemCabinetOccupyRepository;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryOccupyReduceBatchRespDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class StockTaskItemCabinetOccupyRepositoryImpl implements StockTaskItemCabinetOccupyRepository {

    @Resource
    private StockTaskItemCabinetOccupyMapper stockTaskItemCabinetOccupyMapper;

    @Override
    public List<StockTaskItemCabinetOccupyDO> selectListByStockTaskIdOccupyed(Integer stockTaskId, List<String> skuCodeList) {
        if (stockTaskId == null) {
            return new ArrayList<>();
        }

        return stockTaskItemCabinetOccupyMapper.selectListByStockTaskIdOccupyed(stockTaskId, skuCodeList);
    }

    @Override
    public void updatePickChange(StockTaskItemCabinetOccupyUpdate build) {
        if (build == null || build.getId() == null || build.getPickChangeQuantity() == null) {
            throw new BizException("拣货参数不能为空");
        }

        int count = stockTaskItemCabinetOccupyMapper.updatePickChange(build.getId(), build.getPickChangeQuantity());
        if (count <= 0) {
            throw new BizException("拣货提交数量超过剩余拣货数量");
        }
    }

    @Override
    public void updateReleaseChange(Long id, Integer releaseChangeQuantity) {
        if (id == null || releaseChangeQuantity == null) {
            throw new BizException("释放参数不能为空");
        }

        int count = stockTaskItemCabinetOccupyMapper.updateReleaseChange(id, releaseChangeQuantity);
        if (count <= 0) {
            throw new BizException("释放提交数量超过剩余释放数量");
        }
    }

    @Override
    public void handlerCabinetOccupyItem(List<StockTaskItemCabinetOccupyDO> skuOccupyListTmp,
                                         List<CabinetInventoryOccupyReduceBatchRespDTO> occupyReduceBatchRespDTOListTmp) {
        List<CabinetInventoryOccupyReduceBatchRespDTO> occupyReduceBatchRespDTOList = JSONObject.parseArray(
                JSONObject.toJSONString(occupyReduceBatchRespDTOListTmp, SerializerFeature.DisableCircularReferenceDetect), CabinetInventoryOccupyReduceBatchRespDTO.class
        );
        for (CabinetInventoryOccupyReduceBatchRespDTO cabinetInventoryOccupyReduceBatchRespDTO : occupyReduceBatchRespDTOList) {
            for (StockTaskItemCabinetOccupyDO stockTaskItemCabinetOccupyDO : skuOccupyListTmp) {
                if (!cabinetInventoryOccupyReduceBatchRespDTO.getSku().equals(
                        stockTaskItemCabinetOccupyDO.getSku())) {
                    continue;
                }
                if (cabinetInventoryOccupyReduceBatchRespDTO.getProduceDate() != null &&
                        !cabinetInventoryOccupyReduceBatchRespDTO.getProduceDate().equals(
                                stockTaskItemCabinetOccupyDO.getProductionDate())) {
                    continue;
                }
                if (cabinetInventoryOccupyReduceBatchRespDTO.getQualityDate() != null &&
                        !cabinetInventoryOccupyReduceBatchRespDTO.getQualityDate().equals(
                                stockTaskItemCabinetOccupyDO.getQualityDate())) {
                    continue;
                }
                if (cabinetInventoryOccupyReduceBatchRespDTO.getCabinetCode() != null &&
                        !cabinetInventoryOccupyReduceBatchRespDTO.getCabinetCode().equals(
                                stockTaskItemCabinetOccupyDO.getCabinetCode())) {
                    continue;
                }

                if (cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity() <= 0) {
                    continue;
                }
                if (stockTaskItemCabinetOccupyDO.getOccupyQuantity() <= 0) {
                    continue;
                }

                int change;
                if (cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity() < stockTaskItemCabinetOccupyDO.getOccupyQuantity()) {
                    change = cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity();
                } else {
                    change = stockTaskItemCabinetOccupyDO.getOccupyQuantity();
                }

                cabinetInventoryOccupyReduceBatchRespDTO.setOccupyReduceQuantity(
                        cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity() - change
                );
                stockTaskItemCabinetOccupyDO.setOccupyQuantity(
                        stockTaskItemCabinetOccupyDO.getOccupyQuantity() - change
                );

                this.updatePickChange(
                        StockTaskItemCabinetOccupyUpdate.builder()
                                .id(stockTaskItemCabinetOccupyDO.getId())
                                .pickChangeQuantity(change)
                                .build());
            }
        }
        if (occupyReduceBatchRespDTOList.stream()
                .map(CabinetInventoryOccupyReduceBatchRespDTO::getOccupyReduceQuantity)
                .reduce(Integer::sum)
                .orElse(0) > 0) {
            throw new BizException("库位扣减批次分配不足");
        }
    }
}
