package net.summerfarm.module.fms.common.error;

import lombok.AllArgsConstructor;
import net.xianmu.common.exception.error.code.ErrorCode;
import net.xianmu.common.result.ResultStatusEnum;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum FmsBizErrorEnum implements ErrorCode {
    INVOICE_EXPORT_ERROR("发票导出异常"),
    INVOICE_REPEAT_DATA_ERROR("发票池中存在多张重复数据"),
    ;
    private String message;

    @Override
    public Integer getStatus() {
        return ResultStatusEnum.SERVER_ERROR.getStatus();
    }

    @Override
    public String getCode() {
        return name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
