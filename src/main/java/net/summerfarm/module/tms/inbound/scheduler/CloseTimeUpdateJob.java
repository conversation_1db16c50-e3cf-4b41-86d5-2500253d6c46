package net.summerfarm.module.tms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.AreaService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 更新截单时间和加单字段
 * date: 2023/3/22 11:09
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CloseTimeUpdateJob extends XianMuJavaProcessor {

    @Resource
    private AreaService areaService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("更新截单时间和加单字段任务开始");
        areaService.updateCloseTime();
        areaService.updateAddOrder();
        return new ProcessResult(true);
    }
}
