package net.summerfarm.module.products.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.time.LocalDate;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.PrepayInventoryService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

/**
 * 大客户预付商品到期提醒邮件
 * @author: <EMAIL>
 * @create: 2023/3/13
 */
@Slf4j
@Component
public class SendPrepayInventoryExpireMailJob extends XianMuJavaProcessor {

    @Resource
    private PrepayInventoryService prepayInventoryService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("SendPrepayInventoryExpireMailJob 任务开始执行......");
        prepayInventoryService.sendExpireMail(LocalDate.now());
        return new ProcessResult(true);
    }
}
