package net.summerfarm.module.mall.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.StockService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 删除近三天外的销量流水
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeleteSalesRecord extends XianMuJavaProcessor {


    @Resource
    private StockService stockService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("删除近三天外的销量流水 start :{}", LocalDateTime.now());
        stockService.deleteSalesRecord();
        log.info("删除近三天外的销量流水 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}