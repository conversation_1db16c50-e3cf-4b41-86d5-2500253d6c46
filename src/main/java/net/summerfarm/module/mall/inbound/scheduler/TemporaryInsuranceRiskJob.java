package net.summerfarm.module.mall.inbound.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.ActivityNewService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 临保风险品进特价活动线上化
 * @date 2023/6/29 10:23:38
 */
@Component
@Slf4j
public class TemporaryInsuranceRiskJob extends XianMuJavaProcessor {

    @Resource
    private ActivityNewService activityNewService;

    private static String KEY = "jobParameters";

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("TemporaryInsuranceRiskJob[]processResult[]start[]context:{},times:{}", JSON.toJSONString(context), LocalDateTime.now());
        JSONObject jsonObject = JSON.parseObject(context.getJobParameters());
        String key = null;
        if (Objects.nonNull(jsonObject)) {
            key = jsonObject.getString(KEY);
        }
        activityNewService.temporaryInsuranceRiskJob(key);
        log.info("TemporaryInsuranceRiskJob[]processResult[]end:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
