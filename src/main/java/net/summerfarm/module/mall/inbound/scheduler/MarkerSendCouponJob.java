package net.summerfarm.module.mall.inbound.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.MarketCouponSendService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 人工发放卡劵定时任务
 * @date 2023/9/4 14:45:36
 */
@Component
@Slf4j
public class MarkerSendCouponJob extends XianMuJavaProcessor {

    @Resource
    private MarketCouponSendService marketCouponSendService;

    private static String KEY = "jobParameters";

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("MarkerSendCouponJob[]processResult[]start[]context:{},times:{}", JSON.toJSONString(context), LocalDateTime.now());
        JSONObject jsonObject = JSON.parseObject(context.getJobParameters());
        String key = null;
        if (Objects.nonNull(jsonObject)) {
            key = jsonObject.getString(KEY);
        }
        marketCouponSendService.sendMerchantCouponJob(key);
        log.info("MarkerSendCouponJob[]processResult[]end:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
