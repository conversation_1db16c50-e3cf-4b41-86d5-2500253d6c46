package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.infrastructure.mapper.BmsCalculationItemEntityMapper;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/4/4 10:57
 */
@Component
@Slf4j
public class CalcItemSyncJob extends XianMuJavaProcessor {

    @Resource
    private BmsCalculationItemEntityMapper bmsCalculationItemEntityMapper;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("配置项数据同步任务开始:{}", LocalDateTime.now());
        int initBusinessType = bmsCalculationItemEntityMapper.initBusinessType();
        log.info("初始化 initBusinessType : {}", initBusinessType);

        log.info("配置项数据同步任务结束:{}", LocalDateTime.now());

        return new ProcessResult(true);
    }
}
