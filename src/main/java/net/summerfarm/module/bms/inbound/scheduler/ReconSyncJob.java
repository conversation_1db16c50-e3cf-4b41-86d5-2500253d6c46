package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.constant.ReconciliationEnum;
import net.summerfarm.module.bms.domain.repository.param.QuotationAreaParam;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationAreaEntityMapper;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationEntityMapper;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationPaymentEntityMapper;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity;
import net.summerfarm.mq.business.Business;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/30 11:24
 */
@Component
@Slf4j
public class ReconSyncJob extends XianMuJavaProcessor {

    @Resource
    private BmsDeliveryReconciliationEntityMapper bmsDeliveryReconciliationEntityMapper;

    @Resource
    private BmsDeliveryReconciliationPaymentEntityMapper bmsDeliveryReconciliationPaymentEntityMapper;

    @Resource
    private BmsDeliveryQuotationAreaEntityMapper bmsDeliveryQuotationAreaEntityMapper;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        String text = context.getInstanceParameters();
        List<Long> idList = Lists.newArrayList();
        log.info("对账单数据同步 任务开始:{} 参数:{},context:{}", LocalDateTime.now(), text, JSON.toJSONString(context));
        if (StringUtils.isNotEmpty(text)) {
            idList = JSON.parseArray(text, Long.class);
        }
        List<BmsDeliveryQuotationAreaEntity> bmsDeliveryQuotationAreaEntities = bmsDeliveryQuotationAreaEntityMapper.selectByParam(new QuotationAreaParam());
        Map<Long, BmsDeliveryQuotationAreaEntity> areaEntityMap = bmsDeliveryQuotationAreaEntities.stream().collect(Collectors.toMap(BmsDeliveryQuotationAreaEntity::getId, Function.identity(), (oldOne, newOne) -> newOne));

        LocalDateTime now = LocalDateTime.now();
        List<BmsDeliveryReconciliationEntity> all = bmsDeliveryReconciliationEntityMapper.selectAll4Update(idList);
        for (BmsDeliveryReconciliationEntity entity : all) {
            BmsDeliveryReconciliationEntity newEntity = new BmsDeliveryReconciliationEntity();
            newEntity.setId(entity.getId());

            if (Objects.nonNull(entity.getStoreNo())) {
                newEntity.setReconciliationDimensionKey(String.valueOf(entity.getStoreNo()));
            }
            newEntity.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());
            if (Objects.nonNull(entity.getServiceAreaId()) && areaEntityMap.containsKey(entity.getServiceAreaId().longValue())) {
                newEntity.setServiceAreaName(areaEntityMap.get(entity.getServiceAreaId().longValue()).getArea());
            }

            if (Objects.nonNull(entity.getCarrierId())) {
                newEntity.setMerchantId(entity.getCarrierId().longValue());
            }
            bmsDeliveryReconciliationEntityMapper.updateByPrimaryKeySelective(newEntity);

            BmsDeliveryReconciliationPaymentEntity paymentEntity = new BmsDeliveryReconciliationPaymentEntity();
            paymentEntity.setUpdateTime(now);
            paymentEntity.setCreateTime(now);
            paymentEntity.setPaymentType(ReconciliationEnum.ReconPaymentType.FINANCE_PAYMENT.name());
            paymentEntity.setPayableAmount(entity.getPayableAmount().add(entity.getAdvanceWarehouseAmount()).add(entity.getOverhead()).add(entity.getTax()).subtract(entity.getDeductionAmount()));
            paymentEntity.setReconciliationId(entity.getId());

            bmsDeliveryReconciliationPaymentEntityMapper.insertSelective(paymentEntity);
        }

        int initOperator = bmsDeliveryReconciliationEntityMapper.initOperator(idList);
        log.info("初始化 initOperator : {}", initOperator);

        int initCreatName = bmsDeliveryReconciliationEntityMapper.initCreatName(idList);
        log.info("初始化 initCreatName : {}", initCreatName);

        int initUpdateName = bmsDeliveryReconciliationEntityMapper.initUpdateName(idList);
        log.info("初始化 initUpdateName : {}", initUpdateName);

        int initPayment = bmsDeliveryReconciliationEntityMapper.initPayment(idList);
        log.info("初始化 initPayment : {}", initPayment);

        return new ProcessResult(true);
    }
}
