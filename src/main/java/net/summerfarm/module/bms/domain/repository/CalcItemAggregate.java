package net.summerfarm.module.bms.domain.repository;

import net.summerfarm.module.bms.domain.model.CalcItemConfigDO;
import net.summerfarm.module.bms.domain.repository.config.CalcItemConfigRepository;
import net.summerfarm.module.bms.domain.repository.param.CalculationItemQueryParam;
import net.summerfarm.module.bms.model.input.config.CalcItemInsertInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 18:43
 */
@Component
public class CalcItemAggregate {


    @Resource
    private CalcItemConfigRepository calcItemConfigRepository;


    @Transactional(rollbackFor = Exception.class)
    public void insertCalcItemConfig(CalcItemInsertInput input) {
        CalculationItemQueryParam queryParam = new CalculationItemQueryParam();
        queryParam.setBusinessType(input.getBusinessType());
        queryParam.setQuoteName(input.getQuoteName());
        queryParam.setSourceType(input.getSourceType());

        List<CalcItemConfigDO> calcItemConfigDOS = calcItemConfigRepository.queryByParam(queryParam);
        if (CollectionUtils.isNotEmpty(calcItemConfigDOS)) {
            throw new BizException("报价字段命名重复");
        }

        CalcItemConfigDO calcItemConfigDO = new CalcItemConfigDO();
        calcItemConfigDO.setItemName(input.getQuoteName());
        calcItemConfigDO.setUnit(input.getUnit());
        calcItemConfigDO.setSourceType(input.getSourceType());
        calcItemConfigDO.setBusinessType(input.getBusinessType());

        calcItemConfigRepository.insertCustomItem(calcItemConfigDO);
    }

}
