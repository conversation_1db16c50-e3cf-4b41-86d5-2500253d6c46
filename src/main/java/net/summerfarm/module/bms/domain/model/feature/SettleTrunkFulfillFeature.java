package net.summerfarm.module.bms.domain.model.feature;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * 干线 履约信息快照
 *
 * <AUTHOR>
 * @date 2023/3/28 17:18
 */
@Data
@Builder
public class SettleTrunkFulfillFeature {

    /**
     * 司机
     */
    private String driverName;

    /**
     * 司机
     */
    private String driverPhone;

    /**
     * 用车类型
     */
    private Integer carType;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆信息
     */
    private String carInfo;

    /**
     * 车辆存储条件
     */
    private String storageName;

    /**
     * 路线信息
     */
    private List<TrunkPathFeature> trunkPathFeatureList;

    @Tolerate
    public SettleTrunkFulfillFeature() {
    }

    /**
     * 路线信息
     */
    @Data
    @Accessors(chain = true)
    public static class TrunkPathFeature {
        /**
         * 顺序
         */
        private Integer sequence;
        /**
         * 点位
         */
        private String pathName;


    }
}







