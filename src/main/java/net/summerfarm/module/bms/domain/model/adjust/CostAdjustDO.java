package net.summerfarm.module.bms.domain.model.adjust;

import lombok.Data;
import net.summerfarm.module.bms.common.constant.AdjustEnum;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/29 18:06
 */
@Data
public class CostAdjustDO {

    /**
     *
     */
    private Long id;


    /**
     * 状态
     */
    private Integer status;

    /**
     * 费用名称
     */
    private String calculateName;
    /**
     * 调整前金额
     */
    private BigDecimal oldAmount;
    /**
     * 调整后金额
     */
    private BigDecimal newAmount;
    /**
     * 调整原因
     */
    private String remake;
    /**
     * 调整类型
     */
    private Integer adjustType;
    /**
     * 调整原因
     */
    private Integer reconciliationAdjustmentId;
    /**
     * 调整原因
     */
    private Integer settleAdjustmentId;
    /**
     * 是否审批中
     * true:是
     * false：不是
     * @return
     */
    public Boolean approving() {
        return Objects.equals(AdjustEnum.CostAdjustStatusEnum.APPROVING.getCode(), status);
    }

}
