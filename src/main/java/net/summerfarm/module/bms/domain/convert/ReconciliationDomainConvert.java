package net.summerfarm.module.bms.domain.convert;

import net.summerfarm.module.bms.domain.model.reconciliation.ReconciliationDO;
import net.summerfarm.module.bms.domain.model.reconciliation.ReconciliationPaymentDO;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/24 15:14
 */
public class ReconciliationDomainConvert {

    public static ReconciliationDO convertByEntity(BmsDeliveryReconciliationEntity entity) {
        ReconciliationDO reconciliationDO = new ReconciliationDO();
        reconciliationDO.setId(entity.getId());
        reconciliationDO.setReconciliationNo(entity.getReconciliationNo());
        reconciliationDO.setServiceAreaName(entity.getServiceAreaName());
        if (Objects.nonNull(entity.getStoreNo())) {
            reconciliationDO.setStoreNo(entity.getStoreNo());
        }
        if (Objects.isNull(entity.getMerchantId())) {
            reconciliationDO.setMerchantId(entity.getCarrierId() == null ? null : entity.getCarrierId().longValue());
        } else {
            reconciliationDO.setMerchantId(entity.getMerchantId());
        }
        reconciliationDO.setPayableAmount(entity.getPayableAmount());
        reconciliationDO.setActualAmount(entity.getActualAmount());
        reconciliationDO.setDeductionAmount(entity.getDeductionAmount());
        if (Objects.nonNull(entity.getPaymentDocumentsId())) {
            reconciliationDO.setPaymentDocId(entity.getPaymentDocumentsId().longValue());
        }
        reconciliationDO.setProofUrl(entity.getReconciliationProofUrl());
        reconciliationDO.setCreateUserName(entity.getCreateUserName());
        reconciliationDO.setAdvanceWarehouseAmount(entity.getAdvanceWarehouseAmount());
        reconciliationDO.setOverHead(entity.getOverhead());
        reconciliationDO.setTax(entity.getTax());
        reconciliationDO.setSettleMonth(entity.getSettleMonth());
        reconciliationDO.setBusinessType(entity.getBusinessType());
        reconciliationDO.setReconciliationDimensionKey(entity.getReconciliationDimensionKey());
        reconciliationDO.setStatus(entity.getStatus());
        reconciliationDO.setCreateTime(entity.getCreateTime());
        if(Objects.nonNull(entity.getPaymentDocumentsId())){
            reconciliationDO.setPaymentDocId(entity.getPaymentDocumentsId().longValue());
        }
        reconciliationDO.setPaymentDocNo(entity.getPaymentDocNo());
        reconciliationDO.setRemake(entity.getRemake());
        reconciliationDO.setUpdateUserName(entity.getUpdateUserName());

        return reconciliationDO;
    }


    /**
     *
     * @param paymentDO
     * @return
     */
    public static BmsDeliveryReconciliationPaymentEntity convertPaymentEntityByDO(ReconciliationPaymentDO paymentDO){
        BmsDeliveryReconciliationPaymentEntity entity = new BmsDeliveryReconciliationPaymentEntity();
        entity.setPaymentType(paymentDO.getPaymentType());
        entity.setPayableAmount(paymentDO.getPayableAmount());
        return entity;
    }

    public static ReconciliationPaymentDO convertPaymentDOByEntity(BmsDeliveryReconciliationPaymentEntity entity){
        ReconciliationPaymentDO domainDO = new ReconciliationPaymentDO();

        domainDO.setId(entity.getId());
        domainDO.setPaymentType(entity.getPaymentType());
        domainDO.setPayableAmount(entity.getPayableAmount());

        return domainDO;
    }
}
