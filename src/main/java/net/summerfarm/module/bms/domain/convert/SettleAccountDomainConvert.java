package net.summerfarm.module.bms.domain.convert;

import net.summerfarm.module.bms.domain.model.settle.SettleAccountDO;
import net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/29 16:34
 */
public class SettleAccountDomainConvert {

    @NotNull
    public static SettleAccountDO convertByEntity(BmsSettleAccountEntity entity) {
        SettleAccountDO settleAccountDO = new SettleAccountDO();
        settleAccountDO.setId(entity.getId());
        settleAccountDO.setSettleAccountNo(entity.getSettleAccountNo());
        if (Objects.nonNull(entity.getQuotationAreaId())) {
            settleAccountDO.setServiceAreaId(entity.getQuotationAreaId().longValue());
        }
        settleAccountDO.setServiceAreaName(entity.getQuotationAreaName());
        if (Objects.isNull(entity.getBidderId())) {
            settleAccountDO.setBidderId(entity.getCarrierId() == null ? null : entity.getCarrierId().longValue());
        } else {
            settleAccountDO.setBidderId(entity.getBidderId() == null ? null : entity.getBidderId());
        }
        settleAccountDO.setBidderName(entity.getBidderName());
        if (Objects.isNull(entity.getSettleTargetId())) {
            settleAccountDO.setSettleTargetId(entity.getStoreNo() == null ? null : entity.getStoreNo().longValue());
        } else {
            settleAccountDO.setSettleTargetId(entity.getSettleTargetId());
        }
        settleAccountDO.setSettleTargetName(entity.getSettleTargetName());
        settleAccountDO.setDeliveryStartDate(entity.getDeliveryStartDate());
        settleAccountDO.setDeliveryEndDate(entity.getDeliveryEndDate());
        settleAccountDO.setDischargeAmount(entity.getDischargeAmount());
        settleAccountDO.setProvince(entity.getProvince());
        settleAccountDO.setCity(entity.getCity());
        settleAccountDO.setStatus(entity.getStatus());
        settleAccountDO.setReconciliationNo(entity.getReconciliationNo());
        settleAccountDO.setCreateTime(entity.getCreateTime());
        return settleAccountDO;
    }
}
