package net.summerfarm.module.bms.domain;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.ConvertUtils;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.mapper.bms.BmsQuotationProcessMapper;
import net.summerfarm.model.domain.bms.BmsQuotationProcess;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.constant.QuotationProcessEnum;
import net.summerfarm.module.bms.common.constant.SettleAccountEnum;
import net.summerfarm.module.bms.domain.model.QuotationCalcFormulaDO;
import net.summerfarm.module.bms.domain.model.QuotationCalcItemDO;
import net.summerfarm.module.bms.domain.model.QuotationDO;
import net.summerfarm.module.bms.domain.model.feature.SettleTrunkFulfillFeature;
import net.summerfarm.module.bms.domain.model.settle.SettleAccountDO;
import net.summerfarm.module.bms.domain.model.settle.SettleFulfillAmountDO;
import net.summerfarm.module.bms.domain.model.settle.SettleFulfillDO;
import net.summerfarm.module.bms.domain.repository.param.QuotationQueryParam;
import net.summerfarm.module.bms.domain.repository.quota.QuotationRepository;
import net.summerfarm.module.bms.domain.repository.settle.SettleAccountItemRepository;
import net.summerfarm.module.bms.domain.repository.settle.SettleAccountRepository;
import net.summerfarm.module.bms.domain.repository.settle.SettleFulfillAmountRepository;
import net.summerfarm.module.bms.domain.repository.settle.SettleFulfillRepository;
import net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity;
import net.summerfarm.service.bms.ExpressRunnerService;
import net.summerfarm.tms.client.message.TmsBatchMessage;
import net.summerfarm.tms.client.message.TmsDeliveryBatchFareMessage;
import net.summerfarm.tms.client.message.TmsDeliverySiteMessage;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.message.BatchMessage;
import net.summerfarm.tms.message.DeliverySiteMessage;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SettleAccountAggregate extends BaseService {
    @Resource
    SettleAccountRepository settleAccountRepository;
    @Resource
    SettleFulfillAmountRepository settleFulfillAmountRepository;
    @Resource
    QuotationRepository quotationRepository;
    @Resource
    ExpressRunnerService expressRunnerService;
    @Resource
    BmsQuotationProcessMapper bmsQuotationProcessMapper;
    @Resource
    SettleAccountItemRepository settleAccountItemRepository;
    @Resource
    SettleFulfillRepository settleFulfillRepository;


    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public void generate(TmsBatchMessage batchMessage) {
        logger.info("收到mq消息，生成干线明细单，消息体：{}", JSON.toJSONString(batchMessage));
        //幂等校验，如果已存在batchId则不生成结算单
        if (!Objects.equals(settleFulfillRepository.countByFulfillId(batchMessage.getId()), 0)) {
            logger.info("干线明细单已生成, pathId:{}", batchMessage.getId());
            throw new ProviderException("干线明细单已生成");
        }
        DefaultContext<String, Object> presetMap = new DefaultContext<>();
        //干线报价单全去除掉预估运费类目后可删除改行代码
        presetMap.put("预估运费", batchMessage.getEstimateFare());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(batchMessage.getDeliveryBatchFareMessages())){
            batchMessage.getDeliveryBatchFareMessages().stream()
                    .filter( data-> Objects.nonNull(data.getAmount())  && StringUtils.isNotEmpty(data.getFareTypeDesc()))
                    .forEach(data-> presetMap.put(data.getFareTypeDesc(),data.getAmount()));
        }
        //操作：可操作查看进入详情页面
        SettleAccountDO settleAccountDO = new SettleAccountDO();
        //配送日期：tms系统中获取字段为履约时间
        settleAccountDO.setDeliveryStartDate(batchMessage.getDeliveryTime().toLocalDate());
        settleAccountDO.setDeliveryEndDate(batchMessage.getDeliveryTime().toLocalDate());
        //businessType为TRUNK_BUSINESS//承运商：tms系统中获取
        settleAccountDO.setBusinessType(QuotationEnum.BusinessType.TRUNK_BUSINESS.name());
        settleAccountDO.setBidderId(batchMessage.getCarrierId());
        settleAccountDO.setBidderName(batchMessage.getCarrierName());
        settleAccountDO.setSettleAccountNo(String.valueOf(SnowflakeUtil.nextId()));

        settleAccountDO.setSettleTargetId(batchMessage.getType().longValue());
        settleAccountDO.setSettleTargetName(DeliveryBatchTypeEnum.code2Enum(batchMessage.getType()).getName());

        SettleFulfillDO settleFulfillDO = new SettleFulfillDO();
        settleFulfillDO.setFulfillId(batchMessage.getId());
        List<SettleTrunkFulfillFeature.TrunkPathFeature> trunkPathFeatureList = Lists.newArrayList();
        //路线信息：tms系统中获取
        List<TmsDeliverySiteMessage> deliverySiteDTOList = batchMessage.getDeliverySiteMessages();
        if (!CollectionUtils.isEmpty(deliverySiteDTOList)) {
            for (TmsDeliverySiteMessage deliverySiteDTO : deliverySiteDTOList) {
                SettleTrunkFulfillFeature.TrunkPathFeature feature = new SettleTrunkFulfillFeature.TrunkPathFeature();
                feature.setSequence(deliverySiteDTO.getSequence()).setPathName(deliverySiteDTO.getSiteName());
                trunkPathFeatureList.add(feature);
            }
        }
        SettleTrunkFulfillFeature settleTrunkFulfillFeature = SettleTrunkFulfillFeature.builder()
                .driverName(batchMessage.getDriverName())
                .driverPhone(batchMessage.getDriverPhone())
                .carType(batchMessage.getType())
                .carInfo(batchMessage.getCarType())
                .carNumber(batchMessage.getCarNumber())
                .storageName(batchMessage.getCarStorage())
                .trunkPathFeatureList(trunkPathFeatureList).build();
        settleFulfillDO.setCarType(batchMessage.getType());
        settleFulfillDO.setSystemKilometers(batchMessage.getPlanTotalDistance());
        settleFulfillDO.setWeight(batchMessage.getTotalWeight());
        settleFulfillDO.setVolume(batchMessage.getTotalVolume());
        settleFulfillDO.setArea(batchMessage.getArea());
        settleFulfillDO.setShiftType(Objects.equals(batchMessage.getClasses(), SettleAccountEnum.Classes.OVERTIME.getCode()) ? SettleAccountEnum.Classes.OVERTIME.getDesc() : SettleAccountEnum.Classes.NORMAL.getDesc());
        settleFulfillDO.setProductQuantity(batchMessage.getTotalQuantity());
        settleFulfillDO.setRemark(batchMessage.getRemark());
        //保存履约快照信息
        settleFulfillDO.setSettleTrunkFulfillFeature(settleTrunkFulfillFeature);
        //费用信息
        Map<String, SettleFulfillAmountDO> calculateCostAmountMap = Maps.newHashMap();
        //自定义字段：来源计费模型中配置的数据
        List<SettleFulfillAmountDO> settleFulfillAmountDOList = Lists.newArrayList();
        settleAccountDO.setSettleFulfillAmountDOList(settleFulfillAmountDOList);
        //根据承运商，匹配干线报价单
        QuotationQueryParam queryParam = new QuotationQueryParam();
        queryParam.setBidderId(batchMessage.getCarrierId());
        queryParam.setStatus(QuotationEnum.QuotationStatus.NORMAL.getCode());
        queryParam.setHasQueryBidder(true);
        queryParam.setBusinessType(QuotationEnum.BusinessType.TRUNK_BUSINESS.name());
        List<QuotationDO> quotationDOList = quotationRepository.queryQuotationList(queryParam);
        logger.info("干线结算调度单id:{},匹配到的干线报价单：{}",batchMessage.getId(), JSON.toJSONString(quotationDOList));
        settleFulfillAmountDOList.forEach(f->calculateCostAmountMap.put(f.getCalculateCostName(), f));
        // 无匹配报价单，生成默认费用的结算单
        if (CollectionUtils.isEmpty(quotationDOList)) {

        } else {
            //保存报价快照信息
            Optional<QuotationDO> first = quotationDOList.stream().findFirst();
            if (first.isPresent()) {
                QuotationDO quotationDO = first.get();
                settleAccountDO.setServiceAreaId(quotationDO.getServiceAreaDO().getId());
                settleAccountDO.setServiceAreaName(quotationDO.getServiceAreaDO().getArea());
                settleFulfillDO.setSettleQuoteFeatures(JSON.toJSONString(quotationDO));
            }

            for (QuotationDO quotationDO : quotationDOList) {
                // 这份报价单应付费用
                QuotationQueryParam quotationQueryParam = new QuotationQueryParam();
                quotationQueryParam.setId(quotationDO.getId());
                quotationQueryParam.setHasQueryItem(true);
                quotationQueryParam.setHasQueryFormula(true);
                quotationQueryParam.setHasQueryBidder(true);
                Optional<QuotationDO> quotationDetail = quotationRepository.queryQuotationDetail(quotationQueryParam);
                if (!quotationDetail.isPresent()){
                }
                else {
                    QuotationDO quotationDetailInfo = quotationDetail.get();
                    List<QuotationCalcItemDO> calcItemDOList = quotationDetailInfo.getCalcItemDOList();
                    List<QuotationCalcFormulaDO> calcFormulaDOList = quotationDetailInfo.getCalcFormulaDOList();
                    logger.info("干线结算调度单id:{},报价单计算项：{}",batchMessage.getId(), JSON.toJSONString(calcItemDOList));
                    logger.info("干线结算调度单id:{},报价单公式：{}",batchMessage.getId(), JSON.toJSONString(calcFormulaDOList));
                    DefaultContext<String, Object> temporaryFieldMap = new DefaultContext<>();
                    if (!CollectionUtils.isEmpty(calcItemDOList)) {
                        calcItemDOList.forEach(detail -> temporaryFieldMap.put(detail.getItemName(), detail.getAmount()));
                    }
                    if (!CollectionUtils.isEmpty(calcFormulaDOList)) {
                        for (QuotationCalcFormulaDO quotationCalcFormulaDO : calcFormulaDOList) {
                            DefaultContext<String, Object> calculationFieldMap = new DefaultContext<>();
                            calculationFieldMap.putAll(temporaryFieldMap);
                            calculationFieldMap.putAll(presetMap);
                            logger.info("干线结算调度单id:{},计算前公式:{}，预置集合:{}", batchMessage.getId(), quotationCalcFormulaDO.getFormula(), JSON.toJSONString(calculationFieldMap));
                            BigDecimal amount = expressRunnerService.calculation(quotationCalcFormulaDO.getFormula(), calculationFieldMap);
                            logger.info("干线结算调度单id:{},计算结果:{}", batchMessage.getId(), amount);
                            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                                amount = BigDecimal.ZERO;
                            }
                            SettleFulfillAmountDO settleFulfillAmountDO = new SettleFulfillAmountDO();
                            settleFulfillAmountDO.setAmount(amount);
                            settleFulfillAmountDO.setType(1);
                            settleFulfillAmountDO.setCalculateCostName(quotationCalcFormulaDO.getFormulaName());
                            calculateCostAmountMap.put(quotationCalcFormulaDO.getFormulaName(), settleFulfillAmountDO);
                        }
                    }
                }
            }
        }
        Integer settleAccountId = settleAccountRepository.save(settleAccountDO);

        Integer settleAccountDetailId = settleFulfillRepository.save(settleFulfillDO);
        BmsSettleAccountItemEntity accountItem = new BmsSettleAccountItemEntity();
        accountItem.setSettleAccountId(settleAccountId);
        accountItem.setSettleAccountsDetailsId(settleAccountDetailId);
        settleAccountItemRepository.save(accountItem);

            for (String quotaName : calculateCostAmountMap.keySet()) {
                SettleFulfillAmountDO costVO = calculateCostAmountMap.get(quotaName);
                // 新增结算项信息
                BmsCalculationDetailsEntity bmsCalculationDetailsEntity = ConvertUtils.convert(costVO, BmsCalculationDetailsEntity.class);
                bmsCalculationDetailsEntity.setCreateTime(LocalDateTime.now());
                bmsCalculationDetailsEntity.setSourceId(settleAccountDetailId);
                bmsCalculationDetailsEntity.setType(1);
                bmsCalculationDetailsEntity.setSourceType((byte) 0);
                //算出来结果为负数则为0
                if (bmsCalculationDetailsEntity.getAmount().compareTo(BigDecimal.ZERO) < 0){
                    bmsCalculationDetailsEntity.setAmount(BigDecimal.ZERO);
                }
                settleFulfillAmountRepository.save(bmsCalculationDetailsEntity);
            }
            //生成明细单操作记录
            generateProcessRecord(settleAccountDetailId);
            logger.info("生成干线明细单结束，batchId:{}", batchMessage.getId());
        }

    private void generateProcessRecord(Integer settleAccountId) {
        BmsQuotationProcess bmsQuotationProcess = new BmsQuotationProcess();
        bmsQuotationProcess.setSourceId(settleAccountId);
        bmsQuotationProcess.setOperationContent("生成明细单");
        bmsQuotationProcess.setType(QuotationProcessEnum.QuotationProcessType.SETTLE_ACCOUNT.getCode());
        bmsQuotationProcessMapper.insert(bmsQuotationProcess);

    }

}
