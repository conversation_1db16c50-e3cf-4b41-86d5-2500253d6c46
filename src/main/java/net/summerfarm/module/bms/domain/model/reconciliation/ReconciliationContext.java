package net.summerfarm.module.bms.domain.model.reconciliation;

import lombok.Data;
import net.summerfarm.module.bms.common.model.OperatorBO;
import net.summerfarm.module.bms.domain.model.settle.SettleAccountDO;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 明细单 对账 上下文
 *
 * <AUTHOR>
 * @date 2023/3/31 15:26
 */
@Data
public class ReconciliationContext {

    /**
     * 明细单
     */
    private SettleAccountDO settleAccountDO;

    /**
     * 总金额
     * totalAmount = totalDetailAmount + totalDischargeAmount
     *
     * @return
     */
    public BigDecimal calculateTotalAmount() {
        if (Objects.isNull(totalDetailAmount) || Objects.isNull(totalDischargeAmount)) {
            return BigDecimal.ZERO;
        }
        return totalDetailAmount.add(totalDischargeAmount);
    }

    /**
     * 实际应付金额
     * totalActualAmount = totalDetailAmount + totalDischargeAmount+totalDeductionAmount;
     *
     * @return
     */
    public BigDecimal calculateTotalActualAmount() {
        if (Objects.isNull(totalDetailAmount) || Objects.isNull(totalDischargeAmount) || Objects.isNull(totalDeductionAmount)) {
            return BigDecimal.ZERO;
        }
        return totalDetailAmount.add(totalDischargeAmount).add(totalDeductionAmount);
    }


    /**
     * 总明细金额
     */
    private BigDecimal totalDetailAmount;

    /**
     * 总扣减金额
     */
    private BigDecimal totalDeductionAmount;

    /**
     * 总卸货费用
     */
    private BigDecimal totalDischargeAmount;

    /**
     * 总前置仓费用
     */
    private BigDecimal maxAdvanceAmount;

    /**
     * 对账凭证
     */
    private String proofUrl;

    /**
     * 备注
     */
    private String remake;

    /**
     * 登录人
     */
    private OperatorBO operatorBO;

    private String businessType;

}
