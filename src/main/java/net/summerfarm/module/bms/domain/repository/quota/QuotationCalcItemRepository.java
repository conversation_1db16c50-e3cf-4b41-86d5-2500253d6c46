package net.summerfarm.module.bms.domain.repository.quota;

import net.summerfarm.module.bms.domain.model.QuotationCalcItemDO;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationDetailEntityMapper;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/21 16:01
 */
@Component
public class QuotationCalcItemRepository {

    @Resource
    private BmsDeliveryQuotationDetailEntityMapper bmsDeliveryQuotationDetailEntityMapper;

    /**
     * 批量创建
     *
     * @param quotationId
     * @param quotationCalcItemList
     * @return
     */
    public int batchInsertQuotationItemList(Long quotationId, List<QuotationCalcItemDO> quotationCalcItemList) {
        if (Objects.isNull(quotationId) || CollectionUtils.isEmpty(quotationCalcItemList)) {
            return 0;
        }
        List<BmsDeliveryQuotationDetailEntity> collect = quotationCalcItemList.stream().map(itemDO -> {
            BmsDeliveryQuotationDetailEntity entity = new BmsDeliveryQuotationDetailEntity();
            LocalDateTime now = LocalDateTime.now();

            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setBmsDeliveryQuotationId(quotationId.intValue());
            entity.setBmsCalculationItemId(itemDO.getItemId());
            entity.setAmount(itemDO.getAmount());
            entity.setBmsCalculationItemName(itemDO.getItemName());
            entity.setUnit(itemDO.getUnit());
            return entity;
        }).collect(Collectors.toList());

        return bmsDeliveryQuotationDetailEntityMapper.batchInsertItem(collect);
    }

    /**
     * 根据报价单id删除关联表(bms_delivery_quotation_detail)数据
     *
     * @param quotationId 报价单id
     * @return 删除的行数
     */
    public int deleteByQuotationId(Integer quotationId) {
        if (Objects.isNull(quotationId)) {
            return 0;
        }
        return bmsDeliveryQuotationDetailEntityMapper.deleteByQuotationId(quotationId);
    }

}
