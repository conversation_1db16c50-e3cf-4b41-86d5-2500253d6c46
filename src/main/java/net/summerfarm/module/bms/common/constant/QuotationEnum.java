package net.summerfarm.module.bms.common.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/15 19:18
 */
public interface QuotationEnum {

    String PROCESS_TEXT = "城配专属";

    /**
     * 存在审批中的任务
     */
    Integer HAS_EXAMINE_TASK = 1;

    /**
     * 不存在审批中的任务
     */
    Integer NO_EXAMINE_TASK = 0;

    /**
     * 城配报价类型
     */
    @Getter
    enum DeliveryQuotaType {
        /**
         * 内区
         */
        INNER_ZONE(0, "内区"),

        /**
         * 外区
         */
        OUTER_ZONE(1, "外区"),
        ;

        private final Integer code;

        private final String desc;

        DeliveryQuotaType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 报价单 业务类型
     */
    @Getter
    enum BusinessType {
        /**
         * 城配
         */
        DELIVERY_BUSINESS(1,"城配"),

        /**
         * 干线
         */
        TRUNK_BUSINESS(2,"干线"),

        WAREHOUSE_BUSINESS(3,"仓储"),

        ;

        private final Integer code;

        private final String desc;


        BusinessType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Optional<BusinessType> parseByName(String name) {
            if (StringUtils.isEmpty(name)) {
                return Optional.empty();
            }
            for (BusinessType value : BusinessType.values()) {
                if (Objects.equals(value.name(), name)) {
                    return Optional.of(value);
                }
            }
            return Optional.empty();
        }
    }

    /**
     * 报价单状态
     */
    @Getter
    enum QuotationStatus {
        /**
         * 正常
         */
        NORMAL(0, "正常"),

        /**
         * 干线
         */
        DELETED(-1, "作废"),
        ;

        private final Integer code;

        private final String desc;

        QuotationStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }


    }

    /**
     * 计费公式 status
     */
    @Getter
    enum QuotationFormulaStatus {
        /**
         * 正常
         */
        NORMAL(0, "正常"),

        /**
         * 干线
         */
        DELETED(-1, "作废"),
        ;

        private final Integer code;

        private final String desc;

        QuotationFormulaStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 调整单类型枚举
     */
    @Getter
    enum ReconciliationAdjustTypeEnum {
        /**
         * 审批中
         */
        RECONCILIATION(0, "对账单"),

        SETTLE_ACCOUNT(1, "结算单"),

        PAYMENT_DOC(2, "结算打款单"),


        ;

        private final Integer code;

        private final String desc;

        ReconciliationAdjustTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }



}
