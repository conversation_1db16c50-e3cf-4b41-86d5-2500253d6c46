package net.summerfarm.module.bms.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/15 10:32
 */
public interface InvoiceEnum {

    @Getter
    enum InvoiceTypeFaceEnum {
        /**
         * 红色
         */
        RED(1, "红色"),

        /**
         * 蓝色
         */
        BLUE(2, "蓝色"),

        ;

        private final Integer code;

        private final String desc;

        InvoiceTypeFaceEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
