package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BmsDeliveryQuotationDetailEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsDeliveryQuotationDetailEntity record);

    int insertSelective(BmsDeliveryQuotationDetailEntity record);

    BmsDeliveryQuotationDetailEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsDeliveryQuotationDetailEntity record);

    int updateByPrimaryKey(BmsDeliveryQuotationDetailEntity record);


    /**
     * 同步 配置项信息
     *
     * @return
     */
    int initItemName();

    /**
     * 根据报价单查询 配置项
     *
     * @param quotationId
     * @return
     */
    List<BmsDeliveryQuotationDetailEntity> selectByQuotationId(Integer quotationId);

    /**
     * 批量创建 计费项
     *
     * @param entityList
     * @return
     */
    int batchInsertItem(@Param("items") List<BmsDeliveryQuotationDetailEntity> entityList);

    /**
     * 根据报价单id删除关联表(bms_delivery_quotation_detail)数据
     * @param quotationId 报价单id
     * @return 删除的行数
     */
    int deleteByQuotationId(Integer quotationId);
}