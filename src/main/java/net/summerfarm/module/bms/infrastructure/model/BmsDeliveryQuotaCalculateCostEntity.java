package net.summerfarm.module.bms.infrastructure.model;

import java.time.LocalDateTime;

public class BmsDeliveryQuotaCalculateCostEntity {
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer bmsDeliveryQuotationId;

    private String calculateName;

    private String formula;

    private Integer calculateType;

    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBmsDeliveryQuotationId() {
        return bmsDeliveryQuotationId;
    }

    public void setBmsDeliveryQuotationId(Integer bmsDeliveryQuotationId) {
        this.bmsDeliveryQuotationId = bmsDeliveryQuotationId;
    }

    public String getCalculateName() {
        return calculateName;
    }

    public void setCalculateName(String calculateName) {
        this.calculateName = calculateName == null ? null : calculateName.trim();
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula == null ? null : formula.trim();
    }

    public Integer getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(Integer calculateType) {
        this.calculateType = calculateType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}