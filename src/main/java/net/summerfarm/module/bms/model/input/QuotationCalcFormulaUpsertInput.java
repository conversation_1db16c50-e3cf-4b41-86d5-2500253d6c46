package net.summerfarm.module.bms.model.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:03
 */
@Data
public class QuotationCalcFormulaUpsertInput implements Serializable {

    private Long id;

    private Integer calculateType;

    private String formula;

    private String calculateName;

    /**
     * 报价详情字段校验标识
     */
    private List<Integer> bmsCalculationItemIds;

}
