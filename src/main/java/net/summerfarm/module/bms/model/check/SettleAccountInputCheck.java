package net.summerfarm.module.bms.model.check;

import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import com.google.common.base.Preconditions;
import net.summerfarm.module.bms.model.input.settleAccount.SettleAccountInput;
import net.summerfarm.module.bms.model.input.settleAccount.SettleAccountQueryInput;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class SettleAccountInputCheck {

    public static void initAndCheck4List(SettleAccountQueryInput queryInput) {
        Preconditions.checkArgument(Objects.nonNull(queryInput), "请输入查询信息");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryInput.getBusinessType()), "请输入业务类型");

        if (Objects.isNull(queryInput.getPageSize())) {
            queryInput.setPageSize(10);
        }
        if (Objects.isNull(queryInput.getPageIndex())) {
            queryInput.setPageIndex(1);
        }

    }

    public static void detailCheck(SettleAccountQueryInput queryInput) {
        Preconditions.checkArgument(Objects.nonNull(queryInput), "请输入查询信息");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryInput.getBusinessType()), "请输入业务类型");
        Preconditions.checkArgument(Objects.nonNull(queryInput.getId()), "请输入明细单主键");

    }

    public static void exportCheck(SettleAccountQueryInput param) {
        Preconditions.checkArgument(Objects.nonNull(param), "请输入查询信息");
        if (Objects.nonNull(param.getDeliveryStartDate()) && Objects.nonNull(param.getDeliveryEndDate())) {
            Duration duration = Duration.between(param.getDeliveryStartDate().atStartOfDay(), param.getDeliveryEndDate().atStartOfDay());
            if (duration.toDays() + 1 > 31) {
                throw new ParamsException("导出结算单数据超过一个月");
            }
        }
    }

    public static void generateCheck(SettleAccountInput settleAccountInput) {
        //日期和城配仓校验
        Preconditions.checkArgument(Objects.nonNull(settleAccountInput), "请输入查询信息");
        Preconditions.checkArgument(Objects.nonNull(settleAccountInput.getStoreNo()), "请输入城配仓信息");
        Preconditions.checkArgument(Objects.nonNull(settleAccountInput.getDeliveryStartDate()), "请输入配送日期");
    }

    public static void checkCheck(SettleAccountQueryInput settleAccountQueryInput) {
        //检验是否选中数据
        Preconditions.checkArgument(!CollectionUtils.isEmpty(settleAccountQueryInput.getSettleAccountIds()), "请选择明细单数据");
    }
}
