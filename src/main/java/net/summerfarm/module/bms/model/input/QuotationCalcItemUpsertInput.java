package net.summerfarm.module.bms.model.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:02
 */
@Data
public class QuotationCalcItemUpsertInput implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 计费项
     */
    private Integer bmsCalculationItemId;

    /**
     * 计费项名称
     */
    private String bmsCalculationItemName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 单位
     */
    private String unit;

}
