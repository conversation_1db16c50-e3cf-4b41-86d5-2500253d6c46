package net.summerfarm.module.bms.model.input.reconciliation;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/24 15:20
 */
@Data
public class ReconciliationQueryInput implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 编号
     */
    private String reconciliationNo;

    /**
     * 承运商
     */
    private Long carrierId;

    /**
     * 打款单
     */
    private String paymentNo;

    /**
     * 服务区域
     */
    private Integer serviceAreaId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 城配仓
     */
    private Integer storeNo;

    private String settleMonth;

    private String dimensionKey;

    /**
     * 分页
     */
    private Integer pageIndex;

    /**
     * 分页
     */
    private Integer pageSize;
}
