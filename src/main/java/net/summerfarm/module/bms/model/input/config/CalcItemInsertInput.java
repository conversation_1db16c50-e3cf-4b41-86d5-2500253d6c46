package net.summerfarm.module.bms.model.input.config;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/3 18:40
 */
@Data
public class CalcItemInsertInput implements Serializable {

    /**
     * 报价清单名称
     */
    private String quoteName;


    private String unit;

    /**
     * 来源类型 0报价详情 1 计算模型
     */
    private Integer sourceType;

    private String businessType;
}
