package net.summerfarm.module.bms.model.input.config;

import lombok.Data;
import net.summerfarm.module.bms.model.output.config.CalcItemConfigOutput;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 17:54
 */
@Data
public class CalcItemListOutput implements Serializable {

    /**
     * 配置
     */
    private List<CalcItemConfigOutput> calcItemConfigOutputList;

}
