package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.impl.CrmNewsServiceImpl;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
/**
 * crm-新产品钉钉通知
 */
public class NewProductDingJob extends XianMuJavaProcessor {
    @Resource
    CrmNewsServiceImpl crmNewsServiceImpl;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("NewProductDingJob 任务调度成功！");
        crmNewsServiceImpl.newNotificationsOnProducts();
        return new ProcessResult(true);
    }
}