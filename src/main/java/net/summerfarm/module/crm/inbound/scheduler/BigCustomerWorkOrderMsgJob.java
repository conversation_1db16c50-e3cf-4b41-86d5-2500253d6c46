package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.impl.FeedbackWoServiceImpl;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class BigCustomerWorkOrderMsgJob extends XianMuJavaProcessor {
    @Resource
    FeedbackWoServiceImpl feedbackWoService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("BigCustomerWorkOrderMsgJob 任务调度成功！");
        feedbackWoService.autoSendMsg();
        return new ProcessResult(true);
    }
}
