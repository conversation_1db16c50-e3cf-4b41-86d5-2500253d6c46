package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.ProductsService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
/**
 * crm-新产品邮件通知
 */
public class NewProductEmailJob extends XianMuJavaProcessor {
    @Resource
    ProductsService productsService;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("NewProductEmailJob 任务调度成功！");
        productsService.freshProductsNotice();
        return new ProcessResult(true);
    }
}