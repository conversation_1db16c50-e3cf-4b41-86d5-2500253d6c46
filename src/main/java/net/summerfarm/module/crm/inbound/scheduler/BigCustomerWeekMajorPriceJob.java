package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.impl.MajorPriceServiceImpl;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class BigCustomerWeekMajorPriceJob extends XianMuJavaProcessor {
    @Resource
    MajorPriceServiceImpl majorPriceServiceImpl;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("BigCustomerWeekMajorPriceJob 任务调度成功！");
        majorPriceServiceImpl.timedMajorPrice(0);
        return new ProcessResult(true);
    }
}
