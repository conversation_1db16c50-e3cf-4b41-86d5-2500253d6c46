package net.summerfarm.module.scp.infrastructure.model;

import lombok.Data;

import java.time.LocalDateTime;


@Data
public class AllocationPlanDetail {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.allocation_plan_id
     *
     * @mbg.generated
     */
    private Long allocationPlanId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.sku
     *
     * @mbg.generated
     */
    private String sku;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.pd_id
     *
     * @mbg.generated
     */
    private Long pdId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.pd_name
     *
     * @mbg.generated
     */
    private String pdName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.system_adjust
     *
     * @mbg.generated
     */
    private Integer systemAdjust;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.out_quantity
     *
     * @mbg.generated
     */
    private Integer outQuantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.purchaser_adjust
     *
     * @mbg.generated
     */
    private Integer purchaserAdjust;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.purchase_remark
     *
     * @mbg.generated
     */
    private String purchaseRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.sale_adjust
     *
     * @mbg.generated
     */
    private Integer saleAdjust;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.sale_remark
     *
     * @mbg.generated
     */
    private String saleRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.operate_adjust
     *
     * @mbg.generated
     */
    private Integer operateAdjust;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.operate_remark
     *
     * @mbg.generated
     */
    private String operateRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.create_role
     *
     * @mbg.generated
     */
    private Integer createRole;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan_detail.allocation_lock_quantity
     *
     * @mbg.generated
     */
    private Integer allocationLockQuantity;


    private Integer source;


    private Long replenishmentPlanAllocationTaskId;

    /**
     * 是否直采
     */
    private Integer isDirect;


}