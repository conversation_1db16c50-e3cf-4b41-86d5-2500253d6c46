package net.summerfarm.module.scp.infrastructure.model;

import lombok.Data;

import java.time.LocalDateTime;


@Data
public class AllocationPlan {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.list_no
     *
     * @mbg.generated
     */
    private String listNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.plan_list_no
     *
     * @mbg.generated
     */
    private String planListNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.out_warehouse_no
     *
     * @mbg.generated
     */
    private Integer outWarehouseNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.in_warehouse_no
     *
     * @mbg.generated
     */
    private Integer inWarehouseNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.out_warehouse_name
     *
     * @mbg.generated
     */
    private String outWarehouseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.in_warehouse_name
     *
     * @mbg.generated
     */
    private String inWarehouseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.expect_out_time
     *
     * @mbg.generated
     */
    private LocalDateTime expectOutTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.expect_in_time
     *
     * @mbg.generated
     */
    private LocalDateTime expectInTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.in_store_admin
     *
     * @mbg.generated
     */
    private Integer inStoreAdmin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.in_store_admin_name
     *
     * @mbg.generated
     */
    private String inStoreAdminName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.out_store_admin
     *
     * @mbg.generated
     */
    private Integer outStoreAdmin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.out_store_admin_name
     *
     * @mbg.generated
     */
    private String outStoreAdminName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.order_type
     *
     * @mbg.generated
     */
    private Integer orderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.creator_id
     *
     * @mbg.generated
     */
    private Integer creatorId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.creator_name
     *
     * @mbg.generated
     */
    private String creatorName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.updater_id
     *
     * @mbg.generated
     */
    private Integer updaterId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.updater_name
     *
     * @mbg.generated
     */
    private String updaterName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column allocation_plan.out_time
     *
     * @mbg.generated
     */
    private LocalDateTime outTime;

    /**
     * 干线调度(0:否;1:是)
     */
    private Integer trunkFlag;

}