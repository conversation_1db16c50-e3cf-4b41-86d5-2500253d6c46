package net.summerfarm.module.scp.model.vo;


import lombok.Data;
import net.summerfarm.module.pms.model.dto.AllocationOrderDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2022/9/5 10:29
 */
@Data
public class AllocationPlanVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 入库仓名称
     */
    private String inWarehouseName;

    /**
     * 出库仓名称
     */
    private String outWarehouseName;

    /**
     * 调拨单号
     */
    private List<AllocationOrderDTO> allocationOrderDTOList;

    /**
     * 调拨计划单号
     */
    private String planListNo;

    /**
     * 状态(5:草稿 10:待采购确认 15:待销售确认 20:待运营确认 25:已完成)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 0:日常调拨 1:补货计划
     */
    private Integer source;
}
