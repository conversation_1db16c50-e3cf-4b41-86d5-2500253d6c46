package net.summerfarm.module.scp.model.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2022/11/1 10:49
 */
@Data
public class AllocationWarningQueryDTO {


    @NotEmpty(message = "sku不能为空")
    private List<String> sku;

    @NotNull(message = "仓库不能为空")
    private Integer warehouseNo;
}
