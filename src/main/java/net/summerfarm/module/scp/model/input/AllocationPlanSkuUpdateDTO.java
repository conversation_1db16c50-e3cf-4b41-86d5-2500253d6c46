package net.summerfarm.module.scp.model.input;


import lombok.Data;
import net.summerfarm.model.DTO.ProducePeriodDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AllocationPlanSkuUpdateDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 调拨计划id
     */
    private Long allocationPlanId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 商品id
     */
    private String pdNo;

    /**
     * 商品名称
     */
    private String pdName;


    /**
     * 备注
     */
    private String remark;

    /**
     * 运营调整
     */
    private Integer operateAdjust;

    /**
     * 运营备注
     */
    private String operateRemark;

    /**
     * 出库数量
     */
    private Integer outQuantity;

    /**
     * 采购调整
     */
    private Integer purchaserAdjust;

    /**
     * 采购备注
     */
    private String purchaseRemark;

    /**
     * 销售建议
     */
    private Integer saleAdjust;

    /**
     * 销售备注
     */
    private String saleRemark;

    /**
     * 是否直采
     */
    private Integer isDirect;

    /**
     * 保质期列表
     */
    private List<ProducePeriodDTO> producePeriodDTOList;

    /**
     * 是否更新
     */
    private Integer isUpdate;


}
