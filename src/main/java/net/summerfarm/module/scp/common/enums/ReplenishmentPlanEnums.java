package net.summerfarm.module.scp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * 补货计划表枚举
 */
public interface ReplenishmentPlanEnums {

    @Getter
    @AllArgsConstructor
    enum Status implements Enum2Args {
        PREPARE(0,"计划中"),
        FINISH(5,"结束"),
        ;

        private Integer value;
        private String content;


    }
}
