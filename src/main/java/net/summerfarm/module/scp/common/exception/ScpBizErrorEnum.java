package net.summerfarm.module.scp.common.exception;

import lombok.AllArgsConstructor;
import net.xianmu.common.exception.error.code.ErrorCode;
import net.xianmu.common.result.ResultStatusEnum;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2023/2/9 13:49
 */
@AllArgsConstructor
public enum ScpBizErrorEnum implements ErrorCode {

    TRUNK_FLAG_NOT_NULL("请选择是否预约干线物流"),
    TRUNK_FLAG_CANCEL("有关联承运单,取消预约失败"),
    TRUNK_FLAG_SUBMIT("有关联承运单,申请预约失败"),
    TEMPERATURE_SPLIT_RULE_NOT_NULL("温区拆分规则不能为空")
    ,
    ;
    private String message;

    @Override
    public Integer getStatus() {
        return ResultStatusEnum.SERVER_ERROR.getStatus();
    }

    @Override
    public String getCode() {
        return name();
    }

    public String getMessage() {
        return message;
    }
}
