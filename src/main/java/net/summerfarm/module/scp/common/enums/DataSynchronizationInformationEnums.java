package net.summerfarm.module.scp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2StringArgs;


/**
 * 离线数据库信息同步表:表名枚举
 */
public interface DataSynchronizationInformationEnums {

    /**
     * 表名
     */
    @AllArgsConstructor
    @Getter
    enum TableName implements Enum2StringArgs {
        STOCK_DASHBOARD_FUTURE("stock_dashboard_future","库存未来表"),
        STOCK_DASHBOARD_HISTORY("stock_dashboard_history", "库存罗盘历史表"),
        WAREHOUSE_ESTIMATED_CONSUMPTION("warehouse_estimated_consumption","销量预测表"),
        REPLENISHMENT_PLAN_BASE_INFO("replenishment_plan_base_info","补货计划基础表"),
        SKU_WAREHOUSE_SELL_LABEL("sku_warehouse_sell_label","离线sku销售标签表"),
        ;

        private String value;

        private String content;
    }
}
