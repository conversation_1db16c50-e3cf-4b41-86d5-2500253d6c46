package net.summerfarm.module.scp.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.summerfarm.module.scp.common.enums.StockDashboardEnums;
import net.summerfarm.model.DTO.purchase.DashboardCalculateDTO;
import net.summerfarm.module.scp.model.dto.stock.dashboard.CoreDashboardQuantityInfoDTO;
import net.summerfarm.module.scp.model.dto.stock.dashboard.CoreResultDTO;
import net.summerfarm.module.scp.model.input.stock.dashboard.CoreDashboardInput;
import net.summerfarm.module.scp.model.input.stock.dashboard.CoreQuantityInfoInput;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public class StockDashboardUtil {


    /**
     * 补全库存视图数据
     * 必须时间正序,且第一个为当天数据
     * 被补全字段:可用库存,期初库存,状态,
     * 不需传安全水位
     * 其余非被补全字段必须每条有值
     *
     * @param calculateDTOS
     */
    public static void completeStockViewDataList(List<DashboardCalculateDTO> calculateDTOS) {
        for (int i = 0; i < calculateDTOS.size(); i++) {
            DashboardCalculateDTO calculateDTO = calculateDTOS.get(i);
            if (i == 0) {
                calculateDTO.setEnabledQuantity(calCurrentEnabledQuantity(calculateDTO.getQuantity()));
            }
            calStockViewData(calculateDTO);
            if (i != calculateDTOS.size() - 1) {
                calculateDTOS.get(i + 1).setEnabledQuantity(
                        calNextDayEnabledQuantity(calculateDTO.getEnabledQuantity(), calculateDTO.getOnWayQuantity(), calculateDTO.getOnWayOrderQuantity(),
                                calculateDTO.getTransferInQuantity(), calculateDTO.getForecastConsumption()));
            }
        }
    }

    /**
     * 补全补货视图数据
     * 必须时间正序,且第一个为当天数据
     * 被补全字段:可用库存,期初库存,状态
     * 其余非被补全字段必须每条有值
     *
     * @param calculateDTOS
     */
    public static void completeReplenishmentViewDataList(List<DashboardCalculateDTO> calculateDTOS) {
        for (int i = 0; i < calculateDTOS.size(); i++) {
            DashboardCalculateDTO calculateDTO = calculateDTOS.get(i);
            if (i == 0) {
                calculateDTO.setEnabledQuantity(calculateDTO.getQuantity());
            }
            calReplenishmentViewData(calculateDTO);
            if (i != calculateDTOS.size() - 1) {
                calculateDTOS.get(i + 1).setEnabledQuantity(
                        calNextDayEnabledQuantity(calculateDTO.getEnabledQuantity(), calculateDTO.getOnWayQuantity(), calculateDTO.getOnWayOrderQuantity(),
                                calculateDTO.getTransferInQuantity(), calculateDTO.getForecastConsumption()));
            }
        }
    }

    /**
     * 核心库存罗盘计算
     * 抽象处理 `现有库存` , `消耗库存`, `增加库存`
     * 逻辑: `现有库存` - `消耗库存` + `增加库存` = 第二天现有库存
     *
     * @param input
     */
    public static CoreResultDTO coreStockDashboardCalculate(CoreDashboardInput input) {
        CoreResultDTO coreResultDTO = new CoreResultDTO();
        List<CoreDashboardQuantityInfoDTO> dashboardInfos = Lists.newArrayList();
        List<LocalDate> sellOutDateList = Lists.newArrayList();
        // 1.把外部的增减量按照累加规则转为后续所需的Map工具
        Map<LocalDate, BigDecimal> increaseMap = transToMap(input.getIncreaseQuantity());
        Map<LocalDate, BigDecimal> decreaseMap = transToMap(input.getDecreaseQuantity());

        BigDecimal lastDayEndStockQuantity = null;
        for (LocalDate viewDate = input.getStartDate(); viewDate.isBefore(input.getEndDate()) || viewDate.isEqual(input.getEndDate()); viewDate = viewDate.plusDays(1)) {
            CoreDashboardQuantityInfoDTO infoDTO = new CoreDashboardQuantityInfoDTO();
            infoDTO.setViewDate(viewDate);
            if (viewDate.isEqual(input.getStartDate())) {
                infoDTO.setStartStockQuantity(input.getStartStockQuantity());
            } else {
                infoDTO.setStartStockQuantity(lastDayEndStockQuantity);
            }
            infoDTO.setCurrentIncreaseQuantity(increaseMap.containsKey(viewDate) ? increaseMap.get(viewDate) : BigDecimal.ZERO);
            infoDTO.setCurrentDecreaseQuantity(decreaseMap.containsKey(viewDate) ? decreaseMap.get(viewDate) : BigDecimal.ZERO);
            Boolean sellOutFlag = infoDTO.calculateEndStockQuantity(input.getSafeWaterLevel());
            lastDayEndStockQuantity = infoDTO.getEndStockQuantity();
            if (sellOutFlag) {
                sellOutDateList.add(viewDate);
            }
            dashboardInfos.add(infoDTO);
            if (input.getFirstSellOutBreak() && sellOutFlag) {
                break;
            }
        }
        coreResultDTO.setSellOutDateList(sellOutDateList);
        coreResultDTO.setQuantityInfos(dashboardInfos);
        return coreResultDTO;
    }

    /**
     * 把List转为Map
     * @param decreaseInputList
     * @return
     */
    private static Map<LocalDate, BigDecimal> transToMap(List<CoreQuantityInfoInput> decreaseInputList) {
        Map<LocalDate, BigDecimal> resultMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(decreaseInputList)) {
            decreaseInputList.forEach(decreaseInfo -> {
                LocalDate viewDate = decreaseInfo.getViewDate();
                if (resultMap.containsKey(viewDate)) {
                    resultMap.put(viewDate, resultMap.get(viewDate).add(decreaseInfo.getQuantity()));
                } else {
                    resultMap.put(viewDate, decreaseInfo.getQuantity());
                }
            });
        }
        return resultMap;
    }


    /**
     * 计算单条库存视图数据
     *
     * @param calculateDTO
     */
    private static void calStockViewData(DashboardCalculateDTO calculateDTO) {
        calculateDTO.setInitQuantity(calStockViewInitQuantity(calculateDTO.getEnabledQuantity(), calculateDTO.getOnWayQuantity(), calculateDTO.getOnWayOrderQuantity(), calculateDTO.getTransferInQuantity()));
        calculateDTO.setStatus(calStockViewStatus(calculateDTO.getInitQuantity(), calculateDTO.getForecastConsumption()));
    }

    /**
     * 计算单条补货视图数据
     *
     * @param calculateDTO
     */
    private static void calReplenishmentViewData(DashboardCalculateDTO calculateDTO) {
        calculateDTO.setInitQuantity(calReplenishmentViewInitQuantity(calculateDTO.getEnabledQuantity(), calculateDTO.getOnWayQuantity(), calculateDTO.getOnWayOrderQuantity(),
                calculateDTO.getTransferInQuantity()));
        calculateDTO.setStatus(calReplenishmentViewStatus(calculateDTO.getInitQuantity(), calculateDTO.getForecastConsumption(), calculateDTO.getSafeWaterLevel()));
    }

    /**
     * 计算库存视图的期初库存
     *
     * @return
     */
    private static BigDecimal calStockViewInitQuantity(BigDecimal enabledQuantity, Integer onWayQuantity, Integer onWayOrderQuantity, Integer transferInQuantity) {
        return enabledQuantity.add(BigDecimal.valueOf(onWayQuantity)).add(BigDecimal.valueOf(transferInQuantity).add(BigDecimal.valueOf(onWayOrderQuantity)));
    }

    /**
     * 计算补货视图的期初库存
     *
     * @return
     */
    private static BigDecimal calReplenishmentViewInitQuantity(BigDecimal enabledQuantity, Integer onWayQuantity, Integer onWayOrderQuantity, Integer transferInQuantity) {
        return enabledQuantity.add(BigDecimal.valueOf(onWayQuantity)).add(BigDecimal.valueOf(transferInQuantity)).add(BigDecimal.valueOf(onWayOrderQuantity));
    }


    /**
     * 计算可用库存
     *
     * @return
     */
    private static BigDecimal calCurrentEnabledQuantity(BigDecimal quantity) {
        // 实际第二天的可用库存就是前一天的期初库存-消耗量,无需考虑冻结及安全库存,冻结库存已存在前一天的销量中,安全库存由历史期初库存中已减去,无需多次减
        return quantity.compareTo(BigDecimal.ZERO) >= 0 ? quantity : BigDecimal.ZERO;
    }

    /**
     * 计算补货视图可用库存
     *
     * @return
     */
    private static BigDecimal calNextDayEnabledQuantity(BigDecimal currentEnabledQuantity, Integer currentOnWayQuantity, Integer currentOnWayOrderQuantity, Integer currentTransferInQuantity, BigDecimal currentConsumption) {
        BigDecimal currentQuantity = currentEnabledQuantity.add(BigDecimal.valueOf(currentOnWayQuantity)).add(BigDecimal.valueOf(currentTransferInQuantity)).add(BigDecimal.valueOf(currentOnWayOrderQuantity));
//        第二天的可用库存 = 当天的可用库存 + 当天采购在途 + 当天调拨在途 - 当天的消耗量
        return currentQuantity.compareTo(currentConsumption) >= 0 ? currentQuantity.subtract(currentConsumption) : BigDecimal.ZERO;
    }

    /**
     * 计算库存视图的状态
     *
     * @return
     * @see StockDashboardEnums.StockViewStatus
     */
    private static Integer calStockViewStatus(BigDecimal initQuantity, BigDecimal consumption) {
        return initQuantity.compareTo(consumption) > 0 ? StockDashboardEnums.StockViewStatus.ADEQUATE.getValue()
                : StockDashboardEnums.StockViewStatus.SELL_OUT.getValue();
    }

    /**
     * 计算补货视图的状态
     *
     * @return
     * @see StockDashboardEnums.ReplenishmentViewStatus
     */
    private static Integer calReplenishmentViewStatus(BigDecimal initQuantity, BigDecimal consumption, Integer safeWaterLevel) {
        return initQuantity.subtract(BigDecimal.valueOf(safeWaterLevel)).compareTo(consumption) > 0 ? StockDashboardEnums.ReplenishmentViewStatus.NORMAL.getValue() :
                StockDashboardEnums.ReplenishmentViewStatus.OUT_OF_STOCK.getValue();
    }



}
