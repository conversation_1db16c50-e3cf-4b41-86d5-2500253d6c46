package net.summerfarm.module.scp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;
import net.xianmu.common.enums.base.Enum2StringArgs;

public interface NoGeneratorEnums {

    /**
     * 前缀枚举
     * 枚举name:redis前缀, value:单号前缀
     */
    @Getter
    @AllArgsConstructor
    enum Prefix implements Enum2StringArgs {
        REQUIREMENT_NO("XQ", "需求计划单号"),
        REPLENISHMENT_NO("BH", "补货计划单号"),
        REPLENISHMENT_SUB_NO(null, "补货计划子单号"),
        ;

        private String value;
        private String content;
    }

    /**
     * 星期枚举
     */
    @Getter
    @AllArgsConstructor
    enum Week implements Enum2Args {
        MON(1,"周一"),
        TUE(2,"周二"),
        WED(3,"周三"),
        THUR(4,"周四"),
        FRI(5,"周五"),
        SAT(6,"周六"),
        SUN(7,"周日"),
        ;

        private Integer value;
        private String content;

        public static String getContentByValue(Integer code) {
            Week[] values = values();
            for (Week value : values) {
                if (value.getValue().equals(code)) {
                    return value.getContent();
                }
            }
            return null;
        }
    }
}
