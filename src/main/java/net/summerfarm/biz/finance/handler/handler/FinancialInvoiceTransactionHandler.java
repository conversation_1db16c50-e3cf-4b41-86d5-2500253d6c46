package net.summerfarm.biz.finance.handler.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import net.summerfarm.biz.finance.dto.FinancialInvoiceOrderDTO;
import net.summerfarm.biz.finance.dto.FinancialInvoiceUserDTO;
import net.summerfarm.biz.finance.enums.FinancialInvoiceEnum;
import net.summerfarm.biz.finance.handler.FinancialTransaction;
import net.summerfarm.biz.finance.util.BwFinancialInvoiceRequestUtil;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.enums.InvoiceResultEnum;
import net.summerfarm.mapper.BigFinancialInvoiceSkuMapper;
import net.summerfarm.mapper.manage.FinanceInvoiceExpandMapper;
import net.summerfarm.mapper.manage.FinancialInvoiceMapper;
import net.summerfarm.mapper.manage.FinancialInvoiceOrdernoRelationMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.BigFinancialSkuVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @describe 开票事务处理器,防止类内部引用导致的事务失效
 * @date 2022/9/14 19:55
 */
@Service
public class FinancialInvoiceTransactionHandler extends BaseService implements FinancialTransaction {

    private final Logger logger = LoggerFactory.getLogger(FinancialInvoiceTransactionHandler.class);

    @Resource
    FinancialInvoiceMapper financialInvoiceMapper;

    @Resource
    FinancialInvoiceOrdernoRelationMapper financialInvoiceOrdernoRelationMapper;

    @Resource
    OrdersMapper ordersMapper;

    @Resource
    FinanceInvoiceExpandMapper financeInvoiceExpandMapper;
    @Resource
    BigFinancialInvoiceSkuMapper bigFinancialInvoiceSkuMapper;
    @Override
    @Transactional(rollbackOn = Exception.class)
    public void creatInvoice(FinancialInvoiceUserDTO financialInvoiceUserDTO,InvoiceResultEnum.orderInvoiceStatus invoiceStatus) {
        // 生成票据信息
        this.insertInvoicing(financialInvoiceUserDTO);
        // 生成发票拓展信息
        this.insertInvoiceExpand(financialInvoiceUserDTO);
        // 生成票据与订单关系
        this.insertInvoiceRelationAndUpdateOrder(financialInvoiceUserDTO,invoiceStatus);
    }

    private void insertInvoicing(FinancialInvoiceUserDTO financialInvoiceUserDTO){
        // 插入发票表
        FinancialInvoice insertContent = new FinancialInvoice();
        insertContent.setInvoiceId(financialInvoiceUserDTO.getInvoiceId());
        insertContent.setAmountMoney(financialInvoiceUserDTO.getAllMoney());
        insertContent.setTaxAmount(financialInvoiceUserDTO.getTotalTaxMoney());
        insertContent.setInvoiceType(financialInvoiceUserDTO.getInvoiceType());
        insertContent.setTitle(financialInvoiceUserDTO.getInvoiceConfig().getInvoiceTitle());
        Integer adminId = Optional.ofNullable(getAdminId()).orElse(NumberUtils.INTEGER_ZERO);
        insertContent.setCreatorId(adminId);
        insertContent.setCreatorName(financialInvoiceUserDTO.getCreatorName());
        insertContent.setMId(financialInvoiceUserDTO.getMId());
        insertContent.setInvoiceResult(InvoiceResultEnum.invoiceResult.TO_BE_INVOICED.ordinal());
        insertContent.setDutyFreeGood(financialInvoiceUserDTO.getDutyFreeGood());
        String reqMsgId = BwFinancialInvoiceRequestUtil.createReqMsgId(null);
        insertContent.setMailAddress(financialInvoiceUserDTO.getMailAddress());
        insertContent.setSerialNumber(reqMsgId);
        if(Objects.nonNull(financialInvoiceUserDTO.getFinanceOrderId())){
            insertContent.setFinanceOrderId(financialInvoiceUserDTO.getFinanceOrderId());
        }
        insertContent.setBelongType(financialInvoiceUserDTO.getBelongType());
        insertContent.setMailAddress(financialInvoiceUserDTO.getMailAddress());
        insertContent.setCreatorRemark(financialInvoiceUserDTO.getCreatorRemark());
        insertContent.setSellingEntityName(financialInvoiceUserDTO.getSellingEntityName());
        financialInvoiceMapper.insertSelective(insertContent);
        logger.info("插入票据信息表成功:{}",insertContent);

        financialInvoiceUserDTO.setFinancialInvoiceId(insertContent.getId());
        financialInvoiceUserDTO.setReqMsgId(reqMsgId);
    }

    private void insertInvoiceExpand(FinancialInvoiceUserDTO financialInvoiceUserDTO){
        // 插入发票信息拓展表
        FinanceInvoiceExpand financeInvoiceExpand = financialInvoiceUserDTO.getFinanceInvoiceExpand();
        financeInvoiceExpand.setFinancialInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
        financeInvoiceExpandMapper.insertSelective(financeInvoiceExpand);
        logger.info("插入票据拓展信息表成功:{}",financeInvoiceExpand);
    }

    private void insertInvoiceRelationAndUpdateOrder(FinancialInvoiceUserDTO financialInvoiceUserDTO, BigFinancialSkuVO it,
                                                     InvoiceResultEnum.orderInvoiceStatus orderResultEnum) {
        // 插入发票与订单关联表
        List<FinancialInvoiceOrdernoRelation> outs = new ArrayList<>();
        Orders updateContent = new Orders();
        updateContent.setInvoiceStatus(orderResultEnum.ordinal());
        List<Long> orderItemIdList = it.getOrderItemIdList();
        if (CollectionUtil.isEmpty(orderItemIdList)) {
            // 整单均为免税品商品,但存在运费或超时加单等服务费时, 会存在无商品明细id情况,此时明细id默认为0
            orderItemIdList = new ArrayList<>();
            orderItemIdList.add(NumberUtils.INTEGER_ZERO.longValue());
        }
        for (Long orderItemId : orderItemIdList) {
            FinancialInvoiceOrdernoRelation insert = new FinancialInvoiceOrdernoRelation();
            insert.setFinancialInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
            insert.setOrderNo(financialInvoiceUserDTO.getOrderNo());
            insert.setOrderItemId(orderItemId);
            outs.add(insert);
        }
        updateContent.setOrderNo(financialInvoiceUserDTO.getOrderNo());
        ordersMapper.update(updateContent);
        logger.info("变更订单表开票状态至:{},成功,orderNo:{},", orderResultEnum.ordinal(), financialInvoiceUserDTO.getOrderNo());
        financialInvoiceOrdernoRelationMapper.insertBatch(outs);
        logger.info("插入票据与订单关联信息表成功:{}", outs);
    }

    private void insertInvoiceRelationAndUpdateOrder(FinancialInvoiceUserDTO financialInvoiceUserDTO,
                                                     InvoiceResultEnum.orderInvoiceStatus orderResultEnum){
        // 插入发票与订单关联表
        List<FinancialInvoiceOrdernoRelation> list = new ArrayList<>();
        Orders updateContent = new Orders();
        updateContent.setInvoiceStatus(orderResultEnum.ordinal());

        List<FinancialInvoiceOrderDTO> financialInvoiceOrderDTOList = financialInvoiceUserDTO.getFinancialInvoiceOrderDTOList();
        for (FinancialInvoiceOrderDTO financialInvoiceOrderDTO : financialInvoiceOrderDTOList) {
            List<Long> orderItemIdList = financialInvoiceOrderDTO.getOrderItemIdList();
            if(CollectionUtil.isEmpty(orderItemIdList)){
                // 整单均为免税品商品,但存在运费或超时加单等服务费时, 会存在无商品明细id情况,此时明细id默认为0
                orderItemIdList = new ArrayList<>();
                orderItemIdList.add(NumberUtils.INTEGER_ZERO.longValue());
            }

            for (Long orderItemId : orderItemIdList) {
                FinancialInvoiceOrdernoRelation insert = new FinancialInvoiceOrdernoRelation();
                insert.setFinancialInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
                insert.setOrderNo(financialInvoiceOrderDTO.getOrderNo());
                insert.setOrderItemId(orderItemId);
                list.add(insert);
            }
            updateContent.setOrderNo(financialInvoiceOrderDTO.getOrderNo());
            ordersMapper.update(updateContent);
            logger.info("变更订单表开票状态至:{},成功,orderNo:{},",orderResultEnum.ordinal(),financialInvoiceOrderDTO.getOrderNo());
        }
        financialInvoiceOrdernoRelationMapper.insertBatch(list);
        logger.info("插入票据与订单关联信息表成功:{}",list);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateInvoiceOrderStatus(FinancialInvoice financialInvoice) {
        List<FinancialInvoiceOrdernoRelation> financialInvoiceOrderNoRelationList = financialInvoiceOrdernoRelationMapper
                .selectByFinancialInvoiceId(financialInvoice.getId());

        financialInvoiceOrderNoRelationList = Optional.ofNullable(financialInvoiceOrderNoRelationList).orElse(new ArrayList<>());
        List<String> orderNoList = financialInvoiceOrderNoRelationList.stream()
                .map(FinancialInvoiceOrdernoRelation::getOrderNo)
                .collect(Collectors.toList());

        Orders updateContent = new Orders();
        int invoiceStatus = InvoiceResultEnum.orderInvoiceStatus.TO_BE_INVOICED.ordinal();
//        boolean isPartialRedFlush = FinancialInvoiceEnum.invoiceType.isGeneral(financialInvoice.getInvoiceType())
//                && ObjectUtil.notEqual(FinancialInvoiceEnum.invoiceState.FULL_RED_CHARGE.ordinal(),financialInvoice.getInvoiceStatus())
//                && ObjectUtil.notEqual(InvoiceResultEnum.invoiceResult.CANCEL.ordinal(),financialInvoice.getInvoiceResult());
//        if(isPartialRedFlush){
//            invoiceStatus = InvoiceResultEnum.orderInvoiceStatus.PARTIAL_INVOICING.ordinal();
//        }
//        // 红冲票失败,修改订单状态至已开票
//        if( BigDecimal.ZERO.compareTo(financialInvoice.getAmountMoney()) > 0){
//            invoiceStatus = InvoiceResultEnum.orderInvoiceStatus.FULL_INVOICING.ordinal();
//        }

        updateContent.setInvoiceStatus(invoiceStatus);
        for (String orderNo : orderNoList) {
            logger.info("更新订单开票状态为:{},订单号为:{},发票id为:{}",invoiceStatus,orderNo,financialInvoice.getId());
            updateContent.setOrderNo(orderNo);
            ordersMapper.update(updateContent);
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void creatInvoice(FinancialInvoiceUserDTO financialInvoiceUserDTO, BigFinancialSkuVO bigFinancialSkuVO, InvoiceResultEnum.orderInvoiceStatus invoiceStatus) {
        // 抬头id
        this.insertInvoicing(financialInvoiceUserDTO);

        //备注信息
        FinanceInvoiceExpand financeInvoiceExpand = bigFinancialSkuVO.getFinanceInvoiceExpand();
        if (financeInvoiceExpand != null) {
            financeInvoiceExpand.setFinancialInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
            financeInvoiceExpandMapper.insertSelective(financeInvoiceExpand);
        }
        //不是红冲的时候
        if (financialInvoiceUserDTO.getRedFlushTag() == null) {
            List<BigFinancialInvoiceSku> bigFinancialInvoiceSkis = bigFinancialSkuVO.getList().stream().map(
                    it -> {
                        BigFinancialInvoiceSku bigFinancialInvoiceSku = new BigFinancialInvoiceSku();
                        bigFinancialInvoiceSku.setBatchNo(bigFinancialSkuVO.getBatchNo());
                        bigFinancialInvoiceSku.setStatus(0);
                        bigFinancialInvoiceSku.setBillNumber(it.getBillNumber());
                        bigFinancialInvoiceSku.setInvoiceId(financialInvoiceUserDTO.getFinancialInvoiceId());
                        bigFinancialInvoiceSku.setPrice(it.getPrice());
                        bigFinancialInvoiceSku.setSku(it.getSku());
                        bigFinancialInvoiceSku.setOrderNo(bigFinancialSkuVO.getBatchNo().split("_")[0]);
                        bigFinancialInvoiceSku.setOrderSkuAmount(it.getAmount());
                        bigFinancialInvoiceSku.setRedSkuAmount(0);
                        bigFinancialInvoiceSku.setPdName(it.getPdName());
                        bigFinancialInvoiceSku.setWeight(it.getWeight());
                        return bigFinancialInvoiceSku;
                    }
            ).collect(Collectors.toList());
            bigFinancialInvoiceSkuMapper.batchInsert(bigFinancialInvoiceSkis);
        }
        // 生成票据与订单关系 ok
        this.insertInvoiceRelationAndUpdateOrder(financialInvoiceUserDTO, bigFinancialSkuVO, invoiceStatus);

    }
}
