package net.summerfarm.biz.finance.task;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.finance.bo.BwInvoiceBO;
import net.summerfarm.biz.finance.bo.digital.BwContentDigitalRedInvoicePreparationBO;
import net.summerfarm.biz.finance.builder.BwFinancialInvoiceBuilder;
import net.summerfarm.biz.finance.config.FinanceInvoiceConfig;
import net.summerfarm.biz.finance.config.FinanceWebhookConfig;
import net.summerfarm.biz.finance.constant.BwInvoiceConstant;
import net.summerfarm.biz.finance.dto.FinancialInvoiceSellerInfoDTO;
import net.summerfarm.biz.finance.enums.FinancialInvoiceAsyncTaskEnum;
import net.summerfarm.biz.finance.util.BwFinancialInvoiceRequestUtil;
import net.summerfarm.common.exceptions.finance.BwLoginExpirationException;
import net.summerfarm.common.exceptions.finance.BwScanFaceExpirationException;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.facade.bms.fms.SellingEntityFacade;
import net.summerfarm.mapper.FinancialInvoiceAsyncTaskMapper;
import net.summerfarm.mapper.manage.FinancialInvoiceMapper;
import net.summerfarm.model.bo.FinancialInvoiceAsyncTaskBO;
import net.summerfarm.model.domain.FinancialInvoice;
import net.summerfarm.model.domain.FinancialInvoiceAsyncTask;
import net.summerfarm.service.ConfigService;
import net.summerfarm.service.finance.FinancialSellingEntityQueryService;
import net.summerfarm.service.finance.strategy.InvoiceTaskExecutorStrategy;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.security.ProviderException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 财务发票异步任务
 * @author: George
 * @date: 2024-05-13
 **/

@Slf4j
@Component
public class FinancialInvoiceAsyncJob extends XianMuJavaProcessorV2 {

    private static final String MAX_INVOKE_COUNT = "maxInvokeCount";
    private static final String INVOKE_LIMIT = "invokeLimit";
    private static final String TASK_IDS = "taskIds";

    @Resource
    private FinancialInvoiceAsyncTaskMapper financialInvoiceAsyncTaskMapper;
    @Resource
    private InvoiceTaskExecutorStrategy invoiceTaskExecutorStrategy;
    @Resource
    private FinancialInvoiceMapper financialInvoiceMapper;
    @Resource
    private FinanceWebhookConfig financeWebhookConfig;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private FinancialSellingEntityQueryService sellingEntityQueryService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Integer maxInvokeCount = 10;
        Integer invokeLimit = 10;
        List<Long> taskIds = null;
        try {
            if (!StringUtils.isEmpty(context.getInstanceParameters())) {
                JSONObject jsonObject = JSONObject.parseObject(context.getInstanceParameters());
                if (jsonObject.containsKey(MAX_INVOKE_COUNT)) {
                    maxInvokeCount = jsonObject.getInteger(MAX_INVOKE_COUNT);
                }
                if (jsonObject.containsKey(TASK_IDS)) {
                    taskIds = JSONObject.parseArray(jsonObject.getString(TASK_IDS), Long.class);
                }
            }
            if (!StringUtils.isEmpty(context.getJobParameters())) {
                JSONObject jsonObject = JSONObject.parseObject(context.getJobParameters());
                if (jsonObject.containsKey(MAX_INVOKE_COUNT)) {
                    maxInvokeCount = jsonObject.getInteger(MAX_INVOKE_COUNT);
                }
                if (jsonObject.containsKey(INVOKE_LIMIT)) {
                    invokeLimit = jsonObject.getInteger(INVOKE_LIMIT);
                }
            }
        } catch (Exception e) {
            log.error("任务转换参数失败,jobParameters:{}", context.getJobParameters());
        }

        // 查询待执行的任务
        List<FinancialInvoiceAsyncTask> financialInvoiceAsyncTasks = getWaitInvokeTasks(taskIds, maxInvokeCount, invokeLimit);

        if (CollectionUtils.isEmpty(financialInvoiceAsyncTasks)) {
            log.info("没有待执行的发票任务");
            return new ProcessResult(true);
        }

        // 查询本次任务的原票信息
        List<Long> invoiceIds = financialInvoiceAsyncTasks.stream().map(FinancialInvoiceAsyncTask::getInvoiceId).collect(Collectors.toList());
        List<FinancialInvoice> financialInvoices = financialInvoiceMapper.selectByIds(invoiceIds);
        Map<Long, FinancialInvoice> financialInvoiceMap = financialInvoices.stream().collect(Collectors.toMap(FinancialInvoice::getId, financialInvoice -> financialInvoice));

        // 获取销售主体信息
        Set<String> sellingEntitySets = financialInvoices.stream().map(FinancialInvoice::getSellingEntityName).collect(Collectors.toSet());
        Map<String, FinancialInvoiceSellerInfoDTO> sellerInfoMap = sellingEntityQueryService.getSellingEntityInfo(sellingEntitySets);

        try {
            for (FinancialInvoiceAsyncTask financialInvoiceAsyncTask : financialInvoiceAsyncTasks) {
                FinancialInvoiceAsyncTaskBO financialInvoiceAsyncTaskBO = buildBO(financialInvoiceAsyncTask, financialInvoiceMap, sellerInfoMap);
                if (financialInvoiceAsyncTaskBO == null) {
                    continue;
                }

                // 过滤一些任务的执行（红字发票时间调控）
                boolean filterResult = filterOutTask(financialInvoiceAsyncTaskBO);
                if (filterResult) {
                    continue;
                }

                // 先变更成处理中
                financialInvoiceAsyncTaskMapper.updateTaskResult(financialInvoiceAsyncTask.getId(), FinancialInvoiceAsyncTaskEnum.Status.PROCESSING.getResult());
                invoiceTaskExecutorStrategy.getExecutor(financialInvoiceAsyncTask.getType()).execute(financialInvoiceAsyncTaskBO);
            }
        } catch (BwLoginExpirationException e) {
            // 登录 然后结束本次任务
            BwFinancialInvoiceRequestUtil.login(e.getLoginName(), e.getPassword(), e.getTaxNumber(), e.getAreaCode());
        } catch (BwScanFaceExpirationException e) {
            notifyScanFaceExpirationMessage();
        }
        return new ProcessResult(true);
    }

    /**
     * 过滤本次不执行的任务
     * 1、红字发票的执行时间5min一次（百旺红字发票接口比较慢，按照每分钟处理可能下次再次执行该任务，上次执行的结果还没处理好）
     * @param financialInvoiceAsyncTaskBO
     * @return
     */
    private boolean filterOutTask(FinancialInvoiceAsyncTaskBO financialInvoiceAsyncTaskBO) {
        Integer type = financialInvoiceAsyncTaskBO.getType();
        if (Objects.equals(type, FinancialInvoiceAsyncTaskEnum.TaskType.INVALID.getType()) && LocalDateTime.now().getMinute() % 2 != 0) {
            log.info("作废发票任务:{}，当前时间不执行，等待下次执行", financialInvoiceAsyncTaskBO.getId());
            return true;
        }

        // 红冲的时候，如果蓝字发票还没有开票成功，则不执行红冲
        if (Objects.equals(type, FinancialInvoiceAsyncTaskEnum.TaskType.INVALID_INVOICE_PREPARATION.getType())) {
            String invokeParams = financialInvoiceAsyncTaskBO.getInvokeParams();
            BwInvoiceBO bwInvoiceBO = JSONObject.parseObject(invokeParams, BwInvoiceBO.class);
            String digitalContent = bwInvoiceBO.getDigitalContent();
            BwContentDigitalRedInvoicePreparationBO preparationBO = JSONObject.parseObject(digitalContent, BwContentDigitalRedInvoicePreparationBO.class);
            String kprq = preparationBO.getKprq();
            String yfphm = preparationBO.getYfphm();
            if (org.apache.commons.lang3.StringUtils.isBlank(kprq) || org.apache.commons.lang3.StringUtils.isBlank(yfphm)) {
                FinancialInvoice financialInvoice = financialInvoiceAsyncTaskBO.getFinancialInvoice();
                LocalDateTime invoiceIssueTime = financialInvoice.getInvoiceIssueTime();
                if (invoiceIssueTime == null) {
                    // 开票时间没有暂时跳过
                    log.info("红冲发票任务:{}，蓝字发票还没有开票成功，等待下次执行", financialInvoiceAsyncTaskBO.getId());
                    return true;
                }
                kprq = DateUtil.format(invoiceIssueTime, "yyyy-MM-dd HH:mm:ss");
                preparationBO.setKprq(kprq);

                String invoiceNumber = financialInvoice.getInvoiceNumber();
                if (org.apache.commons.lang3.StringUtils.isBlank(invoiceNumber)) {
                    // 开票号码没有暂时跳过
                    log.info("红冲发票任务:{}，蓝字发票还没有开票成功，等待下次执行", financialInvoiceAsyncTaskBO.getId());
                    return true;
                }
                yfphm = invoiceNumber;
                preparationBO.setKprq(kprq);
                preparationBO.setYfphm(yfphm);
                bwInvoiceBO.setDigitalContent(JSON.toJSONString(preparationBO));
                financialInvoiceAsyncTaskBO.setInvokeParams(JSON.toJSONString(bwInvoiceBO));
            }
        }
        return false;
    }

    /**
     * 发送扫脸认证失效消息
     * 财务接收-去百旺的云票助手进行扫脸认证
     * 扫脸未通过则无法进行开票
     */
    private void notifyScanFaceExpirationMessage() {
        String content = "开票任务扫脸认证失效，请扫脸认证";
        String notifyAdminId = BwFinancialInvoiceBuilder.buildBwInvoiceKey(BwInvoiceConstant.SCAN_FACE_EXPIRATION_NOTIFY_ADMIN_ID);
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), null, content);
        DingTalkMsgReceiverIdBO feiShuBo = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        feiShuBo.setReceiverIdList(Collections.singletonList(Long.valueOf(notifyAdminId)));
        dingTalkMsgSender.sendMessageWithFeiShu(feiShuBo);
    }

    private FinancialInvoiceAsyncTaskBO buildBO(FinancialInvoiceAsyncTask task, Map<Long, FinancialInvoice> financialInvoiceMap, Map<String, FinancialInvoiceSellerInfoDTO> sellerInfoMap) {
        Long invoiceId = task.getInvoiceId();
        FinancialInvoice invoice = financialInvoiceMap.get(invoiceId);
        if (invoice == null) {
            log.error("发票任务:{}没有对应的发票信息", task.getId(), new ProviderException("发票任务没有对应的发票信息"));
            return null;
        }
        FinancialInvoiceAsyncTaskBO financialInvoiceAsyncTaskBO = new FinancialInvoiceAsyncTaskBO();
        financialInvoiceAsyncTaskBO.setInvoiceId(invoice.getId());
        financialInvoiceAsyncTaskBO.setFinancialInvoice(invoice);
        financialInvoiceAsyncTaskBO.setInvokeParams(task.getInvokeParams());
        financialInvoiceAsyncTaskBO.setInvokeCount(task.getInvokeCount());
        financialInvoiceAsyncTaskBO.setType(task.getType());
        financialInvoiceAsyncTaskBO.setId(task.getId());
        financialInvoiceAsyncTaskBO.setTaskId(task.getTaskId());
        financialInvoiceAsyncTaskBO.setSellerInfoDTO(sellerInfoMap.get(invoice.getSellingEntityName()));
        return financialInvoiceAsyncTaskBO;
    }

    private List<FinancialInvoiceAsyncTask> getWaitInvokeTasks(List<Long> taskIds, Integer maxInvokeCount, Integer limit) {
        if (!CollectionUtils.isEmpty(taskIds)) {
            return financialInvoiceAsyncTaskMapper.selectByIds(taskIds);
        }
       return financialInvoiceAsyncTaskMapper.queryWaitInvokeTasks(maxInvokeCount, limit);
    }
}
