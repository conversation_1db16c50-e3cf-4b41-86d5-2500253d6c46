package net.summerfarm.biz.finance.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/9/14 20:35
 */
@Data
public class FinancialInvoiceOrderItemDTO {

    /**
     * 详情id
     */
    private Long orderItemId;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;

    /**
     * sku
     */
    private String sku;
    /**
     * 品类id
     */
    private Integer categoryId;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 明细调整金额
     */
    private BigDecimal adjustOrderItemPrice;

    /**
     * 规格型号
     */
    private String weigh;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 购买单价
     */
    private BigDecimal price;

    /**
     * 税率码
     */
    private String taxRateCode;


    private Integer sort;

    public String getSkuPriceGroup(){
        return sku + "_" + price;
    }

    public BigDecimal getSkuTotalPrice(){
        return (price == null ? BigDecimal.ZERO : price).multiply(new BigDecimal(amount == null ? 0 : amount));
    }
}
